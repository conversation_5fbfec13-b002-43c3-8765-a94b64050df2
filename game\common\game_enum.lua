-----------------------------------------------------
-- 游戏中的枚举
-----------------------------------------------------
GameEnum =
{
	LUANDAOU_WAVES = 6, 							-- 乱斗战场轮数
	--职业
	ROLE_PROF_1 = 1, 								--职业-剑
	ROLE_PROF_2 = 2, 								--职业-和尚
	ROLE_PROF_3 = 3, 								--职业-枪
	ROLE_PROF_4 = 4, 								--职业-道士
	
	ROLE_PROF_11 = 11,								--骋逸剑侠
	ROLE_PROF_12 = 12,								--御律琴师
	ROLE_PROF_13 = 13,								--三才伞仙
	ROLE_PROF_14 = 14,								--无双镰姬
	ROLE_PROF_21 = 21,                              --四方神君
	ROLE_PROF_22 = 22,                              --四象神主
	ROLE_PROF_23 = 23,                              --四盈仙鸿
	ROLE_PROF_24 = 24,                              --四蕴仙淑
	ROLE_PROF_31 = 31,                              --八世剑皇
	ROLE_PROF_32 = 32,                              --八音琴帝
	ROLE_PROF_33 = 33,                              --八韵伞王
	ROLE_PROF_34 = 34,                              --八泽镰魁
	ROLE_PROF_41 = 41,                              --九荒天尊
	ROLE_PROF_42 = 42,                              --九星天宿
	ROLE_PROF_43 = 43,                              --九华圣使
	ROLE_PROF_44 = 44,                              --九幽圣姬
	ROLE_PROF_51 = 51,                              --无妄剑宗
	ROLE_PROF_52 = 52,                              --太无琴禅
	ROLE_PROF_53 = 53,                              --虚灵伞法
	ROLE_PROF_54 = 54,                              --空玄镰道

	--模型材质球品质
	MATERIAL_QUALITY_PBR = 0,						--pbr材质球
	MATERIAL_QUALITY_UI = 1,						--ui级别材质球

	ROLE_PROF_NOLIMIT = 5, 							--不限职业（装备）
	ZHUANZHI_PROF_NOLIMIT = 0, 						--不限转职职业（装备）

	--性别
	FEMALE = 0,										--女性
	MALE = 1,										--男性
	SEX_NOLIMIT = 2,								--不限男女（装备）

	-- size
	SMALL = 1,
	MIDDLE = 2,
	LARGE = 3,

	--阵营
	ROLE_CAMP_0 = 0, 								--无
	ROLE_CAMP_1 = 1,								--仙府
	ROLE_CAMP_2 = 2, 								--魔殿(未开放)
	ROLE_CAMP_3 = 3, 								--灵山
	ROLE_CAMP_4 = 4, 								--未开放

	ITEM_MAX_STAR = 5,								-- 物品最大星数
	MAX_CROSS_SERVER_GROUP_COUNT = 10,				-- 最大跨服组数

	--物品颜色
	ITEM_COLOR_WHITE = 0,							-- 白
	ITEM_COLOR_GREEN = 1,							-- 绿
	ITEM_COLOR_BLUE = 2,							-- 蓝
	ITEM_COLOR_PURPLE = 3,							-- 紫
	ITEM_COLOR_ORANGE = 4,							-- 橙
	ITEM_COLOR_RED = 5,								-- 红
	ITEM_COLOR_PINK = 6,							-- 粉色
	ITEM_COLOR_GLOD = 7,							-- 金色
	ITEM_COLOR_COLOR_FUL = 8,						-- 炫彩
	ITEM_COLOR_SHINING_GOLD = 9, 					-- 耀金
	ITEM_COLOR_XUAN_QING = 10,						-- 玄青

	--装备品质颜色
	EQUIP_COLOR_GREEN = 1,							-- 绿
	EQUIP_COLOR_BLUE = 2,							-- 蓝
	EQUIP_COLOR_PURPLE = 3,							-- 紫
	EQUIP_COLOR_ORANGE = 4,							-- 橙
	EQUIP_COLOR_RED = 5,							-- 红
	EQUIP_COLOR_TEMP = 6,							-- 粉
	EQUIP_COLOR_GLOD = 7,							-- 金色
	EQUIP_COLOR_FULL = 8,                           -- 幻彩色

	--物品大类型
	ITEM_BIGTYPE_MEDICINE = 0, 						--回复药品类型
	ITEM_BIGTYPE_BUFF = 1, 							--buff类型
	ITEM_BIGTYPE_EXPENSE = 2, 						--消耗类型 能直接使用
	ITEM_BIGTYPE_GEMSTONE = 3, 						--宝石类型
	ITEM_BIGTYPE_POSITION = 4,						--坐标相关类型
	ITEM_BIGTYPE_OTHER = 5,							--被动使用类型 type value 最好不要直接实用
	ITEM_BIGTYPE_TASK = 6,							--人物类型
	ITEM_BIGTYPE_GIF = 7,							--礼包类型	能直接使用
	ITEM_BIGTYPE_SCENE_BUFF = 8,					--场景buff类型
	ITEM_BIGTYPE_EQUIPMENT = 100,					--装备类型
	ITEM_BIGTYPE_VIRTUAL = 101,						--虚拟类型 金币 铜币
	ITEM_BIGTYPE_JL = 102,							--精灵类型
	E_TYPE_ZHUANSHENG_MIN = 800,					--转生装备类型

	--装备大类型
	EQUIP_BIG_TYPE_FANG = 0,                        --头盔、衣服、裤子、仙链、鞋子  五件防装
	EQUIP_BIG_TYPE_GONG = 1,                        --武器、仙坠、仙符  三件攻击装
	EQUIP_BIG_TYPE_SPECIAL = 2,                     --仙镯、仙戒  两件特殊装

	EQUIP_BIG_TYPE_NORMAL = 0,						--普通装备
	EQUIP_BIG_TYPE_XIANQI = 1,						--仙器装备

	EQUIP_BIG_TYPE_NORMAL_COUNT = 6,				--普通装备数量
	EQUIP_BIG_TYPE_XIANQI_COUNT = 4,				--仙器装备数量

	XIANQI_SHENPIN_ADD_NEED_COUNT = 2,				--仙器升品加成需要数量

	FASHION_CRISIS_NUM = 100,						--时装资源id临界值，大于则转换
	--装备类型
	EQUIP_TYPE_TOUKUI = 100,						--头盔
	EQUIP_TYPE_YIFU = 101,							--衣服
	EQUIP_TYPE_KUZI = 102,							--裤子
	EQUIP_TYPE_XIANLIAN = 103,						--仙链
	EQUIP_TYPE_XIEZI = 104,							--鞋子
	EQUIP_TYPE_WUQI = 105,							--武器 剑 镰 琴 伞
	EQUIP_TYPE_XIANZHUI = 106,						--仙坠
	EQUIP_TYPE_XIANFU = 107,						--仙符
	EQUIP_TYPE_XIANJIE = 108,						--仙戒
	EQUIP_TYPE_XIANZHUO = 109,						--仙镯
	--EQUIP_TYPE_HUFU = 110,							--护符
	EQUIP_TYPE_MIANJIA = 111,						--面甲
	EQUIP_TYPE_XIANGLIAN_1 = 112,					--项链
	EQUIP_TYPE_DIAOZHUI = 113,						--吊坠

    BossEnterPos = -1,                                --特殊处理点击进入pos

	EQUIP_TYPE_JINGLING = 201,						-- 精灵
	EQUIP_TYPE_HUNJIE = 110,						-- 婚戒
	EQUIP_TYPE_XIAOGUI = 203,						-- 小鬼
	EQUIP_TYPE_XIAOGUI_COMPOSE = 250,				-- 小鬼合成材料
	EQUIP_TYPE_TIANSHEN0 = 720,						-- 天神神戒
	EQUIP_TYPE_TIANSHEN1 = 721,						-- 天神神镯
	EQUIP_TYPE_TIANSHEN2 = 722,						-- 天神神符
	EQUIP_TYPE_TIANSHEN3 = 723,						-- 天神神坠

	BABY_MAX_COUNT = 10,							-- 最大可拥有的宝宝数量限制
	BABY_MAX_LEVEL = 10,							-- 宝宝最大等级
	BABY_MAX_GRADE = 10, 							-- 宝宝最大阶级
	BABY_SPIRIT_COUNT = 4,							-- 最大守护精灵数量
	MAX_SHENG_BABY_COUNT = 5,						-- 不超生最多生宝宝数量
	CAN_SHENG_BABY_LEVEL = 4,						-- 不超生最多生宝宝数量

	E_TYPE_SHITU_TOUKUI = 300,						-- 师徒头盔
	E_TYPE_SHITU_YIFU = 301,						-- 师徒衣服
	E_TYPE_SHITU_HUTUI = 302,						-- 师徒护腿
	E_TYPE_SHITU_XIEZI = 303,						-- 师徒鞋子
	E_TYPE_SHITU_HUSHOU = 304,						-- 师徒护手
	E_TYPE_SHITU_XIANGLIAN = 305,					-- 师徒项链
	E_TYPE_SHITU_WUQI = 306,						-- 师徒武器
	E_TYPE_SHITU_JIEZHI = 307,						-- 师徒戒指

	E_TYPE_SHENSHOU_PART_0 = 560,						-- 神兽装备 兽角
	E_TYPE_SHENSHOU_PART_1 = 561,						-- 神兽装备 兽蹄
	E_TYPE_SHENSHOU_PART_2 = 562,						-- 神兽装备 兽盔
	E_TYPE_SHENSHOU_PART_3 = 563,						-- 神兽装备 兽爪
	E_TYPE_SHENSHOU_PART_4 = 564,						-- 神兽装备 兽环

	E_TYPE_XIANJIE_EQUI = 580,						-- 仙界装备
	E_TYPE_JIE_LING = 1100,							-- 界灵

	E_TYPE_SHENJI = 770,							--神机装备

	E_TYPE_SIXIANG = 780,							-- 四象
	E_TYPE_SIXIANG_BONE = 790,						-- 四象魂装

	--装备位置索引
	EQUIP_INDEX_TOUKUI = 0,							-- 头盔
	EQUIP_INDEX_YIFU = 1,							-- 衣服
	EQUIP_INDEX_KUZI = 2,							-- 裤子
	EQUIP_INDEX_XIANLIAN = 3,						-- 仙链
	EQUIP_INDEX_XIEZI = 4,  						-- 鞋子
	EQUIP_INDEX_WUQI = 5,							-- 武器
	EQUIP_INDEX_XIANZHUI = 6,						-- 仙坠
	EQUIP_INDEX_XIANFU = 7,							-- 匕首
	EQUIP_INDEX_XIANJIE = 8,						-- 仙戒
	EQUIP_INDEX_XIANZHUO = 9,						-- 仙镯
	EQUIP_INDEX_HUNJIE = 10,						-- 同心锁（婚戒）
	EQUIP_INDEX_HUFU = 11,							-- 守护
	EQUIP_INDEX_MIANJIA = 12,						-- 面甲
	EQUIP_INDEX_XIANGLIAN_1 = 13,					-- 项链1
	EQUIP_INDEX_DIAOZHUI = 14,						-- 吊坠

	EQUIP_INDEX_MAX_COUNT = 11,						--当前装备数量
	EQUIP_BAOSHI_ATTR_NUM = 4,						--宝石最大属性数
	EQUIP_LINGYU_ATTR_NUM = 4,						--灵玉最大属性数
	EQUIP_YUPO_ATTR_NUM = 2,						--玉魄最大属性数

	MAGIC_EQUIP_MAX_COUNT = 5,			                --魔器装备最大数量
 	MAGIC_EQUIP_STONE_SLOT_COUNT = 6,		            --魔器能镶嵌的宝石孔个数

	--师徒装备位置索引
	E_INDEX_SHITU_TOUKUI = 0,						--师徒头盔
	E_INDEX_SHITU_YIFU = 1,							--师徒衣服
	E_INDEX_SHITU_HUTUI = 2,						--师徒护腿
	E_INDEX_SHITU_XIEZI = 3,						--师徒鞋子
	E_INDEX_SHITU_HUSHOU = 4,						--师徒护手
	E_INDEX_SHITU_XIANGLIAN = 5,					--师徒项链
	E_INDEX_SHITU_WUQI = 6,							--师徒武器
	E_INDEX_SHITU_JIEZHI = 7,						--师徒戒指
	E_INDEX_SHITU_MAX = 8,

	-- 进阶装备
	-- JINJIE_EQUIP_TYPE_MOUNT = 1, 					-- 坐骑装备
	-- JINJIE_EQUIP_TYPE_WING = 2, 					-- 羽翼装备
	-- JINJIE_EQUIP_TYPE_FABAO = 3, 					-- 法宝装备
	-- JINJIE_EQUIP_TYPE_LINGCHONG = 4, 				-- 灵宠装备
	-- JINJIE_EQUIP_TYPE_SHENWU = 5, 					-- 神武装备
	-- JINJIE_EQUIP_TYPE_LINGGONG = 6, 				-- 灵弓装备
	-- JINJIE_EQUIP_TYPE_LINGQI = 7, 					-- 灵器装备
	-- JINJIE_EQUIP_TYPE_LINGYI = 8, 					-- 灵翼装备

	JINJIE_TYPE_MIN = 400,							-- 进阶装备最小值

	E_TYPE_MOUNT_1 = 400,							-- 坐骑头盔
	E_TYPE_MOUNT_2 = 401,							-- 坐骑衣服
	E_TYPE_MOUNT_3 = 402,							-- 坐骑护腿
	E_TYPE_MOUNT_4 = 403,							-- 坐骑鞋子

	E_TYPE_WING_1 = 450,							-- 羽翼头盔
	E_TYPE_WING_2 = 451,							-- 羽翼衣服
	E_TYPE_WING_3 = 452,							-- 羽翼护腿
	E_TYPE_WING_4 = 453,							-- 羽翼鞋子

	E_TYPE_FABAO_1 = 500,							-- 法宝头盔
	E_TYPE_FABAO_2 = 501,							-- 法宝衣服
	E_TYPE_FABAO_3 = 502,							-- 法宝护腿
	E_TYPE_FABAO_4 = 503,							-- 法宝鞋子

	E_TYPE_LINGCHONG_1 = 550,						-- 灵宠头盔
	E_TYPE_LINGCHONG_2 = 551,						-- 灵宠衣服
	E_TYPE_LINGCHONG_3 = 552,						-- 灵宠护腿
	E_TYPE_LINGCHONG_4 = 553,						-- 灵宠鞋子


	E_TYPE_SHENWU_1 = 600,							-- 神武头盔
	E_TYPE_SHENWU_2 = 601,							-- 神武衣服
	E_TYPE_SHENWU_3 = 602,							-- 神武护腿
	E_TYPE_SHENWU_4 = 603,							-- 神武鞋子

	E_TYPE_LINGGONG_1 = 650,						-- 灵弓头盔
	E_TYPE_LINGGONG_2 = 651,						-- 灵弓衣服
	E_TYPE_LINGGONG_3 = 652,						-- 灵弓护腿
	E_TYPE_LINGGONG_4 = 653,						-- 灵弓鞋子

	E_TYPE_LINGQI_1 = 700,							-- 灵器头盔
	E_TYPE_LINGQI_2 = 701,							-- 灵器衣服
	E_TYPE_LINGQI_3 = 702,							-- 灵器护腿
	E_TYPE_LINGQI_4 = 707,							-- 灵器鞋子

	E_TYPE_LINGYI_1 = 750,							-- 灵翼头盔
	E_TYPE_LINGYI_2 = 751,							-- 灵翼衣服
	E_TYPE_LINGYI_3 = 752,							-- 灵翼护腿
	E_TYPE_LINGYI_4 = 753,							-- 灵翼鞋子

	E_TYPE_JIANZHEN =800, 							--剑阵

	JINJIE_TYPE_MAX = 899,							-- 进阶装备最大值

	-- 宝石类型
	STONE_GONGJI = 1,								-- 攻击类型宝石
	STONE_FANGYU = 2,								-- 防御类型宝石
	STONE_HP = 3,									-- 血气类型的宝石

	-- 灵玉类型
	LINGYU_GONGJI = 1,								-- 攻击类型宝石
	LINGYU_FANGYU = 2,								-- 防御类型宝石

	--人物属性类型
	FIGHT_CHARINTATTR_TYPE_BAOSHIATTR = 0,          --宝石属性
	FIGHT_CHARINTATTR_TYPE_GLOBAL_COOLDOWN = 1,		--全局cooldown时间
	FIGHT_CHARINTATTR_TYPE_HP = 2,					--血量

	COLOR_PINK_SORT_IDX = 96,						--粉色仙品属性排序
	COLOR_FULL_SORT_IDX = 97,						--彩装仙品属性排序

	-- 战斗属性枚举
    FIGHT_CHARINTATTR_TYPE_MAXHP = 3,                				-- 最大生命
    FIGHT_CHARINTATTR_TYPE_GONGJI = 4,                    			-- 攻击
    FIGHT_CHARINTATTR_TYPE_FANGYU = 5,                    			-- 防御
    FIGHT_CHARINTATTR_TYPE_POJIA = 6,                    			-- 破甲
    FIGHT_CHARINTATTR_TYPE_YUANSU_SH = 7,                  			-- 元素伤害
    FIGHT_CHARINTATTR_TYPE_YUANSU_HJ = 8,                  			-- 元素护甲
    FIGHT_CHARINTATTR_TYPE_SHANBI_PER = 9,                  		-- 闪避率
    FIGHT_CHARINTATTR_TYPE_MINGZHONG_PER = 10,                		-- 命中率
    FIGHT_CHARINTATTR_TYPE_BAOJI_PER = 11,                  		-- 暴击率
    FIGHT_CHARINTATTR_TYPE_KANGBAO_PER = 12,                  		-- 抗暴率
    FIGHT_CHARINTATTR_TYPE_LINAJI_PER = 13,                  		-- 连击率
    FIGHT_CHARINTATTR_TYPE_LIANJIKANG_PER = 14,                		-- 连击抵抗
    FIGHT_CHARINTATTR_TYPE_JICHUANG_PER = 15,                		-- 击穿率
    FIGHT_CHARINTATTR_TYPE_JICHUANGKANG_PER = 16,              		-- 击穿抵抗
    FIGHT_CHARINTATTR_TYPE_ZHUAGNBEI_SM_JC_PER = 17,              	-- 装备生命加成
    FIGHT_CHARINTATTR_TYPE_ZHUAGNBEI_GJ_JC_PER = 18,              	-- 装备攻击加成
    FIGHT_CHARINTATTR_TYPE_ZHUAGNBEI_FY_JC_PER = 19,              	-- 装备防御加成
    FIGHT_CHARINTATTR_TYPE_ZHUAGNBEI_PJ_JC_PER = 20,              	-- 装备破甲加成
    FIGHT_CHARINTATTR_TYPE_SHENGMING_JC_PER = 21,              		-- 生命加成
    FIGHT_CHARINTATTR_TYPE_GONGJI_JC_PER = 22,                		-- 攻击加成
    FIGHT_CHARINTATTR_TYPE_JIANREN_PER = 23,                  		-- 坚韧
    FIGHT_CHARINTATTR_TYPE_CHUANTOU_PER = 24,                		-- 穿透
    FIGHT_CHARINTATTR_TYPE_YUANSU_SH_JC_PER = 25,             	 	-- 元素伤害加成
    FIGHT_CHARINTATTR_TYPE_YUANSU_HJ_JC_PER = 26,             	 	-- 元素护甲加成
    FIGHT_CHARINTATTR_TYPE_SHENGMING_QQ = 27,                		-- 生命窃取
    FIGHT_CHARINTATTR_TYPE_FANGTAN = 28,                    		-- 反弹伤害
    FIGHT_CHARINTATTR_TYPE_SHANGHAI_ZS = 29,                  		-- 真实伤害
    FIGHT_CHARINTATTR_TYPE_FANGYU_ZS = 30,                  		-- 真实防御
    FIGHT_CHARINTATTR_TYPE_SHENGMING_QQ_JC_PER = 31,              	-- 生命窃取加成
    FIGHT_CHARINTATTR_TYPE_FANGTAN_JC_PER = 32,                		-- 反弹伤害加成
    FIGHT_CHARINTATTR_TYPE_SHANGHAI_ZS_JC_PER = 33,              	-- 真实伤害加成
    FIGHT_CHARINTATTR_TYPE_FANGYU_ZS_JC_PER = 34,           	   	-- 真实防御加成
    FIGHT_CHARINTATTR_TYPE_ZENGSHANG_BOSS_PER = 35,              	-- 首领增伤
    FIGHT_CHARINTATTR_TYPE_JIANSHANG_BOSS_PER = 36,              	-- 首领减伤
    FIGHT_CHARINTATTR_TYPE_ZENGSHANG_GUAIWU_PER = 37,            	-- 怪物增伤
    FIGHT_CHARINTATTR_TYPE_JIANSHANG_GUAIWU_PER = 38,            	-- 怪物减伤
    FIGHT_CHARINTATTR_TYPE_ZENGSHANG_BOSS_DK_PER = 39,            	-- 首领增伤抵抗
    FIGHT_CHARINTATTR_TYPE_JIANSHANG_BOSS_DK_PER = 40,            	-- 首领减伤抵抗
    FIGHT_CHARINTATTR_TYPE_ZENGSHANG_GUAIWU_DK_PER = 41,          	-- 怪物增伤抵抗
    FIGHT_CHARINTATTR_TYPE_JIANSHANG_GUAIWU_DK_PER = 42,          	-- 怪物减伤抵抗
    FIGHT_CHARINTATTR_TYPE_ZENGSHANG_PER = 43,                		-- 玩家增伤
    FIGHT_CHARINTATTR_TYPE_JIANSHANG_PER = 44,                		-- 玩家减伤
    FIGHT_CHARINTATTR_TYPE_ZENGSHANG_BS_PER = 45,             	 	-- 变身增伤
    FIGHT_CHARINTATTR_TYPE_JIANSHANG_BS_PER = 46,             	 	-- 变身减伤
    FIGHT_CHARINTATTR_TYPE_MINGZHONG_YC_PER = 47,             	 	-- 异常状态命中
    FIGHT_CHARINTATTR_TYPE_DIKANG_YC_PER = 48,                		-- 异常状态抵抗
    FIGHT_CHARINTATTR_TYPE_ZHILIAOXIAOGUO_PER = 49,              	-- 治疗效果
    FIGHT_CHARINTATTR_TYPE_ZENGSHANG_YC_PER = 50,              		-- 异常状态增伤
    FIGHT_CHARINTATTR_TYPE_GEDANG_PER = 51,                  		-- 格挡率
    FIGHT_CHARINTATTR_TYPE_PODANG_PER = 52,                  		-- 破档率
    FIGHT_CHARINTATTR_TYPE_BAOJI_SHANGHAI_PER = 53,              	-- 暴击伤害
    FIGHT_CHARINTATTR_TYPE_GEDANG_MS_PER = 54,                		-- 格挡免伤
    FIGHT_CHARINTATTR_TYPE_SHENGMING_HF = 55,                		-- 生命回复
    FIGHT_CHARINTATTR_TYPE_GONGJI_SD = 56,                  		-- 攻击速度
    FIGHT_CHARINTATTR_TYPE_SHANGHAI_JN = 57,                  		-- 技能伤害
    FIGHT_CHARINTATTR_TYPE_MOVE_SPEED = 58,	                		-- 移动速度
    FIGHT_CHARINTATTR_TYPE_SHENGMING_HF_PER = 59,              		-- 生命回复比例
    FIGHT_CHARINTATTR_TYPE_ZENGSHANG_GX_PER = 60,              		-- 高血增伤
    FIGHT_CHARINTATTR_TYPE_ZENGSHANG_XR_PER = 61,              		-- 虚弱增伤
    FIGHT_CHARINTATTR_TYPE_SHANGHAI_JC_PER = 62,                	-- 伤害加成
    FIGHT_CHARINTATTR_TYPE_SHANGHAI_QUAN_JC_PER = 63,            	-- 全属性伤害加成
    FIGHT_CHARINTATTR_TYPE_SHANGHAI_JM_PER = 64,                	-- 伤害减免
    FIGHT_CHARINTATTR_TYPE_SHANGHAI_QUAN_JM_PER = 65,            	-- 全属性伤害减免
    FIGHT_CHARINTATTR_TYPE_YUANSU_JK_PER = 66,                		-- 元素减抗
    FIGHT_CHARINTATTR_TYPE_YUANSU_KX_PER = 67,                		-- 元素抗性
    FIGHT_CHARINTATTR_TYPE_LIANJI_SHANGHAI_PER = 68,				-- 连击伤害比例
	FIGHT_CHARINTATTR_TYPE_SHAGUAI_JB_DIAOLUO_PER = 69,				-- 杀怪金币掉落
	FIGHT_CHARINTATTR_TYPE_JINENG_SHANGHAI_ZJ_PER = 70,				-- 技能伤害增加
	FIGHT_CHARINTATTR_TYPE_JINENG_SHANGHAI_JM_PER = 71,				-- 技能伤害减免
	FIGHT_CHARINTATTR_TYPE_EQUIP_QIANGHUA_PER = 72,					-- 装备强化加成
	FIGHT_CHARINTATTR_TYPE_BOSS_ZHENSHANG = 73,						-- BOSS真伤
	FIGHT_CHARINTATTR_TYPE_BOSS_PALSY_PER = 74,						-- BOSS麻痹
	FIGHT_CHARINTATTR_TYPE_BOSS_SECKILL_PER = 75,					-- BOSS秒杀
	FIGHT_CHARINTATTR_TYPE_LEI_SHANGHAI_ZJ_PER = 76,				-- 雷罚职业伤害增加 废除
	FIGHT_CHARINTATTR_TYPE_LEI_SHANGHAI_JM_PER = 77,				-- 雷罚职业伤害减免 废除
	FIGHT_CHARINTATTR_TYPE_GEDANG_MS_MY_PER = 78,					-- 格挡免伤免疫
	FIGHT_CHARINTATTR_TYPE_BAOJI_SHANGHAI_JM_PER = 79,				-- 暴击伤害减免
	FIGHT_CHARINTATTR_TYPE_LIANJI_SHANGHAI_JM_PER = 80,				-- 连击伤害减免
	FIGHT_CHARINTATTR_TYPE_BAOJI_SHANGHAI_VALUE = 81,				-- 暴击伤害固定值
	FIGHT_CHARINTATTR_TYPE_KANGBAO_SHANGHAI_VALUE = 82,				-- 抗暴伤害固定值
	FIGHT_CHARINTATTR_TYPE_SHENGMING_ZB_ROLE_JC_PER = 83,			-- 生命加成 = 装备 + 人物等级属性
	FIGHT_CHARINTATTR_TYPE_GONGJI_ZB_ROLE_JC_PER = 84,				-- 攻击加成 = 装备 + 人物等级属性
	FIGHT_CHARINTATTR_TYPE_FANGYU_ZB_ROLE_JC_PER = 85,				-- 坚韧-防御加成 = 装备 + 人物等级属性
	FIGHT_CHARINTATTR_TYPE_POJIA_ZB_ROLE_JC_PER = 86,				-- 穿透-破甲加成 = 装备 + 人物等级属性
	FIGHT_CHARINTATTR_TYPE_GONGJI_WUQI_JC_PER = 87,					-- 主武器/副武器 - 攻击加成
	FIGHT_CHARINTATTR_TYPE_POJIA_WUQI_JC_PER = 88,					-- 主武器/副武器 - 破甲加成
	FIGHT_CHARINTATTR_TYPE_GONGJI_SHIPING_JC_PER = 89,				-- 仙链/仙坠/仙戒/仙镯 - 攻击加成
	FIGHT_CHARINTATTR_TYPE_KILL_MONSTER_PER_EXP = 90,				-- 打怪经验加成万份比
	FIGHT_CHARINTATTR_TYPE_BAOJI_JC_PER = 91,						-- 暴击加成
	FIGHT_CHARINTATTR_TYPE_KANGBAO_JC_PER = 92,						-- 抗暴加成
	FIGHT_CHARINTATTR_TYPE_RARE_EXTERIOR_RATE_PER = 93,				-- 珍稀外观掉落加成
	FIGHT_CHARINTATTR_TYPE_RARE_EQUIP_RATE_PER = 94,				-- 珍稀装备掉落加成


	-- 基础属性枚举
	BASE_CHARINTATTR_TYPE_MAXHP = 101,                				-- 最大生命
    BASE_CHARINTATTR_TYPE_GONGJI = 102,                    			-- 攻击
    BASE_CHARINTATTR_TYPE_FANGYU = 103,                    			-- 防御
    BASE_CHARINTATTR_TYPE_POJIA = 104,                    			-- 破甲
    BASE_CHARINTATTR_TYPE_YUANSU_SH = 105,                  		-- 元素伤害
    BASE_CHARINTATTR_TYPE_YUANSU_HJ = 106,                  		-- 元素护甲
    BASE_CHARINTATTR_TYPE_SHANBI_PER = 107,                  		-- 闪避率
    BASE_CHARINTATTR_TYPE_MINGZHONG_PER = 108,                		-- 命中率
    BASE_CHARINTATTR_TYPE_BAOJI_PER = 109,                  		-- 暴击率
    BASE_CHARINTATTR_TYPE_KANGBAO_PER = 110,                  		-- 抗暴率
    BASE_CHARINTATTR_TYPE_LIANJI_PER = 111,                  		-- 连击率
    BASE_CHARINTATTR_TYPE_LIANJIKANG_PER = 112,                		-- 连击抵抗
    BASE_CHARINTATTR_TYPE_JICHUANG_PER = 113,                  		-- 击穿率
    BASE_CHARINTATTR_TYPE_JICHUANGKANG_PER = 114,                	-- 击穿抵抗
    BASE_CHARINTATTR_TYPE_ZHUAGNBEI_SM_JC_PER = 115,              	-- 装备生命加成
    BASE_CHARINTATTR_TYPE_ZHUAGNBEI_GJ_JC_PER = 116,              	-- 装备攻击加成
    BASE_CHARINTATTR_TYPE_ZHUAGNBEI_FY_JC_PER = 117,              	-- 装备防御加成
    BASE_CHARINTATTR_TYPE_ZHUAGNBEI_PJ_JC_PER = 118,              	-- 装备破甲加成
    BASE_CHARINTATTR_TYPE_SHENGMING_JC_PER = 119,                	-- 生命加成
    BASE_CHARINTATTR_TYPE_GONGJI_JC_PER = 120,                		-- 攻击加成
    BASE_CHARINTATTR_TYPE_JIANREN_PER = 121,                  		-- 坚韧
    BASE_CHARINTATTR_TYPE_CHUANTOU_PER = 122,                  		-- 穿透
    BASE_CHARINTATTR_TYPE_YUANSU_SH_JC_PER = 123,                	-- 元素伤害加成
    BASE_CHARINTATTR_TYPE_YUANSU_HJ_JC_PER = 124,                	-- 元素护甲加成
    BASE_CHARINTATTR_TYPE_SHENGMING_QQ = 125,                  		-- 生命窃取
    BASE_CHARINTATTR_TYPE_FANGTAN = 126,                    		-- 反弹伤害
    BASE_CHARINTATTR_TYPE_SHANGHAI_ZS = 127,                  		-- 真实伤害
    BASE_CHARINTATTR_TYPE_FANGYU_ZS = 128,                  		-- 真实护甲
    BASE_CHARINTATTR_TYPE_SHENGMING_QQ_JC_PER = 129,              	-- 生命窃取加成
    BASE_CHARINTATTR_TYPE_FANGTAN_JC_PER = 130,                		-- 反弹伤害加成
    BASE_CHARINTATTR_TYPE_SHANGHAI_ZS_JC_PER = 131,              	-- 真实伤害加成
    BASE_CHARINTATTR_TYPE_FANGYU_ZS_JC_PER = 132,                	-- 真实护甲加成
    BASE_CHARINTATTR_TYPE_ZENGSHANG_BOSS_PER = 133,              	-- 首领增伤
    BASE_CHARINTATTR_TYPE_JIANSHANG_BOSS_PER = 134,              	-- 首领减伤
    BASE_CHARINTATTR_TYPE_ZENGSHANG_GUAIWU_PER = 135,              	-- 怪物增伤
    BASE_CHARINTATTR_TYPE_JIANSHANG_GUAIWU_PER = 136,              	-- 怪物减伤
    BASE_CHARINTATTR_TYPE_ZENGSHANG_BOSS_DK_PER = 137,            	-- 首领增伤抵抗
    BASE_CHARINTATTR_TYPE_JIANSHANG_BOSS_DK_PER = 138,            	-- 首领减伤抵抗
    BASE_CHARINTATTR_TYPE_ZENGSHANG_GUAIWU_DK_PER = 139,            -- 怪物增伤抵抗
    BASE_CHARINTATTR_TYPE_JIANSHANG_GUAIWU_DK_PER = 140,           	-- 怪物减伤抵抗
    BASE_CHARINTATTR_TYPE_ZENGSHANG_PER = 141,                		-- 玩家增伤
    BASE_CHARINTATTR_TYPE_JIANSHANG_PER = 142,                		-- 玩家减伤
    BASE_CHARINTATTR_TYPE_ZENGSHANG_BS_PER = 143,                	-- 变身增伤
    BASE_CHARINTATTR_TYPE_JIANSHANG_BS_PER = 144,                	-- 变身减伤
    BASE_CHARINTATTR_TYPE_MINGZHONG_YC_PER = 145,                	-- 异常状态命中
    BASE_CHARINTATTR_TYPE_DIKANG_YC_PER = 146,                		-- 异常状态抵抗
    BASE_CHARINTATTR_TYPE_ZHILIAOXIAOGUO_PER = 147,              	-- 治疗效果
    BASE_CHARINTATTR_TYPE_ZENGSHANG_YC_PER = 148,                	-- 异常状态增伤
    BASE_CHARINTATTR_TYPE_GEDANG_PER = 149,                  		-- 格挡率
    BASE_CHARINTATTR_TYPE_PODANG_PER = 150,                  		-- 破档率
    BASE_CHARINTATTR_TYPE_BAOJI_SHANGHAI_PER = 151,              	-- 暴击伤害
    BASE_CHARINTATTR_TYPE_GEDANG_MS_PER = 152,                		-- 格挡免伤
    BASE_CHARINTATTR_TYPE_SHENGMING_HF = 153,                  		-- 生命回复
    BASE_CHARINTATTR_TYPE_GONGJI_SD = 154,                  		-- 攻击速度
    BASE_CHARINTATTR_TYPE_SHANGHAI_JN = 155,                  		-- 技能伤害
    BASE_CHARINTATTR_TYPE_MOVE_SPEED = 156,	                		-- 移动速度
    BASE_CHARINTATTR_TYPE_SHENGMING_HF_PER = 157,                	-- 生命回复比例
    BASE_CHARINTATTR_TYPE_ZENGSHANG_GX_PER = 158,                	-- 高血增伤
    BASE_CHARINTATTR_TYPE_ZENGSHANG_XR_PER = 159,                	-- 虚弱增伤
    BASE_CHARINTATTR_TYPE_SHANGHAI_JC_PER = 160,                	-- 伤害加成
    BASE_CHARINTATTR_TYPE_SHANGHAI_QUAN_JC_PER = 161,              	-- 全属性伤害加成
    BASE_CHARINTATTR_TYPE_SHANGHAI_JM_PER = 162,                	-- 伤害减免
    BASE_CHARINTATTR_TYPE_SHANGHAI_QUAN_JM_PER = 163,              	-- 全属性伤害减免
    BASE_CHARINTATTR_TYPE_YUANSU_JK_PER = 164,                		-- 元素减抗
    BASE_CHARINTATTR_TYPE_YUANSU_KX_PER = 165,                		-- 元素抗性
    BASE_CHARINTATTR_TYPE_LIANJI_SHANGHAI_PER =	166,				-- 连击伤害比例
	BASE_CHARINTATTR_TYPE_SHAGUAI_JB_DIAOLUO_PER = 167,				-- 杀怪金币掉落
	BASE_CHARINTATTR_TYPE_JINENG_SHANGHAI_ZJ_PER = 168,				-- 技能伤害增加
	BASE_CHARINTATTR_TYPE_JINENG_SHANGHAI_JM_PER = 169,				-- 技能伤害减免
	BASE_CHARINTATTR_TYPE_EQUIP_QIANGHUA_PER =	170,				-- 装备强化加成
	BASE_CHARINTATTR_TYPE_BOSS_ZHEN_SHANG =	171,					-- BOSS真伤
	BASE_CHARINTATTR_TYPE_BOSS_PALSY_PER = 172,						-- BOSS麻痹
	BASE_CHARINTATTR_TYPE_BOSS_SECKILL_PER = 173,					-- 玄冰职业伤害减免
	BASE_CHARINTATTR_TYPE_LEI_SHANGHAI_ZJ_PER =	174,				-- 雷罚职业伤害增加
	BASE_CHARINTATTR_TYPE_LEI_SHANGHAI_JM_PER =	175,				-- 雷罚职业伤害减免
	BASE_CHARINTATTR_TYPE_GEDANG_MS_MY_PER = 176,					-- 格挡免伤免疫
	BASE_CHARINTATTR_TYPE_BAOJI_SHANGHAI_JM_PER = 177,				-- 暴击伤害减免
	BASE_CHARINTATTR_TYPE_LIANJI_SHANGHAI_JM_PER = 178,				-- 连击伤害减免
	BASE_CHARINTATTR_TYPE_BAOJI_SHANGHAI = 179,						-- 暴击伤害固定值
	BASE_CHARINTATTR_TYPE_KANGBAO_SHANGHAI = 180,					-- 抗暴伤害固定值
	BASE_CHARINTATTR_TYPE_SHENGMING_ZB_ROLE_JC_PER = 181,			-- 生命加成 = 装备 + 人物等级属性
	BASE_CHARINTATTR_TYPE_GONGJI_ZB_ROLE_JC_PER = 182,				-- 攻击加成 = 装备 + 人物等级属性
	BASE_CHARINTATTR_TYPE_FANGYU_ZB_ROLE_JC_PER = 183,				-- 坚韧-防御加成 = 装备 + 人物等级属性
	BASE_CHARINTATTR_TYPE_POJIA_ZB_ROLE_JC_PER = 184,				-- 穿透-破甲加成 = 装备 + 人物等级属性
	BASE_CHARINTATTR_TYPE_GONGJI_WUQI_JC_PER = 185,					-- 主武器/副武器 - 攻击加成
	BASE_CHARINTATTR_TYPE_POJIA_WUQI_JC_PER = 186,					-- 主武器/副武器 - 破甲加成
	BASE_CHARINTATTR_TYPE_GONGJI_SHIPIN_JC_PER = 187,				-- 仙链/仙坠/仙戒/仙镯 - 攻击加成
	BASE_CHARINTATTR_TYPE_KILL_MONSTER_PER_EXP = 188,				-- 打怪经验加成万份比
	BASE_CHARINTATTR_TYPE_BAOJI_JC_PER = 189,						-- 暴击加成
	BASE_CHARINTATTR_TYPE_KANGBAO_JC_PER = 190,						-- 抗暴加成
	BASE_CHARINTATTR_TYPE_RARE_EXTERIOR_RATE_PER = 191,				--  珍稀外观掉落加成
	BASE_CHARINTATTR_TYPE_RARE_EQUIP_RATE_PER = 192,				-- 珍稀装备掉落加成

	Lock_Time = 300,								-- 自动锁屏时间

	SEED_AUTO_JOIN_GUILD = 1, 			--一键申请加入公会


	--切换攻击模式
	SET_ATTACK_MODE_SUCC = 0,						-- 成功
	SET_ATTACK_MODE_PROTECT_LEVEL = 1,				-- 新手保护期
	SET_ATTACK_MODE_NO_CAMP = 2,					-- 没有加入阵营
	SET_ATTACK_MODE_NO_GUILD = 3,					-- 没有加入军团
	SET_ATTACK_MODE_NO_TEAM = 4,					-- 没有组队
	SET_ATTACK_MODE_PEACE_INTERVAL = 5,				-- 小于和平模式切换时间间隔
	SET_ATTACK_MODE_NO_GUILD_UNION = 6,				-- 没有军团联盟
	SET_ATTACK_MODE_STATUS_LIMIT = 7,				-- 当前状态下不允许切换攻击模式
	SET_ATTACK_MODE_SCENE_LIMIT = 8,				-- 该场景不可切换其他模式
	SET_ATTACK_MODE_MAX = 9,

	--名字颜色
	NAME_COLOR_WHITE = 0,							-- 白名
	NAME_COLOR_RED_1 = 1,							-- 红名
	NAME_COLOR_RED_2 = 2,							-- 红名
	NAME_COLOR_RED_3 = 3,							-- 红名
	NAME_COLOR_MAX = 0,

	--NPC任务状态
	TASK_STATUS_NONE = 0,							--无
	TASK_STATUS_CAN_ACCEPT = 1,						--有可接任务
	TASK_STATUS_ACCEPT_PROCESS = 2,					--有未完成的任务
	TASK_STATUS_COMMIT = 3,							--有可提交任务
	TASK_STATUS_FINISH = 4,							--已完成已提交

	--任务类型
	TASK_TYPE_ZHU = 0,								--主线
	TASK_TYPE_ZHI = 1,								--支线
	TASK_TYPE_RI = 2,								--日常
	TASK_TYPE_HU = 3,								--护送
	TASK_TYPE_MENG = 4,								--仙盟
	TASK_TYPE_CAMP = 5,								--阵营任务
	TASK_TYPE_HUAN = 6,								--跑环任务
	TASK_TYPE_SHANG = 7,							--悬赏任务
    TASK_TYPE_ZHUAN = 8,							--转职任务
    TASK_TYPE_GUILD_BUILD = 9,						--仙盟建设
    TASK_TYPE_FAKE_XIUZHENROAD = 10,			    --修真路假任务
    TASK_TYPE_TREASURE_MAP = 12,					--藏宝图
    TASK_TYPE_HUSONG_SPC = 13,						--护送假任务

	TASK_TYPE_GUAJI = 100,							--挂机任务
	TASK_TYPE_LING = 101,							--护送灵石

    XiuZhenRoad_Fake_Task_id = 32002,                --修真路假任务id
    HUSONG_SPC_TASK_ID = 52000,                	  	 --护送假任务id
    ASSIGN_TASK_ID = 52001,                	  	 	 --委托假任务id
	TASK_ZHU_IMP_TASK_ID = 500,						-- 限时小鬼任务id(用于完成任务弹出恭喜获得)
	TASK_ZHU_IMP_ID = 10101,						-- 限时小鬼道具id(用于完成任务弹出恭喜获得)
	
	--任务完成条件
	TASK_COMPLETE_CONDITION_0 = 0, 					--对话
	TASK_COMPLETE_CONDITION_1 = 1, 					--打怪
	TASK_COMPLETE_CONDITION_2 = 2, 					--采集
	TASK_COMPLETE_CONDITION_3 = 3, 					--通关副本
	TASK_COMPLETE_CONDITION_4 = 4, 					--进入场景
	TASK_COMPLETE_CONDITION_5 = 5, 					--完成任务
	TASK_COMPLETE_CONDITION_6 = 6, 					--啥也不干
	TASK_COMPLETE_CONDITION_7 = 7, 					--执行某种操作
	TASK_COMPLETE_CONDITION_8 = 8, 					--满足某种状态
	TASK_COMPLETE_CONDITION_9 = 9, 					-- 进入某种场景类型
	TASK_COMPLETE_CONDITION_10 = 10,				-- 拾取某种物品
	TASK_COMPLETE_CONDITION_11 = 11,				-- 杀怪任务（悬赏）
	TASK_COMPLETE_CONDITION_12 = 12, 				-- 仙品星数达到n级
	TASK_COMPLETE_CONDITION_13 = 13,				-- 消耗
	TASK_COMPLETE_CONDITION_14 = 14,                -- 收集物品
	TASK_COMPLETE_CONDITION_15 = 15,				-- n件装备到达m级
	TASK_COMPLETE_CONDITION_16 = 16,				-- 获取到一定活跃度(日常任务经验)
	TASK_COMPLETE_CONDITION_17 = 17,				-- 捐献n件装备到帮派仓库
	TASK_COMPLETE_CONDITION_18 = 18, 				-- 装备n个聚魂
	TASK_COMPLETE_CONDITION_19 = 19,                -- 变身, 执行某种操作变身找NPC对话
	TASK_COMPLETE_CONDITION_20 = 20,				-- 已解锁技能平均等级达到X级 param1=x
	TASK_COMPLETE_CONDITION_21 = 21,                -- 进入水波纹副本
	TASK_COMPLETE_CONDITION_22 = 22,                -- 购买道具任务
	TASK_COMPLETE_CONDITION_23 = 23,                -- 杀怪伪掉落
	TASK_COMPLETE_CONDITION_24 = 24,				-- 完成委托任务
	TASK_COMPLETE_CONDITION_25 = 25,				-- 完成修仙之旅章节
	TASK_COMPLETE_CONDITION_26 = 26,				-- 行走到特定区域（c_param1=场景ID, c_param2=pos_x, c_param3=pos_y,c_param4=范围）
	TASK_COMPLETE_CONDITION_27 = 27,				-- 行走到特定区域使用技能（c_param1=场景ID, c_param2=pos_x, c_param3=pos_y,c_param4=范围,c_param5=技能id）

	-- 任务 参数1
	TASK_SATISFY_STATUS_TYPE_MOUNT_GRADE = 1,										-- 坐骑阶级达到n级
	TASK_SATISFY_STATUS_TYPE_OWN_FRIEND = 2,										-- 拥有好友达到n个
	TASK_SATISFY_STATUS_TYPE_XIANJIE_LEVEL = 8,										-- 仙阶达到n级
	TASK_SATISFY_STATUS_TYPE_TRAIN_LEVEL = 12,										-- 修炼到N级
	TASK_SATISFY_STATUS_TYPE_13 = 13,												-- 如果是转职任务，这个表示打副本
	TASK_SATISFY_STATUS_TYPE_FB_LAYER = 14,											-- 副本通关到第几层
	TASK_SATISFY_STATUS_TYPE_LIGHT_UP_TIANMING = 15,								-- 点亮全部转职天命
	TASK_SATISFY_STATUS_TYPE_UPGRADE_GRADE = 16,									-- 进阶阶级达到n级
	TASK_SATISFY_STATUS_TYPE_CORNUCOPIA = 17,										-- 聚宝盆次数
	TASK_SATISFY_STATUS_TYPE_JLCHESTSHOP = 18,										-- 精灵寻宝
	TASK_SATISFY_STATUS_TYPE_TOTAL_STRENGTHEN_LEVEL = 19,							-- 强化总等级
	TASK_SATISFY_STATUS_TYPE_INLAY_STONE = 20,										-- 镶嵌石头
	TASK_SATISFY_STATUS_TYPE_DAILY_TASK = 21,										-- 日常任务
	TASK_SATISFY_STATUS_TYPE_EXP_FB = 22,											-- 经验副本
	TASK_SATISFY_STATUS_TYPE_GUILD_TASK = 23,										-- 帮派任务
	TASK_SATISFY_STATUS_TYPE_HUSONG_TASK = 24,										-- 护送任务
	TASK_SATISFY_STATUS_TYPE_HOTSPRING = 25,										-- 温泉
	TASK_SATISFY_STATUS_TYPE_ZHUXIE = 26,											-- 诛邪
	TASK_SATISFY_STATUS_TYPE_BOUNTY_TASK = 27,										-- 悬赏任务
	TASK_SATISFY_STATUS_TYPE_LIGHT_UP_CANGLONG = 28,								-- 转职点亮苍龙到n级
	TASK_SATISFY_STATUS_TYPE_JIANGHU_FB = 29,										-- 江湖副本
	TASK_SATISFY_STATUS_TYPE_CHANGE_AVATAR = 30,									-- 更换头像
	TASK_SATISFY_STATUS_TYPE_TIANMING_FB = 31,										-- 天命闯关
	TASK_SATISFY_STATUS_TYPE_TIAN_XIAN_GE_LAYER = 32,								-- 天仙阁（诛仙塔）到第几层
	TASK_SATISFY_STATUS_TYPE_CON_TO_PROF5 = 33,										-- 达到转职5级的条件
	TASK_SATISFY_STATUS_TYPE_ZHU_SHEN_TA_LAYER = 34,								-- 诛神塔到第几层
	TASK_SATISFY_STATUS_TYPE_LIGHT_UP_LONGPO = 35,									-- 点亮全部转职龙魄
	TASK_SATISFY_STATUS_TYPE_LIGHT_UP_LONGHUN = 36,									-- 点亮全部转职龙魂
	TASK_SATISFY_STATUS_TYPE_LIGHT_UP_LONGLING = 37,								-- 点亮全部转职龙灵
	TASK_SATISFY_STATUS_TYPE_42 = 42,												-- 新手副本
	TASK_SATISFY_STATUS_TYPE_52 = 52, 												-- 竞技场
	TASK_SATISFY_STATUS_TYPE_53 = 53,												-- vip boss
	TASK_SATISFY_STATUS_TYPE_TIANTI = 54,											-- 天梯
	TASK_SATISFY_STATUS_TYPE_66 = 66,												-- 播放一段CG
	TASK_SATISFY_STATUS_TYPE_ZHUANZHI_LONGPO = 68,									-- 三转点亮任务1
	-- TASK_SATISFY_STATUS_TYPE_ZHUANZHI_LONGHUN = 69,									-- 三转点亮任务2
	-- TASK_SATISFY_STATUS_TYPE_ZHUANZHI_LONGHUN = 70,									-- 三转点亮任务3
	TASK_SATISFY_STATUS_TYPE_73 = 73,												-- 做赏金任务(日常)

	TASK_SATISFY_STATUS_TYPE_77 = 77,												-- 世界boss
	TASK_SATISFY_STATUS_TYPE_10 = 10,												-- 护送任务
	TASK_SATISFY_STATUS_TYPE_124 = 124,												-- 使用技能

	--数据列表单项改变原因
	DATALIST_CHANGE_REASON_UPDATE = 0,  			--更新
	DATALIST_CHANGE_REASON_ADD = 1,					--添加
	DATALIST_CHANGE_REASON_REMOVE = 2, 				--移除
	DATALIST_CHANGE_REASON_CHECK = 999, 		    -- 重登 升级检测


	MAX_FB_NUM = 60,								-- 副本数量

	MEDAL_MAX_QUALITY = 10,								--勋章最高品质
	MEDAL_MAX_LEVEL = 99,								--勋章最高等级

	-- 购买类型
	CONSUME_TYPE_BIND = 1,							--绑定元宝
	CONSUME_TYPE_NOTBIND = 2,						--元宝

	-- 商店类型
	SHOP = 1,										--商城
	SECRET_SHOP = 2,								--神秘商店
	EXCHANGE_SHOP = 3,								--兑换商店
	EXCHANGE_GUILD = 4,								--帮派仓库兑换
	TEHUI_SHUP = 5,									-- 随机活动-特惠秒杀
	SPECIAL_SHOP = 6, 								--绑元不足使用元宝

	--存储类型
	STORAGER_TYPE_BAG = 0,							--背包
	STORAGER_TYPE_STORAGER = 1,						--仓库
	STORAGER_TYPE_STUFFBAG = 2,						--材料仓库

	DISCOUNT_BUY_PHASE_MAX_COUNT = 120,				--一折抢购数量
	DISCOUNT_BUY_ITEM_PER_PHASE = 16, 				--一折抢购阶段

	STORAGER_SLOT_NUM = 350,							--仓库格子个数
	ROLE_BAG_SLOT_NUM = 350, 						--背包格子个数

	WORLD_EVENT_TYPE_MAX = 7, 						--世界事件类型数


	CARD_MAX = 12, 									-- 卡牌数
	NEW_BOSS_COUNT = 3, 							-- 镜像boss数

	MENTALITY_SHUXINGDAN_MAX_TYPE = 3, 				-- 属性丹种类(旧的)
	SHUXINGDAN_MAX_SLOT_COUNT = 3,					-- 属性丹种类(旧的)

	-- MAX_MOUNT_SPECIAL_IMAGE_ID = 128,				-- 最大特殊形象(新的)
	MOUNT_IMAGE_STORE_MAX_BYTE = 16,				-- 用来存储所有坐骑特殊形象激活标记所需字节数
	LINGCHONG_IMAGE_STORE_MAX_BYTE = 16,			-- 用来存储所有灵宠特殊形象激活标记所需字节数
	UPGRADE_IMAGE_STORE_MAX_BYTE = 16,			    -- 用来存储所有进阶特殊形象激活标记所需字节数
	MOUNT_SPECIAL_IMAGE__MAX_COUNT = 256,

	--随机活动的常量
	RAND_ACTIVITY_SERVER_PANIC_BUY_ITEM_MAX_COUNT = 16, 	-- 全民疯抢
	RAND_ACTIVITY_PERSONAL_PANIC_BUY_ITEM_MAX_COUNT = 8,	-- 个人疯抢

	NOTIFY_REASON_DEFAULT = 0,						-- 红包默认的
	NOTIFY_REASON_CREATE = 1,						-- 红包创建的

	NOTIFY_REASON_FETCH_SUCC = 0,					-- 领取成功的红包

	COMPOSE_EQUIP_STUFF_NUM = 5,					-- 合成装备材料最大数
	COMPOSE_XIANQI_STUFF_NUM = 4,					-- 合成仙器材料最大数
	WUXINGGUAJI_STUFF_MAX = 5,						-- 材料个数
	WUXINGGUAJI_TARGET_MAX = 5,						-- 目标个数
	WUXINGGUAJI_BOSS_NUM = 1,						-- BOSS的最大数量

	MENTALITY_WUXING_MAX_COUNT = 35,				-- 五行个数

	NOTIFY_REASON_GET = 0,							-- 仙盟运势 所有人
	NOTIFY_REASON_CHANGE = 1,						-- 仙盟运势 改变的人

	MOUNT_EQUIP_LEVEL_COUNT = 4,					-- 坐骑装备等级
	MOUNT_EQUIP_COUNT = 4,							-- 坐骑装备数量
	ADVANCED_INFO_COUNT = 7, 						-- 进阶信息
	ADVANCED_SKILL_COUNT = 5,						-- 最大进阶技能数量
 	SHUXINGDAN_MAX_TYPE = 3, 						-- 属性丹最大类型(新的)
 	ADVANCED_OPEN_QINGKONG_GRADE = 5, 				-- 进阶祝福值清空阶数
 	-----------------------------------------------------------------------
	FIGHT_SOUL_SLOT_NUM = 4,						-- 战魂槽位最大数
 	TEAM_MAX_COUNT = 3,								--组队最大人数

	HUANHUA_MAX_COUNT = 20,							-- 最大幻化数量
	MAX_IMAGE_ID = 20, 								-- 最大形象ID
	EQUIP_UPGRADE_PERCENT = 0.00006,				-- 装备升级乘以的百分比
	MOUNT_EQUIP_ATTR_COUNT = 3,						-- 坐骑装备属性数量
	MOUNT_EQUIP_MAX_LEVEL = 200,					-- 坐骑装备最大等级
	MAX_MOUNT_LEVEL = 100,							-- 坐骑最大等级
	MAX_MOUNT_GRADE = 30,							-- 坐骑最大阶数
	MAX_MOUNT_SPECIAL_IMAGE_ID = 31,				-- 可进阶坐骑特殊形象ID
	MAX_UPGRADE_LIMIT = 10,							-- 坐骑特殊形象进阶最大等级
	MOUNT_SKILL_COUNT = 4,							-- 坐骑技能数量
	MOUNT_SKILL_MAX_LEVEL = 100,					-- 坐骑技能最大等级
	MOUNT_SPECIAL_IMA_ID = 1000,					-- 坐骑特殊形象ID换算
	MAX_MOUNT_SPECIAL_IMAGE_COUNT = 16,				-- 坐骑特殊形象数量

	MAX_XIANJIAN_COUNT = 8,							-- 仙剑把数（原先是8）
	JIANXIN_SLOT_PER_XIANJIAN = 7,					-- 每把剑的剑心孔数

	COMBINE_SERVER_ACTIVITY_RANK_REWARD_ROLE_NUM = 3,		-- 合服排行前几
	COMBINE_SERVER_RANK_QIANGOU_ITEM_MAX_TYPE = 3,			-- 合服抢购第一
	COMBINE_SERVER_SERVER_PANIC_BUY_ITEM_MAX_COUNT = 16,	-- 合服疯狂抢购全服物品数量
	COMBINE_SERVER_PERSONAL_PANIC_BUY_ITEM_MAX_COUNT = 8,	-- 合服疯狂抢购个人物品数量
	COMBINESERVER_CONVERT_REWARD_MAX_COUNT = 4,				-- 合服疯狂抢购合服兑换数量

	WUSHUANG_EQUIP_MAX_COUNT = 8,					-- 无双装备数量
	WUSHUANG_JINGLIAN_ATTR_COUNT = 3,				-- 武装精炼属性数量
	WUSHUANG_FUMO_SLOT_COUNT = 3,					-- 无双附魔槽数量
	WUSHUANG_FUHUN_SLOT_COUNT = 5,					-- 无双附魂槽数量
	WUSHUANG_FUHUN_COLOR_COUNT = 5,					-- 无双附魂颜色
	WUSHUANG_LIEHUN_POOL_MAX_COUNT = 18,			-- 猎魂池
	WUSHUANG_HUNSHOU_BAG_GRID_MAX_COUNT = 36,		-- 魂兽背包格子最大数
	HUNSHOU_EXP_ID = 30000,							-- 经验魂兽ID

	CARDZU_MAX_CARD_ID = 177,						-- 卡牌最大卡牌ID
	CARDZU_MAX_ZUHE_ID = 63,						-- 卡牌组合最大数量
	CARDZU_TYPE_MAX_COUNT = 4,						-- 卡牌类型最大数量

	QINGYUAN_CARD_MAX_ID = 19,						-- 情缘卡牌最大卡号

	CROSS_MULTIUSER_CHALLENGE_SIDE_MEMBER_COUNT = 3,-- 跨服3v3一方参赛人数
	CROSS_MULTIUSER_CHALLENGE_STRONGHOLD_NUM = 3,	-- 跨服3V3据点数量
	MAX_XIANJIAN_SOUL_SKILL_SLOT_COUNT = 8,			-- 剑魂格子总数
	MAX_XIANJIAN_SOUL_COUNT = 14,					-- 剑魂技能总数
	SUOYAOTA_TASK_MAX = 4,							-- 锁妖塔任务数
	LIFESKILL_COUNT = 2,							-- 生活技能数量
	MAX_TEAM_MEMBER_NUM = 3,						-- 钟馗捉鬼最大人数
	TIME_LIMIT_EXCHANGE_ITEM_COUNT = 10, 			-- 随机活动兑换数组长度

	JINGLING_PTHANTOM_MAX_TYPE = 10, 				-- 精灵幻化升级最大等级
	JINGLING_PTHANTOM_MAX_TYPE_NEW = 22,			-- 新增精灵幻化升级等级
	JINGLING_CARD_MAX_TYPE = 16,					-- 精灵最大类型

	MAX_PET_COUNT = 7,								-- 最大宠物数量
	MAX_EQUIP_PART = 10,							-- 最大宠物装备数量

	--化神
  	HUASHEN_MAX_ID = 5,								-- 化神最大数量
	HUASHEN_SPIRIT_MAX_ID_LIMIT = 4,				-- 化神守护精灵数量限制

 	-- 宠物装备类型
	PET_EQUIP_TYPE_YA = 1,							-- 牙
	PET_EQUIP_TYPE_ZHUA = 2,						-- 爪
	PET_EQUIP_TYPE_YIMAO = 3,						-- 羽毛
	PET_EQUIP_TYPE_HUAN = 4,						-- 环
	PET_EQUIP_TYPE_YIFU = 5,						-- 衣服
	PET_EQUIP_TYPE_KUZI = 6,						-- 裤子
	PET_EQUIP_TYPE_MAOZI = 7,						-- 帽子
	PET_EQUIP_TYPE_LINGZHU = 8, 					-- 灵珠

 	-- 宠物装备索引
 	PET_EQUIP_PART_YA = 0,							-- 牙
	PET_EQUIP_PART_ZHUA = 1,						-- 爪
	PET_EQUIP_PART_YIMAO = 2,						-- 羽毛
	PET_EQUIP_PART_HUAN_1 = 3,						-- 环1
	PET_EQUIP_PART_HUAN_2 = 4,						-- 环2
	PET_EQUIP_PART_YIFU = 5,						-- 衣服
	PET_EQUIP_PART_KUZI = 6,						-- 裤子
	PET_EQUIP_PART_MAOZI = 7,						-- 帽子
	PET_EQUIP_PART_LINGZHU_1 = 8, 					-- 灵珠1
	PET_EQUIP_PART_LINGZHU_2 = 9, 					-- 灵珠2

	-- 勋章的类型
	XUNZHANG_BOSS = 0,								-- boss勋章
	XUNZHANG_KUAFU = 1,								-- 跨服勋章
	XUNZHANG_TANXIAN = 2,							-- 探险勋章
	XUNZHANG_XIUXIAN = 3,							-- 休闲勋章
	XUNZHANG_GUAIWU = 4,							-- 怪物勋章
	XUNZHANG_BANGHUI = 5,							-- 帮会勋章
	XUNZHANG_JINGJI = 6,							-- 竞技勋章

 	MAX_FABAO_COUNT = 7, 							-- 法宝最大数量
 	CHEST_SHOP_MODE_MAX = 7, 						-- 最大免费次数时间

 	LINGPO_MAX_COUNT = 16,							-- 灵魄最大数量

 	LIEMING_MAX_SLOT_COUNT_IN_ONCE = 8,				-- 宠物带槽最大数量

 	MAX_ZHENBAOGE_ITEM_NUM = 6,						-- 珍宝阁最大物品种类数量
	MAX_ZHENBAOGE_SERVER_REWARD_NUN = 6,			-- 珍宝阁全服奖励最大种类数量

	RA_KING_DRAW_LEVEL_COUNT = 3,					-- 陛下请翻牌最大牌组数
	MAX_BIXIAXUNBAO_HIT_SEQ_RECORT_COUNT = 40,		-- 陛下寻宝命中物品记录最大数
	BIXIAXUNBAO_SHOW_ITEM_COUNT = 9,				-- 陛下寻宝命中物品最示数量
	MAX_SHENBING_PART = 12,							-- 最大神兵部位
	MAX_CONUSUMESCORE_RECORT_COUNT = 16,			-- 消费积分记录最大数

	MAX_QIUQIAN_COUNT = 6,							-- 求签最大数
	MAX_QIUQIAN_HISTORY_COUNT = 20,					-- 求签记录最大数

	PERSONALIZE_WINDOW_MAX_TYPE = 2,				--个性化聊天窗口类型
	PERSONALIZE_WINDOW_MAX_INDEX = 31,				--单个个性化聊天窗口数量

	RA_MINE_MAX_TYPE_COUNT = 12,					-- 当前挖到的矿石数
	RA_MINE_MAX_REFRESH_COUNT = 8,					-- 当前矿场的矿石
	RA_MINE_REFRESH_MAX_COUNT = 4,					-- 一次刷新出的最大矿石数目
	RA_MINE_TYPE_MAX_COUNT = 12,					-- 矿石类型最大数目
	RA_MINE_SERVER_REWARD_MAX_COUNT = 6,			-- 全服礼包最大数

	MAX_IMAGE_PROMOTION_NUM = 8,					-- 形象进阶最大种类
	MAX_IMAGE_ID_LIMIT = 40,						-- 单个形象最大的阶

	QINGYUAN_COUPLE_HALO_MAX_TYPE = 15,				-- 情缘夫妻光环最大数量
	QINGYUAN_COUPLE_HALO_MAX_ACTIVE_LIMIT = 8,		-- 一个夫妻光环需激活图标数量
	MARRY_MAX_TYPE = 4,								-- 情侣装备图标最大数量

	RA_FUNNY_SHOOT_EXCHANGE_ITEM_COUNT = 12,		-- 趣味射门可兑换物品的数量

	MULTIMOUNT_MAX_ID = 5, 							--双人坐骑最大数量
	MULTIMOUNT_EQUIP_TYPE_NUM = 8,					--双人坐骑装备数量

	MAX_SUIPIAN_EXCHANGE_ITEMS = 20, 				--碎片兑换最大数量

	RA_FANFAN_MAX_ITEM_COUNT = 50,					-- 最大奖励数量
	RA_FANFAN_MAX_WORD_COUNT = 10,					-- 最大字组数量
	RA_FANFAN_CARD_COUNT = 40,						-- 可翻牌数
	RA_FANFAN_CARD_COLUMN = 8,						-- 可翻牌列数
	RA_FANFAN_CARD_ROW = 5,							-- 可翻牌行数
	RA_FANFAN_LETTER_COUNT_PER_WORD = 4,			-- 每个字组字数
	RA_FANFAN_MAX_WORD_ACTIVE_COUNT = 99,			-- 最多激活字组数量

	PERSON_RANK_TYPE_PLANTING_TREE_PLANTING = 23,	-- 植树活动植树次数排行
	PERSON_RANK_TYPE_PLANTING_TREE_WATERING = 24,	-- 植树活动浇水次数排行

	PASTURE_SPIRIT_MAX_COUNT = 12,						--牧场精灵最大数量
	SC_PASTURE_SPIRIT_LUCKY_DRAW_RESULT_MAX_COUNT = 50, --牧场抽奖数量

	ROLE_SHUIJING_MAX_SHUIJING_ID = 10,					-- 最大水晶ID
	ROLE_SHUIJING_MAX_SKILL_TYPE = 20,	                --最大水晶技能类型

	HOT_SPRING_MONSTER_COUNT = 9, 						-- 三消怪物数量

	MAX_WING_FUMO_TYPE = 4,								--羽翼附魔数
	MAX_WING_FUMO_LEVEL = 100,							--羽翼附魔最大等级

	MITAMA_MAX_MITAMA_COUNT = 15,						--御魂最大数量
	MITAMA_MAX_SPIRIT_COUNT = 5,						--御魂等级

	BLACK_MARKET_MAX_ITEM_COUNT = 3, 					--黑市竞拍物品数量

	TIANSHENHUTI_EQUIP_MAX_COUNT = 8,					-- 传承装备部位数量
	TIANSHENHUTI_BACKPACK_MAX_COUNT = 100,				-- 背包格子数量


	MAX_DABIAOQING2_MAX_NUM = 32,						-- 特殊表情最大数

	DAILY_WORK_TYPE_MAX_COUNT = 256,                     -- 每日必做最大任务数量
	SHUTU_INFO_TYPE_MAX_COUNT = 50,                     -- 师徒信息数量
	MAX_ZHUANSHEN_RECYCLE_EQUIP_INDEX_COUNT = 16,		-- 转生装备最大数
	MEMBER_MAX_COUNT = 8,								-- 结阵成员邀请最大数

	NEW_FB_REWARD_ITEM_SC_MAX = 30,						--组队塔防

	-- TEJIE_TYPE_MAX = 8 ,								-- 特戒最大数
	-- TEJIE_ADD_ATTR_TYPE_MAX = 4,						-- 特戒洗练属性最大数
	SWEEP_MODULE_TYPE_MAX_COUNT = 20,					-- 秘境扫荡最大数

	----钓鱼常量
	FISHING_FISH_TYPE_MAX = 7, 							--鱼的种类
	FISHING_FABAO_MAX = 3,  							--钓鱼道具类型

	DONGKU_BOSS_EVERY_LAYER_MAX_BOSS_NUM = 10,          -- 洞窟BOSS单层数
	VIP_BOSS_EVERY_LAYER_MAX_BOSS_NUM = 10,             -- VipBOSS单层数
	SGYJ_LAYER_NUM = 3,									-- 上古遗迹协议层数

	MAX_SUIT_CNT = 32,									-- 套装数最大32套
	SUITE_TOTAL_MAX_CNT = 10,							-- 时装件数最大12件

	I_COLOR_MAX = 8, 									-- 离线挂机物品表

	SUIT_ROLE_HAS_MAX_SUIT_NUM = 6, 					-- 套装最大数量
	SUIT_TYPE_MAX = 3,									-- 套装最大种类
	SUIT_ROLE_INDEX_MAX = 14, 							-- 一套装备的个数
	MAX_STONE_PART_NUM = 14,							-- 宝石装备的最大个数
	MAX_LINGYU_COUNT_SLOT = 14,							-- 灵玉装备的最大个数
	MAX_STONE_COUNT = 6,								-- 每个装备的宝石孔数
	MAX_LINGYU_COUNT = 6,								-- 每个装备的灵玉孔数
	MAX_BAPTIZE_COUNT = 4,                              --每个装备的洗炼条数
	MAX_XIANPIN_COUNT = 5,                              --每个装备的仙品条数
	MAX_TYPE_BY_STONE = 4, 								--宝石能镶嵌的最大种类

	MAX_ROLE_EQUIP_NUM = 375,                           -- 人物可穿戴最大装备数量
	MAX_EQUIP_BODY_EQUIP_NUM = 15,                      -- 每个肉身最大装备数量

	WEDDING_GUESTS_MAX_NUM = 30,                        -- 婚礼宾客最大人数
	GUILD_FIGHT_SIDE_MAX = 2,							-- 攻城战帮派人数
	BUFF_POS_COUT_PER_SIDE = 3,

	JUHUN_MAX_COLOR = 5, 								--聚魂的颜色
	BUILD_TOWER_MAX_TOWER_POS_INDEX = 19,				--塔防副本最大的塔数

	RUNE_SYSTEM_SLOT_MAX_NUM = 10,                      -- 铭纹格子最大数据
	COINFB_LAYER_TYPE_MAX = 5 ,							--铜币本难度数量

	RUNE_SYSTEM_BAG_MAX_GRIDS = 200,                    -- 铭纹背包容量

	EQUIP_BAPTIZE_EQUIP_GRID_MAX_NUM = 14,				-- 装备洗练部位
	EQUIP_BAPTIZE_ONE_PART_MAX_BAPTIZE_NUM = 5, 		-- 装备洗练部位属性条数
	TIANXIANGEFB_MAX_PASS_REWARD = 50,					--天仙阁奖励最大数量

	MAX_CHESTSHOP_TYPE = 3,                             --寻宝种类

	FB_DROP_ITEM_MAX_NUM = 32, 							--副本怪物掉落
	NEW_PETFB_MAX_LEVEL_COUNT = 33,						--宠物副本类型
	NEW_PETFB_LEVEL_TYPE_MAX  = 40, 					--宠物本难度

	TIANSHU_MAX_TYPE = 4,							    -- 天书寻主最大类型
	TIANSHU_CHENGZHANG_TYPE = 5,					    -- 天书寻主 成长天书类型 在普通天书之后

	SHENSHOU_MAX_BACKPACK_COUNT = 200,					-- 神兽背包容量个数
	SHENSHOU_MAX_EQUIP_SLOT_INDEX = 4, 					-- 神兽装备最大部位index
	SHENSHOU_MAX_ID = 60,								-- 神兽最大ID
	SHENSHOU_MAX_EQUIP_ATTR_COUNT = 5,					-- 神兽装备最大随机属性个数
	SHENSHOU_EQ_MAX_LV = 100,							-- 神兽装备最大等级
	SHENSHOU_MAX_EQUIP_COUNT = 5,						-- 神兽最大装备数量
	JINGSHI_ITEM_ID = 27656, 							-- 晶石道具id

	LINGCHONG_DEVOUR_INDEX_MAX = 5, 					-- 灵童吞噬装备
	UPGRADE_TYPE_MAX = 11,								-- 进阶系统个数

	GUILD_QUESTION_SCORE_RANK_MAX = 50,                 -- 帮派答题排行榜最大数

	BAG_CELL_NUM_LIMIT = 5,							-- 设置自动出售装备时背包剩余格子数量限制


	MAX_TELENT_TYPE_COUT = 4,							-- 人物天赋成长路线
	MAX_TELENT_INDEX_COUT = 14,							-- 人物天赋路线技能个数

	SEVENDAY_DISCOUNT_RATE = 0.1,                    -- 七天返还活动消费返还比例
	SEVENDAY_DISCOUNT_DAY= 8,                    	-- 七天返还活动消费持续时间

	TIANMING_LEVEL = 370,                           -- 天命开启等级
	MAX_FUWEN_DISPOSE_NUM = 5,                          --铭纹分解
	BOSS_ATTRI_TITLE_ID = 998,                          --boss归属称号id

	CFG_ITEM_MAX_COUNT = 30,  --冲级累计奖励

	RAND_ACTIVITY_CONVERT_CRAZY_CONVERT_ITEM_MAX_NUM = 30, -- 兑换狂欢兑换最大次数

	EQUIP_ZHULING_GRID_MAX_RAND_ATTR_NUM = 4,				-- 铸魂格子最多的随机属性条数
	EQUIP_ZHULING_PART_MAX_NUM = 14,						-- 铸魂装备部位最大数
	EQUIP_ZHULING_PART_MAX_SLOT_NUM = 4,					-- 铸魂一个部位最多的铸灵槽
	EQUIP_ZHULING_STUFF_TYPE_MAX = 4,						-- 铸魂最多属性种类
	EQUIP_ZHULING_BAG_COL_MAX_NUM = 8,						-- 铸魂背包一行最大格子数

	RAND_ACTIVITY_HAPPY_CONSUME_LEVEL_MAX_COUNT = 6, 		-- 消费欢乐颂最大档位
	RAND_ACTIVITY_HAPPY_RECHARGE_LEVEL_MAX_COUNT = 6, 		-- 充值欢乐颂最大档位

	COMBINE_SERVER_CLOUD_BUY_RECORD_COUNT = 20,		        -- 云购记录条数
	RA_CLOUD_BUY_RECORD_COUNT = 20,     	                -- 随机活动云购记录条数
	RA_CLOUD_BUY_TOP_REWARD_RECORD_COUNT = 100,			    --云购大奖记录条数

	FIRST_RECHARGE_DRAW_RECORD_COUNT = 20;			        --首充抽抽抽记录条数
	SPECIAL_RUNE_INDEX = 8,                                      -- 特殊铭纹槽1
	SPECIAL_RUNE_INDEX2 = 9,                                      -- 特殊铭纹槽2
	SHENGYINFB_TYPE_BUY_JINGLI  =  0 ,				-- 圣印副本续时间
	SHENGYINFB_TYPE_GET_REFRESH_TIME = 1,			-- 获取下次图腾时间戳
	LIEKUN_ZONE_TYPE_COUNT = 5, 					-- 猎鲲地带区域数
	LIEKUN_BOSS_TYPE_COUNT = 5, 					-- 猎鲲boss数量

	OGA_CLOUD_BUY_RECORD_COUNT = 20,								--云购记录条数

	SELECT_ITEM_MAX_NUM = 24,								--自选礼包最大数

	MAX_RANK_COUNT = 16,					-- 乱斗战场奖励排行
	CROSS_1V1_SEASON_MAX = 32,				--跨服1v1赛季戒指最大数量
	COMBINE_SERVER_PANIC_BUY_ITEM_MAX_COUNT = 16,	--特惠秒杀物品数量

	FU_HUO_DAOJISHI = 1,--复活倒计时
	FU_BEN_NEXT_LEVEL = 2,--下一层倒计时
	FU_BEN_OUT_SCENE = 3,--退出场景倒计时
	XIU_LUO_TA_NEXT_LAYER = 4,--青云之巅下一层倒计时
	FU_BEN_ZHUNBEI = 5,--准备副本倒计时
	
	MAX_MAIL_NUM = 160,--最大邮件数量
	PERSON_RANK_TYPE_RA_CHONGZHI_2 = 23,						-- 充值排行2（至尊排行）

	MONEY_BAR = {
		BIND_GOLD = 1,											-- 绑定仙玉
		GOLD = 2,												-- 仙玉
		COIN = 3,												-- 铜币
		SILVER_TICKET = 4,										-- 绑定铜币
		-- SILVER_TICKET = 5,									-- 其他
        -- SILVER_TICKET = 6,									-- 银票
        SHENGWANG = 5,											-- 声望
		CHIVALROUS = 6,                                        	-- 侠义值
		CASH_POINT = 7,                                        	-- 现金点
		MAX_TYPE = 7,

		MINGWEN_EXP = 8,										-- 铭文经验
		MINGWEN_MINYIN = 9,										-- 铭文铭印
        MINGWEN_XINJING = 10,									-- 铭文心晶
        GONG_YU = 11,                                           -- 贡玉
		VOLUME = 12,											-- 代金券
		CANG_JIN_SCORE = 13,									-- 藏金商铺积分
	},

	NEW_MONEY_BAR = {		--仅作对比用，不修改旧枚举
		XIANYU = 1,			--仙玉
		BIND_XIANYU = 2,	--绑定仙玉
		SILVER_TICKET = 3,	--元宝
		COIN = 4,			--铜币
		RECHARGE_VOLUME = 5,--充值券
	},

	IS_TOWER = 1, 	--是否是防御塔类型
	ShangJin_Npc = 10302,									-- 赏金NPC
	Qicheng_Npc = 10301,									-- 比翼双飞NPC

	RD_FLOAT_V = Vector2(450, 70),
	GIVE_PRESENT_MAX_NUM = 5,                               --赠送礼物类型数量
	HISTORY_GIVE_PRESENT_MAX_COUNT = 50,					--赠送记录条数
	DIVINEWEEAPON_MAX_NUM = 6,								--赠送记录条数
	DIVINEWEEAPON_SOUL_PART_MAX_NUM = 4,					--赠送记录条数

	EXP_BUFF_TYPE_MAX = 4,									--经验buff类型
	RA_PREFERENTIAl_GIFT_DAY_SEQ_MAX_COUNT = 7,				--特惠礼包最大档次（从0开始）

	HIDDEN_WELFARE_MAX_STAGE = 3,							--隐藏福利最大任务阶段
	HIDDEN_WELFARE_MAX_TASK_PER_STAGE = 5,					--隐藏福利每阶段最大任务数量从0开始

	CUSTOM_HEAD_ICON = 1000,								--自定義頭像

	MAX_UPGRADE_SKILL_NUM = 5,								--形象最大技能数量

	MAX_GUILD = 4,											--开宗立派 仙盟战力前三
	MAX_MEMBER = 10,										--开宗立派 仙盟成员战力前十


	E_XIANPIN_ADD_BASE_MAXHP_PER = 0,                						-- 角色气血万分比
    E_XIANPIN_ADD_BASE_FANGYU_PER_AND_FA_FANGYU_PER = 1,      				-- 角色物防和法防万分比
    E_XIANPIN_ADD_MIANSHANG_PER = 2,                						-- 免伤万分比
    E_XIANPIN_ADD_MONSTER_DROP_ITEM_PER = 4,            					-- 物品掉落增加万分比
    E_XIANPIN_ADD_MAXHP_OF_ROLE_LEVEL = 5,              					-- 增加（等级 * X）点气血
    E_XIANPIN_ADD_FANGYU_AND_FA_FANGYU_OF_ROLE_LEVEL = 6,      				-- 增加（等级 * X）点物防 和 法防
    E_XIANPIN_ADD_POJIA_PER = 7,                  							-- 破甲万分比
    E_XIANPIN_ADD_POFANG_PER = 8,                  							-- 破防万分比
    E_XIANPIN_ADD_BASE_GONGJI_PER = 9,                						-- 角色攻击万分比
    E_XIANPIN_ADD_MONSTER_DROP_COIN_PER = 10,            					-- 野怪掉落金币增加万分比  （对副本、活动内的怪物无效）
    E_XIANPIN_ADD_POJIA_OF_ROLE_LEVEL = 11,              					-- 增加破甲（等级 * X）点
    E_XIANPIN_ADD_GONGJI_OF_ROLE_LEVEL = 12,              					-- 增加攻击（等级 * X）点
    E_XIANPIN_ADD_BAOJI_PER = 13,                  							-- 暴击万分比
    E_XIANPIN_ADD_PER_KANGBAO = 14,                  						-- 抗暴率万分比
    E_XIANPIN_ADD_SHANGHAI_JIACHENG_PER = 15,            					-- 伤害加成万分比
    E_XIANPIN_ADD_SHANGHAI_JIANSHAO_PER = 16,            					-- 伤害减少万分比
    E_XIANPIN_ADD_BAOJI_HURT_PER = 17,                						-- 暴击增伤
    E_XIANPIN_ADD_BAOJI_HURT_CUT_PER = 18,              					-- 暴击减伤
    E_XIANPIN_ADD_SKILL_SHANGHAI_JIACHENG_PER = 19,          				-- 技能伤害加成万分比
    E_XIANPIN_ADD_SKILL_SHANGHAI_JIANSHAO_PER = 20,          				-- 技能伤害减少万分比

    MAX_GUILD_SYSTEM_REDPAPER_COUNT = 10,                                    -- 最大系统红包发送记录数量

	TeamInvite = -100,								--boss组队邀请
	BossXiezhu = -200,								--boss协助

	TASK_CHAIN_MAX_SHOW_BOX_NUM = 5,										-- 任务链最大可显示进度奖励个数
	SIXTEEN_CELL_NUM = 4, 													-- 十六宫格玩法每一行一列格子数
	MAX_SKILL_TARGET_NUM = 32,												-- 技能目标数量上限

    MAX_EQUIP_TARGET_NUM = 8,                                               --目标装备装备部位
    MAX_EQUIP_TARGET_SUIT_NUM = 8,                                          --目标装备套装数量
    EQUIP_TARGET_GAER = 4,                                                  --目标装备套装档次
	EQUIP_TARGET_SPECIAL_SUIT_START = 4,									--装备目标搞特殊的 （去掉两个饰品） 大于这个值

	SHENJI_MAX_RAND_NUM = 6,							--神机星级属性个数
	OBJ_BUFF_MAX_NUM = 100,													-- 角色BUFF最大上限
	TIANSHEN_SKILL_COUNT = 10, 												-- 天神切技能上限
	MAX_PELLET_COUNT = 80,                                                  -- 炼丹系统  丹药上限
	MAX_FURNACE_COUNT = 10,                                                 -- 炼丹系统  加成炉上限
	MAX_DANZAO_ITEM_COUNT = 15,                                             -- 炼丹系统  丹灶上限
	GET_GOLD_REASON_BOSS_DROP_PRIVILEG_RMB_BUY = 122,						-- BOSS掉落特权	直购请求rmb类型
	BOSS_FB_COUNTDOWN_TIME = 30,											-- Boss特权界面弹出时间倒计时

	ATK_STONE = 0,							-- 弑天-攻击宝石
	DEF_STONE = 1,							-- 弑天-防御宝石
	HP_STONE = 2,							-- 弑天-生命宝石

	PASSIVE_SKILL = {
		PASSIVE_SKILL_INDEX = {1, 6, -6, 7, 8}, --按钮分别对应的被动技能类型
		BEFORE_TYPE = 6,	--试炼前的类型
		AFTER_TYPE = -6,	--标识试炼后的类型
		BEFORE_INDEX = 2,	--试炼前的索引
		AFTER_INDEX = 3,	--试炼后的索引
		SIGN_NUM = 10,		--试炼前数据的数量
		BUTTON_NUM = 5,		--按钮的数量
		SKILL_NUM = 10,		--技能槽的数量
	},
	SHOW_SPIRIT_LV = 30,		-- 展示怒气变身的等级

	YUAN_SHEN_ACTIVATE_ITEM = 50047, -- 元神激活道具id
	MAX_TEAM_MEMBER_NUMS = 5,        -- 最大组队人数
}

TONGYONG_RAWIMAGE_ENUM = {
	HAVE_ROLEMODEL = "a3_ty_bg1",
	NOT_HAVE_ROLEMODEL = "a3_ty_bg4",
}

--通用的游戏枚举 数字
COMMON_GAME_ENUM = 
{
	FUYI = -1,
	ZERO = 0,
	ONE = 1,
	TWO = 2,
	THREE = 3,
	FOUR = 4,
	FIVE = 5,
	SIX = 6,
	SEVEN = 7,
	EIGHT = 8,
	NINE = 9,
	TEN = 10,
	MAX = 99999999,
}

--攻击模式
ATTACK_MODE = {
	PEACE = 0,							-- 和平模式
	GUILD = 1,							-- 仙盟模式
	ALL = 2,							-- 全体模式
	NAMECOLOR = 3,						-- 跨服模式
	CAMP = 6,							-- 阵营模式
	AREA  = 7,					        -- 战区模式 （大跨服组模式）
	JIEYI = 8,							-- 结义模式  不会对结义的成员造成伤害
	-- BAN_WAR = 9,						-- (废弃)禁战模式 
	ROLE_ENEMY = 10,					-- 仇人模式 攻击时只攻击仇人或怪物
	TEAM = 11,							-- 组队模式

	MAX = 12,
}

-- 地区类型
AREA_TYPES = {
	DEFAULT = 0,					-- 国内
	TAIWANG = 1,					-- 台湾
}

-- 支付货币类型
PAY_MONEY_TYPES = {
	RMB = 0,						-- 人民币
	GAME_COUNT = 1,					-- 游戏点
	DOLLAR = 2,						-- 美元
}

PHP_RESULT_INFO = {
	SUCCESS = "SUCCESS",							-- 成功
	OUT_TRADE_NO_USED = "OUT_TRADE_NO_USED",		-- 重复订单，不发货，返回成功
	TRADE_ERROR = "TRADE_ERROR",					-- 交易错误
	PARAM_ERROR = "PARAM_ERROR",					-- 参数错误
	SIGN_NOT_MATCH = "SIGN_NOT_MATCH",				-- 数据签名不匹配
	RADE_NO_ERROR = "RADE_NO_ERROR",				-- 订单号错误
	USER_NOT_EXIST = "USER_NOT_EXIST",				-- 无法找到用户
	ROLE_NOT_EXIST = "ROLE_NOT_EXIST",				-- 无法找到角色
	UNKNOWN_ERROR = "UNKNOWN_ERROR",				-- 未知错误
	IP_FORBIDDEN = "IP_FORBIDDEN",					-- IP无权限
	NO_CONTENT = "NO_CONTENT",						-- 无数据
	REQUEST_METHOD_ERROR = "REQUEST_METHOD_ERROR",	-- 请求方式错误
	FAIL = "FAIL",									-- 通用错误
	PLAT_LIMIT = "PLAT_LIMIT",						-- 平台限制
	CONFIG_ERROR = "CONFIG_ERROR",					-- 配置错误
	TOKEN_EXPIRED = "TOKEN_EXPIRED",				-- Token过期
	TOKEN_INVALID = "TOKEN_INVALID",				-- Token失效
	SERVER_INVALID = "SERVER_INVALID",				-- 区服失效
	PARAM_EXPIRED = "PARAM_EXPIRED",				-- 参数过期		
	EXCEEDED_LIMIT = "EXCEEDED_LIMIT",				-- 超过限制
	LEVEL_LIMIT = "	LEVEL_LIMIT",					-- 等级限制
}

ACCOUNT_KEY_ERROR_RESULT = {
	LOGIN_MD5_ERROR = 1,
}

ACCOUNT_KEY_ERROR = {
	KEY = 0,
	MD5 = 1,
}

-- 物品属性展示，系统分类
ITEMTIPS_SYSTEM = {
	SHJ_TUJIAN = 1,					-- 山海经-图鉴
	FASHION = 2,					-- 时装（时装、足迹、光环、羽翼、法宝、神兵、剑灵、相框、气泡、脸饰、腰饰、尾巴、手环）
	TITLE = 3,						-- 称号
	MOUNT = 4,						-- 坐骑
	LING_CHONG = 5,					-- 灵宠
	HUA_KUN = 6,					-- 化鲲
	TS_HUANHUA = 7,					-- 天神-幻化
	TS_SHENTONG = 8,				-- 天神-神通
	TS_SHENQI = 9,					-- 天神-神器
	BAO_SHI = 10,					-- 宝石
	SHOU_HU = 11,					-- 守护(现读配置)
	XIAN_WA = 12,					-- 仙娃
	ZQ_SHUXINGDAN = 13,				-- 属性丹-坐骑
	LC_SHUXINGDAN = 14,				-- 属性丹-灵宠
	WG_SHUXINGDAN = 15,				-- 属性丹-外观（羽翼、法宝、神兵、剑灵）
	MING_WEN = 16,					-- 铭文
	LONG_HUN = 17,					-- 龙魂
	SHEN_SHI = 18,					-- 天神-神饰
	BA_GUA = 19,					-- 天神-八卦图
	TOU_XIANG = 20,					-- 头像
	LING_YU = 21,					-- 灵玉
	GOD_BOOK_CHIP = 22,				-- 天书碎片
	LING_HE = 23,					-- 天神灵核
	DUANLING_RUTI = 24,				-- 锻灵入体
	ROLE_REFINING = 25,				-- 丹药
	FIVE_ELEMENTS = 26,             -- 五行
	ARTIFACT = 27,                  -- 仙魔神器
	CHARM = 28,                     -- 符咒装备
	BACKGROUND = 29,				-- 背景（奇境）
	ZHIZUN = 30,                   	-- 至尊
	TS_HUAMO = 32,					-- 天神化魔
	CANGMING = 33,					-- 五行沧溟
	WUHUNZHENSHEN = 34,				-- 武魂真身
	NEWFIGHTMOUNT = 35,             -- 战斗坐骑（大龙）
	DRAGON_TEMPLE_PELLET = 36,      -- 龙神殿丹药
	ESOTERICA = 37,					-- 秘笈
	BEAST = 38,						-- 灵兽
	Rumor = 39,                     -- 传闻
	Mecha = 40,                     -- 机甲
	SHJ_QQJYT = 41,                 -- 山海经-千秋绝艳图
	TIANSHENSHUANGSHENG = 42,		-- 双生天神
	CUSTOM_ACTION = 43,				-- 自定义动作
	WUHUN_SHUXINGDAN = 44,			-- 武魂属性丹
	QIWEN_YIWU = 45,				-- 奇闻异物
	LONGSHENDIAN_YULONG = 46,		-- 龙神殿 - 御龙定乾坤
	MENGLING_SXS = 47,				-- 灵阵 - 属性石
	CASTING_STONE = 48,				-- 时装 - 缘晶石
	THUNDER_MANA = 49,              -- 雷法装备
	BEAST_SXD = 50,					-- 幻兽属性丹
	BEAST_SKIN = 51,				-- 幻兽皮肤
	SPIRIT = 52,					-- 元神道具
}	

-- 物品提示框战力显示
TIPS_CAP_SHOW = {
	NO_SHOW = 0,					-- 不显示战力
	SHOW_CALC = 1,					-- 显示客户端计算战力
	SHOW_CFG = 2,					-- 显示战力并取配置战力
}

SPECIAL_FRAME = {
    CAN_SHOW = 1,
    NOT_SHOW = 0,
}

-- 装备特殊基础属性
EQUIP_SPECIAL_ATTR_TYPE = {
	PINK = 1,
	GOLD = 2,
	COLOR = 3,
}

-- 每日次数
DAY_COUNT = {
	DAYCOUNT_ID_FB_START = 0,						-- 副本开始
	DAYCOUNT_ID_FB_XIANNV = 1,						-- 仙女
	DAYCOUNT_ID_FB_COIN = 2, 						-- 铜币
	DAYCOUNT_ID_FB_WING = 3,						-- 羽翼
	DAYCOUNT_ID_FB_XIULIAN = 4,						-- 修炼
	DAYCOUNT_ID_FB_QIBING = 5,						-- 骑兵

	DAYCOUNT_ID_FB_END = GameEnum.MAX_FB_NUM - 1,	-- 副本结束

	VAT_TOWERDEFEND_FB_FREE_AUTO_TIMES = 18,

	DAYCOUNT_ID_EVALUATE = 61,											-- 评价次数
	DAYCOUNT_ID_JILIAN_TIMES = 62,										-- 祭炼次数
	DAYCOUNT_ID_ACCEPT_HUSONG_TASK_COUNT = 63,							-- 护送任务 领取个数
	DAYCOUNT_ID_SHUIJING_GATHER = 64,									-- 采集物
	DAYCOUNT_ID_YAOSHOUJITAN_JOIN_TIMES = 65,							-- 妖兽祭坛参加次数
	DAYCOUNT_ID_FREE_CHEST_BUY_1 = 66,									-- 一次寻宝免费次数
	DAYCOUNT_ID_COMMIT_DAILY_TASK_COUNT = 67,							-- 日常任务 提交个数
	DAYCOUNT_ID_HUSONG_ROB_COUNT = 68,									-- 护送抢劫次数
	DAYCOUNT_ID_HUSONG_TASK_VIP_BUY_COUNT = 69,							-- 护送任务 vip购买次数
	DAYCOUNT_ID_HUSONG_REFRESH_COLOR_FREE_TIMES = 70,					-- 护送任务 免费刷新次数
	DAYCOUNT_ID_CAMP_TASK_COMPLETE_COUNT = 71,							-- 阵营任务完成次数
	DAYCOUNT_ID_GUILD_TASK_COMPLETE_COUNT = 72,							-- 仙盟任务完成次数
	DAYCOUNT_ID_FETCH_DAILY_COMPLETE_TASK_REWARD_TIMES = 73,			-- 日常领取全部任务完成奖励次数
	DAYCOUNT_ID_ANSWER_QUESTION_COUNT = 74,								-- 答题次数
	DAYCOUNT_ID_VIP_FREE_REALIVE = 75,									-- vip免费复活次数
	DAYCOUNT_ID_CHALLENGE_BUY_JOIN_TIMES = 76,							-- 挑战副本购买参与次数
	DAYCOUNT_ID_CHALLENGE_FREE_AUTO_FB_TIMES = 77,						-- 挑战副本免费扫荡次数
	DAYCOUNT_ID_BUY_ENERGY_TIMES = 78,									-- 购买体力次数
	DAYCOUNT_WABAO_TASK_COUNT = 80,										-- 挖宝任务次数
	DAYCOUNT_ID_BOSS_ASSIST = 81,											-- BOSS协助次数
	DAYCOUNT_ID_TEAM_TOWERDEFEND_JOIN_TIMES = 82,						-- 组队塔防参与次数
	DAYCOUNT_ID_GCZ_DAILY_REWARD_TIMES = 83,							-- 攻城战领取每日奖励次数
	DAYCOUNT_ID_XIANMENGZHAN_RANK_REWARD_TIMES = 84,					-- 仙盟战排名奖励次数
	DAYCOUNT_ID_MOBAI_CHENGZHU_REWARD_TIMES = 85,						-- 膜拜城主次数
	DAYCOUNT_ID_GUILD_ZHUFU_TIMES = 86,									-- 仙盟运势祝福次数
	DAYCOUNT_ID_CROSS_MULTIUSER_CHALLENGE_DAYCOUNT_REWARD = 91,			-- 跨服3v3领取次数
	DAYCOUNT_ID_MIGOGNXIANFU_JOIN_TIMES = 92,							-- 迷宫仙府参与次数
	DAYCOUNT_ID_JOIN_YAOSHOUGUANGCHANG = 93,       						-- 参加妖兽广场每日次数
	DAYCOUNT_ID_JOIN_SUOYAOTA = 94,       								-- 参加锁妖塔每日次数
	DAYCOUNT_ID_GATHER_SELF_BONFIRE = 95,       						-- 采集自己仙盟篝火每日次数
	DAYCOUNT_ID_BONFIRE_TOTAl = 96,       								-- 采集仙盟篝火总次数
	DAYCOUNT_ID_DABAO_BOSS_BUY_COUNT = 97,       						-- 购买打宝地图进入次数
	DAYCOUNT_ID_DABAO_ENTER_COUNT = 98,       							-- 打宝地图进入次数
	DAYCOUNT_ID_GUILD_CHUANGONG_INVITE = 99,							-- 仙盟传功邀请
	DAYCOUNT_ID_JINGHUA_GATHER_COUNT = 101,								-- 精华采集次数
	DAYCOUNT_ID_CAMP_GAOJIDUOBAO = 102,									-- 师门高级夺宝
	DAYCOUNT_ID_GUILD_CHUANGONG_REWARD_EXP_TIMES = 104,					-- 仙盟传功获得经验次数
	DAYCOUNT_ID_TIANXIANGEFB_DAY_REWARD = 110,							-- 天仙阁副本领取
	DAYCOUNT_ID_TEAM_WUJINJITAN_OPEN_TIMES = 111,			   			-- 组队无尽祭坛开启次数
	DAYCOUNT_ID_TEAM_WUJINJITAN_BUY_TIMES = 112,				        -- 组队无尽祭坛购买次数
	DAYCOUNT_ID_TEAM_WUJINJITAN_USE_TICKET_TIMES = 113,					-- 无尽祭坛消耗炼狱券增加的次数
	-- DAYCOUNT_ID_EXPFB_JOIN_TIMES = 114,									-- 经验副本参与次数
	DAYCOUNT_ID_SHITUPATA_DAY_REWARD = 115,								-- 师徒爬塔每日奖励
	-- DAYCOUNT_ID_EXPFB_CLEARCD_TIMES = 116, 								-- 经验本清除CD次数
	-- DAYCOUNT_ID_EXPFB_BUY_TIMES = 117, 									-- 经验本购买次数
	DAYCOUNT_ID_FRIENDBLESS_TIMES = 118,								-- 好友祝福次数
	DAYCOUNT_ID_FETCH_FRIENDBLESS_TIMES = 119,							-- 好友祝福领取奖励次数
	DAYCOUNT_ID_WUSHUANGFB_FETCH_REWARD = 122,							-- 无双副本领奖标记
	DAYCOUNT_ID_GUILD_DAY_REWARD =123,									-- 帮派每日福利次数
	DAYCOUNT_ID_GUILD_LINGDI_REWARD =124,								-- 帮派领地福利次数
	DAYCOUNT_ID_MAMMONBLESS =128,										-- 今日已采箱子数
	--DAYCOUNT_ID_BUILDING_BUFF = 130,									-- 今日中立战场领取BUFF数
	DAYCOUNT_ID_EXP_JADE_FETCH = 130,									-- 经验玉领取次数
	DAYCOUNT_ID_EXP_JADE_ONEFIVE_TIMES_FETCH = 131,						-- 经验玉1.5倍领取次数
	DAYCOUNT_ID_LIEMING_BATCH_CHOU = 135,								-- 猎命免费次数
	DAYCOUNTER_ITEM_INFO_SC = 137,										--主宰神殿每日奖励领取次数
	DAYCOUNT_ID_BUILD_TOWER_FB_ENTER_TIMES = 138,						-- 塔防本进入次数
	DAYCOUNT_ID_BUILD_TOWER_FB_BUY_TIMES = 139,							-- 塔防本购买次数
	DAYCOUNT_ID_PERSON_BOSS_EXTRA_ENTER_TIMES = 140,          			-- 个人boss额外进入次数
	DAYCOUNT_ID_CALL_BOSS_TIMES = 141,                          		-- 召唤boss次数

	DAYCOUNT_ID_TEAM_YUANGU_XIANDIAN_VIP_BUY_TIME = 148,                --远古仙殿vip购买次数
	DAYCOUNT_ID_TEAM_YUANGU_XIANDIAN_GET_REWARD_COUNT = 149,                --远古仙殿获得奖励次数
	DAYCOUNT_ID_KILLGODTOWER_FB_ENTER_TIMES = 152,                      -- 诛神塔进入次数
	DAYCOUNT_ID_TEAM_KILLGODTOWER_GET_REWARD_COUNT= 153,                -- 诛神塔奖励领取次数
	DAYCOUNT_ID_SHENYING_FB_ENTER_TIMES= 154,                -- -- 圣印副本每日进入次数

	DAYCOUNT_ID_WORLD_BOSS_TIRE = 159,                          		-- 世界BOSS疲劳值
    DAYCOUNT_ID_MJ_BOSS_TIRE = 160,
    DAYCOUNT_ID_SHENYU_BOSS_TIRE = 161, 							--圣域boss疲劳
    DAYCOUNT_ID_TREASURE_CRYSTAL_NUM = 163,							--跨服BOSS珍稀水晶采集数
	DAYCOUNT_ID_TEAM_LINGHUNSQUARE_OPEN_TIMES = 169,           		-- 灵魂广场进入次数
    DAYCOUNT_ID_TEAM_LINGHUNSQUARE_VIP_BUY_COUNT = 170,          	-- 灵魂广场VIP购买次数
    DAYCOUNT_ID_TEAM_LINGHUNSQUARE_USE_TICKET_TIMES = 171,        	-- 灵魂广场消耗炼狱券增加的次数

	DAYCOUNT_ID_TIANSHEN_FB_ENTER_TIMES = 172,             		 	-- 天神副本进入次数
    DAYCOUNT_ID_TIANSHEN_FB_VIP_BUY_TIMES = 173,            		-- 天神副本VIP购买次数
    DAYCOUNT_ID_TIANSHEN_FB_ITEM_ADD_TIMES = 174,            		-- 天神副本消耗物品增加次数

    DAYCOUNT_ID_BAGUAMIZHEN_FB_ENTER_TIMES = 175,             		 	-- 八卦迷阵副本进入次数
    DAYCOUNT_ID_BAGUAMIZHEN_FB_VIP_BUY_TIMES = 176,            			-- 八卦迷阵副本VIP购买次数

	DAYCOUNT_ID_TEAM_WUJINJITAN_HELP_REWARD_TIMES = 177,				-- 无尽祭坛获得助战奖励次数
	DAYCOUNT_ID_TEAM_EQUIPMENT_FB_HELP_TIMES = 178,          			-- 远古仙殿助战次数
	--DAYCOUNT_ID_TEAM_LINGHUNSQUARE_HELP_REWARD_TIMES = 178,				-- 灵魂广场获得助战奖励次数 --改成只用一个

	DAYCOUNT_ID_MANHUANGGUDIAN_FB_ENTER_TIMES = 179,					-- 蛮荒古殿副本进入次数
	DAYCOUNT_ID_MANHUANGGUDIAN_FB_VIP_BUY_TIMES = 180,					-- 蛮荒古殿副本VIP购买次数
	DAYCOUNT_ID_MANHUANGGUDIAN_FB_HELP_TIMES = 181,						-- 蛮荒古殿副本协助次数
	DAYCOUNT_ID_TEAM_FB_HELP_TIMES = 184, 							--组队副本共享协助有效次数
	DAYCOUNT_ID_FINISH_HUSONG_TASK_COUNT = 185,						--护送完成次数
    DAYCOUNT_ID_TEAM_FB_HELP_TIMES_MAX = 186,                         --组队副本协助最大次数
    SHENYUAN_BOSS_KILL_REWARD = 187,                         --击杀次数
    SHENYUAN_BOSS_JION_REWARD = 188,                         --深渊boss参与次数
    WORLD_BOSS_HAS_BUY_TIMES = 189,                        		-- 世界bossvip购买次数
    SHENYUAN_BOSS_BUY_JION_TIMES = 193,                         --深渊boss购买参与次数\
    DAYCOUNT_ID_CROSS_BOSS_HAS_BUY_TIMES = 194,                         --蛮荒boss购买参与次数\
    DAYCOUNT_ID_CROSS_CHESTSHOP_BOSS_KILL_REWARD = 195,					-- 寻宝boss击杀奖励
	DAYCOUNT_ID_CROSS_CHESTSHOP_BOSS_JOIN_REWARD = 196,					-- 寻宝boss参与奖励
    DAYCOUNT_ID_CROSS_FAIRYLAND_BOSS_ENTER_TIMES = 197,					--跨服仙界BOSS免费进入次数
	DAYCOUNT_ID_CROSS_LONGMAI_GATHER_TIMES = 198,					-- 跨服龙脉采集次数
	DAYCOUNT_ID_CROSS_DRAGON_VEIN_SHOP_REFRESH_TIMES = 199,				--跨服龙脉商店刷新次数   
	DAYCOUNT_ID_KILL_VIP_BOSS_COUNT = 201,								--击杀VIPBOSS数量
	DAYCOUNT_ID_TEAM_TYPE_COMMON_BOSS1_ENTER_TIMES = 202,               --幻兽副本次数					
	DAYCOUNT_ID_TEAM_TYPE_COMMON_BOSS2_ENTER_TIMES = 203,               --女神副本次数
	DAYCOUNT_ID_TEAM_TYPE_COMMON_BOSS3_ENTER_TIMES = 204,               --武魂副本次数
	DAYCOUNT_ID_TEAM_TYPE_COMMON_TOWER1_ENTER_TIMES = 205,              --符文塔副本次数

	COMMON_DAYCOUNT_ID_TEAM_WUJINJITAN_OPEN_TIMES = 0,				--组队无尽祭坛开启次数(新协议)
	COMMON_DAYCOUNT_ID_TEAM_WUJINJITAN_USE_TICKET_TIMES = 1,		--无尽祭坛消耗炼狱券增加的次数(新协议)
}

MONSTER_TYPE = {
	MONSTER = 0,
	BOSS = 1,
	NPC = 2,
	TISNSHEN_CALL = 4, 	-- 天神召唤物（可攻击）
}

GUAI_JI_TYPE = {
	NOT = 0,										-- 不挂机
	ROLE = 1,										-- 挂机打人
	MONSTER = 2,									-- 挂机打怪
}

SERVER_TYPE = {
	RECOMMEND = 1,
	ALL = 2,
}

-- 主界面提醒与变强类型
MAINUI_TIP_TYPE = {
	FRIEND = 1,
	GUILD = 2,
	TEAM_INVITE = 3,								-- 邀请邀请
	TEAM_APPLY = 4,									-- 队伍申请
	TEAM = 5,										-- 队伍
	HU = 6,
	GUAJI_LILIAN = 7,								-- 挂机历练
	YUAN = 8,
	WABAO = 9,
	JILIAN = 10,
	MAIL = 11, 										-- 充值返利邮件
	Trade = 14,										-- 交易
	FIELD1V1_FAIL = 15,								-- 斗法封神失败提醒
	BLESSWISH = 16,									-- 祝福邀请提醒
	WEDDING = 17,									-- 婚宴提醒
	REDENVELOPES = 18,								-- 红包提醒
	MI_JING = 19,									-- 仙盟秘境
	YUNSHI = 20,									-- 仙盟运势提醒图标
	REDNAME = 21,									-- 红名提示
	PRIVILEGE = 22,									-- 一折抢购提示
	TEAM_FB = 23,									-- 团队副本
	XIONGSHOU = 24,									-- 仙盟凶兽
	BONFIRE = 25,									-- 仙盟篝火
	SPACE_GIFT = 26,								-- 空间送礼
	SPACE_LIUYAN = 27,								-- 空间浏览
	CLEAR_BAG = 28,									-- 清理背包
	GONGGAO = 29,									-- 公告栏
	BLESSING = 30,									-- 祝福好友
	REBATE = 31,									-- 回赠好友
	FRIENDYAOQIN = 32,								-- 好友邀请
	PROTECTSKILL = 33,								-- 护送保护技能
	BOSS_RED = 34,									-- boss伤害红包
	TREADURE_MAP = 35,								-- 藏宝图
	--SOCIETY_BLESSING = 36,							-- 福
	SOCIETY_WISH = 37,								-- 祝
	JIEZHEN = 38,									-- 修
	GUILDMERGE = 39,								-- 仙盟合并
	REDPOCKET = 40,                                 -- 仙盟红包
	INVITE = 41,                                    -- 婚宴邀请宾客
	BUYFBCOUNT = 42,                                -- 邀请购买仙侣副本
	SKY_REDENVELOPES = 43,                          -- 天降红包
	SUB_PACKAGE = 44,								-- 分包下载

	BEKILLER = 46,									-- 被杀记录
	FIRST_CHONGZHI_GROUP_BUY = 47,					-- 开服活动 -- 首充团购
	EVERYDAY_SHOP = 48,								-- 开服活动 -- 一折抢购
	LIMIT_BUY = 49,									-- 限制云购
	FRIEND_CHAT = 51,                               -- 好友聊天
	ZHEKOULIBAO = 52,								-- 折扣礼包
	XIEZHU = 53,									-- 仙盟协助
	BOOTYBAY_READY_VIEW = 54,						-- 藏宝湾组队准备界面
	STUDENT_HELP = 55,								-- 师徒修历 徒弟求助

    FENGSHENBANG_REMIND = 56,                       --封, 封神榜被抢提醒

    LIAOYILIAO = 57,								--情缘撩一撩
	EQUIP_TARGET = 58,								--装备收集

	XIAOGUI_XUFEI_1 = 100,							-- 小鬼1续费
	XIAOGUI_XUFEI_2 = 101,							-- 小鬼2续费
	XIAOGUI_XUFEI_3 = 102,							-- 小鬼3续费
	XIAOGUI_XUFEI_4 = 103,							-- 小鬼4续费
	YEZHANWANGCHENG_TIP = 50,						-- 夜战王城奖励提示
	LUANDOUBATTLE_TIP = 105,						-- 乱斗战场奖励提示
	CHAT_WORLD = 106,                               -- 世界聊天
	CHAT_KUAFU = 107,								-- 跨服聊天
	CHAT_GUILD = 108,								-- 仙盟聊天
	GUILD_ALL = 130,								-- 仙盟
	GUILD_LOGNHUI = 131,							-- 龙徽
	GUILD_ACTIVITY = 132,							-- 仙盟活动
	CHAT_UNREAD_TEAM = 120, 						-- 队伍未读消息
	CHAT_UNREAD_ZUDUI = 121,						-- 组队未读消息
	ROLE_BAG_JUHUN = 155,							-- 聚魂提示
	ROLE_SKILL = 156,								-- 技能提示

	FASHION_BODY = 157,								-- 时装提示
	FASHION_WUQI = 158,								-- 时装武器提示
	FASHION_FOOT = 159,								-- 时装足迹提示
	FASHION_GUANGHUAN = 160,						-- 时装光环提示
	FASHION_BUBBLE = 161,							-- 时装气泡提示
	FASHION_PHOTOFRAME = 162,						-- 时装头像框提示
	FASHION_WING = 163,								-- 时装羽翼提示
	FASHION_FABAO = 164,							-- 时装法宝提示
	FASHION_SHENBING = 165,							-- 时装神兵提示
	FASHION_LINGGONG = 166,							-- 时装灵弓提示
	FASHION_MASK = 177,								-- 时装装饰面饰提醒
	FASHION_BELT = 178,								-- 时装装饰腰带提醒
	FASHION_WEIBA = 179,							-- 时装装饰尾巴提醒
	FASHION_SHOUHUAN = 180,							-- 时装装饰手环提醒
	FASHION_JIANZHEN = 253,							-- 时装剑阵提示

	ADVANCED_WING = 181,							-- 羽翼进阶
	ADVANCED_FABAO = 182,							-- 法宝进阶
	ADVANCED_SHENBING = 183,						-- 神兵进阶
	ADVANCED_JIANZHEN = 184,						-- 剑阵进阶

	ROLE_JINJIE = 167,							    -- 境界提示
	ROLE_JINMAI = 168,							    -- 经脉提示
	ROLE_LINGCHONG = 170,							-- 灵宠提示
	ROLE_ZUOQI = 171,								-- 坐骑提示

	SKILL_UPLEVEL = 173,							-- 技能升级
	SKILL_AWAKE = 174,								-- 技能觉醒
	SKILL_UPGRADE = 175,							-- 技能进阶
	XIAOGUI_SHOUHU = 176,							-- 小鬼守护

	XiuZhenRoad = 185,								-- 修真之路
	CapabilityWelfare = 186,						-- 战力福利

	SHENSHOU_BAR = 202, 							-- 神兽提升

	Marry_Equip = 210,                           -- "物",
	Marry_Baby = 211,                            -- "娃",
	Marry_BaoXia = 212,                          -- "匣",
	Marry_FB = 213,                              -- "情",
	Marry_DianChun = 214,                        -- "唇",
	Marry_Title = 215,                           -- "缘",

	Equip_Streng = 220, 						-- 装备强化
	Equip_Tao = 221,                            --套装
	Equip_Bao = 222,
	Equip_Jue = 223,

	ACTDISCOUNT = 224,								-- 折
	SHANGGUJIN_STARSTAR = 225,						--上古神灵星级
	SHANGGUJIN_CANLING = 226,						--上古神灵残灵激活
	SHANGGUJIN_ZHUFU = 227,							--上古神灵祝福
	Equip_XiLian = 228,                         --洗练
	EQUIP_SHENGPIN = 229,						--装备升品
	EQUIP_YINJI = 231,							-- 装备印记
	EQUIP_STRENGTH = 232,                       -- 装备淬火

	MING_WEN_XQ = 250,							--铭文镶嵌
	MING_WEN_FJ = 251,							--铭文分解
	LEVEL_PURCHASE = 252,						-- "等级限购"


	TUJIAN_UP = 260,							-- 图鉴提升
	TUJIAN_SL = 261,							-- 收录达成
	MARRY_BEYAO = 262,							-- 婚宴被邀提示

	ZHAN_DUI_BE_INVITE = 264,						-- 战队邀请
	ZHAN_DUI_NEW_APPLT = 265,						-- 战队申请
	INVITE_START_CROSS_3V3 = 266, 					-- 邀请进入跨服3v3

	TEAM_INVITE_GOTO = 267,						--邀请前往
	TEAM_INVITE_ALERT = 268,						--邀请前往弹窗

	FuWenLongHun = 270,								--符文模块龙魂
	LONG_ZHU = 271,									-- 龙珠

	MOUNT_UPSTAR = 281,
	MOUNT_UPLEVEL = 282,
	LINGCHONG_UPSTAR = 283,
	LINGCHONG_UPLEVEL = 284,
	KUN_UPSTAR = 285,								-- 化鲲升星
	KUN_UPLEVEL = 286,								-- 化鲲升阶

	MOUNT_HuaLing = 267,                            --化灵
	LINGCHONG_HuaLing = 268,
	KUN_HuaLing = 269,

	XIAN_LV = 290,								-- 仙侣
	TIAN_SHEN_UPGRADE = 291, 						-- 天神提升
	TIAN_SHEN_SHENSHI = 292, 						-- 天神神饰提升
	TIAN_SHEN_SHENQI = 293, 						-- 天神神器提升
	TIAN_SHEN_SHENTONG = 294, 						-- 天神神通提升
	TIAN_SHEN_BATTLE = 295, 						-- 天神出战

	SHENSHI_QIANGHUA = 310, 						-- 天神-神饰-强化
	SHENSHI_TAOZHUANG = 311, 						-- 天神-神饰-套装


	TITLE_ACTIVE = 320,								--称号激活
	BAGUA_UP = 330,									--八卦提升
	GUILD_INVITE_HINT = 340,						--仙盟邀请提示

	TEAM_HEBING = 341, 								--队伍合并
	STRENGTH_ATTR_ACTIVE = 342,						--强化属性激活
	STONE_ATTR_ACTIVE = 343,						--宝石属性激活
	SHENGPIN_ATTR_ACTIVE = 344,						--升品属性激活
	STAR_ATTR_ACTIVE = 345,							--星级属性激活

	EQUIP_BETTER = 360, 						-- 装备提升（背包是否有更好的装备）

    Mount_Equip = 404,						        -- 坐骑装备
    Pet_Equip = 405,						        -- 灵宠装备
    HuaKun_Equip = 406,						        -- 化鲲装备

	Welfare_Upgrade_Gift = 440, -- 等級礼包
	Welfare_Vip_Gift = 441,    -- vip礼包
	MingWen_XianQian = 442,		--铭文提升
	LongHun_XianQian = 443,		--龙魂提升
	SIT_REARD = 444,			-- 打坐奖励
	OneVOne_JingCai = 445,		-- 跨服1v1竞猜
	Market_Auction = 446,		-- 市场--拍卖
    Shen_Shou_CZ = 447,         -- 兽魂助战
    New_Compose = 448,          --合成
    Shou_Hun = 449,             --兽魂装备
    Boss_FirstKill = 450,       --boss首杀
    SHOUHU_COMPOSE = 451,							--守护合成
    Market_Auction_Beyond = 452,		--市场拍卖-竞价被超越
    HunYan_Yuyue = 453,        --婚宴预约
    Guild_BaoXaing_Req = 454,			--每日宝箱请求刷新
    SHOU_HU_YOU_HUI = 463,				--守护优惠
    WARDROBE_UPGRADE = 466,				--衣橱提升
    EQUIP_LINGYU = 464,				--灵石提升
    LINGYU_ATTR_ACTIVE = 465,				--灵石屬性
    CROSS_SPY_ACCEPT_CALL_REWARD = 469,		-- 跨服刺探协助奖励
    CROSS_SPY_CALL = 470,				-- 跨服刺探收到召集
    TIAN_SHEN_BAGUA = 472, 		-- F2-八卦提升
    CROSS_SPY_SPY = 473,			-- 跨服刺探情报
	FIGHT_SOUL_TRAIN = 474,			-- 四象养成
	FIGHT_SOUL_COMPOSE = 475,		-- 四象融合
	FIGHT_SOUL_WEAR = 476,			-- 四象上阵
    CHAT_AITE_ME = 477,				--公共聊天频道@我
    XIANLI_CANGET = 478,            --vipBoss 仙力领取
    FEED_BACK = 480,				-- 玩家反馈
	FIGHT_SOUL_BONE_WEAR = 482,		-- 四象魂装穿戴
	FIGHT_SOUL_BONE_UPLV = 483,		-- 四象魂装强化
	FIGHT_SOUL_BONE_COM = 484,		-- 四象魂装融合
	--仙器注灵  仙器铭刻 仙器觉醒 仙器打造
    SHENJI_EQUIP_DETAIL = 485,			-- 仙器装备
	SHENJI_EQUIP_ZL = 486,			-- 仙器注灵
	SHENJI_EQUIP_ZL2 = 487,			-- 仙器进阶
	SHENJI_EQUIP_KM = 488,			-- 仙器刻铭
	SHENJI_EQUIP_AWAKE = 489,			-- 仙器觉醒
	SHENJI_EQUIP_BUILD = 490,			-- 仙器打造
	NATIONAL_PREVIEW = 491,				--国家预告
	EQUIP_COMPOSE = 492,				-- 装备合成
	ENEMY_RECORD = 493,				-- 仇人记录
	BOSS_XIEZHU = 494,				-- 蛮荒boss协助
	FRIEND_LIAOYILIAO = 495,		-- 撩一撩（好友申请）

	WING_SHENJI = 496, 						--羽翼神技
	WING_XIULIAN = 497, 						--羽翼修炼
	FABAO_SHENJI = 498, 						--法宝神技
	FABAO_XIULIAN = 499, 						--法宝修炼
	JIANZHEN_SHENJI = 500, 						--剑阵神技
	JIANZHEN_XIULIAN = 501, 						--剑阵修炼
	SHENBING_SHENJI = 502, 						--神兵修炼
	SHENBING_XIULIAN = 503, 						--神兵修炼

	WING_QL_ACT = 504, 						--羽翼器魂激活
	FABAO_QL_ACT = 505, 						--法宝器魂激活
	JIANZHEN_QL_ACT = 506, 						--剑灵器魂激活
	SHENBING_QL_ACT = 507, 						--神兵器魂激活

	FLE_GODBOOK_JIHUO = 508,							--天书激活
	FLE_GODBODY_JIHUO = 509,							--神体激活
	FLE_GODBODY_UPLEVEL = 510,							--神体修炼
	FLE_HOLYEQUIP_WEAR = 512,							--圣装穿戴
	FLE_HOLYEQUIP_TIHUAN = 513,							--圣装替换
	FLEF_STRENGTHEN = 514,								--圣装强化
	FLEF_EVOLVE = 515,									--圣装进化
	FLEF_UPQUALITY = 516,								--圣装升品

	WORLDS_NO1_BET = 517, 							-- 天下第一活动竞猜
	XIANLV_ZENGSONG = 518,							--仙侣赠送

	QIFU_YUNSHI_TIP = 519,							-- 祈福 运势
	LINGHE_UPLEVEL = 520,							-- 灵核升级
	SHITIAN_SUIT = 521,								-- 弑天套装
	ZHUHUN_EQUIPMENT = 522,							-- 锻造-铸魂
	ZHUSHENTAI_EQUIPMENT = 523,						-- 锻造-铸神台

	HAMMER_PLAN = 530,                              -- 暴揍策划
	ARTIFACT = 531,                                 -- 双修(仙魔神器)
	COLLECTION_WORD_TIP = 532,						-- 天师集字
	DRAGON_TEMP_HATCH = 533,						-- 龙神孵化

	SEND_FLOWER_UP_GRADE = 534,							-- 赠花进阶
	INVITE_START_LOVER_PK = 540, 					-- 邀请进入仙侣PK准备场景

	ASSIGN_COMPLIT = 550, 							-- 委托任务完成
	OPEN_SERVER_INVEST = 551, 						-- 开服投资
	MARKET_AUCTOIN_SUS = 552,                       -- 拍卖物被拍

	BEASTS_CULTURE = 560, 							-- 幻兽强化
	BEASTS_BATTLE = 561, 							-- 幻兽上阵
	BEASTS_BOOK_REWARD = 562,                       -- 幻兽图鉴
	BEASTS_PRIZE_DRAW = 563,                       	-- 幻兽召唤
	REFINING_DAN = 564,                       	-- 秘笈提升
	EVERYDAY_RECHARGE_DAILYGIFT = 565,              -- 秒杀礼包-礼包
	TRANSFER = 570,                       	-- 天地神躯
	ESOTERICA = 571,								--仙法

	ACHIEVENT = 998,								-- 成就
	--这两一定要在最后面，根据索引排序，不要再往下加索引了
	SHOU_CHONG_TE_HUI = 999,						-- 首充特惠（付费引导）
	ZERO_GUIZU = 1000,								-- 0元贵族（付费引导）

}

-- 变强类型 提升战力/获得奖励
MAINUI_STRONG_TIP_TYPE = {
	UP_POWER = 1,
	GET_REWARD = 2,
}

-- 【锚点类型】
AnchorPresets = {
	TopLeft = 1,			-- 左上
	TopCenter = 2,			-- 中上
	TopRight = 3,			-- 右上

	MiddleLeft = 4,			-- 中左
	MiddleCenter = 5,		-- 中间
	MiddleRight = 6,		-- 中右

	BottomLeft = 7,			-- 左下
	BottonCenter = 8,		-- 中下
	BottomRight = 9,		-- 右下

	VertStretchLeft = 10,	-- 左对齐 上下拉
	VertStretchRight = 11,	-- 中对齐 上下拉
	VertStretchCenter = 12,	-- 下对齐 上下拉

	HorStretchTop = 13,		-- 上对齐 左右拉
	HorStretchMiddle = 14,	-- 中对齐 左右拉
	HorStretchBottom = 15,	-- 下对齐 左右拉

	StretchAll = 16,		-- 全拉伸
 }

-- 【中心点类型】
 PivotPresets = {
	TopLeft = 1,			-- 左上
 	TopCenter = 2,			-- 中上
 	TopRight = 3,			-- 右上

 	MiddleLeft = 4,			-- 中左
 	MiddleCenter = 5,		-- 中间
 	MiddleRight = 6,		-- 中右

 	BottomLeft = 7,			-- 左下
 	BottonCenter = 8,		-- 中下
 	BottomRight = 9,		-- 右下
 }

-- 变强类型转换成功能开启名称（如果是基于红点回调的方式写的，则不需要加，因为RemindManager里已经判断了功能开启）
MAINUI_TIP_TYPE_TO_FUNNAME = {
	[MAINUI_TIP_TYPE.BAGUA_UP] = "fuwen_xiangqian", 				-- 八卦提升
	[MAINUI_TIP_TYPE.SKILL_AWAKE] = "skill_awake",					-- 技能觉醒
	[MAINUI_TIP_TYPE.SKILL_UPGRADE] = "skill_upgrade",				-- 技能进阶
	[MAINUI_TIP_TYPE.ROLE_JINJIE] = "jingjie",						-- 境界提示
	[MAINUI_TIP_TYPE.TIAN_SHEN_UPGRADE] = "tianshen_upgrade",		-- 天神提示
	--[MAINUI_TIP_TYPE.TIAN_SHEN_SHENSHI] = "tianshen_shenshi",		-- 天神神饰提升
	[MAINUI_TIP_TYPE.TIAN_SHEN_SHENQI] = "tianshen_shenQi",			-- 天神神器提升
	[MAINUI_TIP_TYPE.TIAN_SHEN_SHENTONG] = "tianshen_shentong",		-- 天神神通提升
	[MAINUI_TIP_TYPE.Equip_Streng] = "equipment_strength",			-- 装备强化
	[MAINUI_TIP_TYPE.Equip_Tao] = "equipment",						-- 套装
	[MAINUI_TIP_TYPE.LIAOYILIAO] = "marry_jiehun",					-- 情缘撩一撩
	[MAINUI_TIP_TYPE.TUJIAN_UP] = "ShanHaiJingView",				-- 万图谱
}

TIPS_KUANG_QUALITY_EFFECT = {
	[GameEnum.ITEM_COLOR_SHINING_GOLD] = "UI_tips_heijin",	-- 耀金
	[GameEnum.ITEM_COLOR_XUAN_QING] = "UI_tips_qin",		-- 玄青
}


TUMO_NOTIFY_REASON_TYPE = {
	TUMO_NOTIFY_REASON_DEFALUT = 0,					--屠魔默认通知类型
	TUMO_NOTIFY_REASON_ADD_TASK = 1,				--增加任务
	TUMO_NOTIFY_REASON_REMOVE_TASK = 2,				--移除任务
}

--属性丹类型
SHUXINGDAN_TYPE = {
	SHUXINGDAN_TYPE_PET = 0,						--宠物
	SHUXINGDAN_TYPE_MOUNT = 1,						--坐骑
	SHUXINGDAN_TYPE_XIULIAN = 2,					--修炼
	SHUXINGDAN_TYPE_WING = 3,						--羽翼
	SHUXINGDAN_TYPE_FABAO = 4,						--法宝
	SHUXINGDAN_TYPE_SHENGWANG = 5,					--声望

	SHUXINGDAN_TYPE_MAX = 6,
	MAX = 5,
}

-- 活跃度类型
ACTIVEDEGREE_TYPE = {
	ONLIME_TIME = 0,								-- 在线小时数
	WELFARE_FB = 1,									-- 福利副本
	COIN_FB = 2,									-- 参加铜币本次数
	TUMOTASK = 3,									-- 完成日常任务次数
	TEAM_FB = 4,									-- 参加多人副本次数
	SHUIJING = 5,									-- 水晶幻境采集次数
	CHALLENGEFIELD = 6,								-- 竞技场发起挑战次数
	STORY = 7,										-- 炼狱剧情副本挑战次数
	ZHUXIE = 9,										-- 诛邪战场次数
	HUSONG_TASK = 10,								-- 护送次数
	CHESTSHOP1 = 11,								-- 普通寻宝次数
	CHESTSHOP2 = 12,								-- 稀世珍宝次数
	GUILD_FIRE = 13,								-- 参加仙盟篝火次数
	GUILD_SHILAN = 14,								-- 参加仙盟试炼次数
	QINGYUAN_FB = 15,								-- 参加姻缘副本次数
	SPACE_LIUYAN = 16,								-- 空间留言
	QUNXIANLUANDOU = 17,							-- 群仙乱斗次数
	MAX = 18,
}

-- 经验加成类型
EXP_ADDITION_TYPE = {
	EXP_DEFEND = 1,
	NOBLE_UP = 2,
	RING_UP = 3,
	BRACELET_UP = 4,
	POTION_UP = 5,
	PASSIVE_SKILL = 6,
	ATEAM_UP = 7,
	LOVE_LOCK = 8,
	MOUNT_UP = 9,
	BUBUGAOSHENG = 10,
}

-- 
TASK_EVENT_TYPE = {
	ACCEPTED = 1,		-- 提交
	CAN_COMMIT = 2,		-- 可提交
	COMPLETED = 3,		-- 完成
}

-- 活动类型
ACTIVITY_TYPE = {
	INVALID              = -1,								-- 无效类型
	ZHUXIE               = 1,								-- 攻城准备战
	QUESTION             = 2,								-- 答题活动
	HUSONG               = 3,								-- 护送活动
	TIANSHENJIANLIN      = 4,								-- 天神降临
	XINGTIANLAIXI        = 5,								-- 刑天来袭
	GONGCHENGZHAN        = 6,								-- 攻城战
	XIANMENGZHAN         = 7,								-- 仙盟战
	NATIONAL_BOSS        = 8,								-- 神兽禁地(全民boss)
	CHAOSWAR             = 9,							 	-- 一战到底
	MOSHEN               = 10,							 	-- 魔神降临
	CAMPTASK             = 11,							 	-- 阵营刺杀
	LUCKYGUAJI           = 12,							 	-- 幸运挂机
	WUXINGGUAJI          = 13,							 	-- 五行挂机
	SHUIJING             = 14,								-- 水晶幻境
	HUANGCHENGHUIZHAN    = 15,							    -- 皇城会战
	CAMP_DEFEND1         = 16,								-- 守卫雕像1
	CAMP_DEFEND2         = 17,								-- 守卫雕像2
	CAMP_DEFEND3         = 18,								-- 守卫雕像3
	CAMPBOSS             = 19,								-- 阵营Boss
	HOTSPRING            = 20,								-- 温泉挂机
	MAMMONBLESS          = 21,								-- 财神赐福
	ANSWER               = 22,                              -- 答题
	QUINTUPLE_EXP        = 23,                              -- 五倍经验
	ACTIVITY_PAOKU       = 24,							    -- 跑酷
	DOUBLE_AWARD         = 26,								-- 双倍奖励
	GUILD_FB             = 27,								-- 帮派试炼（仙盟守护）
	GUILD_ANSWER         = 28,                              -- 帮派答题（仙盟晚宴）
	HUNYAN               = 29,								-- 婚宴
	QINGTIE              = 30,                              -- 请帖
	JIUSHEDAO            = 31,								-- 帮派副本
	INFINITEHELL         = 32,                              -- 无尽神狱
	YEZHANWANGCHENG      = 33,							    -- 夜战王城
	LUANDOUBATTLE        = 34, 							    -- 乱斗战场
	GUILD_CHUAN_GONG     = 36, 							    -- 仙盟传功
	RAND_ACTIVITY_TYPE_GUILD_BOSS 	= 37,                   -- 仙盟神兽
	RAND_ACTIVITY_TYPE_GUDAO_JIZHAN = 38,                   -- 孤岛激战
	ACTIVITY_TYPE_GUILD_INVITE = 39,						-- 仙盟争霸定级赛
	ACTIVITY_TYPE_ETERNAL_NIGHT = 40,						-- 永夜之巅 本服
	ACTIVITY_TYPE_ETERNAL_NIGHT_KF = 3102,					-- 永夜之巅 跨服（仙灵神域）
	HIT_HAMSTER			 = 41,								-- 活动-打地鼠

	LOVERS_FOREVER		 = 54,								-- 三生三世完美情人s
	GUILD_ZHENBA		 = 55,								-- 仙盟争霸
	BOSS_LIEREN			 = 70,								-- Boss猎人
	KUAFUYEZHANWANGCHENG = 3087,					        -- 跨服夜战王城（永夜战场）
	KUAFUSACREDBOSS      = 3089,						    -- 跨服圣域BOSS
	-- 客户端定义的活动类型
	PAIMINGJINGJICHANG   = 101,						        -- 排名竞技场(1V1)
	MarryFB              = 100,								-- 情缘副本
	XIANMENGRENWU        = 102,							    -- 仙盟任务
	XIANMENGSHENSHOU     = 103,							    -- 仙盟神兽
	MIGONGXUNBAO         = 104,								-- 迷宫寻宝
	TEAMFB               = 105,								-- 多人副本
	WABAO                = 106,								-- 挖宝(仙女掠夺)
	Alchemy              = 108, 							-- 炼丹
	GuaJi                = 109,								-- 挂机
	MANYTOWER            = 110,								-- 多人塔防
	MIJING               = 111,								-- 仙盟秘境
	XIONGSHOU            = 112,
	OPENSERVER_COMPETITION           = 113,					-- 开服比拼
	OPENSERVER_KAIZONGLIPAI          = 114,					-- 仙盟封榜
	FengShenBang 		 = 115,								-- 封神榜
    QUN_XIONG_ZHU_LU     = 116,                             -- F2群雄逐鹿
	SEVENDAY_RECHARGE	 = 160,								-- 七日累充
	LIMIT_TIME_GIFT 	 = 170,								-- 限时礼包
	KF_GIFT				 = 171,								-- 开服礼包

	--充值活动类型
	OPEN_SERVER     = 1025,							-- 开服活动
	CLOSE_BETA      = 1026,							-- 封测活动
	BANBEN_ACTIVITY = 1027,							-- 版本活动
	COMBINE_SERVER  = 1028,							-- 合服活动
	KF_ACTIVITY 	= 1029,							-- 开服活动2
	Act_Roller      = 2048,							-- 随机活动转盘(幸运转盘)
	SHITUXIULI 		= 1118,							-- F2师徒修历

	--随机活动
	RAND_ACT                   = 2000, 					-- 客户端用于泛指随机活动类型
	RAND_DAY_CHONGZHI_FANLI    = 2049,					-- 单日充值返利
	RAND_DAY_CONSUME_GOLD      = 2050,					-- 单日消费
	RAND_TOTAL_CONSUME_GOLD    = 2051,					-- 累计消费
	RAND_DAY_ACTIVIE_DEGREE    = 9999,					-- 单日活跃奖励 --废弃
	RAND_CHONGZHI_RANK         = 2053,					-- 充值排行
	RAND_SERVER_PANIC_BUY      = 2055,					-- 全服疯狂抢购
	RAND_PERSONAL_PANIC_BUY    = 2056,					-- 个人疯狂抢购
	RAND_CONSUME_GOLD_FANLI    = 2057,					-- 消费返利
	RAND_EQUIP_STRENGTHEN      = 2058,					-- 装备强化
	RAND_CHESTSHOP             = 2059,					-- 奇珍异宝
	RAND_STONE_UPLEVEL         = 2060,					-- 宝石升级
	RAND_XN_CHANMIAN_UPLEVEL   = 2061,				    -- 仙女缠绵
	-- RAND_MOUNT_UPGRADE      = 2062,					-- 坐骑进阶
	RAND_QIBING_UPGRADE        = 2063,					-- 骑兵升级
	RAND_MENTALITY_TOTAL_LEVEL = 2064,				    -- 根骨全身等级
	RAND_WING_UPGRADE          = 2065,					-- 羽翼进化
	RAND_QUANMIN_QIFU          = 2066,					-- 全民祈福
	RAND_ACTIVITY_TYPE_XIANLING_ZHEN       = 2083,		-- F2仙灵古阵
	RAND_XIANMENG_JUEQI        = 2068,					-- 仙盟崛起
	RAND_XIANMENG_BIPIN        = 2069,					-- 仙盟比拼
	RAND_DAY_ONLINE_GIFT       = 2070,					-- 每日在线好礼
	RAND_DOUFA_KUANGHUAN       = 2072,					-- 斗法狂欢
	RAND_ZHANCHANG_FANBEI      = 2073,					-- 战场翻倍

	BP_CAPABILITY_WING    = 2080,						-- 比拼羽翼战力
	BP_CAPABILITY_MOUNT   = 2079,						-- 比拼坐骑战力
	BP_CAPABILITY_PET     = 2077,						-- 比拼宠物战力
	--BP_CAPABILITY_FABAO   = 2097,						-- 比拼法宝战力
	BP_CAPABILITY_EQUIP   = 2076,						-- 比拼装备战力
	BP_CAPABILITY_JINGMAI = 2078,					    -- 比拼经脉战力
	BP_CAPABILITY_TOTAL   = 2075,						-- 比拼综合战力

	RAND_CHARGE_REPALMENT                   = 2081,					-- 充值回馈
	RAND_SINGLE_CHARGE                      = 2082,					-- 单笔充值
	RAND_CORNUCOPIA                         = 20830,					-- 聚宝盆
	RAND_CHONGZHI_DOUBLE                    = 2084,					-- 双倍充值
	RAND_DAY_DANBI_CHONGZHI                 = 2085, 				-- 单笔充值
	RAND_TOTAL_CHARGE_DAY                   = 2086,					-- 随机活动每日累充
	RAND_TOMORROW_REWARD                    = 2087,					-- 次日福利活动
	RAND_SEVEN_DOUBLE                       = 2088,					-- 七日双倍活动
	RAND_ACTIVITY_TYPE_TOTAL_CHONGZHI       = 2091,		            -- 活动累计充值
	RAND_DOUBLE_XUNBAO_JIFEN                = 2092,				    -- 双倍寻宝积分
	RAND_EQUIP_EXCHANGE                     = 2093,					-- 装备积分兑换
	RAND_SPRITE_EXCHANGE                    = 2094,					-- 精灵积分兑换
	RAND_JINYINTA                           = 2095,					-- 金银塔
	RAND_NIUEGG                             = 2096,					-- 充值扭蛋
	RAND_ZHENBAOGE                          = 2098,					-- 珍宝阁
	RAND_DAILY_ONLINE_LOTTERY               = 2099,				    -- 每日在线抽奖
	RAND_MIJINGXUNBAO                       = 2100,					-- 秘境寻宝
	RAND_BIXIAXUNBAO                        = 2101,					-- 陛下寻宝
	RAND_LOTTERY_TREE                       = 2102,					-- 摇钱树
	JISHUCHONGZHAN_1                        = 2103,					-- 急速冲战1
	JISHUCHONGZHAN_2                        = 2104,					-- 急速冲战2
	JISHUCHONGZHAN_3                        = 2105,					-- 急速冲战3
	RAND_ACTIVITY_TYPE_OA_FORTUNE_CAT       = 2106,                 -- 招财猫
	RAND_CONSUME_SCORE                      = 2107,					-- 消费积分
	--RAND_ACTIVITY_NEW_THREE_SUIT            = 2109, 			    -- 新三件套
	--RAND_ACTIVITY_MINE                      = 2110,					-- 翡翠矿场
	--RAND_TIMEBOSS                           = 2111,					-- 定时BOSS
    --RAND_CONTINUE_CHONGZHI                  = 2112,					-- 连续充值 弃用

    RAND_ACTIVITY_TYPE_CSA_DAY_FIRST_RECHARGE = 2109,                   -- 合服活动 - 每日首充 1
    RAND_ACTIVITY_TYPE_CSA_LIMIT_RECHARGE = 2110,                       -- 合服活动 - 限时累充 1
    RAND_ACTIVITY_TYPE_CSA_DUOBEI = 2111,                               -- 合服活动 - 多倍活动 1
    MERGE_ACT_ZHAOCAIMAO = 2112,                                        -- 合服活动 - 招财猫 1
    RAND_ACTIVITY_TYPE_CSA_LOGIN_GIFT = 2248,                           -- 合服活动 - 登录有礼 1
    RAND_ACTIVITY_TYPE_CSA_RANK = 2129,									-- 合服活动 - 排行榜
    RAND_ACTIVITY_TYPE_CSA_TIMED_SPIKE = 2128,							-- 合服活动 - 限时秒杀 1
    MERGE_ACT_FIREWORKS = 2246,                                         -- 合服活动 - 烟火盛典 1
    RAND_ACTIVITY_TYPE_CSA_BOSS_HUNTER = 2119,							-- 合服活动 - 猎魔达人 1
    MERGE_ACT_GUILDWAR = 2247,                                          -- 合服活动 - 仙盟争霸 1
    MERGE_ACT_HONGMENG_WUDAO = 2118,                                    -- 合服活动 - 鸿蒙悟道 1
    RAND_ACTIVITY_TYPE_CSA_JULINGZHUZHEN = 2127,						-- 合服活动 - 聚灵助阵 1
    RAND_ACTIVITY_TYPE_CSA_CONVERT = 2249,                              -- 合服活动 - 兑换商店

	RAND_MONSTER_DROP                       = 2113,					-- 打怪掉落
	RAND_ACTIVITY_ZHENGGUZJ                 = 2114,					-- 整蛊专家
	RAND_ACTIVITY_BEIZHENGDAREN             = 2115,				    -- 被整达人
	RAND_ACTIVITY_TYPE_FUNNY_SHOOT          = 2116,			        -- 趣味射门
	ACTIVITY_DAILY_LOVE                     = 2117,					-- 每日一爱
	--RAND_ZHANCHANG_ZHENGBA                  = 2118,					-- 战场争霸
	--RAND_ACTIVITY_TYPE_SUIPIAN_EXCHANGE     = 2119,					-- 碎片兑换
	RAND_ACTIVITY_TYPE_CONTINUE_CONSUME     = 2120,					-- 连续消费
	RAND_ACTIVITY_TYPE_PLANTING_TREE        = 2121,					-- 趣味植树
	RAND_ACTIVITY_FANFANZHUAN               = 2122,					-- 翻翻转活动
	RAND_COLLECT_ITEMS                      = 2123,					-- 集字活动
	RAND_ACTIVITY_TYPE_FNALI_PERCNET        = 2124,		            -- 单笔返利
	RAND_ACTIVITY_TYPE_CIRCULATION_CHONGZHI = 2125,					-- 循环充值（充值返豪礼）
	RAND_ACTIVITY_TYPE_CHONGZHI_RANK_2      = 2126,					-- 至尊充值
	--RAND_ACTIVITY_TYPE_SANJIANTAO2          = 2127,					-- 三件套2(分段)
	--RAND_ACTIVITY_TYPE_MIJINGXUNBAO2        = 2128,					-- 秘境寻宝2(分段)
    --RAND_ACTIVITY_TYPE_YAOQIANSHU2          = 2129,					-- 摇钱树2(分段)
	RAND_ACTIVITY_TYPE_CHONGZHIFANYUANBAO   = 2131,					-- 充值返元宝
	RAND_ACTIVITY_TYPE_BLACK_MARKET         = 2132,					-- 黑市竞拍
	RAND_ACTIVITY_TYPE_RUNNING_MAN          = 2133,					-- 奔跑吧仙友
	RAND_ACTIVITY_BATTLE_SOUL               = 2190,  				-- 战场之魂
	RAND_ACTIVITY_FIREWORKS                 = 2193,					-- 烟花庆典

	RAND_MOUNT_JINJIE     = 2062,						-- 坐骑进阶
	RAND_WING_JINJIE      = 2148,						-- 羽翼进阶
	RAND_SHENWU_JINJIE    = 2134,						-- 神武进阶
	RAND_FABAO_JINJIE     = 2135,						-- 法宝进阶
	RAND_LINGCHONG_JINJIE = 2136,						-- 灵宠进阶
	RAND_LINGGONG_JINJIE  = 2137,						-- 灵弓进阶
	RAND_LINGQI_JINJIE    = 2138,						-- 灵骑进阶
	RAND_LINGYI_JINJIE    = 2139,						-- 灵翼进阶

	RAND_MOUNT_CHARGE                  = 2140,						-- 坐骑充值
	RAND_WING_CHARGE                   = 2141,						-- 羽翼充值
	RAND_FABAO_CHARGE                  = 2142,						-- 法宝充值
	RAND_SHENWU_CHARGE                 = 2143,						-- 神武充值
	RAND_LINGCHONG_CHARGE              = 2144,						-- 灵宠充值
	RAND_LINGQI_CHARGE                 = 2145,						-- 灵骑充值
	RAND_LINGGONG_CHARGE               = 2146,						-- 灵弓充值
	RAND_LINGYI_CHARGE                 = 2147,						-- 灵翼充值
	RAND_MOUNT_SHOP                    = 2149,						-- 坐骑抢购
	RAND_WING_SHOP                     = 2150,						-- 羽翼抢购
	RAND_FABAO_SHOP                    = 2151,						-- 法宝抢购
	RAND_SHENWU_SHOP                   = 2152,						-- 神武抢购
	RAND_LINGCHONG_SHOP                = 2153,						-- 灵宠抢购
	RAND_LINGQI_SHOP                   = 2154,						-- 灵骑抢购
	RAND_LINGGONG_SHOP                 = 2155,						-- 灵弓抢购
	RAND_LINGYI_SHOP                   = 2156,						-- 灵翼抢购
	RAND_LOTTERY_DRAW                  = 2157,						-- 幸运抽奖
	RAND_ACTIVITY_TYPE_MYSTERY_SHOPMAN = 2158,						-- 神秘商人
	RAND_ACTIVITY_TYPE_SEVENDAY        = 2159,						-- 七天返利

	RAND_ACTIVITY_TYPE_CONVERT_CRAZY          = 2160,  		    -- 兑换狂欢
	RAND_ACTIVITY_TYPE_DROP_RATE_ADD          = 2161,			-- 掉率增加
	RAND_ACTIVITY_TYPE_EXP_DOUBLE             = 2162,           -- 经验双倍
	RAND_ACTIVITY_TYPE_FB_DOUBLE_DROP         = 2163,  		    -- 副本双倍掉落
	RAND_WEEKEND_BOSS                         = 2165,           -- 周末BOSS
	RAND_KILL_BOSS                            = 2071,			-- 掉落狂欢
	RAND_LOGIN_GIFT                           = 2074,			-- 登录有礼
	RAND_CONSUME_GOLD_RANK                    = 2054,			-- 本服消费排行
	RAND_ACTIVITY_TYPE_CHONGZHI_YOULI         = 2166,		    -- 充值有礼
	RAND_ACTIVITY_TYPE_TOTAL_CONSUME_GOLD2    = 2167,			-- 累计消费2
	RAND_TOTAL_CHONGZHI2                      = 2168,			-- 累计充值2
	RAND_ACTIVITY_TYPE_CHARGE_REPAYMENT2      = 2169, 			-- 充值回馈
	RAND_ACTIVITY_HAPPY_CUNSUME               = 2170, 			-- 消费欢乐颂
	RAND_ACTIVITY_HAPPY_RECHARGE              = 2171,			-- 充值欢乐颂
	RAND_ACTIVITY_TYPE_XUNBAO_CARNIVAL        = 2173,			-- 寻宝狂欢
	ACT_GOBAL_XUNBAO                          = 2172,			-- 全民庆典
	RAND_ACTIVITY_TIANJIANGCAISHEN            = 2174,			-- 天降财神
	RAND_ACTIVITY_RECHARGE_GIFT               = 2175,			-- 充值好礼
	RAND_PIG_TREASURE                         = 2176,			-- 金猪探宝
	RAND_ACTIVITY_HIGHT_REBATE                = 2177, 			-- 高倍返利
	RAND_ACTIVITY_SINGLE_REBATE               = 2178,			-- 单笔返利
	RAND_ACTIVITY_LIMITED_SHOP                = 2179,			-- 限时特卖
	RAND_ACTIVITY_TYPE_WISH_POOL              = 2180,			-- 许愿池
	RAND_ACTIVITY_DAILYCONSUME                = 2181,			-- 每日累消
	RAND_ACTIVITY_TYPE_NATIONAL_TREASURE      = 2182,		    -- 全民鉴宝
	RAND_ACTIVITY_COLLECTBLESS                = 2183,			-- 翻牌集福
	RAND_ACTIVITY_PERFECTLOVER                = 2184,			-- 完美情人
	RAND_ACTIVITY_TYPE_CLOUD_BUY              = 2185,           -- 幸运云购
	ND_ACTIVITY_TYPE_PANIC_BUY                = 2186,           -- 特惠秒杀
	RAND_ACTIVITY_TYPE_SHAKE_MONEY            = 2194,			-- 疯狂摇钱树
	RAND_ACTIVITY_TYPE_CHONGZHI_CRAZY_REBATE  = 2195,			-- 狂返元宝
	RAND_ACTIVITY_TYPE_PROFESS_RANK           = 2196,			-- 表白排行榜
	RAND_ACTIVITY_TYPE_ACTDISCOUNT 			  = 2198,			-- 一折抢购
	RAND_ACTIVITY_TYPE_SPECIALGIFTBIG 		  = 2199,			-- 特惠礼包
	ShopDazzleReturn    					  = 2200, 			-- 时装白送
	RAND_ACTIVITY_TYPE_SMALLGOAL			  = 2201,			-- 小目标
	TotalConsume 							  = 2202, 			-- 累消返利
	XinFuTeMai  							  = 2203, 			-- 新服特卖
	DayDayFanLi  							  = 2204, 			-- 天天返利
	RAND_ACTIVITY_TYPE_SPECIAL_GIFT           = 2205,			-- 奇遇礼包
	RAND_ACTIVITY_TYPE_SUPERGIFTBAG  		  = 2206, 			-- 超级礼包
	RAND_ACTIVITY_TYPE_FIRST_KILL	  		  = 2207, 			-- 开服激战
	RAND_ACTIVITY_TYPE_MUST_BUY		  		  = 2208, 			-- 超值必买
	RAND_ACTIVITY_TYPE_ACTIVE_PLATE	  		  = 2209, 			-- 活跃转盘
    GOD_XUNBAO                                = 2210,           -- 天神主题-天神寻宝
	GOD_DENGLU_YOULI	 		  			  = 2211,			-- 天神主题-登陆有礼
	GOD_CHAOZHI_SHOUCHONG	 		  		  = 2212,			-- 天神主题-超值奖励
	GOD_LEIJI_HAOLI			  		 		  = 2213,			-- 天神主题-累计好礼
	GOD_WANT_SHENQI 				 		  = 2214,			-- 天神主题-我要神器
	GOD_CHONGBANG 	 				 		  = 2215,			-- 天神主题-天神冲榜
	GOD_JIANGLIN 	 				 		  = 2216,			-- 天神主题-天神降临
	GOD_MOWANG_YOULI 				 		  = 2217,			-- 天神主题-魔王有礼
	GOD_DOUBLE_JIANGLI	 	 				  = 2218,			-- 天神主题-多陪奖励
	GOD_XIANSHI_MIAOSHA	 	 				  = 2272,			-- 天神主题-限时秒杀
	RAND_ACTIVITY_TYPE_LOVING_CITY	  		  = 2219, 			-- 开服活动 完美情人_全城热恋
	RAND_ACTIVITY_TYPE_LOVING_BIAOBAI	 	  = 2220,			-- 完美情人_爱的表白

	RAND_ACT_BEIZHAN_XINGTIANLAIXI 			 	= 2221,         -- 备战主题 刑天来袭
	RAND_ACT_BEIZHAN_DENGLUYOULI 			 	= 2222,         -- 备战主题 登录有礼
	RAND_ACT_BEIZHAN_MEIRISHOUCHONG 			= 2223,         -- 备战主题 每日首充
	RAND_ACT_BEIZHAN_LEICHONGHAOLI 				= 2224,         -- 备战主题 累充豪礼
	RAND_ACT_BEIZHAN_DUOBEILAIXI 				= 2225,         -- 备战主题 多倍奖励
	RAND_ACT_BEIZHAN_WOYAOLONGHUN 				= 2226,         -- 备战主题 我要龙魂
	RAND_ACT_BEIZHAN_QUANFUJUANXIAN 			= 2227,         -- 备战主题 全服捐献
	RAND_ACT_BEIZHAN_ZHANLIBIPIN 				= 2228,         -- 备战主题 战力比拼
	RAND_ACT_BEIZHAN_BEIZHANHAOLI 				= 2229,         -- 备战主题 备战豪礼
	RAND_ACT_BEIZHAN_DALIAN 				    = 2230,         -- 备战主题 打脸
	RAND_ACT_BEIZHAN_LONGHUNRANK 				= 2108,         -- 备战主题 龙魂冲榜

	RAND_ACT_XIANQIJIEFENG_XINGTIANLAIXI 			 	= 2252,         -- 仙器解封 刑天来袭
	RAND_ACT_XIANQIJIEFENG_DENGLUYOULI 			 		= 2253,         -- 仙器解封 登录有礼
	RAND_ACT_XIANQIJIEFENG_MEIRISHOUCHONG 				= 2254,         -- 仙器解封 每日首充
	RAND_ACT_XIANQIJIEFENG_LEICHONGHAOLI 				= 2255,         -- 仙器解封 累充豪礼
	RAND_ACT_XIANQIJIEFENG_DUOBEILAIXI 					= 2256,         -- 仙器解封 多倍奖励
	RAND_ACT_XIANQIJIEFENG_WOYAOLONGHUN 				= 2257,         -- 仙器解封 我要仙器
	RAND_ACT_XIANQIJIEFENG_ZHANLIBIPIN 					= 2258,         -- 仙器解封 战力比拼
	RAND_ACT_XIANQIJIEFENG_BEIZHANHAOLI 				= 2259,         -- 仙器解封 备战豪礼
	RAND_ACT_XIANQIJIEFENG_LONGHUNRANK 					= 2260,         -- 仙器解封 仙器冲榜
	-- RAND_ACT_XIANQIJIEFENG_DALIAN 				    	= 99999,         -- 仙器解封 打脸
	-- RAND_ACT_XIANQIJIEFENG_QUANFUJUANXIAN 				= 99999,         -- 仙器解封 全服捐献
	-- RAND_ACT_XIANQIJIEFENG_TURNTABLE 					= 99999,         -- 仙器解封 转盘


	RAND_ACT_TIANSHEN_DALIAN 					= 2231,         -- 天神主题 打脸
	RAND_ACT_OPEN_SERVER_JUANXIAN 				= 2232,         -- 开服活动 捐献
	RAND_ACT_OPEN_SERVER_ZHENLONG 				= 2233,         -- 开服活动 真龙送礼
	RAND_ACT_OPEN_SERVER_DALIAN 				= 2234,         -- 开服活动 打脸
	RAND_ACT_OPEN_SERVER_XUNYULU				= 2235,			-- 开服活动 寻玉录
    BOSS_XUAN_SHANG                             = 2236,         -- BOSS悬赏
	RAND_ACTIVITY_TYPE_LOGIN_LUXURY_GIFT		= 2237,			-- 登录豪礼
	RAND_ACTIVITY_TYPE_CRAZY_HIGH_POINT			= 2238,			-- 开服嗨点
	RAND_ACT_LINGYAOMAIHUO						= 2241,			-- 灵妖卖货
	RAND_ACTIVITY_TYPE_ZHOUYI_YUNCHENG 			= 2243,			-- F2周一运程

    RAND_ACTIVITY_TYPE_FANLIBAOXIAN 			= 2250,			-- F2七日返利

	OPERA_ACT_DUOBEI_YOULI 						= 2091, 		-- 运营活动_多倍有礼
	OPERA_ACT_LOGIN_REWARD						= 2092,			-- 运营活动_登录有礼
	OPERA_ACT_FENGZHENG							= 2093,		    -- 运营活动_风筝夺宝
	OPERA_ACT_CTN_RECHARGE 						= 2094, 		-- 运营活动_连续充值
	OPERATION_FIRST_RECHARGE				    = 2095,			-- 运营活动_每日首充
	OPERA_ACT_MOWANG_YOULI 						= 2096, 		-- 运营活动_魔王有礼
	OPERA_ACT_MOWU_JIANGLIN 		 			= 2097,			-- 运营活动_魔物降临
	OPERA_ACT_QUANFU_JUANXIAN 					= 2098,			-- 运营活动_全服捐献
	OPERA_ACT_CHUSHEN							= 2099,		    -- 运营活动_天才厨神
	OPERA_ACT_TURNTABLE							= 2100,			-- 运营活动_兔女郎宝藏
	OPERA_ACT_TOTAL_RECHARGE 		 			= 2101,			-- 运营活动_限时累充
	OPERA_ACT_XIANSHI_MIAOSHA 					= 2102, 		-- 运营活动_限时秒杀
	OPERA_ACT_HAPPY_KUANGHUAN					= 2103,			-- 运营活动_幸福狂欢
	OPERA_ACT_FISH								= 2104,			-- 运营活动_辛运锦鲤
	OPERA_ACT_WATERING_FLOWERS 					= 2105, 		-- 运营活动_种花浇水
	OPERA_ACT_EXCHANGE							= 2107,			-- 运营活动_兑换商店
	OPERA_ACT_TASK_CHAIN 		 				= 2239,			-- 运营活动_任务链
	OA_FUYUANXIANCE								= 2253,			-- 运营活动_福缘仙册
    OPERA_ACT_IMAGE_SHOW 		 				= 2273,			-- 运营活动_形象展示--占位使用
    LIMITEDTIMEOFFER                            = 2274,         -- f2限时特惠
    MARRY_DISCOUNT								= 2275,			-- 结婚折扣
    RAND_ACTIVITY_TYPE_OA_RECHARGE_DISCOUNTS	= 2277,			-- 返利活动-充值立减
    RAND_ACTIVITY_TYPE_OA_FIREWORKS_DRAW		= 2278,			-- 返利活动-烟花抽奖
    RAND_ACTIVITY_TYPE_OA_EXTINCT_GIFT			= 2279,			-- 返利活动-绝版赠礼
	REBATE_ZHIGOU_FANLI 						= 2052,			-- 直购返利
	RAND_ACTIVITY_TYPE_LIANXUCHONGZHI 			= 2251,			-- F2连续充值
	RAND_ACTIVITY_OPERA_REQ_CS                  = 2355,         -- 幻兽抽奖运营活动
	PRIVILEGEBUY								= 2306,			-- 特权直购
	OPERA_ACT_DRAGONSECRET						= 2308, 		-- 神龙密藏
	ACTIVITY_TYPE_OA_EXCHANGE_SHOP				= 2309,			-- 天师集字 兑换商店
	PREMIUM_GIFT                                = 2314,         -- 超值赠礼


	--节日活动
	FESTIVAL_ACT_OA_DUOBEI_2 = 2261,							-- 节日活动 - 多倍活动
	FESTIVAL_ACT_OA_LOGINGIFT = 2262,							-- 节日活动 - 登录有礼
	FESTIVAL_ACT_OA_LIMIT_RECHARGE = 2263,						-- 节日活动 - 限时累充
	FESTIVAL_ACT_HAPPY_MONDAY = 2264,							-- 节日活动 - 周一狂欢
	FESTIVAL_ACT_CONVERT_SHOP = 2265,							-- 节日活动 - 兑换商店
	FESTIVAL_ACT_OA_MOWANGYOULI_2 = 2267,						-- 节日活动 - 魔王有礼
	FESTIVAL_ACT_OA_TIANCAICHUSHEN_2 = 2268,					-- 节日活动 - 天才厨神
	FESTIVAL_ACT_RANK_2 = 2269,									-- 节日活动 - 排行榜
	FESTIVAL_ACT_YANHUA_SHENGDIAN_2 = 2270,						-- 节日活动 - 扭蛋机
	FESTIVAL_ACT_OA_TIMED_SPIKE_2 = 2271,						-- 节日活动 - 限时秒杀
	FESTIVAL_ACT_OA_LIAN_CHONG = 2053,							-- 节日活动 - 连冲

	RAND_ACTIVITY_TYPE_OA_CAT_VENTURE = 2276,					-- 运营活动 - 小猫冒险
	RAND_ACTIVITY_TYPE_OA_LIMIT_RMB_BUY = 2280,					-- 运营活动 - 限时直购
	RAND_ACTIVITY_TYPE_OA_HELP_RANK = 2281,						-- 运营活动 - 冲榜助力
	RAND_ACTIVITY_TYPE_OA_GOD_RMB_BUY = 2282,					-- 运营活动 - 神藏直购
	RAND_ACTIVITY_TYPE_OA_ZHI_ZUN = 2312,						-- 运营活动 - 领域直购
	RAND_ACTIVITY_TYPE_OA_TIANSHEN_RMB_BUY = 2283,				-- 运营活动 - 仙灵直购
	RAND_ACTIVITY_TYPE_OA_DIY1_DRAW = 2284,						-- 运营活动 - 月下求灯
	RAND_ACTIVITY_TYPE_OA_DIY2_DRAW = 2285,						-- 运营活动 - DIY抽奖2
	RAND_ACTIVITY_TYPE_OA_SYSTEM_SWORN = 2286,					-- 运营活动 - 五行冲榜
	RAND_ACTIVITY_TYPE_OA_SYSTEM_SHENJI = 2287,					-- 运营活动 - 神技冲榜
	RAND_ACTIVITY_TYPE_OA_SYSTEM_LINGHE = 2288,					-- 运营活动 - 灵核冲榜
	RAND_ACTIVITY_TYPE_OA_SYSTEM_SQGUANG = 2289,				-- 运营活动 - 圣器-光冲榜
	RAND_ACTIVITY_TYPE_OA_SYSTEM_SQAN = 2290,					-- 运营活动 - 圣器-暗冲榜
	RAND_ACTIVITY_TYPE_OA_GLORY_CRYSTAL = 2291,					-- 运营活动 - 荣耀水晶
	RAND_ACTIVITY_TYPE_YANHUA_SHENGDIAN_3 = 2292,				-- 运营活动 - 扭蛋机3
	RAND_ACTIVITY_TYPE_OA_LIMIT_RMB_BUY2 = 2293,				-- 运营活动 - 限时直购2
	RAND_ACTIVITY_TYPE_OA_ONLINE_REWARD = 2294,					-- 运营活动 - 在线奖励
	RAND_ACTIVITY_TYPE_OA_SYSTEM_RANK_CAST_SOUL = 2295,			-- 运营活动 - 铸魂冲榜
	RAND_ACTIVITY_TYPE_OA_SYSTEM_RANK_DRAGON_TEMPLE = 2296,		-- 运营活动 - 龙神冲榜
	RAND_ACTIVITY_TYPE_OA_SYSTEM_RANK_YULING = 2297,			-- 运营活动 - 御灵冲榜
	RAND_ACTIVITY_TYPE_OA_WEEK_CARD = 2298,						-- 周卡活动 - 重拳出击打策划
	RAND_ACTIVITY_TYPE_OA_DAILY_RECHARGE = 2299,				-- 运营活动 - 每日累充
	RAND_ACTIVITY_TYPE_OA_FIREWORKS_DRAW2 = 2300,				-- 运营活动 - 烟花抽奖2
	RAND_ACTIVITY_TYPE_OA_SPECIAL_DRAW1 = 2301,					-- 运营活动 - 特殊抽奖1
	RAND_ACTIVITY_TYPE_OA_LIMIT_RMB_BUY3 = 2302,				-- 运营活动 - 限时直购3
	RAND_ACTIVITY_TYPE_OA_HIDE_GOLD_SHOP = 2303,				-- 运营活动 - 藏金商铺
	RAND_ACTIVITY_TYPE_OA_CAT_VENTURE2 = 2304,					-- 运营活动 - 翻牌好礼
	RAND_ACTIVITY_TYPE_OA_SPECIAL_DRAW2 = 2305,					-- 运营活动 - 特殊抽奖2
	RAND_ACTIVITY_TYPE_OA_CHAIN_DRAW = 2307,					-- 运营活动 - 一线牵
	RAND_ACTIVITY_TYPE_OA_LIKE = 2310,							-- 运营活动 - 点赞活动
	RAND_ACTIVITY_TYPE_OA_CANGMING_BUY = 2313,					-- 运营活动 - 沧溟直购
	RAND_ACTIVITY_TYPE_OA_LIMIT_RMB_SHOP = 2315,				-- 运营活动 - 特惠卖场
	RAND_ACTIVITY_TYPE_OA_HOLY_BEAST_CALL = 2316,               -- 圣兽召唤
	RAND_ACTIVITY_TYPE_OA_CHARMRANK = 2317,						-- 本服魅力榜
	RAND_ACTIVITY_TYPE_WEIWODUZUN = 2318,						-- 唯我独尊
	RAND_ACTIVITY_TYPE_OA_CHESSBOARD = 2319,					-- 运营活动 - 棋盘寻宝
	RAND_ACTIVITY_TYPE_OA_SHENNUTIANZHU = 2321,					-- 运营活动 - 神怒天诛
	RAND_ACTIVIYY_TYPE_OA_ODYSSEYRMB = 2322,					-- 运营活动 - 一生所爱直购
	RAND_ACTIVITY_TYPE_OA_A_LIFELONG_LOVE_TASK = 2323,			-- 运营活动 - 一生所爱
	RAND_ACTIVITY_PANACEA_FURNACE = 2324,						-- 运营活动-灵丹宝炉
	RAND_ACTIVITY_ReikiSeed = 2325,								-- 运营活动-灵气之种
	RAND_ACTIVITY_TYPE_OA_WANXIAN_TIANYIN = 2328, 				-- 运营活动 - 万象天引
	RAND_ACTIVITY_TYPE_OA_JIANG_SHAN_RU_HUA = 2326,				-- 运营活动 - 山海绘卷
	RAND_ACTIVITY_TYPE_OA_MoRan_XuanYuan = 2327,				-- 运营活动-墨染轩辕
	RAND_ACTIVITY_TYPE_Twin_Direct_Purchase = 2331,				-- 运营活动-双生直购
	RAND_ACTIVITY_TYPE_GUANRI_CHANGHONG = 2330,					-- 运营活动 - 贯日长虹
	RAND_ACTIVITY_TYPE_OA_SYSTEM_RANK_WUHUN = 2332,				-- 运营活动 - 武魂冲榜
	RAND_ACTIVITY_TYPE_OA_SYSTEM_RANK_YUSHOU = 2333,			-- 运营活动 - 驭兽冲榜
	RAND_ACTIVITY_TYPE_BF_CAP_RANK = 2334,		                -- 本服提战榜
	RAND_ACTIVITY_TYPE_OA_BLIND_BOX_CARD = 2335,				-- 运营活动 - 盲盒卡包
	RAND_ACTIVITY_TYPE_OA_DREAM_SECRET = 2337,					-- 运营活动 - 幻梦秘境
	RAND_ACTIVITY_TYPE_OA_TRIPLE_RECHARGE = 2342,				-- 运营活动 - 三倍充值
	RAND_ACTIVITY_TYPE_OA_HAOLI_WANZAHNG = 2338,				-- 运营活动 - 豪礼万丈
	RAND_ACTIVITY_TYPE_OA_TOTAL_RECHARGE_GIFT = 2339,			-- 运营活动 - 累充豪礼
	RAND_ACTIVITY_TYPE_OA_HAOLI_WANZAHNG3 = 2340,				-- 运营活动 - 豪礼万丈3(绝版套装ver3.0)
	RAND_ACTIVITY_TYPE_RECHARGE_RANK = 2353,					-- 运营活动 - 充值榜
	RAND_ACTIVITY_TYPE_CONSUME_RANK = 2354,						-- 运营活动 - 消费榜
	RAND_ACTIVITY_TYPE_OA_DRAGON_TRIAL = 2356,					-- 运营活动 - 五气朝元（原龙神试炼）
	RAND_ACTIVITY_TYPE_OA_COLLECT_CARD = 2357,					-- 运营活动 - 开服集卡

	NEW_FESTIVAL_ACT_TEHUI_SHOP = 2344,                         -- 新节日活动-特惠商店
	RAND_ACTIVITY_TYPE_NEW_FESACT_DENGLU = 2345,				-- 新节日活动 - 登陆奖励
	NEW_JRHD_JRSD	= 2346,										-- 新节日活动-节日盛典
	NEW_FESTIVAL_ACT_PRAYER = 2347,		                        -- 新节日活动 - 节日祈愿
	NEW_FESTIVAL_ACT_CONSUME_REBATE = 2348,                     -- 新节日活动-消费返利
	NEW_JRHD_JFSC   = 2349,		        						-- 新节日活动-节日收集
	NEW_JRHD_JFHD   = 2350,		        						-- 新节日活动-集福活动
	NEW_JRHD_HDLC	= 2351,										-- 新节日活动-活动累充
	NEW_JRHD_JFDL   = 2352,		        						-- 新节日活动-节日掉落
	NEW_JRHD_JRPH	= 5009,										-- 新节日活动-节日达人排行
	NEW_JRHD_NA_COLLECT_CARD = 2359,							-- 新节日活动 - 集卡

	NEW_JRHD_NA_DAILY_RECHARGE = 2358,							-- 新节日活动 - 每日充值

	RAND_ACTIVITY_TYPE_HIDEWELFARE				= 10021,		-- 隐藏福利
	GOD_TIANSHENROAD							= 20003,		-- 天神之路
	GOD_QUANMIN_BEIZHAN							= 20005,		-- 全民备战
	OPEN_SERVER_ASSIST							= 20008,		-- 开服助力

	KF_HONORHALLS    = 3073, 							-- 永夜幻都
	KF_ONEVONE       = 3074, 							-- 跨服1V1
	KF_PVP           = 3075, 							-- 跨服3V3
	KF_SHUIJING      = 3076, 							-- 跨服水晶裂痕
	KF_XIANMENGZHAN  = 3076, 							-- 跨服仙盟战
	KF_BOSS          = 3079,							-- 跨服BOSS

	TIANSHEN_3V3     = 3081,							-- 天神3v3
	WORLDS_NO1 		 = 3082, 							-- 天下第一
	KF_GUILDBATTLE   = 3083,					 		-- 跨服帮派战
	KF_HOTSPRING     = 3084,							-- 跨服温泉
	KF_DUCK_RACE     = 3085,							-- 跨服小鸭疾走
	kf_FISHING       = 3086, 							-- 跨服钓鱼
	KF_LUANDOUBATTLE = 3088, 							-- 跨服乱斗战场
	KF_SHENGYU_BOSS  = 3089, 							-- 跨服圣域boss

	KF_SHENYIN       = 3090,							-- 跨服圣印副本
	KF_LIEKUN        = 3091, 							-- 跨服猎鲲地带
	KF_MJBOSS        = 3092,							-- 跨服秘境boss
	KF_BOSS_FIGHT 	 = 3094,							-- BOSS乱斗
	KF_ZHUXIE		 = 3097,							-- 跨服诛邪战场
	KF_ANSWER        = 3099,							-- 跨服答题

	KF_COUNTRY_SECRET_AREA = 3109,                      -- 星图秘境
	KF_LONGMAI		 = 3110,							-- 跨服龙脉
	KF_YANGLONGSI	 = 3111,							-- 跨服养龙
	KF_FLAG_GRABBING_BATTLEFIELD = 3112,                -- 跨服夺旗战场
	CROSS_ACTIVITY_TYPE_DIVINE_DOMAIN = 3114,			-- 圣天神域
	CROSS_ACTIVITY_TYPE_ULTIMATE_BATTLE = 3115,			-- 终极战场（1v31）
	CROSS_ACTIVITY_TYPE_REAL_CHARGE_BOSS = 3116,        -- 臻充boss
	CROSS_ACTIVITY_TYPE_LOVERPK = 3117,                 -- 仙侣PK
	CROSS_ACTIVITY_TYPE_LOVERPK_SEND_FLOWERS = 3118,    -- 仙侣PK送花
	CROSS_ACTIVITY_TYPE_LAND_WAR = 3119,                -- 阵地战
	CROSS_ACTIVITY_TYPE_AIR_WAR = 3120,					-- 跨服空战
	CROSS_RED_PACKET_RAIN = 3122,						-- 跨服红包天降
	CROSS_ACTIVITY_TYPE_BOSS_INVASION = 3123,           -- 跨服boss入侵
	CROSS_ACTIVITY_TYPE_BOSS_BEAST = 3124,           	-- 跨服采集狂暴幻兽

	CROSS_ACTIVITY_RECHARGE              = 4000,				-- 跨服充值排行
	CROSS_ACTIVITY_MARRIED               = 4001,				-- 跨服结婚
	CROSS_ACTIVITY_CONSUME               = 4002,				-- 跨服消费排行
	CROSS_RAND_ACTIVITY_TYPE_FLOWER_RANK = 4003,				-- 跨服鲜花榜
	CROSS_PERSON_RANK_TYPE_SMASHED_EGG   = 4004,				-- 跨服欢乐砸蛋
	CROSS_YUNGOU                         = 4005,				-- 跨服云购
	CROSS_MIAOSHA 						 = 4006, 				-- 跨服秒杀

	CROSS_CHANNEL_ACTIVITY_TYPE_GOLD_ZHUANPAN = 5000,			-- 跨服仙玉转盘
	CROSS_ACTIVITY_LuckyGiftBag               = 5001,           -- 幸运大礼包
	CROSS_ACTIVITY_LUCKYGIFTBAGLOCAL          = 2329,           -- 幸运大礼包(本服)
	CROSS_CHANNEL_ACTIVITY_TYPE_CHARM_RANK	  = 5002,			-- 新-跨服鲜花榜
	CROSS_CHANNEL_ACTIVITY_TYPE_CHONGZHI_RANK  = 5003,			-- 跨服充值榜
	CROSS_CHANNEL_ACTIVITY_TYPE_CONSUME_RANK  = 5004,			-- 跨服消费榜
	CROSS_CHANNEL_ACTIVITY_TYPE_KF_CAP_RANK = 5005,		        -- 跨服提战榜
	CROSS_CHANNEL_ACTIVITY_TYPE_MARRY_RANK 	  = 5006,		    -- 跨服仙侣充值榜
	CROSS_CHANNEL_ACTIVITY_TYPE_SWORN_RECHARGE_RANK = 5007,		-- 跨服结义充值榜
	CROSS_LINGYUPOOL_ACTIVE = 5008,								-- 超级锦鲤活动

	--手动添加活动
	HUN_YAN             = 10001, 								-- 婚宴
	SUOYAOTA            = 10002,								-- 锁妖塔
	YAOSHOUPLAZA        = 10003,								-- 妖兽广场
	GUILD_SHILIAN       = 10004,								-- 仙盟试炼
	GUILD_BONFIRE       = 10005,								-- 仙盟篝火
	PROTECT_EQ          = 10006,								-- 传承装备
	SEVENDAY_INVESTPALN = 10007,								-- 开服活动七天投资计划
	GOLD_REVERSION      = 10008,								-- 百倍返利
	CANGBAOGE           = 10009,								-- 藏宝阁
	GOLDMEMBER          = 10010,								-- 黄金会员
	INVESTPLAN          = 10011,								-- 投资计划
	IMMORTAL            = 10012,								-- 仙尊卡
	MOLONG              = 10013,								-- 魔龙
	MAZE                = 10014,								-- 奇珍宝殿
	FISH                = 10015,								-- 捕鱼
	TIANSHUXUNZHU       = 10016, 								-- 天书寻主
	HUANLEZADANRANK     = 10017, 								-- 跨服砸蛋榜
	LUCKYBUY            = 10018, 								-- 开服幸运云购
	FASHIONACCESSORY    = 10019,								-- 时装炫卖
	LingYuanHaoLi       = 10020, 								-- 0元豪礼
	FIRST_CHONGZHI      = 20000,								-- 首充
	MEIRI_LEICHONG      = 20001,								-- 每日累充
	XIUZHEN_ROAD	 	= 20002,								-- 修真之路
	MARRY_PAIQI	 		= 20004,								-- 婚宴排期
	ZEROBUY 			= 20006,								-- 零元购
	YIBWNWANLI 			= 20007,								-- 一本万利
	OPERATIONACTIVITY 	= 20018,								-- 动态运营活动（第一个按钮）
	CLIENT_MOWUJINGLIN 	= 20009,								-- 动态运营活动(魔物降临只是为了客户端生成按钮用)
	OPERATIONACTIVITY_TWO = 20010,								-- 动态运营活动第二个按钮
	OPERATIONACTIVITY_JUANXINA_BUFF = 20011,					-- 动态运营活捐献buff
    OPESERVER_TIMING_GIFT_BTN 	= 20012,						-- 开服活动定时福利主界面按钮
    HEFU_ACTIVITY		= 20015,                                -- 合服活动 - 用于图标显示
    REBATE_ACTIVITY		= 20016,                                -- 返利活动 - 用于图标显示
	SIXIANG_YUGAO		= 20020,								-- 四象预告
	FESTIVA_ACTIVITY	= 20021,								-- 节日活动
	ACTIVITY_XIANQI_JIEFENG = 20022,							-- 仙器觉醒(仙器解封一样的)
	RAND_ACT_XIANQIJIEFENG_XINGTIANLAIXI_CHILD = 20030,			-- 仙器觉醒--刑天来袭子活动
	ACT_PIERRE_DIRECT_PURCHASE = 20031,							-- 臻品直购
    SHENBINGAWAKEN_ACTIVITY = 20033,							-- 神兵觉醒自定义活动号
    TREASURE_HUNT_BOSS_ACTIVITY = 20034,						-- 寻宝boss假活动
    YIYUANHAOLI_ACTIVITY = 20035,								-- 一元豪礼自定义活动
    MONTH_CARD_PROVOLEGE = 20039,								-- 月卡特权
    SCERET_ACTIVITY_SHOW = 20050,                               -- 星图秘境按钮显示自定义活动号
	GOD_OF_WEALTH		= 20052,                                -- 喜迎财神 - 用于图标显示
	ZHI_ZUN_VIEW        = 20053,                                -- 至尊特权 -- 用于图标显示
	SEA_PRIVILEGE_SHOP  = 20054,                                -- 至尊商店 -- 用于图标显示
	BOSS_MABI_FB        = 20055,                                -- boss麻痹副本
	GOLD_STONE_VIEW  	= 20056,                                -- 金石之言 -- 用于图标显示
	PRIVILEGED_GUIDANCE_VIEW 	= 20057,                        -- 特权引导 -- 用于图标显示
	SHEN_MI_HAI_YU_ICON 	= 20058,                            -- 神秘海域
	TREASUREPALACE 	= 20059,                            		-- 臻宝殿
	CAP_UP_GIFT = 20060,                                		-- 战力飙升礼包
	LIMIT_TIME_GIFT_PURCHASE 	= 20061,                        -- 神秘直购礼包
	KF_ATTRIBUTE_STONE_RANK = 20062,							-- 跨服属性宝石榜
	DISCOUNT_PURCHASE = 20063,									-- 一折弹窗礼包
	-- CANGJINSHOP = 20064,                                        -- 藏金商铺
	YanYuGe = 20064,                                            -- 烟雨阁
	MOST_VENERABLE = 20065,										-- 最强仙尊
	CAPABILITY_WELFARE = 20066,									-- 战力福利
	OPEN_SERVER_INVEST = 20067,									-- 开服投资
	LORD_EVERY_DAY_SHOP = 20073,                                -- 魔王仙藏 -- 图标显示
	WORLD_TREASURE = 20082,										-- 天财地宝(假活动)
	WORLD_TREASURE_JIANGLIN = 20083,							-- 天财地宝 试炼副本(假活动) 策划要的额外入口
	NEW_FESTIVA_ACTIVITY = 20084,                               -- 新节日活动
	LIFE_INDULGENCE_ACTIVITY = 20085,                           -- 终身特惠(假活动)
	HUNDRED_EQUIP = 20086,										-- 百倍爆装(假活动)
	EXP_GUIDE = 20087,											-- 升级引导(假活动)

	SKILL_BREAK_PURCHASE = 2336,								-- 技能突破直购
	BOUNDLESSJOY_VIEW 	= 2320,                                -- 长乐未央 -- 用于图标显示
	--活动集合按钮
	ACTIVITY_FOLD_ONE = 30001,                                 	-- 活动折叠自定义活动号--1
	ACTIVITY_FOLD_TWO = 30002,                                 	-- 活动折叠自定义活动号--2
	ACTIVITY_FOLD_THREE = 30003,                                -- 活动折叠自定义活动号--3
	ACTIVITY_FOLD_FOUR = 30004,                                 -- 活动折叠自定义活动号--4

	-- 收起随机活动按钮
	RAND_ACTIVITY_HIDE_BTN = 50009,
	SHOW_TASK_CHAIN		= 99998,								-- 只是用来显示的任务链按钮

	BossAssist = -99, 											-- boss协助
}

SENCE_OBJ_LOADED_STATUS = {
	WAIT = 0,
	LOADED = 1,
	FAKE_REMOVE = 2,
}

SENCE_OBJ_WAIT_ANIM_SYNC_TYPE = {
	ROLE = 0,			-- 角色
	TIAN_SHEN = 1,		-- 天神
	GUNDAM = 2,			-- 高达
}

ACTOR_PREFAB_DATA_TYPE = {
	NONE = 0,
	ROLE = 1,
	TIAN_SHEN = 2,
	MONSTER = 3,
	ZUO_QI = 4,
	GUNDAM = 5,
	NUQI_BIANSHEN = 6,
}


-- 人物属性类型
ROLE_SPECIAL_ATTR_TYPE= {
	BASE_ATTR = 1,			-- 基础属性
	SPECIAL_ATTR = 2,		-- 特殊属性
}

FU_BEN_TYPE = {
	FB_XIANNV = 1,									--仙女本
	FB_COIN = 2,									--铜币本
	FB_WING = 3,									--羽翼本
	FB_XIULIAN = 4,									--修练本
	FB_QIBING = 5,									--骑兵本
}

CSOGA_ZERO_GIFT_REQ_TYPE = {
	OGA_ZERO_GIFT_REQ_TYPE_INFO = 0,
	OGA_ZERO_GIFT_REQ_TYPE_BUY = 1,
	OGA_ZERO_GIFT_REQ_TYPE_FETCH = 2
}

-- 活动状态
ACTIVITY_STATUS = {
	CLOSE = 0,										-- 活动关闭状态
	STANDY = 1,										-- 活动准备状态
	OPEN = 2,										-- 活动进行中
	XUNYOU = 3, 									-- 巡游
}

ACTIVITY_ROOM_STATUS = {
	CLOSE = 0,										-- 活动房间关闭
	OPEN = 1,										-- 活动房间开启
}

CHAT_TYPE = {
	CHANNEL = 1,									-- 频道聊天
	PRIVATE = 2,									-- 私聊
	GUILD = 3,										-- 帮会聊天
}

CHAT_CONTENT_TYPE = {
	TEXT = 0,										-- 文本
	AUDIO = 1,										-- 语音
	FEES_AUDIO = 2,									-- 收费语音
	NOT_FILTER_TEXT = 10, 								-- 特殊需求，不做敏感词过滤
}

--聊天内容样式类型.
CHAT_CONTENT_SHOW_TYPE = {
	CHAT = 0,		--普通聊天框.
	RED_PACKET = 1,	--仙盟红包.
	BILLION_DEZG = 2,	--百亿补贴-大额直购分享助力.
	BILLION_DRPT = 3,	--百亿补贴-多人拼团邀请.
}

AUTHORITY_TYPE = {
	INVALID = 0,						-- 无任何权限
	GUIDER = 1,						-- 新手指导员
	GM = 2,							-- GM
	TEST = 3,						-- 测试帐号（内部号）
}

SYS_MSG_TYPE = {
	--当前使用的消息类型
	SYS_MSG_CHANNEL_ROLL_LAMP = 20,                 -- 跑马灯
	SYS_MSG_CHANNEL_PUBLIC_NOTICE = 21,            	-- 公告
	SYS_MSG_CHANNEL_GUILD = 22,                     -- 仙盟频道
	SYS_MSG_CHANNEL_WORLD = 23,                     -- 世界频道
	SYS_MSG_CHANNEL_SYSTEM_MSG = 24,                -- 传闻频道
	SYS_MSG_CHANNEL_TEAM = 25,					    -- 队伍频道
	SYS_MSG_CHANNEL_CROSS = 26,						-- 跨服频道
	SYS_MSG_CHANNEL_ZHANDUI_3V3 = 27,               -- 3V3战队频道
	SYS_MSG_CHANNEL_SYSTEM = 28,                	-- 系统频道
	SYS_MSG_CHANNEL_SPECIAL_SCENE_ROLL_AND_SYSTEM_MSG = 29,		-- 战场专用滚动 + 传闻频道(不拦截)
	SYS_MSG_CHANNEL_NO_LIMIT_SYSTEM_MSG = 30,		-- 传闻频道(不拦截)
	SYS_MSG_CHANNEL_SCENE = 31,						-- 附近频道

	--旧的消息类型，请勿使用，留下来兼容之前的代码
	SYS_MSG_PUBLIC_NOTICE = 0,						--系统公告，滚屏播放
	SYS_MSG_PERSONAL = 1,	 						--个人系统消息
	SYS_MSG_CENTER = 2,	 							--屏幕中央信息，如击杀、任命等
	SYS_MSG_ROLL = 3,	 							--滚动公告，用于播放重要的游戏内信息
	SYS_MSG_ONLY_CHAT_WORLD = 4,					--只添加到聊天世界频道
	SYS_MSG_ONLY_CHAT_GUILD = 5,	 				--只添加到聊天仙盟频道
	SYS_MSG_CENTER_AND_ROLL = 6,					--中央显示和滚动同时显示
	SYS_MSG_SPECIAL_SCENE_CENTER_AND_ROLL = 7,		--战场专用滚动和聊天框传闻
	SYS_MSG_HOTSPRING = 8,				            --温泉传闻
	SYS_MSG_ONLY_CHAT_TEAM = 9,                     --只添加到聊天队伍频道
	SYS_MSG_CENTER_PERSONAL_NOTICE = 10,			--屏幕中央, 个人信息弹出播放
	SYS_MSG_ROLE_ADD_GUILD = 11,					--角色加入帮派
	SYS_MSG_SEND_TO_SCENE = 12, 					--场景内传闻
	SYS_MSG_CROSS_TO_SCENE = 13, 					--六界争霸跨服提示
	SYS_MSG_SEND_TO_SCENE_PIAOZI_AND_CHUANWEN = 14, --场景内 战场专用滚动 + 传闻
	SYS_MSG_ROLL_AND_WORLD = 15,					-- 跑马灯和世界频道显示
	SYS_MSG_SPEAK_ROLL = 16,						-- 系统喊话喇叭
	SYS_MSG_CROSS_AND_GUILD = 17,					-- 跨服、仙盟、千里传音
	SYS_MSG_CHAT_WORLD_AND_ROLL_REAL = 18			-- 世界频道+跑马灯 (圣兽特殊处理)
}


--聊天格子消息种类
CHAT_MSG_RESSON = {
	NORMAL = 0,		 	--普通聊天格子
	TEAM_TIPS = 1,		--队伍提示
	GUILD_TIPS = 2,	 	--帮派提示
	HUDONG_PYP = 3,	 	--互动--拍一拍
	LYL_SAORAO = 4,		--情缘撩一撩信息骚扰
}
CHANNEL_TYPE = {
	WORLD = 0,										-- 世界
	CAMP = 1,										-- 阵营
	SCENE = 2,										-- 场景
	TEAM = 3,										-- 队伍
	GUILD = 4,										-- 公会
	ZUDUI = 5, 										-- 组队(策划需求)
	CROSS = 6,										-- 跨服
	ZHANDUI3V3 = 7, 								-- 战队聊天

	SPEAKER = 8,									-- 喇叭
	CHUAN_WEN = 10, 								-- 传闻
	EMAIL = 77,										-- 邮件(凑数可改)
	PRIVATE = 12,									-- 私聊
	SYSTEM = 16,									-- 系统
	MAINUI = 99,									-- 主界面
	ALL = 100,										-- 全部
}

--互动类型
HUDONG_TYPE = {
	PYP = 1,  		--拍一拍
}

--拍一拍消息引导类型
PYP_MSG_GUIDE_TYPE = {
	None = 0, 	--不显示引导
	First = 1, 	--显示首次引导
	PingBi = 2, 	--显示屏蔽引导
}

--@消息操作类型
AITE_MSG_TYPE = {
	Send = 0, 		--发送
	Read = 1, 		--已读
	ReqInfo = 2, 	--请求@列表信息
	ClearInfo = 3, 	--清除@列表信息
}

AITE_STRING_CONST = {
	Format = "%s###aite_start|%s|aite_end###",
	Start = "###aite_start|",
	End = "|aite_end###",
	Color = "{wordcolor;006A25;%s}",		--聊天框
	FindColor = "{wordcolor;006A25;",		--聊天框颜色查找替换
	MainUiColor = "{wordcolor;6FBB6F;", 	--主界面底部聊天框
}

--可以展示频道标签
CanShowChannel =
{
	[CHANNEL_TYPE.ALL] = true,
	[CHANNEL_TYPE.GUILD] = true,
	[CHANNEL_TYPE.WORLD] = true,
	[CHANNEL_TYPE.SCENE] = true,
	[CHANNEL_TYPE.SYSTEM] = true,
	[CHANNEL_TYPE.TEAM] = true,
	[CHANNEL_TYPE.CAMP] = true,
	[CHANNEL_TYPE.SPEAKER] = true,
	[CHANNEL_TYPE.CROSS] = true,
	[CHANNEL_TYPE.PRIVATE] = true,
	[CHANNEL_TYPE.CHUAN_WEN] = true,
	[CHANNEL_TYPE.ZUDUI] = true,
	[CHANNEL_TYPE.ZHANDUI3V3] = true,
}

--可手动发消息的频道
CanSendMsgChannel =
{
	[CHANNEL_TYPE.ALL] = true,
	[CHANNEL_TYPE.GUILD] = true,
	[CHANNEL_TYPE.WORLD] = true,
	[CHANNEL_TYPE.SYSTEM] = false,
	[CHANNEL_TYPE.TEAM] = true,
	[CHANNEL_TYPE.CAMP] = true,
	[CHANNEL_TYPE.SPEAKER] = true,
	[CHANNEL_TYPE.CROSS] = true,
	-- [CHANNEL_TYPE.PRIVATE] = true,
	[CHANNEL_TYPE.CHUAN_WEN] = false,
	[CHANNEL_TYPE.ZUDUI] = false,				-- 组队(策划需求)
}

ITEM_CHANGE_TYPE = {
	ITEM_CHANGE_TYPE_SNEAKY_IN = -4,				-- 偷偷的放入 不需要通知玩家获得 当脱下装备和宝石镶嵌惩罚时使用这个
	ITEM_CHANGE_TYPE_CHANGE = -3,	 				-- 发生改变
	ITEM_CHANGE_TYPE_OUT = -2,	 					-- 从背包进入外部
	ITEM_CHANGE_TYPE_IN = -1,	 					-- 从外部进入背包
	-- 0以上表示是从背包/仓库的其他格子里移动过来/去 值表示原来的下标
}

RED_PAPER_TYPE = {					--红包类型
	RED_PAPER_TYPE_INVALID = 0,
	RED_PAPER_TYPE_COMMON = 1, 		--普通
	RED_PAPER_TYPE_RAND = 2,		--拼手气
	RED_PAPER_TYPE_GLOBAL = 3,		--全服
	RED_PAPER_TYPE_WORLDBOSS = 4,	--世界boss
}

PUT_REASON_TYPE = {
	PUT_REASON_INVALID = 0,							-- 无效
	PUT_REASON_NO_NOTICE = 1,						-- 不通知
	PUT_REASON_GM = 2,								-- GM命令
	PUT_REASON_PICK = 3,							-- 捡取掉落
	PUT_REASON_GIFT = 4,							-- 礼包打开
	PUT_REASON_COMPOSE = 5,							-- 合成产生
	PUT_REASON_TASK_REWARD = 6,						-- 任务奖励
	PUT_REASON_MAIL_RECEIVE = 7,					-- 邮件
	PUT_REASON_CHEST_SHOP = 8,						-- 宝箱
	PUT_REASON_RANDOM_CAMP = 9,						-- 听天由命礼包
	PUT_REASON_SHOP_BUY = 10,						-- 商城购买
	PUT_REASON_WELFARE = 11,						-- 福利
	PUT_REASON_ACTIVE_DEGREE = 12,					-- 活跃度
	PUT_REASON_CONVERT_SHOP = 13,					-- 兑换商店
	PUT_REASON_ZHUXIE_ACTIVITY_REWARD = 14,			-- 诛邪战场奖励
	PUT_REASON_FB_TOWERDEFEND_TEAM = 15,			-- 多人塔防副本
	PUT_REASON_SEVEN_DAY_LOGIN_REWARD = 16,			-- 七天登录活动奖励
	PUT_REASON_YAOJIANG = 17,						-- 摇奖
	PUT_REASON_ACTIVITY_FIND = 18,					-- 活动找回
	PUT_REASON_NEQ_STAR_REWARD = 19,				-- 新装备本星星奖励
	PUT_REASON_NEQ_AUTO = 20,						-- 新装备本扫荡
	PUT_REASON_NEQ_ROLL = 21,						-- 新装备本翻牌
	PUT_REASON_MAZE = 22,							-- 迷宫寻宝
	PUT_REASON_EXP_FB = 23,							-- 经验副本
	PUT_REASON_CHALLENGE_FB = 24,					-- 挑战副本
	PUT_REASON_VIP_LEVEL_REWARD = 25,				-- VIP等级奖励
	PUT_REASON_QIFU_TIMES_REWARD = 26,				-- 祈福次数奖励
	PUT_REASON_GUILD_TASK_REWARD = 27,				-- 仙盟任务奖励
	PUT_REASON_CHONGZHI_ACTIVITY = 28,				-- 充值活动
	PUT_REASON_OPENGAME_ACTIVITY = 29,				-- 开服活动
	PUT_REASON_DISCOUNT_BUY = 30,					-- 一折抢购
	PUT_REASPN_PHASE_AUTO = 31,						-- 阶段本扫荡奖励

	PUT_REASON_LUCKYROLL = 39,						-- 幸运转盘
	PUT_REASON_LUCKYROLL_EXTRAL = 40,			 	-- 幸运转盘额外奖励
	PUT_REASON_LUCKYROLL_CS = 79,			 		-- 合服活动幸运转盘
	PUT_REASON_ZHUXIE_GATHER = 96,					-- 诛邪采集获得
	PUT_REASON_EXP_BOTTLE = 97,						-- 凝聚经验
	PUT_REASON_GCZ_DAILY_REWARD = 98,				-- 攻城战每日奖励
	--PUT_REASON_LIFE_SKILL_MAKE= 99,					-- 生活技能制造
	PUT_REASON_PAOHUAN_ROLL = 100,					-- 跑环任务翻牌

	PUT_REASON_CAS_CLOUD_BUY = 119,					-- 合服活动云购
	PUT_REASON_RA_CLOUD_BUY = 120,                --随机活动云购
	PUT_REASON_COOL_BUY = 121,                		--炫装特卖
	PUT_RANDACT_DAILYONLINELOTTERY = 131,			-- 随机活动每日在线抽奖

	PUT_REASON_ONLINE_REWARD = 539,					-- 在线奖励
	PUT_REASON_RA_LUCKY_DRAW_REWARD = 556,			--随机活动幸运抽奖
    PUT_REASON_TOUCH_GOLD_REWARD = 565,				--摸金
    PUT_REASON_TU_NV_LANG = 637,					--运营活动_兔女郎宝藏  伏羲演卦
    PUT_REASON_FISH = 640,					        --运营活动_幸运锦鲤
	PUT_REASON_RA_ACTIVE_ROLL_REWARD  = 673,		-- 活跃转盘
	PUT_REASON_RA_TIANSHEN_XUNBAO = 688,			-- 天神夺宝
    PUT_REASON_RA_TIANXIANZHEN_NO_NOTICE = 1092,				-- 灵犀六芒图
    PUT_REASON_CSA_YANHUA_SHENGDIAN_REWARD = 783,		--合服活动烟花盛典扭蛋机
    PUT_REASON_RA_YANHUA_SHENGDIAN_2_REWARD = 796,		-- 节日活动烟花盛典扭蛋机
	PUT_REASON_CROSS_ACT_GOLD_ZHUANGPAN = 800,			-- 仙玉转盘
	PUT_REASON_ChECK_TIP = 1000,					-- 客户端登陆升级等检测类型
	PUT_REASON_ZHOU_YI_YUN_CHENG = 1006,				-- 周一运程
	PUT_REASON_TIAN_SHEN_BAO_XIA_REWARD = 1054,			-- 天神宝匣抽奖
	PUT_REASON_FESTIVAL_TURN_TABLE = 1084,			--节日转盘
	PUT_REASON_YINJI_TURN_TABLE = 1096,				--印记转盘
	PUT_REASON_Glory_Crystal_TABLE = 1144,			--荣耀水晶
	PUT_REASON_SPECIAL_DRAW = 1162,					--运营活动 - 特殊抽奖奖励
	PUT_REASON_OGADRAWREWAD = 1385,					--开服活动 - 抽奖奖励
}

CROSS_LIEKUNFB_REQ_TYPE = {
	LIEKUNFB_TYPE_GET_PLAYER_INFO = 0,				--获取玩家信息 param1:0 param2:帮派id
	LIEKUNFB_TYPE_GOTO = 1,							-- 场景跳转 param1:scene_id param2:scene_key
	LIEKUNFB_TYPE_MAX = 2,
}

--固定的错误码，直接在收到错误码时处理，简单粗暴
FIX_ERROR_CODE =
{
	EN_GET_ACCOUNT_GOLD_TOO_FAST = 100000,			-- 从账号提取元宝间隔时间不足
	EN_COIN_NOT_ENOUGH = 100001,					-- 金币不足
	EN_GOLD_NOT_ENOUGH = 100002,					-- 您仙玉不足，请前往充值！
	EN_BIND_GOLD_NOT_ENOUGH = 100003,				-- 绑定仙玉不足
	EN_MONEY_IS_LOCK = 100004,						-- 金钱已经锁定
	EN_ROLE_ZHENQI_NOT_ENOUGH = 100005,				-- 仙魂不足
	EN_XIANNV_EXP_DAN_LIMIT = 100006,				-- 仙女经验丹不足
	EN_CONVERTSHOP_BATTLE_FIELD_HONOR_LESS = 100007,-- 声望不足
	EN_ROLE_NV_WA_SHI_NOT_ENOUGH = 100008,			-- 女蜗石不足
	EN_ROLE_HUNLI_NOT_ENOUGH = 100009,				-- 魂力不足
	EN_SHENGWANG_SHENGWANG_NOT_ENOUGH = 100010,		-- 声望不足
	EN_RUNE_BAG_HAS_NO_SPACING = 100011,			-- 铭纹背包空间不足
	EN_SILVER_TICKET_NOT_ENOUGH = 100012,			-- 元宝不足
	EN_ZHENQI_NOT_ENOUGH = 100013,					-- 真气不足
}

--传闻链接类型
CHAT_LINK_TYPE = {
	GUILD_APPLY = 0,								-- 申请加入 仙盟申请
	WO_BAISHI = 1,                                  -- 非服务端下发，客户端写在language中
	CAN_JIA_HUN_YAN = 41,							-- 我要参加 参加婚宴
	WO_QIUHUN = 42,									-- 我要求婚
	WO_CHONGZHI = 46,								-- 我要充值
	DAY_DANBI = 48,									-- 查看活动 单笔充值
	TO_ASSIST = 68, 								-- 前往协助
	SKY_REDENVELOPES = 84,							-- 我要红包 天降红包
	RED_PACKET = 119,  								--搶紅包s
	ACTDISCOUNT = 121,								--一折抢购
	SUIJI_GONGGAO = 123,							--随机公告
	GUILD_FB_END = 142, 							-- 仙盟守护 查看详情
	ZERO_BUY = 145,									--零元购
    GOTOVIP6 = 156,                                  --天神觉醒购买VIP6后激活天神，跳转到VIP6
    TaskChain = 167,								-- 任务链
	IWANT = 503,  									-- 七天累充 我也要
	RECHARGE_TQTZ_1 = 508,							-- 特权投资第一档
	RECHARGE_TQTZ_2 = 509,							-- 特权投资第二档
	RECHARGE_TQTZ_3 = 510,							-- 特权投资第三档
	OPENSERVER_COMPETITION_RUSHTYPE = 515, 		    --开服冲榜
    CHAT_LINK_TYPE_524 = 524,						--鸿蒙神域拦截
	CHAT_LINK_TYPE_563 = 563,						--衣橱 我要激活
    FORTUNE_CAT_JUMP = 963,						    --招财猫跳转
    TIANSHENJIANLIN = 966,							--天神降临
    XINGTIANLAIXI = 968,							--刑天来袭
    FAKE_CLIENT_LINK_TYPE = 10001,                  --客户端自定义的假的，用于跳转
    BAOXIANG_CODE = 1006,							--每日宝箱 交换密码
    GATHER_FENG_LING = 1010,							--凤灵灵晶采集，立即前往
    ST_XIULI_LINK = 1300,							--师徒修炼
    BOSS_XIEZHU_LINK = 1213,						-- 新 Boss协助
    QIHUN_BUY_LINK = 1215,							-- 器魂直购
    GUILD_HEBING_MAIL_LINK = 1226, 					-- 仙盟合并的邮件跳转
    MERGE_FIREWORK_JULING = 1227, 					-- 合服活动-炼丹邀请.
    BILLION_DEZG = 1229, 							--百亿补贴-大额直购分享助力.
    BILLION_DRPT = 1230, 							--百亿补贴-多人拼团邀请.
	CROSSTREASURE_BEAST = 1231,						--跨服捕捉幻兽
	XUNYUAN_LINK = 1234,							--寻缘
}

ATTACH_SKILL_TYPE = {
	ADD_BUFF_MABI = 1,								-- 附带麻痹buff
	ADD_ACTION_JITUI = 2,							-- 击退效果
	ADD_ACTION_BOSS_ADD = 3,						-- BOSS伤害增加
	ADD_ACTION_NORMAL_MONSTER_ADD = 4,				-- 小怪伤害增加
	CHECK_TARGET_NUM_DECREASE_CD = 5,				-- 击中目标减少CD
	POSI_SET = 6,									-- 自己位移
	REDUCE_HP_PERCENT = 7,							-- 减少血量百分比
	CHECK_EFFECT_RANDOM = 8,						-- 检测BUFF，随机，增伤
	CHECK_EFFECT_RANGE_ADD_HP = 9,					-- 检测BUFF,周围群体增加HP`
	BOSS_INJURE_VAMPIRE = 10,						-- BOSS伤害吸血
	CHECK_EFFECT_LAYER = 11,						-- 检测buff，层级，增伤
	ADD_EFFECT = 12,								-- 增加buff
	RESET_CD_BY_EFFECT_LAYER = 13,					-- 重置CD,BUFF,层数
	CHECK_HP_ADD_INJURE = 14,						-- 检测血量，增加伤害
	SHARE_INJURE = 15,								-- 平坦伤害
	TARGET_HP_MUST_CRIT = 16,						-- 目标血量，必定暴击
	RANGE_ADD_BUFF = 17,							-- 周围群体增加BUFF
	POSI_PULL = 19,									-- 拉人
	CHECK_HP_ADD_EFFECT = 20,						-- 检测血量，增加BUFF
	CALL_MONSTER = 21,								-- 召唤
	CLEAR_CONTROL = 22,								-- 解除控制
	TARGET_LESS_INJURE_ADD = 23,					-- 检测人数，伤害增加
	CHECK_BUFF_INJURE_ADD = 24,						-- 检测BUFF，伤害增加
	TARGET_BUFF = 25,								-- 目标增加BUFF
	TARGET_CHECK_BUFF_LAYER_INJURE_ADD = 26,		-- 检测目标BUFF层数，伤害增加
	ADD_RECOVER_HP = 27,							-- 增加随机回血BUFF
	SELF_CHECK_BUFF_LAYER_RECOVE_HP = 28,			-- 检测自身BUFF层数，回血
	SELF_REMOVE_EFFECT = 29,						-- 移除自身BUFF
	PERFORM_FAZHEN = 30,							-- 释放法阵
	KILL_NEAR_NORMAL_MONSTER = 31,					-- 秒杀附近小怪
	PERFORM_SKILL = 32,								-- 再次释放技能
	SELF_CHECK_BUFF_REMOVE_BUFF = 33,				-- 检测自身BUFF层数，移除BUFF
	KILL_NEAR_TARGET = 34,							-- 秒杀附近目标
	ADD_SCENE_EFFECT = 35,							-- 增加场景类型
	NOW_HP_PERCENT_INJURE = 36,						-- 当前血量百分比伤害
	ADD_NUQI_BY_ROLE = 37,							-- 怒气增加 
	RANGE_ADD_BUFF_BY_TEAM = 38,					-- 周围队友增加BUFF
	RANDOM_ADD_HURT = 39,							-- 随机 增伤
	RECOVE_HP = 40,									-- 回血
	RANDOM_TARGET_ADD_EFFECT = 41,					-- 随机 目标增加BUFF
	RECOVE_ROLE_LEVEL_HP = 42,						-- 角色等级固定回血
	NOT_BOSS_ADD_EFFECT = 43,						-- 非boss目标增加buff
	CHECK_SELF_BUFF_LAYER_PERFORM = 44,				-- 检测自己BUFF层数才能释放
	ADD_INJURE = 45,								-- 增加伤害
	ADD_TARGET_HP_ERCENT_INJURE = 46,				-- 目标血量百分比伤害
	NEAR_HP_LESS_ADD_EFFECT = 47,					-- 附近血量最少N个人加effect
	REMOVE_TARGET_EFFECT = 48,						-- 移除对方effect
	CLASH = 49,										-- 冲撞
	VAMPIRE = 50,									-- 吸血
	KILL = 51,										-- 击杀
	SET_FIGHT_TYPE = 52,							-- 设置fighttype
	SET_ELEMENT_TYPE = 53,							-- 设置五行
	REMOVE_TIANSHEN_SKILL_ADD_SKILL = 54,			-- 移除并添加技能
	ADD_SKILL_CD = 55,								-- 增加技能CD
	TARGET_EFFECT_LAYER_ADD_INJURE = 56,			-- 目标effect层数增伤
}


FLYING_PROCESS_TYPE = {
	NONE_FLYING = 0,
	FLYING_UP = 1,
	FLYING_IN_MAX_HEIGHT = 2,
	FLYING_DOWN = 3,
}

JUMPING_PROCESS_TYPE = {
	NONE_JUMPING = 0,
	JUMPING_UP = 1,
	JUMPING_DOWN = 2,
}

MOVE_MODE = {
	MOVE_MODE_NORMAL = 0,										--正常
	MOVE_MODE_FLY = 1,											--飞行
	MOVE_MODE_LONGJUMP_UP = 2,									--长跳加速
	MOVE_MODE_LONGJUMP_DOWN = 3,								--长跳减速
	MOVE_MODE_JUMP1 = 4,										--跳跃1
	MOVE_MODE_JUMP2 = 5,										--跳跃2
	MOVE_MODE_SIT = 6, 											--打坐
	MOVE_MODE_SWORD = 7, 										--御剑运动模式
	MOVE_MODE_DUJIE = 8, 										--渡劫

	MOVE_MODE_MAX = 9
}

SPECIAL_APPEARANCE_TYPE = {
	NORMAL = 0,
	XMZ = 3,								-- 仙盟战变身
	BIANSHEN_SYSTEM = 10, 					-- 变身系统变身
	GUNDAM = 11,							-- 高达
	XIUWEIBIANSHEN = 12,					-- 修为变身
	CROSS_HOTSPRING = 104,					-- 雪人
	CROSS_HOTSPRING_SHUANGXIU = 105,		-- 双修
	CROSS_HOTSPRING_COMMON = 106,			-- 温泉普通形象
	STORY_ROBERT = 1000,
}

MOUNT_ANIM_STATUS = {
	NONE = 0,
	UP_MOUNT = 1,
	DOWN_MOUNT = 2,
}

-- 变身类型
BIANSHEN_EFEECT_APPEARANCE =
{
	APPEARANCE_NORMAL = 0,									-- 正常外观
	APPEARANCE_DATI_XIAOTU = 1,								-- 答题变身卡-小兔
	APPEARANCE_DATI_XIAOZHU = 2,							-- 答题变身卡-小猪
	APPEARANCE_MOJIE_GUAIWU = 3,							-- 魔戒技能-怪物形象

	MINGJIANG = 99,											-- 名将变身
}

DISCONNECT_NOTICE_TYPE =
{
	INVALID = 0,
	LOGIN_OTHER_PLACE = 1,									-- 玩家在别处登录
	CLIENT_REQ = 2,											-- 客户端请求
	LUA_INITDB_FAIL = 3,									-- lua角色数据库读取初始化失败
	HEARTBEAT_ERROR = 4,									-- 心跳错误
	-- 客户端定义
	CLIENT_CHANGE_VERSION = 100,							-- 客户端版本变更						
	LOGIN_FORBID = 101,										-- 登录限制
	SERVER_MAINTENANCE = 102,								-- 服务器维护
	CONNECT_OUTTIME = 200,									-- 断开连接时间过长
}


HUNYAN_NOTIFY_REASON = {
	HUNYAN_NOTIFY_REASON_STATE_CHANGE = 0,				-- 状态改变
	HUNYAN_NOTIFY_REASON_ENTER_HUNYAN = 1,				-- 进入婚宴
	HUNYAN_NOTIFY_REASON_LOGIN = 2,						-- 上线
	HUNYAN_NOTIFY_REASON_INVITE_FRIEND = 3,				-- 邀请好友
	HUNYAN_NOTIFY_REASON_INVITE_GUILD = 4,				-- 邀请仙盟好友
	HUNYAN_NOTIFY_REASON_GATHER = 5,					-- 采集
	HUNYAN_NOTIFY_REASON_GIVE_HONGBAO = 6,				-- 发红包
}

BAOXIA_TYPE = {
	BUY_BAOXIA = 0,										-- 购买宝匣
	BUY_GET = 1,                                        -- 购买即得
	EVERYDAY_GET = 2,									-- 每天领取
}

QINGYUAN_LOVEBOX_REWARD_TYPE = {
  QINGYUAN_LOVEBOX_REWARD_TYPE_OPEN = 0,      -- 开通奖励
  QINGYUAN_LOVEBOX_REWARD_TYPE_SELF_DAILY = 1,    -- 每日奖励（本人）
  QINGYUAN_LOVEBOX_REWARD_TYPE_LOVER_DAILY = 2,    -- 每日奖励（伴侣）
}

SKILL_PERFOM_TYPE = {
	NONE = 0,											--不能释放
	AIM_TARGET = 1,										--瞄准目标
	DIRECT = 2,											--直接释放
}

-----化神
HUASHEN_REQ_TYPE = {
	HUASHEN_REQ_TYPE_ALL_INFO = 0,						-- 所有信息
	HUASHEN_REQ_TYPE_CHANGE_IMAGE = 1,					-- 切换形象
	HUASHEN_REQ_TYPE_UP_LEVEL = 2,						-- 升级
	HUASHEN_REQ_TYPE_SPIRIT_INFO = 3,		            -- 请求化神精灵信息
	HUASHEN_REQ_TYPE_UPGRADE_SPIRIT = 4,	            -- 化神精灵升级
	HUASHEN_REQ_TYPE_MAX = 5,
}

SPEAKER_TYPE = {
	SPEAKER_TYPE_LOCAL = 0,								-- 本服喇叭
	SPEAKER_TYPE_CROSS = 1,								-- 跨服传音

	SSPEAKER_TYPE_MAX = 2,
}

LIEMING_HUNSHOU_OPERA_TYPE = {							-- 猎命操作类型
	INVALID = 0,
	CHOUHUN = 1,
	SUPER_CHOUHUN = 2,
	BATCH_HUNSHOU = 3,                                  -- 一键猎魂
	SPECIAL_BATCH_HUNSHOU = 4,
	PUT_BAG = 5,
	CONVERT_TO_EXP = 6,
	MERGE = 7,
	SINGLE_CONVERT_TO_EXP = 8,
	TAKEON = 9,
	TAKEOFF = 10,
	FUHUN_ADD_EXP = 11,
	OPERA_TYPE_MAX = 12,
}

JINGLING_OPER_TYPE = {						-- 精灵操作类型
	JINGLING_OPER_TAKEOFF = 0,				-- 取下
	JINGLING_OPER_CALLBACK = 1,				-- 召回
	JINGLING_OPER_FIGHTOUT = 2,				-- 出战
	JINGLING_OPER_UPLEVEL = 3,				-- 升级
	JINGLING_OPER_UPLEVELCARD = 4,			-- 升级卡牌
	JINGLING_OPER_RENAME = 5,				-- 精灵改名
	JINGLING_OPER_UPGRADE = 6,				-- 升阶
	JINGLING_OPER_STRENGTH = 7,				-- 强化装备
	JINGLING_OPER_USEIMAGE = 8,				-- 使用形象
	JINGLING_OPER_USEPHANTOM_IMG = 9,		-- 使用幻化形象
	JINGLING_OPER_PHANTOMUPLEVEL = 10,		-- 幻化形象升级
	JINGLING_OPER_UPGRADESOUL = 11,			-- 灵魄升级
	JINGLING_OPER_UPGRADE_HALO = 12,		-- 精灵光环
	JINGLING_OPER_USE_HALO_IMG = 13,		-- 使用精灵光环

}

SHENGWANG_OPERA_TYPE = {						-- 声望操作类型
	SHENGWANG_OPERA_REQ_INFO = 0,				-- 请求声望相关信息
	SHENGWANG_OPERA_XIANJIE_UPLEVEL = 1,		-- 仙阶升级
	SHENGWANG_OPERA_XIANDAN_UPLEVEL = 2,		-- 仙丹升级
}

CHENGJIU_OPER_TYPE = {
	CHENGJIU_REQ_INFO = 0,						-- 请求成就信息
	CHENGJIU_OPER_TITLE_UPLEVEL = 1,			-- 提升称号
	CHENGJIU_OPER_FETCH_REWARD = 2,				-- 领取奖励
	CHENGJIU_OPER_FUWEN_UPLEVEL = 3,	 		-- 提升铭纹
}

CHEST_SHOP_MODE = {									-- 宝箱商店
	CHEST_SHOP_MODE_1 = 1,							-- 装备抽X次
	CHEST_SHOP_MODE_30 = 2,							-- 装备抽X次
	CHEST_SHOP_MODE_150 = 3,						-- 装备抽X次
	CHEST_SHOP_JL_MODE_1 = 4,						-- 精灵抽X次
	CHEST_SHOP_JL_MODE_30 = 5,						-- 精灵抽X次
	CHEST_SHOP_JL_MODE_150 = 6,						-- 精灵抽X次
}

CHEST_SHOP_TYPE = {
	CHEST_SHOP_TYPE_EQUIP = 1,						-- 装备类型宝箱抽奖
	CHEST_SHOP_TYPE_JINGLING = 2,					-- 精灵类型宝箱抽奖
}

REALIVE_TYPE = {
	BACK_HOME = 0,							-- 回城复活
	HERE_STUFF = 1,							-- 使用材料复活
	HERE_AUTOBUY_STUFF = 2,					-- 自动购买材料复活
	HERE_ICON = 3,							-- 使用铜钱原地复活
	TOZI_FREE = 4,							-- VIP投资免费复活
}

SCORE_TO_ITEM_TYPE = {
	INVALID = 0,
	GOUYU = 1,											-- 勾玉兑换
	NORMAL_ITEM = 2,									-- 道具兑换
	EQUIP = 3,											-- 装备兑换

	CS_EQUIP1 = 4,										-- 装备寻宝商店兑换1		 6仙品 幸运

	CS_XUNBAO = 5,										-- 寻宝
	-- CS_EQUIP2 = 5,										-- 装备寻宝商店兑换2		 6仙品

	CS_JINYINTA = 6,										-- 金银塔
	-- CS_EQUIP3 = 6,										-- 装备寻宝商店道具兑换3

	CS_JINGLING1 = 7,									-- 精灵寻宝商店兑换1
	CS_JINGLING2 = 8,									-- 精灵寻宝商店兑换2

	CS_MEDCHINE = 9,									-- 药店兑换购买

	CS_HUOLI = 10,										-- 活力

	MAX = 19,
}

TEAM_FB_OPERAT_TYPE = {
	REQ_ROOM_LIST = 0,			-- 请求房间列表
	CREATE_ROOM = 1,			-- 创建房间
	JOIN_ROOM = 2,				-- 加入指定房间
	START_ROOM = 3,				-- 开始
	EXIT_ROOM = 4,				-- 退出房间
	KICK_OUT = 5,				-- T人
}

MIGONGXIANFU_LAYER_TYPE = {
	MGXF_LAYER_TYPE_NORMAL = 0,							-- 普通层
	MGXF_LAYER_TYPE_BOSS = 1,							-- Boss层
	MGXF_LAYER_TYPE_HIDE = 2,							-- 隐藏层
}

MIGONGXIANFU_STATUS_TYPE = {
	MGXF_DOOR_STATUS_NONE = 0,
	MGXF_DOOR_STATUS_TO_PRVE = 1,
	MGXF_DOOR_STATUS_TO_HERE = 2,
	MGXF_DOOR_STATUS_TO_NEXT = 3,
	MGXF_DOOR_STATUS_TO_HIDE = 4,
	MGXF_DOOR_STATUS_TO_BOSS = 5,
	MGXF_DOOR_STATUS_TO_FIRST = 6,
}

LIFE_SKILL_OPERAT_TYPE = {
	LIFE_SKILL_OPERAT_TYPE_REQ_INFO = 0,				-- 生活技能请求信息
	LIFE_SKILL_OPERAT_TYPE_UPLEVEL = 1,					-- 生活技能升级
	LIFE_SKILL_OPERAT_TYPE_MAKE = 2,					-- 生活技能制作物品
}

-- 精灵配置天赋
JL_GAY_WAY = {
	LIBAO = 1
}

-- 卡牌操作
CARD_OPERATE_TYPE = {
	REQ = 0,				-- 请求信息
	INLAY = 1,			-- 镶嵌
	UPLEVEL = 2,			-- 升级
	KEY_UPLEVEL = 3,		-- 一键升级
}

-- 仙盟仓库操作
GUILD_STORGE_OPERATE = {
	GUILD_STORGE_OPERATE_PUTON_ITEM = 1, -- 放进仓库
	GUILD_STORGE_OPERATE_TAKE_ITEM = 2,  -- 取出仓库
	GUILD_STORGE_OPERATE_REQ_INFO = 3,	 -- 请求仓库信息
}

-- 客户端操作请求类型
COMMON_OPERATE_TYPE = {
	COT_JINGHUA_HUSONG_COMMIT = 1,				-- 精华护送提交
	COT_JINGHUA_HUSONG_COMMIT_OPE = 2,			-- 精华护送提交次数请求
	COT_KEY_ADD_FRIEND = 3,						-- 一键踩好友空间
	COT_ZHUANZHI_TIANMING_UPLEVEL = 4,			-- 转职天命升级请求    param1   0、魂石 1、经验
	COT_ZHUANZHI3_TASK_ONE_KEY_FINISH = 5,		-- 3转任务一键完成请求
	COT_TEAM_EQUIP_INFO = 6,                   	-- 组队装备本信息请求
	COT_OFFLINE_SETTING_INFO_REQ = 7,			-- 离线挂机设置信息
	COT_ZHUANZHI_CANGLONG_UPLEVEL = 8,			-- 转职苍龙升级请求
	COT_YUANGU_XIANDIAN_FB_INFO_REQ = 9,        -- 远古仙殿信息
	COT_ZHUSHENTA_FB_INFO_REQ = 10,	-- 诛神塔信息
	COT_ZHUANZHI_LONGPO_UPLEVEL = 11,      --转职龙魄升级请求
	COT_ZHUANZHI_LONGHUN_UPLEVEL = 12,      --转职龙魂升级请求
	COT_ZHUANZHI_LONGLING_UPLEVEL = 13,      --转职龙灵升级请求

	COT_TIANSHU_XUNZHU_FETCH_REWARD = 16,		-- 天书寻主，领取奖励
	COT_TIANSHU_XUNZHU_REQ_INFO = 17,			-- 天书寻主, 获取信息
	COT_OFFLINE_FETCH_REWARD = 19,				--离线挂机领取奖励
	COT_CROSS_1V1_SELF_INFO = 20,				--跨服1v1信息请求
	COT_ZHUANZHI = 21,							--转职请求
	COT_ZHUANZHI_DIANLIANG_5 = 22,				--五转点亮
	COT_ZHUANZHI_DIANLIANG_6 = 23,				--六转点亮
	COT_OFFLINE_ZHAOHUI_REQ = 24,				--离线经验找回
	COT_MOVE_SWORD_DOWN = 25,					--御剑结束
	COT_MOVE_SWORD_SPRINT = 26,					--御剑冲刺
	COT_CREATE_TASK_MONSTER = 27,				--新手期间，通知创建怪物
	COT_DO_MONSTER_QTE = 28,				    --怪物QTE完成
	COT_TASK_BEAST_SKILL = 29,					--完成幻兽释放技能任务 
	COT_DO_MONSTER_SEC_KILL = 30,				--boss秒杀
	COT_NPC_TALK = 31,							-- NPC对话 param1: npc_id
	COT_TEAM_COMMON_TOWER_FB_DRAW = 33,			-- 组队通用爬塔本-抽奖 param1:index param2:is_added
	COT_TEAM_COMMON_FB_LIKE = 34,				-- 组队通用本-点赞 param1:type
	COT_FETCH_POP_SERVICE_REWARD = 35,			-- 领取弹窗客服每日奖励

	COT_ACT_BUY_EQUIPMENT_GIFT = 1000,			-- 活动 购买装备礼包
}

-- 服务器通知客户端信息类型
SC_COMMON_INFO_TYPE = {
	SCIT_JINGHUA_HUSONG_INFO = 1,				-- 同步精华护送信息
	SCIT_RAND_ACT_ZHUANFU_INFO = 2,				-- 随机活动专服信息
	SCIT_ZHUANZHI_TIANMING_INFO = 3,			-- 转职天命等级信息
	SCIT_OFFLINE_SETTING_INFO_REQ = 4,			-- 离线挂机设置信息
	SCIT_ZHUANZHI_CANGLONG_INFO = 5,			-- 转职苍龙等级信息
	SCIT_GUILD_DEVOTE_INFO = 6,					-- 仙盟贡献
	SCIT_ZHUANZHI_DIANLIANG_INFO = 7,			-- 转职点亮等级信息 param1 = 5转 param2 = 6转
}

JH_HUSONG_STATUS = {
	NONE = 0,
	FULL = 1,
	LOST = 2,
}

SHENZHUANG_OPERATE_TYPE = {
	REQ = 0,					-- 神装请求信息
	UPLEVEL = 1,				-- 神装升级
	SHENZHUANG_OPREATE_JINJIE = 2,			-- 新增 进阶
	SHENZHUANG_OPERATE_SHENZHU = 3,			-- 新增 神铸
}

MYSTERIOUSSHOP_OPERATE_TYPE = {
	MYSTERIOUSSHOP_OPERATE_TYPE_REQINFO = 0,		--请求神秘商店信息
	MYSTERIOUSSHOP_OPERATE_TYPE_BUY = 1,			--购买
}

CAMPEQUIP_OPERATE_TYPE = {
	CAMPEQUIP_OPERATE_TYPE_REQ_INFO = 0,		-- 请求信息
	CAMPEQUIP_OPERATE_TYPE_TAKEOFF = 1,			-- 脱下
	CAMPEQUIP_OPERATE_TYPE_HUNLIAN = 2,			-- 魂炼
}

CAMP_NORMALDUOBAO_OPERATE_TYPE = {
	ENTER = 0,		-- 请求进入师门普通夺宝
	EXIT = 1,			-- 请求退出师门普通夺宝
}

SOIL_STATUS = {
	SOIL_STATUS_IDLE = 0,				-- 没有植物，可以种植
	SOIL_STATUS_WAITING = 1,			-- 植物成长，等待中
	SOIL_STATUS_RIPE = 2,				-- 植物成熟，可以收获
	SOIL_STATUS_DIRTY = 3,				-- 植物被偷光了，处于贫瘠状态
}

-- 宝宝系统
-- BABY_REQ_TYPE = {
-- 	BABY_REQ_TYPE_INFO = 0,								-- 请求单个宝宝信息  参数1 宝宝ID
-- 	BABY_REQ_TYPE_ALL_INFO = 1,							-- 请求所有宝宝信息
-- 	BABY_REQ_TYPE_UPLEVEL = 2,							-- 升级请求	参数1 宝宝ID
-- 	BABY_REQ_TYPE_QIFU = 3,								-- 祈福请求 参数1 祈福类型
-- 	BABY_REQ_TYPE_QIFU_RET = 4,							-- 祈福答应请求 参数1 祈福类型，参数2 是否接受
-- 	BABY_REQ_TYPE_CHAOSHENG = 5,						-- 宝宝超生
-- 	BABY_REQ_TYPE_SPIRIT_INFO = 6,						-- 请求单个宝宝的守护精灵的信息，发baby_index
-- 	BABY_REQ_TYPE_TRAIN_SPIRIT = 7,						-- 培育精灵请求，发baby_index(param1)，spirit_id（param2, 从0开始，0-3）
-- 	BABY_REQ_TYPE_REMOVE_BABY = 8,						-- 遗弃宝宝请求
-- }

ROLE_SHADOW_TYPE = {
	ROLE_SHADOW_TYPE_CHALLENGE_FIELD = 0,		-- 竞技场
	ROLE_SHADOW_THPE_WORLD_EVENT = 1,			-- 世界事件
	ROLE_SHADOW_TYPE_ROLE_BOSS = 2,				-- 角色boss
	ROLE_SHADOW_TYPE_DIAOXIANG = 3,				-- 守卫雕像
	ROLE_SHADOW_TYPE_DIAOXIANG_DIE = 4,			-- 守卫雕像（死亡）
	ROLE_SHADOW_TYPE_ROBERT = 5,				-- 机器人
	ROLE_SHADOW_TYPE_OFFLINE_REST = 6,			-- 离线挂机假人
	ROLE_SHADOW_TYPE_TEAM_FUBEN = 7, 			-- 组队副本机器人
}

RA_CHONGZHI_NIU_EGG_OPERA_TYPE = {
	RA_CHONGZHI_NIU_EGG_OPERA_TYPE_QUERY_INFO = 0,				-- 请求活动信息
	RA_CHONGZHI_NIU_EGG_OPERA_TYPE_CHOU = 1,					-- 抽奖
	RA_CHONGZHI_NIU_EGG_OPERA_TYPE_FETCH_REWARD = 2,			-- 领取全服奖励

	RA_CHONGZHI_NIU_EGG_OPERA_TYPE_MAX = 3,
}

LINGYUSHUISHOU_OPERA_TYPE = {
	INVALID = 0,
	FETCH_XMZ_REWARD = 1,	--仙盟战
	FETCH_GCZ_REWARD = 2,	--攻城战
	MOBAI = 3,				--膜拜城主，param@ 0:普通膜拜，1:高级膜拜
	MAX = 4,
}


-- 每日必做
DAILY_WORK_OPERA_REQ_TYPE = {
	DW_OPERA_REQ_TYPE_CHARGE_SWORD = 0,			-- 幻化请求
	DW_OPERA_REQ_TYPE_UPLEVEL = 1,	   			-- 升级请求
	DW_OPERA_REQ_TYPE_FETCH_REWARD = 2,			-- 领取等级奖励请求
	DW_OPERA_REQ_TYPE_IS_CHARGE = 3,			-- 是否幻化
	DW_OPERA_REQ_TYPE_FIND_BACK = 4,			-- 找回
	DW_OPERA_REQ_TYPE_FETCH_DAILY_REWARD = 5,	-- 领取活跃奖励
	DW_OPERA_REQ_TYPE_SIGNUP = 6,      			-- 报名，param1是活动id
}

CHONGZHI_REWARD_TYPE = {
		CHONGZHI_REWARD_TYPE_SPECIAL_FIRST = 0,										-- 特殊首充
		CHONGZHI_REWARD_TYPE_DAILY_FIRST = 1,										-- 日常首充
		CHONGZHI_REWARD_TYPE_DAILY_TOTAL = 2,										-- 日常累充
		CHONGZHI_REWARD_TYPE_DIFF_WEEKDAY_TOTAL = 3,	--新增						-- 每日累冲(星期几区分奖励配置)
	}

LINGYU_FB_OPERA_TYPE = {
	REQINFO = 0,			-- 挑战副本请求信息
	BUYJOINTIMES = 1,		-- 挑战副本购买次数
	AUTO = 2,				-- 挑战副本扫荡
	RESETLEVEL = 3,			-- 挑战副本重置关卡
}

TAOZHUANG_TYPE =
{
	BAOSHI_TAOZHUANG = 0,
	STREGNGTHEN_TAOZHUANG =1
}

MAGIC_EQUIPMENT_REQ_TYPE = {
	MAGIC_EQUIPMENT_REQ_TYPE_UPGRADE = 0,		-- 吞噬进阶：param1 魔器类型，param2 消耗数量
	MAGIC_EQUIPMENT_REQ_TYPE_STRENGTHEN = 1,	-- 锻造强化：param1 魔器类型，param2 是否自动强化， param3 是否自动购买
	MAGIC_EQUIPMENT_REQ_TYPE_EMBED = 2,		    -- 镶嵌魔石：param1 魔器类型，param2 镶嵌孔位，param3 魔石下标（配置里的）
	MAGIC_EQUIPMENT_REQ_TYPE_TAKE_OFF_STONE = 3,-- 卸下魔石： param1	魔器类型，param2 镶嵌孔位

	MAGIC_EQUIPMENT_REQ_TYPE_MAX = 4,
}

-- 灵玉图片的名字
LINGYU_TYPE_ICON = {
	[1] = "a3_ls_1",
	[2] = "a3_ls_2",
}


PASTURESPIRIT_REQ_TYPE = {
	PASTURE_SPIRIT_REQ_TYPE_ALL_INFO = 0,							-- 请求所有信息
	PASTURE_SPIRIT_REQ_TYPE_UPGRADE = 1,							-- 请求升级
	PASTURE_SPIRIT_REQ_TYPE_PROMOTE_QUALITY = 2,					-- 请求提示品质
	PASTURE_SPIRIT_REQ_TYPE_AUTO_PROMOTE_QUALITY = 3,				-- 请求一键提示品质
	PASTURE_SPIRIT_REQ_TYPE_FREE_DRAW_ONCE = 4,						-- 请求免费抽一次
	PASTURE_SPIRIT_REQ_TYPE_LUCKY_DRAW_ONCE = 5,					-- 请求抽奖一次
	PASTURE_SPIRIT_REQ_TYPE_LUCKY_DRAW_TIMES = 6,					-- 请求抽奖多次

	PASTURESPIRIT_REQ_TYPE_MAX = 7,
}


-- 锻造类型枚举
EQUIPMENT_OPERA_TYPE = {
	STRENGTH								= 0,				-- 锻造 强化
	JINGLIAN								= 1,				-- 锻造 精炼
	SHENZHU									= 2,				-- 锻造 神铸
	LEGEND									= 3,				-- 锻造 传说
	COMPOSE									= 4,				-- 锻造 合成
}

STONE_OPERA_TYPE = {
	INLAY									= 0,				-- 宝石镶嵌
	UNINLAY									= 1,				-- 宝石摘下
	UP										= 2,				-- 宝石升级
	STONE_REFINE_UPLEVEL					= 3,				-- 宝石精炼
	STONE_REFINE_POLISH				    	= 4,				-- 宝石抛光
	STONE_ALL_INFO							= 5,				-- 宝石信息
	ACTIVE_STONE_LEVEL                      = 6,				-- 宝石等级加成属性激活
	AUTO_LEVEL_UP 							= 7,				-- 宝石升级 part_index
}


SHEN_OPERA_TYPE = {
	UPLEVEL									= 0,				-- 神装升级
	SHENZHUANG_REQ_DROP_RECORD				= 1,				-- 获得掉装掉落记录
}

WASH_OPERA_TYPE = {
	WASH_REQ_INFO							= 0,				-- 洗炼请求信息
	WASH_REQ_WASH							= 1,				-- 请求洗炼
	WASH_SAVE								= 2, 				-- 请求洗炼保存
	WASH_REPLACE 							= 3,				-- 洗炼属性替换
}

GUILD_BUILD_OPERA_TYPE = {
	GUILD_DEVELOP_CONTRIBUTE				= 0,				-- 仙盟捐献
	GUILD_DEVELOP_OPENGUWU					= 1,				-- 仙盟开启鼓舞
}

XIANSHU_OPERA_TYPE = {
	XIANSHU_REQINFO							= 0,				-- 仙术请求信息
	XIANSHU_UPLEVEL							= 1,				-- 仙术升级
}

FABAO_OPERA_TYPE = {
	FABAO_REQ_ALLINFO						= 0,				-- 法宝请求所有信息
	FABAO_ACTIVE							= 1,				-- 激活法宝(fabao_id)
	FABAO_CALLOUT							= 2,				-- 法宝召唤(fabao_id)
	FABAO_UPLEVEL							= 3,				-- 法宝升级(fabao_id)
	FABAO_UPGRADE 							= 4,				-- 法宝进阶(fabao_id, repeat_times, is_autobuy)
	FABAO_LIANQI 							= 5,				-- 法宝炼器(fabao_id, repeat_times)
	FABAO_CLEAR_UPLEVEL_CD					= 6,				-- 法宝清除升级CD(fabao_id)
	FABAO_USE_SPECIAL_IMG 					= 7,				-- 法宝使用特殊形象
}

EQUIP_FB_OPERA_TYPE = {
	EQUIPFB_REQ_INFO						= 0,				-- 装备副本请求信息
	EQUIPFB_BUY_TIMES						= 1,				-- 装备本购买次数(新加一个分支 第一个参数代表是第几章)
	EQUIPFB_AUTO							= 2,				-- 装备副本扫荡
}


FULI_FB_OPERA_TYPE = {
	WELFAREFB_REQ_INFO						= 0,				-- 福利副本请求信息
	WELFAREFB_AUTO							= 1,				-- 福利副本扫荡((fb_index, is_autobuy, is_double))
	WELFAREFB_REQ_BUY_TIMES					= 2,

}

PATA_FB_OPERA_TYPE = {
	TIANXIANGEFB_REQ_INFO					= 0,				-- 天仙阁副本请求信息
	TIANXIANGEFB_FETCH_DAILY_REWARD 		= 1,				-- 天仙阁副本领取日常奖励
}

FAN_REN_XIU_ZHEN_FB_OPERA_TYPE = {
	LEVEL									= 1,				-- 凡人修真领取宝箱奖励
	CHPATER 								= 2,				-- 凡人修真领取章节奖励
}


WUSHUANG_OPERA_TYPE = {
	WUSHUANGFB_REQ_INFO					= 0,					-- 无双副本请求信息
	WUSHUANGFB_FETCH_REWARD 			= 1,					-- 无双副本请求领奖
}

LINGPO_OPERA_TYPE = {
	LINGPO_REQ_INFO							= 0,				-- 灵魄请求信息
	LINGPO_UPLEVEL							= 1,				-- 灵魄升级（index)
}

WEAPONFB_OPERA_TYPE = {
	WEAPONFB_REQ_INFO						= 0,				-- 武器副本请求信息
	WEAPONFB_FETCH_REWARD					= 1,				-- 武器副本领取奖励（chapter, reward_index）   reward_index是每个章节中的reward_index （0 - 2）
	WEAPONFB_AUTO							= 2,				-- 武器副本扫荡（chapter, level, is_autobuy）
	WEAPONFB_ADD_TIMES						= 3,				-- 武器副本增加次数
}

TEAM_TOWERD_OPERA_TYPE = {
	NEXT_WAVE								= 0,				-- 组队副本下一波
}

ZHUXIE_OPERA_TYPE = {
	TASK_REWARD								= 0,				-- 诛邪领取任务奖励(task_id)
}

CROSS_ZHUXIE_OP_TYPE = 											--跨服诛邪领取任务奖励(task_id)
{
	CROSS_ZHUXIE_OP_TYPE_TASK_REWARD = 0,
}

SIEGE_OPERA_TYPE = {
	REQ_INFO								= 0,				-- 攻城战信息
	FETCH_DAY_REWARD						= 1,				-- 攻城战领取任务奖励
}

SHITU_NOTICE = {
	REQUEST_BAISHI 							= 0,				-- 请求拜师
	AGREE_BAISHI_REQUEST 					= 1,				-- 同意拜师请求
	REFUSE_BAISHI_REQUEST 					= 2,				-- 拒绝拜师请求
}

SHITU_OPERA_TYPE = {
	REQ_INFO								= 0,				-- 请求信息
	RQUEST_BAISHI							= 1,				-- 请求拜师(target_roleid, type)
	AGREE_BAISHI_REQUEST					= 2,				-- 同意拜师请求(target_roleid)
	REFUSE_BAISHI_REQUEST					= 3,				-- 拒绝拜师请求(target_roleid)
	REMOVE_RELATION							= 4,				-- 解除关系(target_roleid)
	FETCH_DEGREE_REWARD						= 5,				-- 师徒领取活跃度奖励
	UPLEVEL									= 6,				-- 师徒升级
	ENTER_BOSSSCENE							= 7,				-- 师徒进入boss场景
	EXIT_BOSSSCENE							= 8,				-- 师徒退出boss场景
}

SHITU_EQUIP_OPERA_TYPE = {
	EQUIP_REQ_INFO							= 0,				-- 师徒装备请求信息
	EQUIP_PUTON								= 1,				-- 师徒装备请求穿戴(bag_index)
	EQUIP_TAKEOFF							= 2,				-- 师徒装备请求脱下(equip_index)
}

SHITU_FB_OPERA_TYPE = {
	REQ_INFO								= 0,				-- 师徒爬塔副本请求信息
	FETCH_DAILY_REWARD 						= 1,				-- 师徒爬塔副本领取日常奖励
}

SHITU_WASH_OPERA_TYPE = {
	REQ_INFO								= 0,				-- 师徒洗炼请求信息
	REQ_WASH								= 1,				-- 师徒洗炼请求洗炼
	SAVE 									= 2,				-- 师徒洗炼属性保存
}

GUILD_SHOUHU_OPERA_TYPE = {
	GuWu 									= 0, 				-- 鼓舞
	GUILD_FB_OPERA_TYPE_RANK 				= 1, 				-- 原服仙盟排行榜
	GUILD_FB_OPERA_TYPE_RANK_CROSS 			= 2,    			-- 跨服仙盟排行榜
    GUILD_FB_OPERA_TYPE_DETAIL_INFO			= 3,  				-- 请求展示信息
}

GUILD_SHOUHU_GUWU_TYPE = {
	Coin 									= 0, 				-- 铜币鼓舞
	Gold 									= 1, 				-- 元宝鼓舞
}

-- 仙盟捐献类型
GUILD_CONTRIBUTE_TYPE = {
	MIN = 0,
	ITEM = 1,
	GOLD = 2,
	COIN = 3,
	MAX = 4,
}

CHEST_XUNBAO_SHOP_XUNBAO ={
	MODE_INVALID = 0,
	MODE_1 = 1,
	MODE_2 = 2,
	MODE_3 = 3,
	FM_MODE_1 = 4,
	FM_MODE_2 = 5,
	FM_MODE_3 = 6,
	MODE_MAX = 7,
}
CHEST_XUNBAO_SHOP_MODE_OPERTE = {
	CHESTSHOP_REQ_STORGE_INFO					= 0,				-- 请求宝箱仓库信息
	CHESTSHOP_BUY								= 1,				-- 宝箱抽取
	CHESTSHOP_FETCH_ITEM						= 2,				-- 宝箱仓库取出单个物品
	CHESTSHOP_FETCH_ALL							= 3,				-- 宝箱仓库取出所有物品
	CHESTSHOP_RECORD_REQ						= 4,				-- 飞仙寻宝日志请求
}

JLCHEST_XUNBAO_SHOP_MODE_OPERTE = {
	JLCHESTSHOP_REQ_STORGE_INFO					= 0,				-- 请求精灵宝箱仓库信息
	JLCHESTSHOP_BUY								= 1,				-- 精灵宝箱抽取
	JLCHESTSHOP_FETCH_ITEM						= 2,				-- 精灵宝箱仓库取出单个物品
	JLCHESTSHOP_FETCH_ALL						= 3,				-- 精灵宝箱仓库取出所有物品
	JLCHESTSHOP_RECYCLE_JL						= 4,				-- 精灵宝箱回收精灵
}

MONEY_TYTE = {
	XINHUN = 0,										--星魂
	BANGGONG = 1,									--帮贡
	HISTORY_LONGHUN = 2,							--历史龙魂
	PET_EXP = 3,									--宠的经验值
	SHITU_EXP = 4,									--师徒经验
	DAY_LONGHUN = 5,							    --每日贡献
}

QIANDDAO_STATUS =
{
	NONE = 0,										--无
	BACK = 1,										--可找回
	WAIT = 2, 										--即将领取
	ALREADYGET = 3,									--已经签到
	QIANDAO = 4, 									--可领取
}

MEDAL_REQ_TYPE = {
	MEDAL_REQ_TYPE_INFO = 0,							-- 勋章信息请求
	MEDAL_REQ_TYPE_UPGRADE_QUALITY = 1,					-- 勋章提升品质请求
	MEDAL_REQ_TYPE_UPGRADE_LEVEL = 2,					-- 勋章提升等级请求
}

PET_OPER_TYPE = {									-- 宠物操作类型
	PET_REQ_INFO = 0,								-- 宠物请求信息
	PET_ACTIVE = 1,									-- 宠物激活(pet_id)
	PET_UPLEVEL = 2,								-- 宠物升级(pet_id, stuff_id)
	PET_KEY_UPLEVEL = 3,							-- 宠物直升一级(pet_id)(没用了)
	PET_ADDZIZHI = 4,								-- 宠物增加资质(pet_id, stuff_id)
	PET_UPGRADE = 5,								-- 宠物升阶(pet_id, repeat_times, is_autobuy)
	PET_HALO_UPLEVEL = 6,							-- 宠物光环升级(repeat_times, is_autobuy)
	PET_USE_SPECIAL_IMG = 7,						-- 宠物使用特殊形象(img_id)
	PET_HALO_USE_IMG = 8,							-- 宠物光环使用形象(img_id)
	PET_FIGHT_OUT = 9,								-- 宠物出战(pet_id)
	PET_LAY_UPLEVEL	= 10,							-- 宠物提升飞升等级(repeat_times, is_autobuy)
	PET_FLY_USE_IMG	= 11,							-- 宠物飞升使用形象(img_id)
	PET_FAZHEN_UPGRADE = 12,						-- 宠物法阵升阶
	PET_USE_FAZHEN_GRADE = 13,						-- 宠物使用法阵阶


	-- 自动升级时返回操作类型
	SC_PET_HALO_UPLEVEL = 20,						-- 宠物光环升级
	SC_PET_UPGRADE = 21,							-- 宠物进阶
}

PET_EQUIP_TYPE = {									-- 宠物装备操作类型
	PETEQUIP_REQ_INFO = 0,							-- 宠物装备请求信息
	PETEQUIP_PUTON = 1,								-- 宠物装备穿上(bag_index, part_index)
	PETEQUIP_UPLEVEL = 2,							-- 宠物装备升级(part_index)
	PETEQUIP_UPQUALITY = 3,							-- 宠物装备提升品质(part_index)
	PETEQUIP_UPSTAR = 4,							-- 宠物装备升星(part_index, is_autobuy)
	PETEQUIP_STRENGTH = 5,							-- 宠物强化(part_index, is_autobuy)
}

TITLE_TYPE = {
	TITLECARD = 1,
	TOP_RANK_CAPABILITY = 2,						-- 战力前10
	TOP_RANK_LEVEL = 3,								-- 等级前10
	TOP_RANK_XIANNV = 4,							-- 仙女前10
	TOP_RANK_CAMP = 5,								-- 阵营前10
	LINGPO =6,										-- 灵魄
}

TITLE_OPERTYPE = {
	REQ_INFO = 0,									-- 称号请求信息
	REQ_USE = 1,									-- 称号使用称号
	REQ_UN_USE = 2,									-- 请求卸下称号
	REQ_UP_LEVEL = 3,                               -- 升级称号
}

BACK_OPERTYPE = {
	BACK_REQ_INFO = 0,								-- 背景请求信息
    BACK_REQ_USE = 1,								-- 背景使用背景
    BACK_REQ_UNUSE = 2,								-- 卸下背景
    BACK_REQ_UP_LEVEL = 3,							-- 背景升级
}

CAMP_OPREATE = {
	REQ_INFO = 0,									-- 阵营请求信息
	JOIN = 1,										-- 阵营加入阵营
	CHANGE = 2,										-- 阵营改变阵营
}

ACTIVEDEGREE_OPREATE = {
	REQ_INFO = 0,									-- 活跃度请求信息
	FETCH_REWARD = 1,								-- 活跃度领取奖励
}

WING_OPERATE = {
	REQ_INFO = 0,									-- 羽翼请求信息
	JINHUA = 1,										-- 羽翼进化
	USE_IMAGE = 2,									-- 羽翼使用形象
	FAZHEN_UPGRADE = 3,								-- 羽翼法阵升阶
	USE_FAZHEN_GRADE = 4,							-- 羽翼使用法阵阶
	WING_FUMO = 5,									-- 羽翼附魔
}

JINGMAI_OPERATE = {
	REQ_INFO = 0,									-- 经脉请求信息
	TRAIN = 1,										-- 经脉训练
	KEY_TRAIN = 2,									-- 经脉一键训练
	CLEARCD = 3,									-- 经脉清除CD
	GENGU_UPLEVEL = 4,								-- 经脉根骨升级
}

NEQ_CARD_STATUS = 									-- 卡牌状态
{
	DEFAULT = 1, 									--初始
	PREVIEW = 2, 									--预览
	SHUFFLE = 3, 									--洗牌
	COMPLETE_SHUFFLE = 4, 							--完成洗牌
	OPEN_ING = 5, 									--开启中
	OPEN = 6, 										--已开启
}

RAND_ACTIVITY_OPEN_TYPE = {
	RAND_ACTIVITY_OPEN_TYPE_NORMAL = 0,				--正常随机活动
	RAND_ACTIVITY_OPEN_TYPE_VERSION = 1,			--版本活动

}

ZHENBAOGE_OPERATE = {
	RA_ZHENBAOGE_REQ_INFO = 0,						-- 珍宝阁请求信息
	RA_ZHENBAOGE_GOLD_REFRESH = 1,					-- 珍宝阁元宝刷新
	RA_ZHENBAOGE_GOLD_KEY_BUY = 2,					-- 珍宝阁元宝一键刷新
	RA_ZHENBAOGE_BUY = 3,							-- 珍宝阁购买（item_index (物品列表索引，不是序列号))
	RA_ZHENBAOGE_BUY_ALL = 4,						-- 珍宝阁购买全部
	RA_ZHENBAOGE_FETCH_REWARD = 5,					-- 珍宝阁领取全服奖励（seq)
}

DAILY_ONLINE_LOTTERY_OPERATE = {
	RA_DAILY_ONLINE_LOTTERY_REQ_INFO = 0,			-- 随机活动每日在线抽奖请求信息
	RA_DAILY_ONLINE_LOTTERY_FETCH = 1,				-- 随机活动每日在线抽奖领取
}

-- 随机活动秘境寻宝模式
MINGJING_CHESTSHOP_MODE = {
	MODE_INVALID = 0,
	MODE_1 = 1,										--淘宝一次
	MODE_2 = 2,										--淘宝十次
	MODE_3 = 3,										--淘宝五十次
	MODE_MAX = 4,
}

RA_MIJINGXUNBAO_OPERA_TYPE = {
	RA_MIJINGXUNBAO_REQ_INFO = 0,					-- 秘境寻宝请求信息
	RA_MIJINGXUNBAO_BUY = 1,						-- 秘境寻宝购买
}

-- 随机活动陛下寻宝模式
BIXIA_CHESTSHOP_MODE = {
	MODE_INVALID = 0,
	CZ_MODE_1 = 1,
	CZ_MODE_2 = 2,
	CZ_MODE_3 = 3,
	ZZ_MODE_1 = 4,
	ZZ_MODE_2 = 5,
	ZZ_MODE_3 = 6,
	GZ_MODE_1 = 7,
	GZ_MODE_2 = 8,
	GZ_MODE_3 = 9,
	MODE_MAX = 10,
}

RA_BIXIA_OPERA_TYPE = {
	RA_BIXIAXUNBAO_REQ_INFO = 0,					-- 陛下寻宝请求信息
	RA_BIXIAXUNBAO_BUY = 1,							-- 陛下寻宝购买（mode, fabai_index(如果是翻牌模式下))
}

RA_CHONGZHI_MONEY_TREE_OPERA_TYPE = {				-- 摇钱树
	RA_MONEY_TREE_OPERA_TYPE_QUERY_INFO = 0,		-- 请求活动信息-- 摇钱树请求信息
	RA_MONEY_TREE_OPERA_TYPE_CHOU = 1,				-- 抽奖-- 摇钱树购买(mode)
	RA_MONEY_TREE_OPERA_TYPE_FETCH_REWARD = 2,		-- 领取全服奖励-- 摇钱树领取全服奖励(seq)
}

RA_SHENBING_OPERA_TYPE = {							-- 神兵类型
	SHENBING_REQ_INFO = 0,							-- 神兵请求信息
	SHENBING_UPLEVEL = 1,							-- 神兵请求升级
}

RA_CONSUMESCORE_OPERA_TYPE = {						-- 消费积分类型
	RA_CONSUMESCORE_REQ_INFO = 0,					-- 消费积分请求信息
	RA_CONSUMESCORE_FETCH_REWARD = 1,				-- 消费积分领取奖励 （seq)
}

TOTAL_CONSUME_OP_TYPE = {						-- 累消返利类型
	TOTAL_CONSUME_OP_TYPE_INFO = 0,					-- 累消返利请求信息
	TOTAL_CONSUME_OP_TYPE_FETCH = 1,				-- 累消返利领取奖励 （seq)
}

CROSS_SHUIJING_OPERATETYPE = {
	CROSS_SHUIJING_OPERATETYPE_REQ_INFO = 0,  		-- 请求信息
	CROSS_SHUIJING_OPERATETYPE_BUY_BUFF = 1,  		-- 购买buff
	CROSS_SHUIJING_OPERATETYPE_GOTOLAYER = 2, 		-- 直接跳层（第n层, 0-2)
}


-- 战斗力类型
CAPABILITY_TYPE = {
	INVALID = 0,

	BASE = 1,								-- 基础属性战斗力
	MENTALITY = 2,							-- 元神属性战斗力
	EQUIPMENT = 3,							-- 装备属性战斗力
	WING = 4,								-- 羽翼属性战斗力
	MOUNT = 5,								-- 坐骑属性战斗力
	BUFF = 6,								-- buff战斗力
	SKILL = 7,								-- 技能属性战斗力
	FOOTPRINT = 12,							-- 足迹属性战斗力
	QINGYUAN = 13,							-- 情缘属性战斗力
	WINGBASE = 14,							-- 羽翼基础属性战斗力
	SHIZHUANG = 15,							-- 时装属性战斗力
	CARD = 16,								-- 卡牌属性战力
	ATTR_PER = 19,							-- 基础属性百分比加的战斗力
	RESERVE1 = 20,							--
	-- CARDBASE = 21,						-- 卡牌战力
	VIPBUFF = 22,							-- vipbuff战力
	SHENGWANG = 23,							-- 声望
	CHENGJIU = 24,							-- 成就
	WASH = 25,								-- 洗炼
	SHENGZHUANG = 26,						-- 神装
	EQUIP_DUANZAO = 27,						-- 装备煅造(强化+升品)
	LINGYU = 28,							-- 灵玉
	TIANXIANGEFB = 29,						-- 天仙阁副本
	QINGYUAN_PROFESS = 30,					-- 情缘-表白
	QINGYUAN_EQUIP = 31,					-- 情缘-信物
	FABAO = 32,								-- 法宝
	LINGPO = 33,							-- 灵魄
	TITLE = 34,								-- 称号
	TIANSHEN_SHENSHI = 35,					-- 天神-神饰
	TIANSHEN_UP = 36,						-- 天神-提升
	SHITUWASH = 37,							--
	XIANJIE_EQUIP_SHENTI = 38,				-- 神体
	XIANJIE_EQUIP_TIANSHU = 39,				-- 天书
	XIANJIE_EQUIP_EQUIP = 40,				-- 圣装
	OTHERCAPABILITY = 41,					-- 其他战力
	IMAGEPROMOTION = 42,					-- 形象进阶
	MULTI_MOUNT = 43,						-- 多人坐骑
	RAND_ACTIVITY = 44,						-- 随机活动
	SHENZHOU_WEAPON = 45,					-- 神州六器战斗力
	TUJIAN = 46,							-- 图鉴
	BABY = 47,								-- 宝宝属性战斗力
	EQUIP_TARGET_SKILL = 48,				-- 装备目标技能
	ZHUANGZHI_SKILL = 49,					-- 转职技能
	HUASHEN = 50,							-- 化神系统
	WUSHUANG_EQUIP = 51,					-- 无双装备战力
	ROLE_SHUIJING = 52,						-- 水晶系统战力
	MAGIC_CARD = 53,						-- 魔卡战力
	PASTURE_SPIRIT = 54,					-- 牧场精灵战力
	MITAMA = 55,							-- 御魂战斗力
	PERSONALIZE_WINDOW = 56,				-- 个性聊天框战力
	-- JINGLING = 57,						-- 飞宠（精灵）战力
	CARDZU = 58,							-- 卡牌组合战力
	TIANSHENHUTI = 59,						-- 天神护体战力
	FAIRY_TREE = 60,						-- 仙树
	-- CHINESE_ZODIAC = 61,					-- 生肖系统战斗力
	MAGIC_EQUIPMENT	= 62,					-- 魔器战斗力
	MEDAL = 63,								-- 勋章战斗力
	WUSHANG_EQUIP = 64,						-- 无上神兵战斗力
	DABIAOQING2 = 65,						-- 新大表情
	-- ZHUANSHENGEQUIP = 66,				-- 转生属性战斗力
	-- TIANMING = 67,						-- 天命
	UPGRADE = 68,							-- 进阶
	NEW_FB = 69,							-- 新副本通关属性
	DAILYWORK = 70,							-- 每日必做
	UPGRADE_WING = 73,						-- 羽翼进阶
	UPGRADE_FABAO = 74,						-- 法宝进阶
	UPGRADE_SHENBING = 75,					-- 神兵进阶
	UPGRADE_LINGGONG = 76,					--
	UPGRADE_SHIZHUANG = 77,					--
	UPGRADE_FOOTPRINT = 78,					--
	UPGRADE_HALO = 79,						--
	UPGRADE_JIANZHEN = 80,					-- 剑阵进阶
	-- TEJIE = 81,					        -- 特戒战斗力
	-- ZHUANSHEN_RAND_ATTR = 82,			-- 转生装备随机属性
	RUNE_SYSTEM = 83,						-- 符文系统
	ZHUANZHI = 84,							-- 转职
	SHIZHUANGSUIT = 85,						-- 时装组合
	JINGJIE = 86,							-- 角色境界
	SHITU = 87,								-- 师徒
	JUHUN = 88,								-- 聚魂
	SUIT = 89,								-- 套装
	EQUIP_BAPTIZE = 90,						-- 装备洗炼
	SHENSHOU = 91,							-- 神兽
	LINGCHONG = 92,							-- 灵宠
	TALENT = 93,							-- 天赋
	IMP_GUARD = 94,							-- 小鬼守护
	IMP_LONGMAIN  = 95,						-- 龙脉
	EQUIP_ZHULING = 96,						-- 装备铸灵
	-- 97 战斗坐骑(废弃)
	LINGQI_MOUNT = 98,						-- 灵骑
	LINGYI = 99,							-- 灵翼
	SHENGLING_EQUIP = 100,					-- 圣灵神装
	NEW_TITLE_UPGRADE = 101,				-- 称号进阶
	SIXIANG = 102,							-- 四象
	CHENGZHANG_TIANSHU = 103,				-- 成长天书
	BOSS_CARD = 104,						-- BOSS图鉴
	ZHIZUN_EQUIP = 105,						-- 至尊装备
	ROLE_CROSS = 106,					    -- 人物跨服
	EQUIP_FUMO = 107,						-- 装备附魔
	SHENGQI = 108,							-- 圣器系统
	SEAL = 109,								-- 圣印系统
	GODDEVIL = 110,                         -- 神魔系统
	EQUIP_MELT = 111,                       -- 装备熔炼
	ZODIAC = 112,							-- 十二生肖
	XINMO = 113,							-- 心魔被动技能
	EQUIP_AWAKEN = 114,						-- 装备觉醒
	EQUIP_SPIRIT = 115,						-- 装备附灵
	EQUIP_SHENGPIN = 116,					-- 装备升品
	DIVINEWEAPON = 117,						-- 天兵
	ATTRHEAD = 118,							-- 属性头像
	BIANSHEN = 119,							-- 变身
	HUNGU = 120,							-- 魂骨
	TIANSHEN = 121,							-- 天神
	SHENLING = 122,							-- 上古神灵
	XIUZHENZHILU = 123,						-- 修真之路
	HUAKUN = 124,							-- 化鲲
	TIANJIPU = 125,							-- 天机谱
	GUILD_SKILL = 126,						-- 仙盟技能
	TIANSHENSHENQI = 127,					-- 天神神器
	BAGUA = 128,							-- 八卦阵
	BATTLE_FLAG = 129,						-- 战旗
	BATTLE_FLAG2 = 130,						-- 战旗2
	FEISHENG = 131,							-- 飞升
	STONE = 132,							-- 宝石
	POSY = 133,								-- 铭纹
	NEW_HUAKUN = 134,						-- 化鲲（新）
	LIFESOUL = 135,							-- 命魂
	XIUXIAN_ZHILV = 136,					-- 修仙之旅
	TIANSHEN_AWAKE = 137,					-- 天神觉醒
	EQUIP_TARGET = 138,						-- 装备目标战力
    WARDROBE = 139,							-- 衣橱
    
    CAPABILITY_TYPE_XIANJIE_EQUIP = 144,	-- 仙界装备
	TOTAL = 179,							-- 总战斗力，(战斗力计算方式改为所有属性算好后再套公式计算，取消各个模块分别计算再加起来的方式）
	MAX = 180,								--
}

RA_WUXINGSHENJIANG_OPERA_TYPE = {
	RA_WUXINGSHENGJIANG_REQ_INFO = 0,		-- 五行神将请求信息
	RA_WUXINGSHENGJIANG_CHALLENGE = 1,		-- 五行神将请求挑战
}

--	怪物图鉴操作类型
MONSTERCARD_OPERATE_TYPE =
{
	MONSTERCARD_OPERATE_TYPE_REQ = 0,			--请求信息
	MONSTERCARD_OPERATE_TYPE_UPLEVEL = 1,		--图鉴升级
}

-- 趣味射门
RA_FUNNY_SHOOT_OPERA_TYPE = {
	RA_FUNNY_SHOOT_OPERA_TYPE_QUERY_INFO = 0,			-- 请求活动信息
	RA_FUNNY_SHOOT_OPERA_TYPE_SHOOT = 1,				-- 射门
	RA_FUNNY_SHOOT_OPEAR_TYPE_EXCHANG_ITEM = 2,			-- 兑换物品

	RA_FUNNY_SHOOT_OPERA_TYPE_MAX = 3,
}

RA_FUNNY_SHOOT_SHOOT_TYPE = {
	RA_FUNNY_SHOOT_SHOOT_TYPE_ONE = 0,					-- 一次射门
	RA_FUNNY_SHOOT_SHOOT_TYPE_TEN = 1,					-- 十次射门
	RA_FUNNY_SHOOT_SHOOT_TYPE_FIFTY = 2,				-- 五十次射门
	RA_FUNNY_SHOOT_SHOOT_TYPE_MAX = 3,
}

--翡翠矿场
RA_MINE_TYPES = {
	RA_MINE_TYPES_INVALID = 0,
	RA_MINE_TYPES_BEGIN = 10,
	RA_MINE_TYPES_END = 10 + GameEnum.RA_MINE_TYPE_MAX_COUNT - 1,
}

--翡翠矿场
RA_MINE_OPERA_TYPE = {
	RA_MINE_OPERA_TYPE_QUERY_INFO = 0,				-- 请求活动的信息
	RA_MINE_OPERA_REFRESH = 1,						-- 换矿请求，发一个参数，1使用元宝，0不使用
	RA_MINE_OPERA_GATHER = 2,						-- 挖矿请求，发一个参数，下标，[0, 3]
	RA_MINE_OPERA_FETCH_SERVER_REWARD = 3,			-- 领取全服奖励请求，发一个参数，下标
	RA_MINE_OPERA_EXCHANGE_REWARD = 4,				-- 兑换锦囊，发一个参数，下标

}

LIMIT_TIME_DABAO_OPERA_TYPE = {						-- 请求操作类型
	LIMIT_TIME_DABAO_OPERA_TYPE_REQ_INFO = 0,		-- 请求限时打宝信息
	LIMIT_TIME_DABAO_OPERA_TYPE_FETCH_KEY = 1,		-- 领取免费钥匙
	LIMIT_TIME_DABAO_OPERA_TYPE_USE_KEY = 2,		-- 使用免费钥匙
	LIMIT_TIME_DABAO_OPERA_TYPE_MAX = 3,
}

QINGYUAN_COUPLE_HALO_REQ_TYPE = {
	QINGYUAN_COUPLE_REQ_TYPE_INFO = 0,					-- 请求信息
	QINGYUAN_COUPLE_REQ_TYPE_ACTIVITE_ICON = 1,			-- 激活图标
	QINGYUAN_COUPLE_REQ_TYPE_EQUIP = 2,					-- 装备光环
	QINGYUAN_COUPLE_REQ_TYPE_MAX = 3,
}

RA_CONTINUE_CHONGZHI_OPERA_TYPE = {
	RA_CONTINUE_CHONGZHI_OPERA_TYPE_QUERY_INFO = 0,			-- 请求活动信息
	RA_CONTINUE_CHONGZHI_OPEAR_TYPE_FETCH_REWARD = 1,		-- 获取奖励
	RA_CONTINUE_CHONGZHI_OPEAR_TYPE_FETCH_EXTRA_REWARD = 2,	-- 获取额外奖励

	RA_CONTINUE_CHONGZHI_OPERA_TYPE_MAX = 3,
}

RA_CONTINUE_CONSUME_OPERA_TYPE = {
	RA_CONTINUME_CONSUME_OPERA_TYPE_QUERY_INFO = 0,			-- 请求活动信息
	RA_CONTINUE_CONSUME_OPEAR_TYPE_FETCH_REWARD = 1,		-- 获取奖励
	RA_CONTINUE_CONSUME_OPEAR_TYPE_FETCH_EXTRA_REWARD =2 ,	-- 获取额外奖励

	RA_CONTINUE_CONSUME_OPERA_TYPE_MAX = 3,
}

SHENZHOU_WEAPON_REQ_TYPE = {
	SHENZHOU_WEAPON_REQ_TYPE_UPGRADE_WEAPON = 0,			-- 提升神器等级
	SHENZHOU_WEAPON_REQ_TYPE_UPGRADE_IDENTIFY = 1,			-- 提升鉴定等级
	SHENZHOU_WEAPON_REQ_TYPE_INDENTIFY = 2,					-- 鉴定物品 param1 背包物品下标
	SHENZHOU_WEAPON_REQ_TYPE_TAKE_OUT = 3,					-- 取出物品到背包 param1 背包物品下标
	SHENZHOU_WEAPON_REQ_TYPE_RECYCLE = 4,					-- 垃圾熔炼 param1 背包物品下标
	SHENZHOU_WEAPON_REQ_TYPE_ONE_KEY_RECYCLE = 5,			-- 一键垃圾熔炼
	SHENZHOU_WEAPON_REQ_TYPE_EXCHANGE_IDENTIFY_EXP = 6,		-- 兑换鉴定经验
	SHENZHOU_WEAPON_REQ_TYPE_DUANZAO = 7,					-- 锻造
    SHENZHOU_WEAPON_REQ_TYPE_XISUI = 8,						-- 洗髓
    SHENZHOU_WEAPON_REQ_TYPE_XISUI_SAVE = 9,				-- 保存洗髓
}

MULTI_MOUNT_REQ_TYPE = {
	MULTI_MOUNT_REQ_TYPE_SELECT_MOUNT = 0,			-- 选择使用坐骑：param1 坐骑id
	MULTI_MOUNT_REQ_TYPE_UPGRADE = 1,				-- 坐骑进阶：param1 坐骑id
	MULTI_MOUNT_REQ_TYPE_RIDE = 2,					-- 上坐骑
	MULTI_MOUNT_REQ_TYPE_UNRIDE = 3,				-- 下坐骑
	MULTI_MOUNT_REQ_TYPE_INVITE_RIDE = 4,			-- 邀请骑乘：param1 玩家id
	MULTI_MOUNT_REQ_TYPE_INVITE_RIDE_ACK = 5,		-- 回应邀请骑乘：param1 玩家id，param2 是否同意
	MULTI_MOUNT_REQ_TYPE_USE_SPECIAL_IMG = 6,		-- 请求使用幻化形象：param1特殊形象ID
	MULTI_MOUNT_REQ_TYPE_UPGRADE_EQUIP = 7,			-- 请求升级坐骑装备：param1 装备类型（下标）
}

MULTI_MOUNT_CHANGE_NOTIFY_TYPE = {
	MULTI_MOUNT_CHANGE_NOTIFY_TYPE_SELECT_MOUNT = 0,					-- 当前使用中的坐骑改变, param1 坐骑id
	MULTI_MOUNT_CHANGE_NOTIFY_TYPE_UPGRADE = 1,							-- 进阶数据改变, param1 坐骑id，param2 阶数，param3 祝福值
	MULTI_MOUNT_CHANGE_NOTIFY_TYPE_INVITE_RIDE = 2,						-- 收到别人坐骑邀请, param1 玩家ID，param2 坐骑ID
	MULTI_MOUNT_CHANGE_NOTIFY_TYPE_ACTIVE_SPECIAL_IMG = 3,				-- 激活双人坐骑特殊形象 param1特殊形象激活标记
	MULTI_MOUNT_CHANGE_NOTIFY_TYPE_USE_SPECIAL_IMG = 4,					-- 使用特殊形象 param1特殊形象id
	MULTI_MOUNT_CHANGR_NOTIFY_TYPE_UPGRADE_EQUIP = 5,					-- 坐骑装备数据改变
}


FRIENDBLESS_NOTICE_TYPE = {
	FRIENDBLESS_BLESS = 0,							-- 好友祝福请求祝福 (role_id, is_keybress)
	FRIENDBLESS_FETCH_BLESSEXP = 1,					-- 好友祝福领取祝福经验 (role_id)
	FRIENDBLESS_PRESENT_GIFT = 2,					-- 好友祝福赠送礼物 (role_id, cost_gold)
}

-- 好友祝福通知信息
FRIENDBLESS_NOTICE = {
	UPLEVEL = 0,									-- 好友升级达到要求通知 （参数1：角色等级） -- 检查列表中是否有，无则添加，有则更新
	BEBLESSED = 1,									-- 收到被祝福通知	(参数1:经验值)			-- 检查列表中是否有，无则添加，有则更新
	BLESS_SUCCESS = 2,								-- 单次祝福成功	(参数1:值为1时代表礼物已赠送) -- 删除列表中对应记录
	FETCH_SUCCESS = 3,								-- 领取经验成功  -- 领取按钮状态变化
	PRESENT_GIFT_SUCCESS = 4,						-- 赠送礼物成功  -- 赠送按钮状态变化
}

FRIENDINVITE_TYPE = {
	FRIENDINVITE_REQ_INFO = 0,						-- 好友邀请信息请求
	FRIENDINVITE_REQ_INVITE = 1,					-- 好友邀请请求邀请(role_id)
	FRIENDINVITE_AGREE_INVITE = 2,					-- 好友邀请同意邀请(role_id)
	FRIENDINVITE_REFUSE_INVITE = 3,					-- 好友邀请拒绝邀请(role_id)
	FRIENDINVITE_FETCH_REWARD = 4,					-- 好友邀请获取奖励(index)
}

-- 好友邀请通知信息
FRIENDINVITE_NOTICE = {
	REQUEST_INVITE = 0,								-- 收到邀请通知
	AGREE_REQUEST = 1,								-- 收到同意邀请
	REFUSE_REQUEST = 2,								-- 收到拒绝邀请
}


-- 碎片兑换请求信息
FRAGMENT_EXCHANGE = {
	RA_EXCHANGE_REQ_INFO = 0,						-- 碎片兑换信息请求
	RA_EXCHANGE_REWARD = 1,							-- 碎片兑换请求兑换
}
--植树活动
RA_PLANTING_TREE_OPERA_TYPE = {
	RA_PLANTING_TREE_OPERA_TYPE_RANK_INFO = 0,		-- 请求排行榜信息
	RA_PLANTING_TREE_OPERA_TYPE_TREE_INFO = 1,		-- 请求植树信息
	RA_PLANTING_TREE_OPERA_MINI_TYPE_MAP_INFO = 2, 	-- 请求小地图树的信息
}

-- 神将信息类型
SHENJIANG_INFO_TYPE = {
	SC_CHOU_SHENJIANG_MAX_TIMES = 10,				-- 神将最大十连抽

	SHENJIANG_MAX_COUNT_LIMIT = 12,					-- 神将最大数量限制
	SHENJIANG_MAX_STORE_COUNT = 48,					-- 神将抽奖背包最大数量
	SHENJIANG_MAX_LEVEL_LIMIT = 100,				-- 神将最大等级限制
	SHENJIANG_MAX_GRADE_LIMIT = 15,					-- 神将最大阶数限制
	SHENJIANG_EGG_MAX_COUNT_LIMIT = 15,				-- 神将蛋最大数限制
	SHENJIANG_REWARD_CFG_COUNT_LIMIT = 100,			-- 神将奖品配置最大数量先知
	SHENJIANG_SKILL_CFG_MAX_COUNT_LIMIT = 12,		-- 神将技能配置最大个数
	INVALID_SHENJIANG_ID = -1,						-- 无效的神将ID
	SHENJIANG_SKILL_MAX_LEVEL = 3,					-- 神将技能最大等级
}

-- 神将协议类型
SHENJIANG_REQ_TYPE = {
	SHENJIANG_REQ_TYPE_INFO = 0,					-- 神将基础信息请求
	SHENJIANG_REQ_TYPE_BACKPACK_INFO = 1,			-- 神将背包信息请求
	SHENJIANG_REQ_TYPE_SELECT_PET = 2,				-- 神将出战请求
	SHENJIANG_REQ_TYPE_CHANGE_NAME = 3,				-- 神将改名请求
	SHENJIANG_REQ_TYPE_UP_LEVEL = 4,				-- 神将升级请求
	SHENJIANG_REQ_TYPE_UP_GRADE = 5,				-- 神将升阶请求	，作废，用6551
	SHENJIANG_REQ_TYPE_CHOU = 6,					-- 神将抽取请求
	SHENJIANG_REQ_TYPE_RECYCLE_EGG = 7,				-- 神将蛋回收请求
	SHENJIANG_REQ_TYPE_PUT_REWARD_TO_KNAPSACK = 8,	-- 神将领取奖励请求
	SHENJIANG_REQ_TYPE_ACTIVE = 9,					-- 神将激活请求
	SHENJIANG_REQ_TYPE_LEARN_SKILL = 10,			-- 学习技能
	SHENJIANG_REQ_TYPE_UPGRADE_SKILL = 11,			-- 升级技能
	SHENJIANG_REQ_TYPE_FORGET_SKILL = 12,			-- 遗忘技能
	SHENJIANG_REQ_TYPE_HALO_UP_LEVEL = 13,			-- 神将光环升级
	SHENJIANG_REQ_TYPE_HALO_SEL_IMG = 14,			-- 神将光环选择
	SHENJIANG_REQ_TYPE_FLY_IMAGE = 15,				-- 神女飞升
	SHENJIANG_REQ_TYPE_SELECT_FLY_IMAGE = 16,		-- 选择飞升形象
}

SHENJIANG_SKILL_SLOT_TYPE = {
	SHENJIANG_SKILL_SLOT_TYPE_ACTIVE = 0,			-- 主动技能槽
	SHENJIANG_SKILL_SLOT_TYPE_PASSIVE_1 = 1,		-- 被动技能槽1
	SHENJIANG_SKILL_SLOT_TYPE_PASSIVE_2 = 2,		-- 被动技能槽2
	SHENJIANG_SKILL_SLOT_TYPE_COUNT = 3,			-- 技能槽总数量
}

--翻翻转
RA_FANFAN_OPERA_TYPE = {
	RA_FANFAN_OPERA_TYPE_QUERY_INFO = 0,		-- 请求活动信息
	RA_FANFAN_OPERA_TYPE_FAN_ONCE = 1,			-- 翻一次牌
	RA_FANFAN_OPERA_TYPE_FAN_ALL = 2,			-- 翻全部牌
	RA_FANFAN_OPERA_TYPE_REFRESH = 3,			-- 重置
	RA_FANFAN_OPERA_TYPE_WORD_EXCHANGE = 4,		-- 字组兑换

	RA_FANFAN_OPERA_TYPE_MAX = 5,
}

RA_FANFAN_CARD_TYPE = {
	RA_FANFAN_CARD_TYPE_BEGIN = 0,

	RA_FANFAN_CARD_TYPE_HIDDEN = 0,			-- 隐藏卡牌类型
	RA_FANFAN_CARD_TYPE_ITEM_BEGIN = 100,	-- 物品卡牌类型起始值
	RA_FANFAN_CARD_TYPE_WORD_BEGIN = 200,	-- 字组卡牌类型起始值

	RA_FANFAN_CARD_TYPE_MAX = 5,
}

--循环充值
RA_CIRCULATION_CHONGZHI_OPERA_TYPE = {
	RA_CIRCULATION_CHONGZHI_OPERA_TYPE_QUERY_INFO = 0,			-- 请求活动信息
	RA_CIRCULATION_CHONGZHI_OPEAR_TYPE_FETCH_REWARD = 1,		-- 获取奖励
	RA_CIRCULATION_CHONGZHI_OPEAR_TYPE_FETCH_EXTRA_REWARD = 2,	-- 获取额外奖励

	RA_CIRCULATION_CHONGZHI_OPERA_TYPE_MAX = 3,
}

-- 仙盟召集
GUILD_CALL_OPERA_TYPE = {
	GUILD_CALL_CALL = 0,				-- 仙盟召集 召集
	GUILD_CALL_FOLLOW = 1,				-- 仙盟召集 跟随
}

MAGIC_CARD = {
	MAGIC_CARD_SLOT_TYPE_LIMIT_COUNT = 4,				-- 魔卡位置最大种类限制
	MAGIC_CARD_MAX_LIMIT_COUNT = 27,					-- 魔卡最大卡牌数量
	MAGIC_CARD_CHOU_CARD_LIMIT_REWARD_COUNT = 16,		-- 魔卡抽卡奖品最大数量
	MAGIC_CARD_LIMIT_STRENGTH_LEVEL_MAX = 10,			-- 魔卡最大强化等级
}

MAGIC_CARD_COLOR_TYPE = {
	MAGIC_CARD_COLOR_TYPE_BLUE = 0,						-- 蓝色
	MAGIC_CARD_COLOR_TYPE_PURPLE = 1,					-- 紫色
	MAGIC_CARD_COLOR_TYPE_ORANGE = 2,					-- 橙色
	MAGIC_CARD_COLOR_TYPE_RED = 3,						-- 红色

	MAGIC_CARD_COLOR_TYPE_COLOR_COUNT = 4,
}

--灵晶
ROLE_SHUIJING_REQ_TYPE = {
	ROLE_SHUIJING_REQ_TYPE_UPGRADE_SHUIJING = 0,						    --升级水晶：param1 水晶id
	ROLE_SHUIJING_REQ_TYPE_UPGRADE_SKILL = 1,						        --升级水晶技能：param1 水晶id param2 技能类型
	ROLE_SHUIJING_REQ_TYPE_USE_SKILL = 2,									-- 使用水晶技能：param1 水晶id param2 技能类型
	ROLE_SHUIJING_REQ_TYPE_SCORE_EXCHANGE = 3,							    --水晶积分兑换：param1 物品索引,param2 物品数量
}

ROLE_SHUIJING_CHANGE_NOTIFY_TYPE = {
	ROLE_SHUIJING_CHANGE_NOTIFY_TYPE_LEVEL_CHANGE = 0,					    --水晶等级改变：param1 水晶id，param2 等级
	ROLE_SHUIJING_CHANGE_NOTIFY_TYPE_SKILL_LEVEL_CHANGE = 1,				-- 技能等级改变：param1 水晶id param2 技能类型，param3 技能等级
	ROLE_SHUIJING_CHANGE_NOTIFY_TYPE_USE_SKILL = 2,							--使用的技能：param1 技能ID
}

-- 个性聊天框
CHAT_WIN_REQ_TYPE = {
	PERSONALIZE_WINDOW_ALL_INFO = 0,
	PERSONALIZE_WINDOW_CONSUME_ITEM = 1,			-- 1,窗口类型 2,对应seq, 3,物品ID
	PERSONALIZE_WINDOW_USE_RIM = 2,					-- 1,窗口类型 2,对应seq

	PERSONALIZE_WINDOW_ACTIVE_BUBBLE_RIM_SUIT = 3,	-- 激活气泡框一个套装部位，参数1套装seq，参数2套装部位part
	PERSONALIZE_WINDOW_ACTIVE_BUBBLE_RIM = 4,		-- 激活气泡框，参数1气泡框seq
	PERSONALIZE_WINDOW_USE_BUBBLE_RIM = 5,			-- 使用气泡框，参数1气泡框seq
	PERSONALIZE_WINDOW_BUBBLE_ITEM = 6,				-- 1,对应seq, 2,物品ID
}

-- 等级类限制类型
OPENLEVEL_LIMIT_TYPE = {
	OPENLEVEL_LIMIT_TYPE_WORLD = 0, --世界
	OPENLEVEL_LIMIT_TYPE_CAMP = 1, --阵营
	OPENLEVEL_LIMIT_TYPE_SCENE = 2, --场景
	OPENLEVEL_LIMIT_TYPE_TEAM = 3,--队伍
	OPENLEVEL_LIMIT_TYPE_GUILD = 4,--仙盟
	OPENLEVEL_LIMIT_TYPE_SINGLE = 5,--私聊
	OPENLEVEL_LIMIT_TYPE_SEND_MAIL = 6,--邮件
	OPENLEVEL_LIMIT_TYPE_CROSS_CHAT = 7,--跨服
	OPENLEVEL_LIMIT_TYPE_CREATE_TEAM = 8,--组队
	OPENLEVEL_LIMIT_TYPE_ZHANDUI3V3 = 9, --战队
	OPENLEVEL_LIMIT_TYPE_SPEAKER = 10,  --喇叭

	OPENLEVEL_LIMIT_TYPE_MAX = 11
}

--聊天映射表
CHANNEL_LIMINT_TYPE = {
	[OPENLEVEL_LIMIT_TYPE.OPENLEVEL_LIMIT_TYPE_WORLD] = CHANNEL_TYPE.WORLD,
	[OPENLEVEL_LIMIT_TYPE.OPENLEVEL_LIMIT_TYPE_CAMP] = CHANNEL_TYPE.CAMP,
	[OPENLEVEL_LIMIT_TYPE.OPENLEVEL_LIMIT_TYPE_SCENE] = CHANNEL_TYPE.SCENE,
	[OPENLEVEL_LIMIT_TYPE.OPENLEVEL_LIMIT_TYPE_TEAM] = CHANNEL_TYPE.TEAM,
	[OPENLEVEL_LIMIT_TYPE.OPENLEVEL_LIMIT_TYPE_GUILD] = CHANNEL_TYPE.GUILD,
	[OPENLEVEL_LIMIT_TYPE.OPENLEVEL_LIMIT_TYPE_SINGLE] = CHANNEL_TYPE.PRIVATE,
	[OPENLEVEL_LIMIT_TYPE.OPENLEVEL_LIMIT_TYPE_SEND_MAIL] = CHANNEL_TYPE.EMAIL,
	[OPENLEVEL_LIMIT_TYPE.OPENLEVEL_LIMIT_TYPE_CREATE_TEAM] = CHANNEL_TYPE.ZUDUI,
	[OPENLEVEL_LIMIT_TYPE.OPENLEVEL_LIMIT_TYPE_ZHANDUI3V3] = CHANNEL_TYPE.ZHANDUI3V3,
	[OPENLEVEL_LIMIT_TYPE.OPENLEVEL_LIMIT_TYPE_SPEAKER] = CHANNEL_TYPE.SPEAKER,
	[OPENLEVEL_LIMIT_TYPE.OPENLEVEL_LIMIT_TYPE_CROSS_CHAT] = CHANNEL_TYPE.CROSS,
}

-- 伤害的飘字类型
FIGHT_TEXT_TYPE =
{
	NORMAL = 0,			-- 普通
	BAOJU = 1,			-- 宝具
	NVSHEN = 2,			-- 女神

	SHENSHENG = 10,		-- 神圣
}

-- 御魂
MITAMA_REQ_TYPE = {
	MITAMA_REQ_TYPE_ALL_INFO = 0,						-- 请求所有信息
	MITAMA_REQ_TYPE_UPGRADE = 1,						-- 升级御魂
	MITAMA_REQ_TYPE_TASK_FIGHTING = 2,					-- 出征
	MITAMA_REQ_TYPE_TASK_AWARD = 3,						-- 领取出征奖励
	MITAMA_REQ_TYPE_EXCHANGE_ITEM = 4,					-- 兑换物品

	MITAMA_REQ_TYPE_MAX = 5,
}

FAIRY_TREE_REQ_TYPE = {
	FAIRY_TREE_REQ_TYPE_ALL_INFO = 0,
	FAIRY_TREE_REQ_TYPE_FETCH_MONEY_REWARD = 1,			-- 领取在线金钱奖励
	FAIRY_TREE_REQ_TYPE_FETCH_GIFT_REWARD = 2,			-- 领取在线礼包奖励
	FAIRY_TREE_REQ_TYPE_UPLEVEL = 3,					-- 升级
	FAIRY_TREE_REQ_TYPE_UPGRADE = 4,					-- 进阶
	FAIRY_TREE_REQ_TYPE_DRAW_ONCE = 5,					-- 抽奖1次
	FAIRY_TREE_REQ_TYPE_DRAW_TEN_TIMES = 6,				-- 抽奖10次
}

-- 跨服充值排行榜
CROSS_RA_CHONGZHI_RANK_TYPE = {
	CS_CROSS_RA_CHONGZHI_RANK_REQ_TYPE_INFO = 0,		-- 请求活动期间玩家充值信息
}

-- 特殊表情类型
TESHUFACE_TYPE = {
	DABIAOQING2_REQ_INFO = 0,							-- 请求信息
	DABIAOQING2_REQ_ACTIVE = 1,							-- 激活形象
}

-- 奔跑吧仙友
RA_RUNNINGMAN_OPERA_TYPE = {
	RA_RUNNINGMAN_OPERA_TYPE_TRIBUTE_INFO = 0,
	RA_RUNNINGMAN_OPERA_TYPE_RANK_INFO = 1,
	RA_RUNNINGMAN_OPERA_TYPE_FETCH_SERVER_REWARD = 2,
	RA_RUNNINGMAN_OPERA_TYPE_MAX = 3,
}

-- 温泉动作类型
HOTSPRING_ACTION_TYPE =	{
	HOTSPRING_ACTION_TYPE_SHUANGXIU = 0,				-- 双修请求
	HOTSPRING_ACTION_TYPE_DISMISS_SHUANGXIU = 1,		-- 取消双修
	HOTSPRING_ACTION_TYPE_SNOW = 2,						-- 扔雪球
	HOTSPRING_ACTION_TYPE_INTIMIDATION = 3,				-- 恐吓
	HOTSPRING_ACTION_TYPE_ANMO = 4,				 		-- 按摩
}

HOTSPRING_BOAT_TYPE = {
	NONE = 0, 				--取消当前动作
	SHUANGXIU = 1, 			--双休
	ANMO = 2, 				--按摩
}

--双修类型
SHUANGXIU_TYPE = {
	IS_SHUANGXIU = 1,									-- 正在双修
	NO_SHUANGXIU = 0, 									-- 没有双修
}

--按摩类型
ANMO_TYPE = {
	IS_ANMO = 1,									-- 正在按摩
	NO_ANMO = 0, 									-- 没有按摩
}

CROSS_COMMON_OPERA_REQ =
{
	CROSS_COMMON_OPERA_REQ_INVALID = 0,
	CROSS_COMMON_OPERA_REQ_1VN_INFO = 1,				-- 1VN个人信息
	CROSS_COMMON_OPERA_REQ_1VN_SIGN_UP = 2,				-- 1VN报名
	CROSS_COMMON_OPERA_REQ_MAX = 3,
}

CROSS_1VN_FIGHTING_RESULT_TO_ROLE_TYPE = {
	CROSS_1VN_FIGHTING_RESULT_TO_ROLE_TYPE_WINNER = 0,			-- 胜利
	CROSS_1VN_FIGHTING_RESULT_TO_ROLE_TYPE_LOSER = 1,			-- 失败
	CROSS_1VN_FIGHTING_RESULT_TO_ROLE_TYPE_WAITING_OTHER = 2,	-- 自己死亡，等待其他人战斗结果
}

WUSHANG_EQUIP_REQ_TYPE = {
	WUSHANG_REQ_TYPE_ALL_INFO = 0,						-- 所有信息请求
	WUSHANG_REQ_TYPE_PUT_ON_EQUIP = 1,					-- 穿装备
	WUSHANG_REQ_TYPE_TAKE_OFF_EQUIP = 2,				-- 脱装备
	WUSHANG_REQ_TYPE_JIFEN_EXCHANGE = 3,				-- 积分兑换
	WUSHANG_REQ_TYPE_STRENGTHEN = 4,					-- 强化
	WUSHANG_REQ_TYPE_UP_STAR = 5,						-- 升星
	WUSHANG_REQ_TYPE_GLORY_EXCHANGE = 6,				-- 荣耀兑换
}

-- 神兵图鉴
CARDZU_REQ_TYPE = {
	CARDZU_REQ_TYPE_CHOU_CARD = 0,						-- 抽卡请求
	CARDZU_REQ_TYPE_HUALING = 1,						-- 化灵请求
	CARDZU_REQ_TYPE_LINGZHU = 2,						-- 灵铸请求
	CARDZU_REQ_TYPE_ACTIVE_ZUHE = 3,					-- 激活卡牌组合
	CARDZU_REQ_TYPE_UPGRADE_ZUHE = 4,					-- 升级卡牌组合
}

BUY_LINGLI_TYPE = {
	BUY_LINGLI_TYPE_COIN = 0,							-- 铜币单抽
	BUY_LINGLI_TYPE_GOLD = 1,							-- 元宝单抽
	BUY_LINGLI_TYPE_GOLD10 = 2,							-- 元宝十连抽
}
MAGIC_EQUIPMENT_CHANGE_NOTIFY_TYPE = {
	MAGIC_EQUIPMENT_CHANGE_NOTIFY_TYPE_QUALITY_LEVEL = 0,	 	-- 品质等级改变：param1 魔器类型，param2 魔器品质等级， param3 吞噬进度
	MAGIC_EQUIPMENT_CHANGE_NOTIFY_TYPE_STRENGTHEN_LEVEL = 1,	-- 锻造等级改变：param1 魔器类型，param2 魔器锻造等级， param3 锻造值（祝福值）
	MAGIC_EQUIPMENT_CHANGE_NOTIFY_TYPE_EMBED = 2,				-- 镶嵌魔石：param1 魔器类型，param2 魔石孔位， param3 魔石下标（配置里的）
	MAGIC_EQUIPMENT_CHANGE_NOTIFY_TYPE_TAKE_OFF = 3,			-- 卸下魔石：param1 魔器类型，param2 魔石孔位， param3 魔石下标（配置里的）
}


------- 跑酷  --------------------------------
	-- 状态
	ACTIVITY_PHASE = {
		ACTIVITY_PHASE_INVALID = 0,						--跑酷未开启
		ACTIVITY_PHASE_PREPARE = 1,						--在起点准备
		ACTIVITY_PHASE_RUNNING = 2,						--开跑
	}

	-- 原因
	YUJIANQIUMINGSHAN_REASON = {
		REASON_DEFAULT = 0,								-- 默认
		REASON_ADD_SKILL = 1,							-- 获得技能
		REASON_UPDATE = 2,								-- 定时更新
		REASON_PERFORM_SKILL = 3,						-- 释放技能
		REASON_CIRCLE_END = 4,							-- 跑完一圈
	}

	-- 技能效果
	YUJIANQIUMINGSHAN_SKILLIDX = {
		YUJIANQIUMINGSHAN_SKILLIDX_ZHULING = 0,			-- 注灵 effectparam:注灵时的速度类型
		YUJIANQIUMINGSHAN_SKILLIDX_JUMP = 1,			-- 跳跃
		YUJIANQIUMINGSHAN_SKILLIDX_XUANYUN = 2,			-- 眩晕
		YUJIANQIUMINGSHAN_SKILLIDX_JINSHEN = 3,			-- 金身
		YUJIANQIUMINGSHAN_SKILLIDX_HUANLING = 4,		-- 唤灵
		YUJIANQIUMINGSHAN_SKILLIDX_JISU = 5,			-- 极速 effectparam:加多少
		YUJIANQIUMINGSHAN_SKILLIDX_XIANGJIAOPI = 6,		-- 香蕉皮 effectparam:减多少
		YUJIANQIUMINGSHAN_SKILLIDX_PAOPAO = 7,			-- 泡泡
		YUJIANQIUMINGSHAN_SKILLIDX_MAX = 8,				-- 最大数
	}
-----------------------------------------------

-- 爱情印记请求类型
LoveStampOperaType = {
	LOVE_STAMP_OPERA_TYPE_ALL_INFO = 0,											-- 爱情印记全部信息
	LOVE_STAMP_OPERA_TYPE_NORMAL_UP_STAR = 1,									-- 普通升星
	LOVE_STAMP_OPERA_TYPE_AUTO_UP_STAR = 2,										-- 一键升星
	LOVE_STAMP_OPERA_TYPE_USE_IMG = 3,											-- 切换形象   param1:形象id~
}
---------------------------------------


--答题协议通知原因
NOTIFY_ANSWER_REASON =	{
	NOTIFY_REASON_DEFAULT = 0,							-- 无
	NOTIFY_REASON_END = 1,								-- 活动结束
}

--答题通知原因
NOTIFY_QUESTION_STATE_REASON = {
	NOTIFY_QUESTION_STATE_REASON_INVALID = 0,
	NOTIFY_QUESTION_STATE_REASON_START = 1,				-- 活动开始
	NOTIFY_QUESTION_STATE_REASON_CLOSE = 2,				-- 活动结束
	NOTIFY_QUESTION_STATE_REASON_UPDATE = 3, 			-- 活动更新
}

--答题状态
QUESTION_STATE = {
	QUESTION_STATE_INVALID = 0,
	QUESTION_STATE_READY = 1,							-- 准备
	QUESTION_STATE_READ = 2,							-- 读题阶段
	QUESTION_STATE_ANSWER = 3,							-- 答题阶段
}

-- 技能重置位置类型
SKILL_RESET_POS_TYPE = {
	SKILL_RESET_POS_TYPE_INVALID = 0,
	SKILL_RESET_POS_TYPE_CHONGFENG = 1,					-- 冲锋
	SKILL_RESET_POS_TYPE_JUMP = 2,						-- 跳跃
	SKILL_RESET_POS_TYPE_FOUNTAIN = 3,					-- 喷泉
	SKILL_RESET_POS_TYPE_CAPTURE = 4,					-- 捕抓
	SKILL_RESET_POS_TYPE_JITUI = 5,						-- 击退
	SKILL_RESET_POS_TYPE_TOWER_DEFEND_ZHENFA = 6,		-- 塔防阵法
	SKILL_RESET_POS_TYPE_SHUNYI = 7,					-- 瞬移
	SKILL_RESET_POS_TYPE_YUEJI = 8,						-- 跃击
	SKILL_RESET_POS_TYPE_LA = 9,						-- 拉
	SKILL_RESET_POS_TYPE_YOULI = 10,					-- 游历
	SKILL_RESET_POS_TYPE_ZHENYI = 11,					-- 帧移
	SKILL_RESET_POS_TYPE_SHANSHUO = 12,					-- 闪烁
	SKILL_RESET_POS_TYPE_CLASH = 13,					-- 冲撞
	SKILL_RESET_POS_TYPE_BACKSTAB = 14,					-- 前置背刺
	SKILL_RESET_POS_TYPE_BLACK_BACKSTAB = 15,			-- 后置背刺
	SKILL_RESET_POS_TYPE_GRADUALLY_LA = 16,				-- 逐渐拉
	SKILL_RESET_POS_TYPE_MAX = 17,
}

-- 七天投资计划请求类型
SEVENDAY_INVESTPALN_REQ_TYPE = {
	DAY_REWARD = 1,
	VIP_REWARD = 2,
}

MAX_PLAN_COUNT = 3 					-- 投资计划最大数
MAX_PLAN_GRADE_REWARD_COUNT = 64	-- 投资计划奖励最大数

-- 首充请求类型
RECHARGEREWARD_REQ_TYPE = {
	FIRST_RECHARGE = 0,									-- 首充
	TOTAL_REWARD = 1,									-- 今日首充累计奖励
	DAILY_RECHARGE = 2,									-- 今日首充
	DAILY_TOTAL_RECHARGE = 3,                           -- 每日累充
	TOTAL_RECHARGE_DAY = 4,	                            -- 每日累充天数
	DAILY_TOTAL_CONSUME_INFO = 5,						-- 每日消费信息
	DAILY_TOTAL_CONSUME = 6,                  			-- 每日消费
	TOTAL_CONSUME_DAY = 7,                    			-- 每日消费天数
}

--黄金会员请求类型
GOLD_MEMBER_REQ_TYPE =
{
	OPERA_TYPE_ACTIVE = 0,               	 -- 激活
	OPERA_TYPE_FETCH_DAY_REWARD = 1,         -- 领取每日奖励
	OPERA_TYPE_CONVERT_GOLD = 2,			 -- 兑换元宝
	OPERA_TYPE_CONVERT_ITEM = 3,			 -- 兑换道具
	OPERA_TYPE_CONVERT_SHOP = 4,             -- 兑换商店

}

RAND_LOTTERY_DRAW =
{
	RA_LUCKY_DRAW_OPERA_TYPE_QUERY_INFO = 0,
	RA_LUCKY_DRAW_OPERA_TYPE_BUY_ITEM = 1,
}

RA_TOTAL_CHONGZHI_DAY_OPERA_TYPE =
{
	RA_TOTAL_CHONGZHI_DAY_OPERA_TYPE_QUERY_INFO = 0,
	RA_TOTAL_CHONGZHI_DAY_OPERA_TYPE_FETCH_REWARD = 1,
}

RA_DAY_CHONGZHI_FANLI_OPERA_TYPE =
{
	RA_DAY_CHONGZHI_FANLI_OPERA_TYPE_QUERY_INFO = 0,
	RA_DAY_CHONGZHI_FANLI_OPERA_TYPE_FETCH_REWARD = 1,
}

RA_SINGLE_CHONGZHI_OPERA_TYPE =
{
	RA_SINGLE_CHARGE_OPERA_TYPE_QUERY_INFO = 0,
	RA_SINGLE_CHARGE_OPERA_TYPE_FETCH_REWARD = 1,
}

RA_CHONGZHI_HAOLI_OPERA_TYPE =
{
	RA_CHONGZHI_HAOLI_OPERA_TYPE_QUERY_INFO = 0,
	RA_CHONGZHI_HAOLI_OPERA_TYPE_GET_REWARD_ITEM = 1,
	RA_CHONGZHI_HAOLI_OPERA_TYPE_MAX = 2,
}

UPGRADE_TYPE_MINGHUN =
{
	WING = 0,
	FABAO = 1,
	LINGCHONG = 2,
	SHENWU = 3,
	LINGGONG = 4,
	LINGQI = 5,
	LINGYI = 6,
    MOUNT = 7,
    MAX = 8
}

-- 返回跨服后的处理标记
BACK_FROM_CROSS_SERVER_FLAG =
{
	ACTIVITY_HALL_MIN_PANEL_FORBID_OPEN = 1,	-- 活动小面板提示
}

-- 飞仙boss
PERSONAL_BOSS_OPERA_TYPE =
{
	OPERA_TYPE_PERSONAL_BOSS_ENTER = 0,       -- 进入请求  param为BOSS层数
	OPERA_TYPE__PERSONAL_BOSS_INFO_REQ = 1,   -- 信息请求
	OPERA_TYPE_FEIXIAN_BOSS_BUY_REQ = 2,      -- 购买请求
}

-- 飞仙装备
ZHUANSHENG_REQ_TYPE =
{
	ZHUANSHENG_REQ_TYPE_ALL_INFO = 0,
	ZHUANSHENG_REQ_TYPE_OTHER_INFO = 2,
	ZHUANSHENG_REQ_TYPE_UPLEVEL = 3,				-- 升级请求
	ZHUANSHENG_REQ_TYPE_CHANGE_XIUWEI = 4,			-- 兑换修为请求
	ZHUANSHENG_REQ_TYPE_TAKE_OFF_EQUIP = 6,			-- 脱装备
}

EXERCISE_ROOM_OPERA_TYPE =
{
	ALL_INFO_REQ = 0,		  						-- 所有信息
	EXERCISE_REQ = 1,          						-- 练功
	FETCH_REWARD = 2,		  						-- 领取奖励
	AUTO_RECYCLE = 3,								-- 智能回收
}

JIEZHEN_OPERA_TYPE = {
	JIEZHEN_OPERA_TYPE_ALL_INFO = 0,				-- 所有信息
	JIEZHEN_OPERA_TYPE_JIQI_REQ = 1,				-- 集气
	JIEZHEN_OPERA_TYPE_INVITE_REQ = 2,				-- 邀请target_uid
	JIEZHEN_OPERA_TYPE_BLESS_REQ = 3,				-- 祝福target_uid
	JIEZHEN_OPERA_TYPE_KICKOUT_REQ = 4,				-- 踢人target_uid
	JIEZHEN_OPERA_TYPE_FETCH_REWARD = 5,			-- 领取精华奖励
	JIEZHEN_OPERA_TYPE_ACCEPT_INVITE = 6,			-- 接受邀请target_uid
}

TEJIE_OPERA_TYPE =
{
	TEJIE_OPERA_TYPE_ALL_INFO = 0,		      		-- 所有信息
	TEJIE_OPERA_TYPE_ACTIVE_REQ = 1,                -- 激活
	TEJIE_OPERA_TYPE_CHARGE_REQ = 2,		     	-- 装备技能
	TEJIE_OPERA_TYPE_DEVELOP_REQ = 3,		     	-- 培养
	TEJIE_OPERA_TYPE_XILIAN_REQ = 4,		      	-- 洗炼
	TEJIE_OPERA_TYPE_CONVERT_REQ = 5,		     	-- 替换
	TEJIE_OPERA_TYPE_CONTINUE_CONVERT = 6,			-- 连续培养
	TEJIE_OPERA_TYPE_MAX = 6,
}

MIJING_OPERA_TYPE = {
	MIJING_OPERA_TYPE_BUY = 0,						-- 购买永久免费扫荡
	MIJING_OPERA_TYPE_SWEEP = 1,					-- 扫荡
	MIJING_OPERA_TYPE_ALL_INFO = 2,					-- 请求所有信息
}

-- 秘境扫荡类型
SWEEP_MODULE_TYPE = {
	SMT_DAOBAO_BOSS = 0,							-- 打宝BOSS
	SMT_TEAM_TOWER = 1,								-- 组队塔防
	SMT_FEIXIAN_BOSS = 2,							-- 飞仙BOSS
	SMT_NEUTRAL_AREA = 4,							-- 中立战场
	SMT_SHENZHOU_WEAPON = 5,						-- 藏宝仙地
	SMT_MULTI_FB = 6,								-- 多人爬塔
	SMT_BABY_BOSS = 7,								-- 萌宝BOSS
	SMT_COUNT = 8,
}

CROSS_GUILDBATTLE = {
	CROSS_GUILDBATTLE_MAX_FLAG_IN_SCENE = 3,		-- 最大旗子数在场景中
	CROSS_GUILDBATTLE_MAX_SCENE_NUM = 6,			-- 帮派场景个数
	CROSS_GUILDBATTLE_MAX_GUILD_RANK_NUM = 5,		-- 跨服帮派战前5
}


--跨服帮派战请求
CROSS_GUILDBATTLE_OPERATE = {
	CROSS_GUILDBATTLE_OPERATE_REQ_INFO = 0,
	CROSS_GUILDBATTLE_OPERATE_FETCH_REWARD = 1,
	CROSS_GUILDBATTLE_OPERATE_HAND_OUT_REWARD = 2, 	-- 分发奖励
	CROSS_GUILDBATTLE_OPERATE_GOTO_SCENE = 3,		-- 跳转到其他卫城
	CROSS_GUILDBATTLE_OPERATE_ALLOCATE_REQ_INFO = 4,-- 奖励分配信息
	CROSS_GUILDBATTLE_OPERATE_ALLOCATE_RECORD = 5, 	-- 分配奖励记录
}

-- 装备随机属性
EQUIPMENT_XIANPIN_ATTRIBUTE = {										-- 装备仙品属性类型
	E_XIANPIN_ADD_PER_HP = 1,										-- 气血+%d%%
	E_XIANPIN_ADD_PER_FANG_YU = 2,									-- 物防+%d%%
	E_XIANPIN_ADD_PER_FA_FANG_YU = 3,								-- 法防+%d%%
	E_XIANPIN_ADD_PER_MIANSHANG = 4,								-- 免伤+%d%%
	E_XIANPIN_ADD_PER_ITEM_DROP = 5,								-- 物品掉率+5%
	E_XIANPIN_ADD_HP_OF_ROLE_LEVEL = 6,								-- 每3级气血+20
	E_XIANPIN_ADD_FANG_YU_OF_ROLE_LEVEL = 7,						-- 每3级物防+2
	E_XIANPIN_ADD_FA_FANG_YU_OF_ROLE_LEVEL = 8,						-- 每3级法防+2
	E_XIANPIN_ADD_PER_POFANG = 9,									-- 破防+%d%%
	E_XIANPIN_ADD_PER_SHANGHAI = 10,								-- 伤害+%d%%
	E_XIANPIN_ADD_PER_GONGJI = 11,									-- 攻击+%d%%
	E_XIANPIN_ADD_PER_COPPER = 12,									-- 铜币掉落+4%
	E_XIANPIN_ADD_FUJIA_SHANGHAI_OF_ROLE_LEVEL = 13,				-- 每3级附加伤害+1
	E_XIANPIN_ADD_GONGJI_OF_ROLE_LEVEL = 14,						-- 每3级攻击+2
}


-- 洞窟BOSS
DONGKU_BOSS_OPERA_TYPE = {
		DONGKU_BOSS_OPERA_TYPE_ENTER = 0,			            --进入请求
		DONGKU_BOSS_OPERA_TYPE_ALL_BOSS_INFO = 1,               --信息请求
		DONGKU_BOSS_OPERA_TYPE_LAYER_BOSS_INFO = 2,		        --单层信息
}

-- VipBOSS
VIP_BOSS_OPERA_TYPE = {
		VIP_BOSS_OPERA_TYPE_ENTER = 0,				            --进入请求
		VIP_BOSS_OPERA_TYPE_ALL_BOSS_INFO = 1,		            --所有信息
		VIP_BOSS_OPERA_TYPE_LAYER_BOSS_INFO = 2,	            --单层信息

		-- VIP_BOSS_OPERA_TYPE_SET_CONCERN = 3,					-- 设置关注, param1 层数 param2 BOSS id  param3 是否关注（1或0）
		-- VIP_BOSS_OPERA_TYPE_RECEIVE_XIANLI = 4,					-- 领取仙力，param1 索引

		VIP_BOSS_OPERA_TYPE_SET_EXTRA_REWARDS_STATE = 5,		-- 开关必爆特权，param1 (1->开, 0->关)
		VIP_BOSS_OPERA_TYPE_UPGRADE_EXTRA_REWARDS_LEVEL = 6,	-- 升级必爆特权等级
		VIP_BOSS_OPERA_TYPE_BUY_TIMES = 7,						-- 购买必爆特权次数
}

OFFLINE_REST_INFO_NOTIFY_REASON = {
	BUY_TIME = 0,					-- 购买时间
	LOGIN = 1,						-- 离线挂机打坐
	GET_EXP_CHANGE = 2,				-- 经验系数改变
	ONLINE_REWARD = 3,				-- 在线挂机打坐
}

-- 红包
GUILD_RED_POCKET_OPERATE_TYPE = {
	GUILD_RED_POCKET_OPERATE_INFO_LIST = 0,                 -- 仙盟红包 请求红包列表信息
	GUILD_RED_POCKET_OPERATE_DISTRIBUTE	= 1,				-- 仙盟红包 请求分发红包
	GUILD_RED_POCKET_OPERATE_GET_POCKET	= 2,				-- 仙盟红包 请求获取红包
	GUILD_RED_POCKET_OPERATE_DISTRIBUTE_INFO = 3,			-- 仙盟红包 请求分发详情
}

-- 红包领取状态
GUILD_RED_POCKET_STATUS = {
    INVALID = 0 ,
	UN_DISTRIBUTED = 1,										-- 未发放
	DISTRIBUTED = 2,										-- 已发放
	DISTRIBUTE_OUT = 3,                                     -- 发放完
}

--仙盟红包子类型
GUILD_RED_POCKET_SUB_TYPE = {
  INVALID = 0,
  COMMON = 1,              --普通红包
  RAND = 2,              --随机红包

  TYPE_MAX = 3,
}

EQUIP_GRID_STRENGTH_OPERA_TYPE = {
	EQUIP_GRID_STRENGTH_OPERA_TYPE_ALL_INFO = 0,			-- 装备格子所有信息
	EQUIP_GRID_STRENGTH_OPERA_TYPE_STRENGTH = 1,			-- 装备格子强化 param_1:部位
	EQUIP_GRID_STRENGTH_OPERA_TYPE_ACTIVE_SRTEBGTH = 2,		-- 激活装备强化等级属性   -- param2 肉身索引
	EQUIP_GRID_STRENGTH_OPERA_TYPE_ACTIVE_XIANQI = 3,		-- 激活仙器强化等级属性   -- param2 肉身索引
}

SUIT_OPERA_TYPE = {
	SUIT_OPERA_TYPE_SUIT_ALL_INFO = 0,					    -- 套装所有信息
	SUIT_OPERA_TYPE_SUIT_FORGE = 1,							-- 套装锻造
	SUIT_OPERA_TYPE_SUIT_SMELT_LEVELUP = 2,    			    -- 套装熔炼升级
	SUIT_OPERA_TYPE_SUIT_SMELT_RETURN = 3,    				-- 套装熔炼返还
}

WELFARE_QIFU_TYPE =	{									    -- 祈福类型
	QIFU_TYPE_BUYCOIN = 1,									-- 祈福模块中的摇钱
	QIFU_TYPE_BUYEXP = 2,									-- 祈福模块中的摇经验
}

--6776 协议
QINGYUAN_OPERA_TYPE = {
	QINGYUAN_OPERA_TYPE_WEDDING_YUYUE          = 0,		-- 婚礼预约 param1 预约下标
	QINGYUAN_OPERA_TYPE_WEDDING_INVITE_GUEST   = 1,	 	-- 邀请宾客 param1 宾客uid
	QINGYUAN_OPERA_TYPE_WEDDING_REMOVE_GUEST   = 2,		-- 移除宾客 param1 宾客uid
	QINGYUAN_OPERA_TYPE_WEDDING_BUY_GUEST_NUM  = 3,	 	-- 购买宾客数量
	QINGYUAN_OPERA_TYPE_WEDDING_GET_YUYUE_INFO = 4,	 	-- 获取预约信息
	QINGYUAN_OPERA_TYPE_WEDDING_GET_ROLE_INFO  = 5,	 	-- 获取玩家信息
	QINGYUAN_OPERA_TYPE_WEDDING_YUYUE_FLAG     = 6,		-- 获取婚礼预约标记
	QINGYUAN_OPERA_TYPE_WEDDING_YUYUE_RESULT   = 7,     -- 是否同意婚礼预约时间
	QINGYUAN_OPERA_TYPE_BUY_QINGYUAN_FB        = 8,		-- 购买副本次数
	QINGYUAN_OPERA_TYPE_BUY_QINGYUAN_FB_REQ    = 9,	 	-- 请求对方购买次数
	QINGYUAN_OPERA_TYPE_LOVER_INFO_REQ         = 10,	-- 请求伴侣信息
	QINGYUAN_OPERA_TYPE_LOVER_TITLE_INFO       = 11,    -- 仙侣称号信息
	QINGYUAN_OPERA_TYPE_FETCH_LOVER_TITLE      = 12,    -- 领取仙侣称号
	QINGYUAN_OPERA_TYPE_BUY_AND_PUTON_EQUIP    = 13,	-- 购买切穿戴情缘装备
	QINGYUAN_OPERA_TYPE_XUNYOU_ROLE_INFO       = 14,	-- 获取玩家巡游信息
	QINGYUAN_OPERA_TYPE_XUNYOU_SA_HONGBAO      = 15,	-- 巡游撒红包 param1:is_buy
	QINGYUAN_OPERA_TYPE_XUNYOU_GIVE_FLOWER     = 16,	-- 巡游购买撒花次数 parap1:is_buy
	QINGYUAN_OPERA_TYPE_XUNYOU_OBJ_POS         = 17,	-- 获取巡游对象当前坐标 param1:seq
	QINGYUAN_OPERA_TYPE_WEDDING_SCHEDULE_REQ   = 18,	-- 婚宴排期信息请求
	HUNYAN_OPERA_TYPE_APPLY          = 19,				-- 申请参加婚礼
	HUNYAN_GET_APPLICANT_INFO        = 20,				-- 获取申请者信息
	QINGYUAN_OPERA_TYPE_REWARD_LIMIT_INFO     = 21,				-- 请求结婚奖励限制信息
	QINGYUAN_INFO_TYPE_SEND_XUNYOU_FLAG		  = 22,					--请求巡游飘字
	QINGYUAN_OPERA_TYPE_WEDDING_GET_XUNYOU_INFO = 23,				-- 获取巡游信息
	QINGYUAN_OPERA_TYPE_WEDDING_REFUSAL_APPLY = 24,                 -- 拒绝宾客申请 
}

--6777协议
QINGYUAN_INFO_TYPE = {
	QINGYUAN_INFO_TYPE_WEDDING_YUYUE          = 0,	                -- 婚礼预约
	QINGYUAN_INFO_TYPE_WEDDING_STANDBY        = 1,		            -- 婚礼准备
	QINGYUAN_INFO_TYPE_GET_BLESSING           = 2,		            -- 收到祝福 param_ch1 祝福类型 param2 参数
	QINGYUAN_INFO_TYPE_BAITANG_RET            = 3,					-- 拜堂请求
	QINGYUAN_INFO_TYPE_BAITANG_EFFECT         = 4,		            -- 拜堂特效 param_ch1 是否已经拜堂
	QINGYUAN_INFO_TYPE_LIVENESS_ADD           = 5,		            -- 婚礼热度增加 param2 当前热度
	QINGYUAN_INFO_TYPE_HAVE_APPLICANT         = 6,		            -- 婚礼申请者 param2 申请者uid
	QINGYUAN_INFO_TYPE_APPLY_RESULT           = 7,		            -- 申请结果 param2 1:同意 0:拒绝
	QINGYUAN_INFO_TYPE_ROLE_INFO              = 8,			        -- 玩家信息 param_ch1 婚姻类型 param_ch2 是否有婚礼次数 param_ch3 当前婚礼状态 param_ch4 婚礼预约seq param_ch5 婚姻类型 param_ch6 是否有婚礼次数 param_ch7 当前婚礼状态 param_ch8 婚礼预约seq
	QINGYUAN_INFO_TYPE_WEDDING_YUYUE_FLAG     = 9,	   				-- 婚礼预约标记
	QINGYUAN_INFO_TYPE_YUYUE_RET              = 10,			        -- 婚礼预约请求 param_ch1 seq
	QINGYUAN_INFO_TYPE_YUYUE_POPUP            = 11,			        -- 婚礼预约弹窗
	QINGYUAN_OPERA_TYPE_BUY_QINGYUAN_FB_RET   = 12,           		-- 收到购买次数请求
	QINGYUAN_INFO_TYPE_YUYUE_SUCC             = 13,			        -- 婚礼预约成功
	QINGYUAN_INFO_TYPE_LOVER_INFO             = 14,			        -- 伴侣信息 param2 伴侣uid param_ch1 伴侣阵营 role_name 伴侣名字
	QINGYUAN_INFO_TYPE_LOVER_TITLE_INFO       = 15,	            	-- 仙侣称号信息 param2 领取flag
	-- QINGYUAN_INFO_TYPE_REQ_LOVER_BUY_LOVE_BOX = 16,         		-- 请求仙侣购买宝匣
	QINGYUAN_INFO_TYPE_WEDDING_BEGIN_NOTICE   = 16,          		-- 婚宴开启通知
	QINGYUAN_INFO_TYPE_XUNYOU_INFO            = 17,					-- 婚宴巡游信息 p_c1:撒红包次数 p_c2:撒红包购买次数 p_c3:撒花次数 p_c4:撒花购买次数 p_c5:采集巡游红包次数 p_c6:婚姻类型
	QINGYUAN_INFO_TYPE_XUNYOU_OBJ_POS         = 18,					-- 婚宴巡游对象坐标 p_c1:是否是巡游主人 p_c2:x    p_c3:y     p_c4:obj_id     p_c5:scene_id
	QINGYUAN_INFO_TYPE_ENTER_WEDDING      	  = 19,					-- 婚宴巡游对象坐标 p_c1:是否是巡游主人 p_c2:x    p_c3:y     p_c4:obj_id     p_c5:scene_id
}

MARRY_REQ_TYPE = {
	MARRY_REQ_TYPE_PROPOSE = 0,    -- 求婚 p1:婚礼类型 p2: 对方uid
    MARRY_PRESS_FINGER_REQ = 1,         --摁手指
    MARRY_REQ_SINGLE = 2,         --请求单身玩家信息
}

MARRY_RET_TYPE = {
	MARRY_PRESS_FINGER = 0,			-- 摁手指
}

--当前婚宴场数
MARRY_WEDDING_TYPE = {
	NONE          = 0,										-- 没有婚宴
	WEDDING_ONE   = 1, 										-- 第一场婚宴
	WEDDING_TWO   = 2,										-- 第二场婚宴
	WEDDING_THREE = 3,										-- 第三场婚宴
}

HUNYAN_OPERA_TYPE = {
	HUNYAN_OPERA_TYPE_INVALID        = 0,
	HUNYAN_OPERA_TYPE_YANHUA         = 1,                       -- 放燃烟花
	HUNYAN_OPERA_TYPE_RED_BAG        = 2,                       -- 婚宴送红包 param1 目标uid param2 seq
	HUNYAN_OPERA_TYPE_FOLWER         = 3,						-- 婚宴送花 param1 目标uid param2 seq
	HUNYAN_OPERA_TYPE_BAITANG_REQ    = 4,						-- 请求拜堂
	HUNUAN_OPERA_TYPE_BAITANG_RET    = 5,						-- 收到拜堂 param1 1:同意 0:拒绝
	HUNYAN_OPERA_TYPE_APPLY          = 6,						-- 申请参加婚礼(已转移6776)
	HUNYAN_OPERA_TYPE_IS_AGREE_APPLY = 7,						-- 是否同意请求 param1 目标uid param2 1:同意 0:拒绝
	HUNYAN_GET_BLESS_RECORD_INFO     = 8,						-- 获取祝福历史
	HUNYAN_GET_APPLICANT_INFO        = 9,						-- 获取申请者信息(已转移6776)
	HUNYAN_GET_WEDDING_INFO          = 10,                      -- 获取婚礼信息
	HUNYAN_GET_WEDDING_ROLE_INFO     = 11,                  	-- 获取婚礼个人信息
	HUNYAN_OPERA_APPLICANT_OPERA     = 12,						-- 申请者操作 param1 目标uid param2 1:同意 0:拒绝(已转移6776)
}

HUNYAN_STATE_TYPE = {
	HUNYAN_STATE_TYPE_INVALID = -1,

	HUNYAN_STATE_TYPE_STOP    = 0, 						--婚宴已结束
	HUNYAN_STATE_TYPE_STANDBY = 1, 						--婚宴准备中
	HUNYAN_STATE_TYPE_START   = 2,						--婚宴开始中
	HUNYAN_STATE_TYPE_XUNYOU  = 3,						--婚宴巡游
}

--表白墙
--8470 协议操作码
PROFESS_WALL_REQ_TYPE =
{
	PROFESS_WALL_REQ_INFO       = 0,			--表白墙信息（p1:0自己 1对方 2公共 ；p2:时间戳）
	PROFESS_WALL_REQ_LEVEL_INFO = 1,			--表白等级信息
	PROFESS_WALL_REQ_DELETE     = 2,			--删除表白（p1:墙类型；p2:时间戳；p3:角色id） 结果由1145返回，operate为85
	PROFESS_WALL_REQ_TYPE_MAX   = 3,
}

PROFESS_RANK_TYPE = {
	PERSON_RANK_TYPE_RA_PROFESS_MALE   = 51,                       --男榜
	PERSON_RANK_TYPE_RA_PROFESS_FEMALE = 52,                       --女榜
	SEND_FLOWER_MALE = 80,         						--魅力男榜
	SEND_FLOWER_FEMALE = 81,       						--魅力女榜
	PERSON_RANK_TYPE_OA_RECHARGE_RANK = 94,				-- 运营活动 - 充值榜
	PERSON_RANK_TYPE_OA_CONSUME_RANK = 95,				-- 运营活动 - 消费榜
}

GUILD_BATTLE_COUNT = {
	GUILD_BATTLE_MAX_FIGHT_ZONE = 5, 					-- 赛区数量
	GUILD_BATTLE_FIGHT_ZONE_MAX_GUILD_NUM = 4,			-- 位置数量
}

BUILD_TOWER_OPERA_TYPE = {
	BUILD_TOWER_OPERA_TYPE_BUILD = 0,					-- 建造塔
	BUILD_TOWER_OPERA_TYPE_UPGRADE = 1,					-- 升级塔
	BUILD_TOWER_OPERA_TYPE_REMOVE = 2,					-- 移除塔
	BUILD_TOWER_OPERA_TYPE_FLUSH = 3,					-- 立即刷怪
	BUILD_TOWER_OPERA_TYPE_CALL = 4,					-- 召唤鬼蟾王
}

SHIZHUANG_SUIT_OPERA_TYPE = {
	SHIZHUANG_SUIT_OPERA_TYPE_ALL_INFO = 0,						-- 查询所有信息
	SHIZHUANG_SUIT_OPERA_TYPE_EXCHANGE = 1,						-- 兑换时装
}


------------------------铭纹
RUNE_SYSTEM_REQ_TYPE = {
		RUNE_SYSTEM_REQ_TYPE_ALL_INFO = 0,					   -- 请求所有信息
		RUNE_SYSTEM_REQ_TYPE_BAG_ALL_INFO = 1,                 -- 请求背包所有信息
		RUNE_SYSTEM_REQ_TYPE_RUNE_GRID_ALL_INFO = 2,		   -- 请求铭纹槽所有信息
		RUNE_SYSTEM_REQ_TYPE_ONE_KEY_DISPOSE = 3,			   -- 一键分解	 p1 虚拟背包索引
		RUNE_SYSTEM_REQ_TYPE_COMPOSE = 4,					   -- 合成				 p1 索引1  p2 非零（索引1是背包索引);零（索引1是格子索引） p3 索引2  p4 非零（索引2是背包索引);零（索引2是格子索引）；
		RUNE_SYSTEM_REQ_TYPE_SET_RUAN = 5,					   -- 装备铭纹			 p1 虚拟背包索引	 p2 格子索引
		RUNE_SYSTEM_REQ_TYPE_XUNBAO_ONE = 6,				   -- 寻宝一次			 p1:是否自动购买
		RUNE_SYSTEM_REQ_TYPE_XUNBAO_TEN = 7,				   -- 寻宝十次			 p1:是否自动购买
		RUNE_SYSTEM_REQ_TYPE_UPLEVEL = 8,					   -- 升级铭纹			 p1 格子索引
		RUNE_SYSTEM_REQ_TYPE_CONVERT = 9,					   -- 铭纹兑换           p1 铭纹id
		RUNE_SYSTEM_REQ_TYPE_OTHER_INFO = 10,					--其他信息
		RUNE_SYSTEM_REQ_TYPE_AWAKEN = 11,							--铭纹格觉醒				p1 格子 p2 觉醒类型
		RUNE_SYSTEM_REQ_TYPE_AWAKEN_CALC_REQ = 12,		--铭纹格觉醒重算战力
}

RUNE_SYSTEM_INFO_TYPE =	{
		RUNE_SYSTEM_INFO_TYPE_SIGLE_CHANGE = 0,			       -- 背包单个铭纹信息
		RUNE_SYSTEM_INFO_TYPE_ALL_BAG_INFO = 1,				   -- 背包全部信息
		RUNE_SYSTEM_INFO_TYPE_RUNE_XUNBAO_INFO = 2,			   -- 铭纹寻宝信息
		RUNE_SYSTEM_INFO_TYPE_OPEN_BOX_INFO = 3,               -- 打开铭纹宝箱
		RUNE_SYSTEM_INFO_TYPE_CONVERT_INFO = 4,                -- 铭纹兑换信息
		RUNE_SYSTEM_INFO_TYPE_OPEN_JINGHUA_BOX_INFO = 5,	   -- 打开精华宝箱
		RUNE_SYSTEM_INFO_TYPE_LEVEL_LIMIT_BUY = 6, 			   --等级限购
		RUNE_SYSTEM_INFO_TYPE_DAILY_SINGIN = 7, 			   --日常福利签到
}
 COIN_FB_OPERA_TYPE = {
		COIN_FB_OPERA_TYPE_BUY_FB_TIMES = 0,		-- 购买次数
		COIN_FB_OPERA_TYPE_SWEEP_FB = 1,			-- 扫荡副本 param 选择关卡
		COIN_FB_OPERA_TYPE_GET_FB_INFO = 2,			-- 获取角色副本信息
		COIN_FB_OPERA_TYPE_MAX = 3,
}

CS_ROLE_JINGJIE_OPERA ={
	GET_INFO = 0,
	PROMOTE_LEVEL = 1,
}

-- 洗炼
EQUIP_BAPTIZE_OPERA_TYPE = {
	EQUIP_BAPTIZE_OPERA_TYPE_ALL_INFO = 0,			-- 所有信息
	EQUIP_BAPTIZE_OPERA_TYPE_LOCK_OR_UNLOCK = 1,	-- 加锁 or 去锁    param_1:部位 param_2: slot_id
	EQUIP_BAPTIZE_OPERA_TYPE_BAPTIZE = 2,			-- 洗炼            param_1:部位 param_2: 0:不保底 1:保底  param3:target_quality_id 洗单次发0(默认0)  配置表品质
	EQUIP_BAPTIZE_OPERA_TYPE_UNLOCK_BAPTIZE = 3,    -- 购买解锁洗练槽   param_1:部位 param_2: slot_id
	EQUIP_BAPTIZE_OPERA_TYPE_UP_GRADE = 4,			-- 进阶            param_1:部位
}

--聚魂
--新
JUHUN_REQ_TYPE ={
	JUHUN_REQ_TYPE_ALL_INFO = 0,					--请求所有信息
	JUHUN_REQ_TYPE_PUT_ON = 1,						-- 装备， param1 背包格子index，param2 装备槽格子index
	JUHUN_REQ_TYPE_TAKE_OFF = 2,					-- 卸下， param1 装备槽index
	JUHUN_REQ_TYPE_STAR_MASTER_UNLOCK = 3,			--星级大师激活，param1激活等级
	JUHUN_REQ_TYPE_ITEM_ACTIVE_SLOT = 4,			--消耗物品激活槽位，param1 槽下标
}

-- 神兽
SHENSHOU_REQ_TYPE ={
	SHENSHOU_REQ_TYPE_ALL_INFO = 0,					-- 请求所有信息
	SHENSHOU_REQ_TYPE_PUT_ON = 1,					-- 装备， param1 神兽ID，param2 背包格子index, param3 装备槽格子index
	SHENSHOU_REQ_TYPE_TAKE_OFF = 2,					-- 卸下， param1 神兽ID, param2 装备槽index
	SHENSHOU_REQ_TYPE_ZHUZHAN = 3,					-- 助战， param1 神兽ID，
	SHENSHOU_REQ_TYPE_ADD_ZHUZHAN = 4,				-- 扩展神兽助战位 param1 位置序列1~5
	SHENSHOU_REQ_TYPE_COMPOSE = 5,					-- 合成， param_1 物品id ，param_2 背包格子index1 ，param_3 背包格子index2，param_4 背包格子index3
	SHENSHOU_REQ_TYPE_DECOMPOSE = 6,				-- 分解， param_1 背包格子index
	SHENSHOU_REQ_TYPE_ONE_KEY_TAKE_OFF = 7,				-- 一键卸下
	-- DESTROY = 8,									-- 销毁 param1 背包格子index
	EQUIP_UPGRADE = 8,                              -- 装备升阶 param_1 魂环索引soul_ring_seq  param_2 部位(一键升级时候发-1)
}

-- 神兽
NEW_SHENSHOU_REQ_TYPE ={
	SHENSHOU_REQ_TYPE_ALL_INFO = 0,					-- 请求所有信息
	SHENSHOU_REQ_TYPE_PUT_ON = 1,					-- 装备， param1 神兽soul_ring_seq，param2 背包格子index, param3 装备槽格子index
	SHENSHOU_REQ_TYPE_TAKE_OFF = 2,					-- 卸下， param1 神兽soul_ring_seq, param2 装备槽 index
	SHENSHOU_REQ_TYPE_ADD_ZHUZHAN = 3,				-- 扩展神兽助战位 param1 位置序列1~5助战位索引index
	SHENSHOU_REQ_TYPE_COMPOSE = 4,					-- 合成， param_1 物品id ，param_2 背包格子index1 ，param_3 背包格子index2，param_4 背包格子index3
	SHENSHOU_REQ_TYPE_DECOMPOSE = 5,				-- 分解， param_1 背包格子index
	SHENSHOU_REQ_TYPE_ONE_KEY_TAKE_OFF = 6,			-- 一键卸下 param1 soul_ring_seq
	DESTROY = 7,									-- 销毁 param1 背包格子index
}

LINGQI_MOUNT_OPERA_TYPE = {									-- 坐骑 灵骑
	LINGQI_MOUNT_OPERA_TYPE_ALL_INFO = 0,
	LINGQI_MOUNT_OPERA_TYPE_UPSTAR = 1,						-- param1 升星物品id
	LINGQI_MOUNT_OPERA_TYPE_USE_SHUXINGDAN = 2,				-- param1 格子索引 param2 数量
	LINGQI_MOUNT_OPERA_TYPE_UPGRADE_SPECIAL_IMG = 3,		-- param1 形象索引 param2 升阶物品id
	LINGQI_MOUNT_OPERA_TYPE_USE_IMAGE = 4,					-- param1 形象id
	LINGQI_MOUNT_OPERA_TYPE_UNUSE_IMAGE = 5,				-- param1 形象id
}

LINGYI_MOUNT_OPERA_TYPE = {									-- 坐骑 灵翼
	LINGYI_MOUNT_OPERA_TYPE_ALL_INFO = 0,
	LINGYI_MOUNT_OPERA_TYPE_UPSTAR = 1,						-- param1 升星物品id
	LINGYI_MOUNT_OPERA_TYPE_USE_SHUXINGDAN = 2,				-- param1 格子索引 param2 数量
	LINGYI_MOUNT_OPERA_TYPE_UPGRADE_SPECIAL_IMG = 3,		-- param1 形象索引 param2 升阶物品id
	LINGYI_MOUNT_OPERA_TYPE_USE_IMAGE = 4,					-- param1 形象id
	LINGYI_MOUNT_OPERA_TYPE_UNUSE_IMAGE = 5,				-- param1 形象id
}

-- 可报名多次的活动
SPECAIL_MANY_TIMES_ACT = {
	[ACTIVITY_TYPE.HUSONG] = true,
}

NEW_PETFB_REQ_TYPE = {
	NEW_PETFB_REQ_TYPE_OTHER_INFO = 0,		-- 其他信息
	NEW_PETFB_REQ_TYPE_AUTO_FB = 1,			-- 副本扫荡
	NEW_PETFB_REQ_TYPE_BUY_TIMES = 2,		--购买一次
}

FB_HIGH_REQ_TYPE = {
	FB_HIGH_REQ_TYPE_BUY_TIMES = 0,
	FB_HIGH_REQ_TYPE_GUID_FINISH = 1,
}

HUNDRED_REBATE_OPERA_TYPE = {
	HUNDRED_REBATE_OPERA_TYPE_BUY = 0,					-- 购买
	HUNDRED_REBATE_OPERA_TYPE_FETCH_GOLD = 1,			-- 领取元宝返利
	HUNDRED_REBATE_OPERA_REQ_INFO = 2,					--请求所有信息
	HUNDRED_REBATE_OPERA_TYPE_MAX = 3,
}

---仙侣宝宝
BABY_REQ_TYPE = {
	BABY_REQ_TYPE_INFO = 0,									-- 请求单个宝宝信息	param1:type  param2:id
	BABY_REQ_TYPE_ALL_INFO = 1,								-- 请求所有信息
	BABY_REQ_TYPE_UPGRADE = 2,								-- 升阶请求			param1:type  param2:id
	BABY_REQ_TYPE_CHANGE_BABY = 3,							-- 更换出战宝宝		param1:type  param2:id
	BABY_REQ_TYPE_DISPOSE_CARD = 4,							-- 分解宝宝激活卡	param1:item_id
	BABY_REQ_TYPE_FETCH_BABY = 5,							-- 领取	param1:type  param2:id
}


TEAM_EXP_TYPE = {
	BUY_ENTER_COUNT = 0,                    -- 购买多人经验本进入次数
	WUJINJITAN_REQ_TYPE_BUY_PASS_ITEM = 1,                    -- 购买门票
	WUJINJITAN_REQ_TYPE_FIRST_TIME = 2,                    -- 是否首次
	LINGHUNSQUA_REQ_TYPE_BUY_ONCE = 3,      --购买灵魂广场额外进入次数
	LINGHUNSQUA_REQ_TYPE_SNEER = 4, 		--双倍灵魂-灵魂广场
	WUJINJITAN_REQ_TYPE_SET_FIRST_TIME = 5, -- 无尽祭坛设置首次免费
	YUGUXIANDIAN_REQ_TYPE_SET_FIRST_TIME = 6, -- 远古仙殿设置首次免费
--	WUJINJITAN_REQ_TYPE_SET_FIRST_TIME = 22, -- 无尽祭坛设置首次免费
}

FISHING_OPERA_REQ_TYPE = {
	FISHING_OPERA_REQ_TYPE_START_FISHING = 0,						-- 开始钓鱼（进入钓鱼界面）
	FISHING_OPERA_REQ_TYPE_CASTING_RODS = 1,						-- 抛竿
	FISHING_OPERA_REQ_TYPE_PULL_RODS = 2,							-- 收竿
	FISHING_OPERA_REQ_TYPE_CONFIRM_EVENT = 3,						-- 确认本次钓鱼事件
	FISHING_OPERA_REQ_TYPE_USE_GEAR = 4,							-- 使用法宝
	FISHING_OPERA_REQ_TYPE_BIG_FISH_HELP = 5,						-- 帮忙拉大鱼
	FISHING_OPERA_REQ_TYPE_STOP_FISHING = 6,						-- 停止钓鱼（离开钓鱼界面）
	FISHING_OPERA_REQ_TYPE_AUTO_FISHING = 7,						-- 自动钓鱼
	FISHING_OPERA_REQ_TYPE_RAND_USER = 8,							-- 随机角色请求
	FISHING_OPERA_REQ_TYPE_BUY_STEAL_COUNT = 9,						-- 请求购买钓鱼次数
	FISHING_OPERA_REQ_TYPE_RANK_INFO = 10 							-- 请求购买钓鱼排行榜信息
}

HUSONG_REWARD_INFO_NOTFIY_REASON ={
	HUSONG_REWARD_INFO_NOTFIY_REASON_TIMEOUT = 0, 	--超时
	HUSONG_REWARD_INFO_NOTFIY_REASON_FAIL = 1, 		--被劫镖
	HUSONG_REWARD_INFO_NOTFIY_REASON_FINISH = 2, 	--任务完成
}

FISHING_STATUS = {
	FISHING_STATUS_IDLE = 0,										-- 未钓鱼，即不在钓鱼界面
	FISHING_STATUS_WAITING = 1,										-- 在钓鱼界面等待抛竿
	FISHING_STATUS_CAST = 2,										-- 已经抛竿，等待触发事件
	FISHING_STATUS_HOOKED = 3,										-- 已经触发事件，等待拉杆
	FISHING_STATUS_PULLED = 4,										-- 已经拉杆，等待玩家做选择
}

SPECIAL_STATUS = {
	SPECIAL_STATUS_OIL = 0,											-- 使用香油中
	SPECIAL_STATUS_AUTO_FISHING = 1,								-- 自动钓鱼
}

FISHING_EVENT_TYPE = {
	FISHING_FISH_HOOK_UP = 0,										-- 鱼类上钩
	FISHING_OLD_CHEST = 1,											-- 宝箱事件
	FISHING_OLD_FISHER = 2,											-- 垂钓的渔翁
	FISHING_ROBBER = 3,												-- 遇见盗贼
	FISHING_LEGENDARY_FISH = 4,										-- 传说中的大鱼
}

FISHING_GEAR = {
	FISHING_GEAR_NET = 0,											-- 渔网
	FISHING_GEAR_SPEAR = 1,											-- 鱼叉
	FISHING_GEAR_OIL = 2,											-- 香油
}


AUTO_HAVE_TEAM_REQ_TYPE = {
	AUTO_HAVE_TEAM_REQ_TYPE_JOIN = 0,			       --匹配
	AUTO_HAVE_TEAM_REQ_TYPE_CANCEL = 1,			       --取消匹配
}

EXP_ADD_REASON = {
	EXP_ADD_REASON_NO_ADD = 0,						-- 没有加成
	EXP_ADD_REASON_KILL_MONSTER = 1,				-- 杀怪
	EXP_ADD_PREVENT_ADDICTION = 2,					-- 防沉迷
	EXP_ADD_REASON_VIP_EXPPOOL = 3,				-- 经验池获取经验
	EXP_ADD_REASON_ADD_ALL = 4,					-- 所有加成
	EXP_ADD_REASON_WORLD = 5,					-- 只要世界加成
	EXP_ADD_REASON_HOTSPRING = 6,				-- 温泉加成
	EXP_ADD_REASON_OFFLINE = 7,					-- 挂机
	EXP_ADD_REASON_SCENE = 8,					-- 场景经验加成
}

IMP_GUARD_REQ_TYPE = {
	IMP_GUARD_REQ_TYPE_RENEW_PUTON = 0, 				-- 续费穿在身上的小鬼 param2:是否使用绑元
	IMP_GUARD_REQ_TYPE_RENEW_KNAPSACK = 1,				-- 续费背包中的小鬼 param1:背包index param2:是否使用绑元
	IMP_GUARD_REQ_TYPE_TAKEOFF = 2,						-- 脱下小鬼
	IMP_GUARD_REQ_TYPE_ALL_INFO = 3,
	IMP_GUARD_REQ_TYPE_OPEN_SLOT = 4,
	IMP_GUARD_REQ_TYPE_HUANHUA = 5,                     --幻化
	IMP_GUARD_REQ_TYPE_MAX = 6,
}

ROLE_TALENT_OPERATE_TYPE = {
	ROLE_TALENT_OPERATE_TYPE_INFO = 0,							-- 请求天赋信息
	ROLE_TALENT_OPERATE_TYPE_UPLEVEL = 1,							-- 升级天赋
	ROLE_TALENT_OPERATE_TYPE_RESET = 2,								-- 重置天赋点
}

PERSONALIZE_WINDOW_OPERA_TYPE = {
	PERSONALIZE_WINDOW_BUBBLE_INFO = 0,					--请求气泡框信息
	PERSONALIZE_WINDOW_BUBBLE_UP_LEVEL = 1,				--升级气泡框请求
	PERSONALIZE_WINDOW_BUBBLE_USE = 2,					--使用气泡框请求

	BUBBLE_WINDOW_MAX_TYPE = 50,						--气泡框最大配置数量
}

RONG_LIAN_CONTENT_TYPE = {
	FAN_HUAN = 0, 										--一键返还
	EQUIP = 1, 											--装备时替换熔炼装备
}

OPEN_SERVER_REWARD_TYPE = {
	REWARD_TYPE_TOTAL_CONSUME_REWARD = 0 ,              -- 累计消费
	REWARD_TYPE_GUILD_BATTLE_REWARD = 1,                -- 仙盟奖励
	REWARD_TYPE_WORD_EXCHANGE_REWARD = 2,               -- 字帖兑换
	REWARD_TYPE_FIRST_CHONGZHI_GROUP_BUY = 3,			-- 首充团购
	REWARD_TYPE_CREATE_GUILD_REWARD = 4,                -- 开宗立派
	REWARD_TYPE_DAILY_LIMIT_BUY = 5,                    -- 每日限购
	REWARD_TYPE_RECHANGER = 6,							-- 累计充值
	REWARD_TYPE_CLOUD_BUY = 7,                          -- 幸运云购
	REWARD_TYPE_COOLSUI_CRAZYBUY = 8,                       --炫装特卖
	REWARD_TYPE_RUSH_RANK_REWARD = 9 ,					-- 冲榜达人
}
RA_QITIAN_XIAOFEI_FANLI_OPE_TYPE = {
	QITIAN_XIAOFEI_FANLI_REQ_INFO = 0, 					--请求信息
	QITIAN_XIAOFEI_FANLI_FRETCH_REWARD = 1,				--领取奖励
}


IS_VIP_OR_NOT  = {
	NOT_VIP = 0,		--不是VIP
	IS_VIP = 1,			-- 是VIP
}

VIP_MODEL_PART_TYPE = {
	FASHION = 1,  	--时装
	MOUNT = 2,		--坐骑
	LING_CHONG = 3,	--灵宠
	HUA_KUN = 4,	--化鲲
	XIAN_WA = 5,	--仙娃
	HALO = 6,		--光环
}

VIP_WEEK_CARD_GRADE_ADDITION_TYPE = {
	WEEK_CARD_GRADE_ADDITION_TYPE_WING = 1,   --仙翼.
	WEEK_CARD_GRADE_ADDITION_TYPE_FABAO = 2,  --法宝.
	WEEK_CARD_GRADE_ADDITION_TYPE_SHENBING = 3, --神兵.
	WEEK_CARD_GRADE_ADDITION_TYPE_JIANZHEN = 4, --剑阵.
	WEEK_CARD_GRADE_ADDITION_TYPE_LINGCHONG = 5, --灵宠.
	WEEK_CARD_GRADE_ADDITION_TYPE_MOUNT = 6,  --坐骑.
}

-- 每日必做和任务升级按钮
BIZUO_TYPE = {
	RI_CHANG = 1,         -- 日常任务
	JJC = 2,              -- 竞技场
	LIAN_YU = 3,          -- 炼狱洞窟
	HU_SONG = 6,          -- 美人护送
	XIAN_MENG = 11,       -- 仙盟周任务
	GUA_JI = 12,           -- 前往挂机
	GUILD_BUILD_TASK = 35, -- 仙盟建设任务
	OPEN_VIEW = 14,        -- 打开面板
	QIFU = 15,             -- 祈福
	YEWAI_GUAJI = 49,		--野外挂机
	SIT = 53,				-- 打坐
}

-- 每日必做和任务升级按钮
-- 对应 R-任务引导-引导途径 配置表
UP_LV_GUIDE_ID = {
	RI_CHANG = 1,         -- 日常任务
	QIFU = 2,             -- 祈福
	GUA_JI = 3,           -- 前往挂机
	FRXZ = 4,			  -- 凡人修真
	DJXZ = 5,			  -- 渡劫仙舟

	ACT_JJC = 7, 		  -- 竞技场
	GUILD_BUILD = 8, 	  -- 仙盟建设
	EQUIP_FUBEN = 9, 	  -- 海底废墟（装备本）
    FIRST_RECHARGE = 10,  -- 首充
    WORLD_BOSS = 11,      --世界boss
    HuSong = 12,		  -- 仙女护送
}

CROSS_1V1_FETCH_REWARD_TYPE = {
	CROSS_1V1_FETCH_REWARD_TYPE_JOIN_TIMES = 1,			--跨服1v1参与奖励
	CROSS_1V1_FETCH_REWARD_TYPE_SCORE = 2,				--跨服1v1积分奖励
	CROSS_1V1_FETCH_REWARD_TYPE_HONOR = 3,				--跨服1v1积分奖励
}

CROSS_1V1_RING_OPER_TYPE = {
	CROSS_1V1_RING_OPER_WEAR = 1 , 					-- 佩戴戒指
	CROSS_1V1_RING_OPER_OFF = 2, 					-- 脱下戒指
}

CROSS_1V1_MATCH_REQ_TYPE = {
	CROSS_1V1_MATCH_REQ_RESULT = 0,                -- 匹配状态查询
	CROSS_1V1_MATCH_REQ_CANCEL = 1,                -- 取消匹配
}

CROSS_PVP_CARD_OPER_TYPE = {
	CROSS_PVP_CARD_OPER_WEAR = 1 , 					-- 佩戴令牌
	CROSS_PVP_CARD_OPER_OFF = 2, 					-- 脱下令牌
}

CROSS_PERSON_RANK_TYPE = {
	CROSS_PERSON_RANK_TYPE_CAPABILITY_ALL = 0,							-- 跨服战力榜
	CROSS_PERSON_RANK_TYPE_WEEK_ADD_CHARM = 1,							-- 跨服魅力榜
	CROSS_PERSON_RANK_TYPE_1VN = 2,										-- 跨服1vN排行榜
	CROSS_PERSON_RANK_TYPE_XIULUO_TOWER = 3,							-- 跨服修罗塔
	CROSS_PERSON_RANK_TYPE_DINGJI_WEDDING = 4,							-- 顶级婚礼
	CROSS_PERSON_RANK_TYPE_1V1_SCORE = 5,								-- 跨服1v1积分排行榜
	CROSS_PERSON_RANK_TYPE_FLOWER_HANDSOME = 6,							-- 跨服鲜花榜-男神榜
	CROSS_PERSON_RANK_TYPE_FLOWER_BEAUTY = 7,							-- 跨服鲜花榜-女神榜
	CROSS_PERSON_RANK_TYPE_SMASHED_EGG = 8,            					-- 跨服砸蛋榜
	CROSS_PERSON_RANK_TYPE_ROLE_LEVEL = 9,								-- 跨服等级排行榜
	CROSS_PERSON_RANK_TYPE_3V3_SCORE = 10,								-- 跨服3v3积分排行榜
};

VIP_LEVEL_AUTH_TYPE = {
	EXP_ADD_PER = 4, 										-- 经验加成
	JJC = 6, 												-- 竞技场购买次数
	HIGH_TEAM_EQUIP_BUY_TIMES = 17,                         -- 远古仙殿vip进入次数
	SG_ENTER_TIMES = 31,                                    -- 上古遗迹进入次数
	SG_TIRE_TIMES = 32,                                     -- 上古遗迹boss疲劳
	FMFB_ENTER_TIMES = 20,                                  -- 战骑本进入次数
	HUOYUEDU = 23,                                 			-- 日常活跃度
	DAY_TOUCH_MAX_NUM = 29,									--虎摸次数上限
    FRIENDDAY_TOUCH_MAX_NUM = 30,							--好友被虎摸次数上限
    PERSONBOSS_ENTER_TIMES = 34,                             --个人boss进入次数
	MARKET_SELL_AMOUNT = 35, 								-- 市场可上架数目
	MARKET_TAX = 36, 										-- 市场交易税率
    MARKET_PASSWORD = 37, 									-- 市场上架密码
    WORLDBOSS_ENTER_TIMES = 39,                             --世界boss进入次数
    MAMHUANG_ENTER_TIMES = 41,                             --蛮荒神兽购买次数
    VAT_CROSS_FAIRYLAND_BOSS_ENTER_TIMES = 42,					-- 跨服仙界BOSS进入次数
};                                                          -- vip等级特权


RA_CHARGE_REPAYMENT2_OPERA_TYPE = {
	RA_CHARGE_REPAYMENT2_OPERA_TYPE_QUERY_INFO = 0,
	RA_CHARGE_REPAYMENT2_OPERA_TYPE_FETCH_REWARD = 1,
	RA_CHARGE_REPAYMENT2_OPERA_TYPE_MAX = 2,
};

EQUIP_ZHULING_OPERA_TYPE = {								--装备铸灵
	EQUIP_ZHULING_OPERA_TYPE_ALL_INFO = 0,					-- 所有信息
	EQUIP_ZHULING_OPERA_TYPE_PUTON = 1,						-- 放入铸灵材料 param_1:装备部位 param_2：索引 param_3：虚拟背包索引bag_index
	EQUIP_ZHULING_OPERA_TYPE_TAKEOFF = 2,					-- 拿下铸灵材料 param_1:装备部位 param_2：索引
	EQUIP_ZHULING_OPERA_TYPE_COMPOSE = 3,					-- 合成铸灵材料 param_1:合成类型 param_2：合成等级 param_3：bag_index1 param_4:bag_index2 param_5：bag_index3 param_6：bag_index4
	EQUIP_ZHULING_OPERA_TYPE_ACT_SUIT = 4,					-- 激活套装 param_1:0-3
	EQUIP_ZHULING_OPERA_TYPE_SORT_BAG = 5,					-- 整理背包
	EQUIP_ZHULING_OPERA_TYPE_RECYCLE = 6,					-- 回收 param_1:bag_index
}

EQUIP_FUMO_PROTO = {								--装备附魔
	EQUIP_FUMO_PROTO_GETINFO = 0,					--所有信息
	EQUIP_FUMO_PROTO_LEVELUP = 1,                 	--附魔升级
	MAX_EQUIP_PART = 11,
}
FUMO_TUPO_PROTO = {
	FUMO_TUPO_PROTO_GET_INFO = 0,  --请求信息:
	FUMO_TUPO_PROTO_XILIAN = 1,    --请求洗练: part部位
	FUMO_TUPO_PROTO_TEN_XILIAN = 2, --智能十连: part部位
	FUMO_TUPO_PROTO_LOCK = 3,    --请求锁操作: part部位， param属性类型[0,4]
	FUMO_TUPO_PROTO_SAVE = 4,    --请求保存: part部位
	NUM_ATTR = 5,
}

RA_HAPPY_CONSUME_REQ_TYPE = {
	RA_HAPPY_CONSUME_REQ_TYPE_REQ_INFO = 0, 				-- 信息
	RA_HAPPY_CONSUME_REQ_TYPE_EXCHANGE_ITEM = 1,			-- 兑换物品
}

RA_HAPPY_RECHARGE_REQ_TYPE = {
	RA_HAPPY_RECHARGE_REQ_TYPE_REQ_INFO = 0, 				-- 信息
	RA_HAPPY_RECHARGE_REQ_TYPE_EXCHANGE_ITEM = 1,			-- 兑换物品
}

RA_GOLDPIG_LOTTERY_OPERA_TYPE = {
	RA_GOLDPIG_LOTTERY_OPERA_TYPE_QUERY_INFO = 0,			-- 请求活动信息
	RA_GOLDPIG_LOTTERY_OPERA_TYPE_TYPE_DO_LOTTERY = 1,			-- 发起抽奖请求 p1抽奖类型 (1:一抽 2：十抽)
	RA_GOLDPIG_LOTTERY_OPERA_TYPE_TYPE_EXCHANGE = 2,			-- 发起兑换请求 p1兑换序号

	RA_GOLDPIG_LOTTERY_OPERA_TYPE_TYPE_MAX = 3,
}

RA_NATIONAL_CELEBRATION_REQ_TYPE = {
	RA_NATIONAL_CELEBRATION_REQ_TYPE_INFO = 0,
	RA_NATIONAL_CELEBRATION_REQ_TYPE_COMMIT = 1,
	RA_NATIONAL_CELEBRATION_REQ_TYPE_FETCH_REWARD = 2,
	-- RA_NATIONAL_CELEBRATION_REQ_TYPE_COMMIT_TEN = 3,

	RA_NATIONAL_CELEBRATION_REQ_TYPE_MAX = 3,
}

NATIONAL_CELEBRATION_COMMIT_TYPE = {
	NATIONAL_CELEBRATION_COMMIT_TYPE_ITEM = 0,
	NATIONAL_CELEBRATION_COMMIT_TYPE_GOLD = 1,

	NATIONAL_CELEBRATION_COMMIT_TYPE_MAX = 2,
}

-- 限时特卖
LIMITED_SHOP_OPERA_TYPE = {
    LIMITED_SHOP_OPERA_TYPE_ALL_INFO = 0,
    LIMITED_SHOP_OPERA_TYPE_BUY = 1,           -- 购买 param1 序号seq
  }

-------------------百倍返利----------------------
RA_HIGH_REBATE_OPERA_TYPE = {
	RA_HIGH_REBATE_OPERA_TYPE_QUERY_INFO = 0,         --信息请求
	RA_HIGH_REBATE_OPERA_TYPE_FETCH_REWARD = 1,       --领取请求
}

RA_DANBI_CHONGZHI2_OPERA_TYPE = {
	RA_DANBI_CHONGZHI2_OPERA_TYPE_QUERY_INFO = 0,	--请求所有信息
	RA_DANBI_CHONGZHI2_OPERA_TYPE_FETCH_REWARD = 1, --领取奖励
}

JIUSHEDAOFB_NOTIFY_REASON = {
	NOTIFY_REASON_ENTER = 0,
	NOTIFY_REASON_WAIT = 1,
	NOTIFY_REASON_UPDATE = 2,
	NOTIFY_REASON_FINISH = 3,
	NOTIFY_REASON_KILL_MONSTER = 4,
}

JIUSHEDAOFB_REQUESET_REASON = {
	REQUESET_REASON_DEAD = 0,
	REQUESET_REASON_BEEN_PLATFORM = 1,
	REQUESET_REASON_BEEN_SPRINGBOARD = 2,
	REQUESET_REASON_JUMP = 3,
}

COMPOSE_EQUIP_OPERA_TYPE = {
	EQUIP_OPERA_TYPE_COMPOSE = 0,    -- 合成 p1-p5 背包索引
	EQUIP_OPERA_TYPE_DECOMPOSE = 1,      -- 分解 p1 背包索引
	LING_CHONG_TYPE_COMPOSE = 2,   --合成宠物、坐骑
	EQUIP_OPERA_TYPE_IMPRESSION_INHERIT = 3,	--装备印记继承 p1主装备背包索引，p2 副装备背包索引
	EQUIP_OPERA_TYPE_XIANQI_REFINE = 4,        -- 仙器真炼 p1 装备索引， p2是否身上装备（是的话p1就是装备格子索引，否的话p1为背包格子索引）
    EQUIP_OPERA_TYPE_XIANQI_UPGRADE = 5,      -- 仙器进阶 p1 背包索引
    EQUIP_OPERA_TYPE_XIANQI_UPSTAR = 6,        -- 仙器升星 p1 背包索引
    EQUIP_OPERA_TYPE_XIANQI_UPQUALITY = 7,         -- 仙器升品 p1 装备索引
    EQUIP_OPERA_TYPE_EQUIP_UPQUALITY = 8,         -- 装备升品 p1 装备索引
    EQUIP_OPERA_TYPE_EQUIP_ACTIVE = 9,			--普通装备品级等级属性激活
    EQUIP_OPERA_TYPE_XIANQI_ACTIVE = 10,			--仙器品级等级属性激活
}

COMPOSE_EQUIP_CELL_NUM = {
	COL_CELL_NUM = 3,
}

------------------boss图鉴----------------------
 BOSS_CARD_OPERA_TYPE = {
    BOSS_CARD_OPERA_TYPE_ALL_INFO = 0,			-- 请求图鉴信息，param1 图鉴类型
    BOSS_CARD_OPERA_TYPE_ACTIVE = 1,            -- 激活图鉴，param1 图鉴类型, param2 序号seq
    BOSS_CARD_OPERA_TYPE_MAX = 2,
  }

RA_WISH_POOL_TYPE = {
	RA_WISH_POOL_TYPE_USE_GOLD_INFO = 0,		--请求活动信息
    RA_WISH_POOL_TYPE_USE_ITEM = 1,				--物品许愿
    RA_WISH_POOL_TYPE_USE_GOLD = 2,				--元宝许愿
}

RA_NATIONAL_TREASURE_TYPE = {
    RA_NATIONAL_TREASURE_TYPE_USE_GOLD_INFO = 0,
    RA_NATIONAL_TREASURE_TYPE_USE_ITEM = 1,
    RA_NATIONAL_TREASURE_TYPE_USE_GOLD = 2,
}

RA_COLLECT_BLESSING_OPEAR_TYPE = {
	RA_COLLECT_BLESSING_OPERA_TYPE_QUERY_INFO = 0,
	RA_COLLECT_BLESSING_OPERA_TYPE_TURN_CARD = 1,						--翻牌 param1 是否元宝（是为1） param2格子索引
	RA_COLLECT_BLESSING_OPERA_TYPE_FETCH_REWARD = 2,				--领奖励 param1 seq
}

-- 欢乐砸蛋
RA_CS_CROSS_RA_SMASHED_EGG_INFO = {
	CS_CROSS_RA_SMASHED_EGG_INFO  = 0, --请求活动期间玩家砸蛋信息
	CS_CROSS_RA_SMASHED_EGG = 1,          -- 请求砸蛋  P1:砸蛋index
	CS_CROSS_RA_SMASHED_ALL_EGG = 2,          -- 请求砸蛋  P1:砸蛋index
	CS_CROSS_RA_SMASHED_EGG_FETCH_REWARD = 3,    -- 请求领取累计奖励  P1:seq
	RA_COMMON_LOTTERY_HISTORY_MAX_COUNT = 10,    -- 抽奖历史纪录最大数量
	CROSS_RA_GOLD_EGG_NUM = 8,                          -- 金蛋个数
}

RA_CS_CROSS_RA_MIAOSHA_INFO = {
	CS_CROSS_RA_MIAOSHA_INFO  = 0, 			--请求活动期间玩家跨服秒杀信息
	CS_CROSS_RA_MIAOSHA_BUY = 1,          	-- 请求购买
}

SACRED_BOSS_OPERA_TYPE = {
	SACRED_BOSS_OPERA_TYPE_GET_FLUSH_INFO = 0,	-- 刷新信息 param1 层数
	SACRED_BOSS_OPERA_TYPE_BOSS_KILL_RECORD = 1,	-- 击杀记录 param1 层数 param2 boss_id
	SACRED_BOSS_OPERA_TYPE_DROP_RECORD = 2,			-- 掉落记录
	-- SACRED_BOSS_OPERA_TYPE_CONCERN_BOSS = 3,		-- 关注boss	param1 层数 param2 boss_id
	-- SACRED_BOSS_OPERA_TYPE_UNCONCERN_BOSS = 4,		-- 关注boss param1 层数 param2 boss_id
	-- SACRED_BOSS_OPERA_TYPE_FORENOTICE = 5,			-- boss通知
	-- SACRED_BOSS_OPERA_TYPE_RELIVE_TIMES = 6,        -- 购买复活次数
	-- SACRED_BOSS_OPERA_TYPE_PLAYER_INFO = 7,        -- 玩家信息
	SACRED_BOSS_OPERA_TYPE_GET_ALL_INFO = 3,        -- 玩家信息
}

RA_CS_GUILD_BATTLE_RANK_INFO= {
	CS_BATTLE_RANK = 1,
}

-- 特惠秒杀
RA_PANICBUY_OPERA_TYPE = {
	RA_PANICBUY_OPERA_BUY = 0,	-- 购买
	RA_PANICBUY_OPERA_INFO = 1,	--所有信息
}

-- 外观武器使用类型
APPEARANCE_USE_TYPE = {
	APPEARANCE_WUQI_USE_TYPE_INVALID = 0,					-- 不可用
	APPEARANCE_WUQI_USE_TYPE_SHIZHUANG = 1,
	APPEARANCE_WUQI_USE_TYPE_SHENQI = 2,
	APPEARANCE_USE_TYPE_MAX = 3,
}

-- 进阶系统的类型
ADVANCED_UPGRADE_TYPE = {
	UPGRADE_TYPE_WING = 0, 					-- 羽翼
	UPGRADE_TYPE_FABAO = 1,					-- 法宝
	UPGRADE_TYPE_SHENBING = 2, 				-- 神兵
	UPGRADE_TYPE_LINGGONG = 3, 				-- 灵弓
	UPGRADE_TYPE_JIANZHEN = 7, 				-- 剑阵
}

-- 外观衣服使用类型
APPEARANCE_BODY_USE_TYPE = {
	APPEARANCE_BODY_USE_TYPE_INVALID = 0,
	APPEARANCE_BODY_USE_TYPE_SHIZHUANG = 1,
	APPEARANCE_BODY_USE_TYPE_SHENQI = 2,
	APPEARANCE_BODY_USE_TYPE_MAX = 3,
}

CameraType = {
	Fixed = 0,		-- 固定视角
	Free = 1,		-- 自由视角
}

EquipIcon = {
	RoleBag = 1,		--角色背包
	ShenShou = 2,		--神兽装备
	Mount = 3,			--坐骑装备
	LingChong = 4,		--灵宠装备
}

FunResType =
{
	SHEN_WU = 0,			--神兵/武器/神武
	SHI_ZHUANG = 1,			--时装
	FOOT = 2,				--足迹
	HALO = 3,				--光环
	WING = 4,				--羽翼
	MOUNT = 5,				--坐骑
	LING_CHONG = 6,			--宠物（原来是灵宠）
	LING_QI = 7,			--灵骑
	LING_YI = 8,			--灵翼
	LING_GONG = 9,			--灵弓
	FA_BAO = 10,			--法宝
	MONSTER = 11, 			--怪物
	SHENG_QI = 12,			--圣器
	MARRY_MODEL = 13, 		--仙侣
	ITEM_MODEL = 14, 		--物品模型
	NPC = 15, 		        --NPC
	SHENSHOU = 16, 			--神兽
	FEI_CHONG = 17, 		--飞宠
	QILINGBI = 18, 			--麒麟
	TIANSHEN_SHENQI = 19, 	--天神神器
	HUA_KUN = 20,			--化鲲
	JIAN_LING = 21,			--剑灵
	SG_SHENLING = 22,		--上古神灵
	TIAN_SHEN = 23,			--天神
	TIAN_JIPU = 24,			--天机谱
	ZHUAN_ZHI_ONE = 25,		--转职一
	ZHUAN_ZHI_TWO = 26,		--转职二
	GOD_BODY = 27,			--神体
	SI_XIANG = 28,			--四象
	BEAST = 29,				--御兽
	GUNDAM = 30,			--机甲
	WU_HUN_ZHEN_SHEN = 31,	--武魂真身
	SHOU_HU = 32,			--守护
}

FunTrailerType =
{
	Icon = 0,
	Model = 1,
}

FunOpenIconFlyPos = {
	Top = "Top",
	Bottom = "Bottom",
}

Fly_Shoe_Return_Type = {
	NONE = 0,
	LIMIT = 1,
}

ShowRedPoint = {
	NOT_SHOW_RED_POINT = 0,		-- 不显示红点
	SHOW_RED_POINT = 1,			-- 显示
}

--0=过期 ，1=即将过期 ， 2=临界点一天 ，3=临界点12小时，4=临界点1小时，5=临界点30分钟，6=临界点10分钟，7=临界点5分钟，8=临界点1分钟
GuardTimeType = {

	GuoQi = 0,
	JiJiangGuoQi = 1,
	LinJieDianOneDay = 2,
	LinJieDianHour12 = 3,
	LinJieDianHour1 = 4,
	LinJieDianMin30 = 5,
	LinJieDianMin10 = 6,
	LinJieDianMin5 = 7,
	LinJieDianMin1 = 8,
}

KNAPSACK_TYPE = {
	INVALID = 0,			--普通背包
	NORMAL = 1,				--材料背包 (前端普通和材料是放在一起的)
	SHENSHOU = 2,			--魂环背包
	LINGCHONG = 4,  		--灵宠背包
	MOUNT = 5,				--坐骑背包
	SEAL = 6,				--圣印背包
	GOD_DEVIL = 7,			--神魔背包
	ZODIAC = 8,				--生肖背包
	DIVINEWEAPON = 9,		--天兵背包
	TIANSHEN = 10,			--天神背包
	TUJIAN = 12,            --图鉴
	HUAKUN = 13,			--化鲲
	XIANJIE_EQUIP_BAG = 15,	--仙界装备
	MingWen = 16,           --铭文背包
	SHENJI_EQUIP = 21,		--神机装备
	KUN = 22,				--鲲装备
	TIANSHEN_LINGHE = 23,	--天神灵核
	FIVE_ELEMENTS = 24,     --五行背包
	RELIC_LIGHT = 25,		--圣器背包 - 光
	RELIC_DRAK = 26,		--圣器背包 - 暗
	DRAGON_TEMPLE = 27,	 	--龙神殿背包
	CHARM = 28,             --符咒背包
	DAOHANG = 29,           --道行背包
	MengLing = 30,          --梦灵背包
	BAG_WAY_ESOTERICA = 31,	--秘籍背包
	BAG_WAY_BEAST = 32,		--驭兽背包
	BAG_WAY_MECHAN = 33,	--机甲背包
	BAG_WAY_THUDNER_MANA = 36, -- 雷法背包
	MAX = 36,
}

--宝石/升星/强化属性tips面板
ROLE_EQUIP_ATTR_TIPS = {
	STONE_TIP = 1,
	UP_STAR_TIP = 2,
	STREHGTH_TIP = 3,
	LINGYU_TIP = 4,		--灵玉属性
	STONE_AND_LINGYU_TIP = 5,		--宝石 和 灵玉属性一起显示
}

--装备觉醒相关枚举
EQUIP_AWAKE_OPERATOR = {
	EQUIP_AWAKEN_OPERA_TYPE_INFO = 0,		--获取觉醒装备
	EQUIP_AWAKEN_OPERA_TYPE_LEVELUP = 1,	--觉醒升级
	EQUIP_AWAKEN_OPERA_TYPE_MAX = 2, 		--枚举最大值
}

--装备附灵相关枚举
EQUIP_SPIRIT_OPERATOR = {
    EQUIP_SPIRIT_OPERA_TYPE_INFO = 0,    	-- 获取附灵信息
    EQUIP_SPIRIT_OPERA_TYPE_LEVELUP = 1,  	-- 附灵升级
}

EQUIP_SHENGPING_OPERATOR = {
    EQUIP_SHENGPING_OPERA_TYPE_INFO = 0,    	-- 获取升品信息
    EQUIP_SHENGPING_OPERA_TYPE_LEVELUP = 1,  	-- 升品升级
}

-- 装备升品 结果
EQUIP_SHENGPING_RESULT = {
	UP_STAR_SUCCESS = 1,		-- 升星成功
	UP_STAR_FAILURE = 2,		-- 升星失败
	UP_QUALITY_SUCCESS = 3,		-- 升品成功
	UP_QUALITY_FAILURE = 4,		-- 升品失败
}

IsPerAttr = {
	[GameEnum.BASE_CHARINTATTR_TYPE_SHANBI_PER] = 1,
	[GameEnum.BASE_CHARINTATTR_TYPE_MINGZHONG_PER] = 1,
	[GameEnum.BASE_CHARINTATTR_TYPE_BAOJI_PER] = 1,
	[GameEnum.BASE_CHARINTATTR_TYPE_KANGBAO_PER] = 1,
	[GameEnum.BASE_CHARINTATTR_TYPE_LIANJI_PER] = 1,
	[GameEnum.BASE_CHARINTATTR_TYPE_LIANJIKANG_PER] = 1,
	[GameEnum.BASE_CHARINTATTR_TYPE_JICHUANG_PER] = 1,
	[GameEnum.BASE_CHARINTATTR_TYPE_JICHUANGKANG_PER] = 1,
	[GameEnum.BASE_CHARINTATTR_TYPE_ZHUAGNBEI_SM_JC_PER] = 1,
	[GameEnum.BASE_CHARINTATTR_TYPE_ZHUAGNBEI_GJ_JC_PER] = 1,
	[GameEnum.BASE_CHARINTATTR_TYPE_ZHUAGNBEI_FY_JC_PER] = 1,
	[GameEnum.BASE_CHARINTATTR_TYPE_ZHUAGNBEI_PJ_JC_PER] = 1,
	[GameEnum.BASE_CHARINTATTR_TYPE_SHENGMING_JC_PER] = 1,
	[GameEnum.BASE_CHARINTATTR_TYPE_GONGJI_JC_PER] = 1,
	[GameEnum.BASE_CHARINTATTR_TYPE_JIANREN_PER] = 1,
	[GameEnum.BASE_CHARINTATTR_TYPE_CHUANTOU_PER] = 1,
	[GameEnum.BASE_CHARINTATTR_TYPE_YUANSU_SH_JC_PER] = 1,
	[GameEnum.BASE_CHARINTATTR_TYPE_YUANSU_HJ_JC_PER] = 1,
	[GameEnum.BASE_CHARINTATTR_TYPE_SHENGMING_QQ_JC_PER] = 1,
	[GameEnum.BASE_CHARINTATTR_TYPE_FANGTAN_JC_PER] = 1,
	[GameEnum.BASE_CHARINTATTR_TYPE_SHANGHAI_ZS_JC_PER] = 1,
	[GameEnum.BASE_CHARINTATTR_TYPE_FANGYU_ZS_JC_PER] = 1,
	[GameEnum.BASE_CHARINTATTR_TYPE_ZENGSHANG_BOSS_PER] = 1,
	[GameEnum.BASE_CHARINTATTR_TYPE_JIANSHANG_BOSS_PER] = 1,
	[GameEnum.BASE_CHARINTATTR_TYPE_ZENGSHANG_GUAIWU_PER] = 1,
	[GameEnum.BASE_CHARINTATTR_TYPE_JIANSHANG_GUAIWU_PER] = 1,
	[GameEnum.BASE_CHARINTATTR_TYPE_ZENGSHANG_BOSS_DK_PER] = 1,
	[GameEnum.BASE_CHARINTATTR_TYPE_JIANSHANG_BOSS_DK_PER] = 1,
	[GameEnum.BASE_CHARINTATTR_TYPE_ZENGSHANG_GUAIWU_DK_PER] = 1,
	[GameEnum.BASE_CHARINTATTR_TYPE_JIANSHANG_GUAIWU_DK_PER] = 1,
	[GameEnum.BASE_CHARINTATTR_TYPE_ZENGSHANG_PER] = 1,
	[GameEnum.BASE_CHARINTATTR_TYPE_JIANSHANG_PER] = 1,
	[GameEnum.BASE_CHARINTATTR_TYPE_ZENGSHANG_BS_PER] = 1,
	[GameEnum.BASE_CHARINTATTR_TYPE_JIANSHANG_BS_PER] = 1,
	[GameEnum.BASE_CHARINTATTR_TYPE_MINGZHONG_YC_PER] = 1,
	[GameEnum.BASE_CHARINTATTR_TYPE_DIKANG_YC_PER] = 1,
	[GameEnum.BASE_CHARINTATTR_TYPE_ZHILIAOXIAOGUO_PER] = 1,
	[GameEnum.BASE_CHARINTATTR_TYPE_ZENGSHANG_YC_PER] = 1,
	[GameEnum.BASE_CHARINTATTR_TYPE_GEDANG_PER] = 1,
	[GameEnum.BASE_CHARINTATTR_TYPE_PODANG_PER] = 1,
	[GameEnum.BASE_CHARINTATTR_TYPE_BAOJI_SHANGHAI_PER] = 1,
	[GameEnum.BASE_CHARINTATTR_TYPE_GEDANG_MS_PER] = 1,
	[GameEnum.BASE_CHARINTATTR_TYPE_SHENGMING_HF_PER] = 1,
	[GameEnum.BASE_CHARINTATTR_TYPE_ZENGSHANG_GX_PER] = 1,
	[GameEnum.BASE_CHARINTATTR_TYPE_ZENGSHANG_XR_PER] = 1,
	[GameEnum.BASE_CHARINTATTR_TYPE_SHANGHAI_JC_PER] = 1,
	[GameEnum.BASE_CHARINTATTR_TYPE_SHANGHAI_QUAN_JC_PER] = 1,
	[GameEnum.BASE_CHARINTATTR_TYPE_SHANGHAI_JM_PER] = 1,
	[GameEnum.BASE_CHARINTATTR_TYPE_SHANGHAI_QUAN_JM_PER] = 1,
	[GameEnum.BASE_CHARINTATTR_TYPE_YUANSU_JK_PER] = 1,
	[GameEnum.BASE_CHARINTATTR_TYPE_YUANSU_KX_PER] = 1,
	[GameEnum.BASE_CHARINTATTR_TYPE_LIANJI_SHANGHAI_PER] = 1,
	[GameEnum.BASE_CHARINTATTR_TYPE_SHAGUAI_JB_DIAOLUO_PER] = 1,
	[GameEnum.BASE_CHARINTATTR_TYPE_JINENG_SHANGHAI_ZJ_PER] = 1,
	[GameEnum.BASE_CHARINTATTR_TYPE_JINENG_SHANGHAI_JM_PER] = 1,
	[GameEnum.BASE_CHARINTATTR_TYPE_EQUIP_QIANGHUA_PER] = 1,
	[GameEnum.BASE_CHARINTATTR_TYPE_BOSS_ZHEN_SHANG] = 0,
	[GameEnum.BASE_CHARINTATTR_TYPE_BOSS_PALSY_PER] = 1,
	[GameEnum.BASE_CHARINTATTR_TYPE_BOSS_SECKILL_PER] = 1,
	[GameEnum.BASE_CHARINTATTR_TYPE_LEI_SHANGHAI_ZJ_PER] = 1,
	[GameEnum.BASE_CHARINTATTR_TYPE_LEI_SHANGHAI_JM_PER] = 1,
	[GameEnum.BASE_CHARINTATTR_TYPE_GEDANG_MS_MY_PER] = 1,
	[GameEnum.BASE_CHARINTATTR_TYPE_BAOJI_SHANGHAI_JM_PER] = 1,
	[GameEnum.BASE_CHARINTATTR_TYPE_LIANJI_SHANGHAI_JM_PER] = 1,
	[GameEnum.BASE_CHARINTATTR_TYPE_SHENGMING_ZB_ROLE_JC_PER] = 1,
	[GameEnum.BASE_CHARINTATTR_TYPE_GONGJI_ZB_ROLE_JC_PER] = 1,
	[GameEnum.BASE_CHARINTATTR_TYPE_FANGYU_ZB_ROLE_JC_PER] = 1,
	[GameEnum.BASE_CHARINTATTR_TYPE_POJIA_ZB_ROLE_JC_PER] = 1,
	[GameEnum.BASE_CHARINTATTR_TYPE_GONGJI_WUQI_JC_PER] = 1,
	[GameEnum.BASE_CHARINTATTR_TYPE_POJIA_WUQI_JC_PER] = 1,
	[GameEnum.BASE_CHARINTATTR_TYPE_GONGJI_SHIPIN_JC_PER] = 1,
	[GameEnum.BASE_CHARINTATTR_TYPE_KILL_MONSTER_PER_EXP] = 1,
	[GameEnum.BASE_CHARINTATTR_TYPE_BAOJI_JC_PER] = 1,
	[GameEnum.BASE_CHARINTATTR_TYPE_KANGBAO_JC_PER] = 1,
	[GameEnum.BASE_CHARINTATTR_TYPE_RARE_EXTERIOR_RATE_PER] = 1,
	[GameEnum.BASE_CHARINTATTR_TYPE_RARE_EQUIP_RATE_PER] = 1,


	[GameEnum.FIGHT_CHARINTATTR_TYPE_SHANBI_PER] = 1,
	[GameEnum.FIGHT_CHARINTATTR_TYPE_MINGZHONG_PER] = 1,
	[GameEnum.FIGHT_CHARINTATTR_TYPE_BAOJI_PER] = 1,
	[GameEnum.FIGHT_CHARINTATTR_TYPE_KANGBAO_PER] = 1,
	[GameEnum.FIGHT_CHARINTATTR_TYPE_LINAJI_PER] = 1,
	[GameEnum.FIGHT_CHARINTATTR_TYPE_LIANJIKANG_PER] = 1,
	[GameEnum.FIGHT_CHARINTATTR_TYPE_JICHUANG_PER] = 1,
	[GameEnum.FIGHT_CHARINTATTR_TYPE_JICHUANGKANG_PER] = 1,
	[GameEnum.FIGHT_CHARINTATTR_TYPE_ZHUAGNBEI_SM_JC_PER] = 1,
	[GameEnum.FIGHT_CHARINTATTR_TYPE_ZHUAGNBEI_GJ_JC_PER] = 1,
	[GameEnum.FIGHT_CHARINTATTR_TYPE_ZHUAGNBEI_FY_JC_PER] = 1,
	[GameEnum.FIGHT_CHARINTATTR_TYPE_ZHUAGNBEI_PJ_JC_PER] = 1,
	[GameEnum.FIGHT_CHARINTATTR_TYPE_SHENGMING_JC_PER] = 1,
	[GameEnum.FIGHT_CHARINTATTR_TYPE_GONGJI_JC_PER] = 1,
	[GameEnum.FIGHT_CHARINTATTR_TYPE_JIANREN_PER] = 1,
	[GameEnum.FIGHT_CHARINTATTR_TYPE_CHUANTOU_PER] = 1,
	[GameEnum.FIGHT_CHARINTATTR_TYPE_YUANSU_SH_JC_PER] = 1,
	[GameEnum.FIGHT_CHARINTATTR_TYPE_YUANSU_HJ_JC_PER] = 1,
	[GameEnum.FIGHT_CHARINTATTR_TYPE_SHENGMING_QQ_JC_PER] = 1,
	[GameEnum.FIGHT_CHARINTATTR_TYPE_FANGTAN_JC_PER] = 1,
	[GameEnum.FIGHT_CHARINTATTR_TYPE_SHANGHAI_ZS_JC_PER] = 1,
	[GameEnum.FIGHT_CHARINTATTR_TYPE_FANGYU_ZS_JC_PER] = 1,
	[GameEnum.FIGHT_CHARINTATTR_TYPE_ZENGSHANG_BOSS_PER] = 1,
	[GameEnum.FIGHT_CHARINTATTR_TYPE_JIANSHANG_BOSS_PER] = 1,
	[GameEnum.FIGHT_CHARINTATTR_TYPE_ZENGSHANG_GUAIWU_PER] = 1,
	[GameEnum.FIGHT_CHARINTATTR_TYPE_JIANSHANG_GUAIWU_PER] = 1,
	[GameEnum.FIGHT_CHARINTATTR_TYPE_ZENGSHANG_BOSS_DK_PER] = 1,
	[GameEnum.FIGHT_CHARINTATTR_TYPE_JIANSHANG_BOSS_DK_PER] = 1,
	[GameEnum.FIGHT_CHARINTATTR_TYPE_ZENGSHANG_GUAIWU_DK_PER] = 1,
	[GameEnum.FIGHT_CHARINTATTR_TYPE_JIANSHANG_GUAIWU_DK_PER] = 1,
	[GameEnum.FIGHT_CHARINTATTR_TYPE_ZENGSHANG_PER] = 1,
	[GameEnum.FIGHT_CHARINTATTR_TYPE_JIANSHANG_PER] = 1,
	[GameEnum.FIGHT_CHARINTATTR_TYPE_ZENGSHANG_BS_PER] = 1,
	[GameEnum.FIGHT_CHARINTATTR_TYPE_JIANSHANG_BS_PER] = 1,
	[GameEnum.FIGHT_CHARINTATTR_TYPE_MINGZHONG_YC_PER] = 1,
	[GameEnum.FIGHT_CHARINTATTR_TYPE_DIKANG_YC_PER] = 1,
	[GameEnum.FIGHT_CHARINTATTR_TYPE_ZHILIAOXIAOGUO_PER] = 1,
	[GameEnum.FIGHT_CHARINTATTR_TYPE_ZENGSHANG_YC_PER] = 1,
	[GameEnum.FIGHT_CHARINTATTR_TYPE_GEDANG_PER] = 1,
	[GameEnum.FIGHT_CHARINTATTR_TYPE_PODANG_PER] = 1,
	[GameEnum.FIGHT_CHARINTATTR_TYPE_BAOJI_SHANGHAI_PER] = 1,
	[GameEnum.FIGHT_CHARINTATTR_TYPE_GEDANG_MS_PER] = 1,
	[GameEnum.FIGHT_CHARINTATTR_TYPE_SHENGMING_HF_PER] = 1,
	[GameEnum.FIGHT_CHARINTATTR_TYPE_ZENGSHANG_GX_PER] = 1,
	[GameEnum.FIGHT_CHARINTATTR_TYPE_ZENGSHANG_XR_PER] = 1,
	[GameEnum.FIGHT_CHARINTATTR_TYPE_SHANGHAI_JC_PER] = 1,
	[GameEnum.FIGHT_CHARINTATTR_TYPE_SHANGHAI_QUAN_JC_PER] = 1,
	[GameEnum.FIGHT_CHARINTATTR_TYPE_SHANGHAI_JM_PER] = 1,
	[GameEnum.FIGHT_CHARINTATTR_TYPE_SHANGHAI_QUAN_JM_PER] = 1,
	[GameEnum.FIGHT_CHARINTATTR_TYPE_YUANSU_JK_PER] = 1,
	[GameEnum.FIGHT_CHARINTATTR_TYPE_YUANSU_KX_PER] = 1,
	[GameEnum.FIGHT_CHARINTATTR_TYPE_LIANJI_SHANGHAI_PER] = 1,
	[GameEnum.FIGHT_CHARINTATTR_TYPE_SHAGUAI_JB_DIAOLUO_PER] = 1,
	[GameEnum.FIGHT_CHARINTATTR_TYPE_JINENG_SHANGHAI_ZJ_PER] = 1,
	[GameEnum.FIGHT_CHARINTATTR_TYPE_JINENG_SHANGHAI_JM_PER] = 1,
	[GameEnum.FIGHT_CHARINTATTR_TYPE_EQUIP_QIANGHUA_PER] = 1,
	[GameEnum.FIGHT_CHARINTATTR_TYPE_BOSS_ZHENSHANG] = 0,
	[GameEnum.FIGHT_CHARINTATTR_TYPE_BOSS_PALSY_PER] = 1,
	[GameEnum.FIGHT_CHARINTATTR_TYPE_BOSS_SECKILL_PER] = 1,
	[GameEnum.FIGHT_CHARINTATTR_TYPE_LEI_SHANGHAI_ZJ_PER] = 1,
	[GameEnum.FIGHT_CHARINTATTR_TYPE_LEI_SHANGHAI_JM_PER] = 1,
	[GameEnum.FIGHT_CHARINTATTR_TYPE_GEDANG_MS_MY_PER] = 1,
	[GameEnum.FIGHT_CHARINTATTR_TYPE_BAOJI_SHANGHAI_JM_PER] = 1,
	[GameEnum.FIGHT_CHARINTATTR_TYPE_LIANJI_SHANGHAI_JM_PER] = 1,
	[GameEnum.FIGHT_CHARINTATTR_TYPE_SHENGMING_ZB_ROLE_JC_PER] = 1,
	[GameEnum.FIGHT_CHARINTATTR_TYPE_GONGJI_ZB_ROLE_JC_PER] = 1,
	[GameEnum.FIGHT_CHARINTATTR_TYPE_FANGYU_ZB_ROLE_JC_PER] = 1,
	[GameEnum.FIGHT_CHARINTATTR_TYPE_POJIA_ZB_ROLE_JC_PER] = 1,
	[GameEnum.FIGHT_CHARINTATTR_TYPE_GONGJI_WUQI_JC_PER] = 1,
	[GameEnum.FIGHT_CHARINTATTR_TYPE_POJIA_WUQI_JC_PER] = 1,
	[GameEnum.FIGHT_CHARINTATTR_TYPE_GONGJI_SHIPING_JC_PER] = 1,
	[GameEnum.FIGHT_CHARINTATTR_TYPE_KILL_MONSTER_PER_EXP] = 1,
	[GameEnum.FIGHT_CHARINTATTR_TYPE_BAOJI_JC_PER] = 1,
	[GameEnum.FIGHT_CHARINTATTR_TYPE_KANGBAO_JC_PER] = 1,
}

FIGHT_POWER_ENUM = {

}

ATTR_SORT = {

}

-- 特效数量限制
UI_Effect_MAX_NUM = {
	["UI_caishenjuan_get"] = 5,
}

--所有界面特效
Ui_Effect = {
	UI_click = "UI_click",
	UI_xianyu_zhakai = "UI_lingyu_get",		-- 仙玉炸开
	UI_xianyu_single = "UI_lingyu_get",				-- 仙玉单个
	UI_bangyu_zhakai = "UI_yuanbao_get",			-- 绑玉炸开
	UI_bangyu_single = "UI_yuanbao_get",			-- 绑玉单个
	UI_recharge_volume_zhakai = "UI_caishenjuan_get",		-- 充值券炸开
	UI_recharge_volume_single = "UI_caishenjuan_get",		-- 充值券单个

	UI_qhjm = "UI_qhjm",
	UI_qhtx = "UI_qhtx",
	UI_xltx = "UI_xltx",
	UI_xilianchenggong = "UI_xilianchenggong",
	UI_jjtx = "UI_jjtx",
	UI_jinjiechenggong = "UI_jinjiechenggong",
    UI_shengxingchenggong = "UI_shengxingchenggong",
	UI_shengxing = "UI_shengxing",
	UI_tianshenzhuzi_baodian = "UI_tianshenzhuzi_baodian",
	UI_tscg = "UI_tscg",
    UI_fmtx = "UI_fmtx",
	UI_fumochenggong = "UI_fumochenggong",
	UI_bsxqtx = "UI_bsxqtx",
    UI_bsjm_1 = "UI_bsjm_1",
    UI_ronglian_baozha = "UI_ronglian_baozha",
	UI_xiangqianchenggong = "UI_xiangqianchenggong",
	UI_jlgctx = "UI_jlgctx",
	UI_jinglianchenggong = "UI_jinglianchenggong",
	UI_zxtx = "UI_zxtx",
	UI_zstx = "UI_zstx",
	UI_duanzaochenggong = "UI_duanzaochenggong",
	UI_jxtx = "UI_jxtx",
	UI_fltx = "UI_fltx",
	UI_jinjietexiao = "UI_jinjietexiao",
    UI_jinjietexiao_two = "UI_jinjietexiao_two",
	UI_huanhua = "UI_huanhua",
	UI_shengjichenggong = "UI_shengjichenggong",
	UI_dysytx = "UI_dysytx",
	UI_tftstx = "UI_tftstx",
	UI_xzsj_0 = "UI_xzsj_0",
	UI_xzsj_1 = "UI_xzsj_1",
	UI_lsjh = "UI_lsjh",
	UI_lssh = "UI_lssh",
    UI_lssh_1 = "UI_lssh_1",
    UI_xiuxian_yunwu = "UI_xiuxian_yunwu",
	UI_shenhuachenggong = "UI_shenhuachenggong",
	UI_hecheng_shuaxin = "UI_hecheng_shuaxin",
	UI_hechengmianban = "UI_hechengmianban",
	UI_hechengmianban1 = "UI_hechengmianban1",
	UI_hechengmianban2 = "UI_hechengmianban2",
	UI_hechengmianban3 = "UI_hechengmianban3",
	UI_hechengmianban_4 = "UI_hechengmianban_4",
	UI_hechengbaodian = "UI_hechengbaodian",
	UI_shenshoubeibao = "UI_shenshoubeibao",
	UI_shenshouqianghua = "UI_shenshouqianghua",
	UI_qianghuachenggong = "UI_qianghuachenggong",
	UI_zhuzhanchenggong = "UI_zhuzhanchenggong",
	UI_fuwenfenjie = "UI_fuwenfenjie",
	UI_fuwen_fenjie = "UI_fuwen_fenjie",
	UI_juebanfuwen = "UI_juebanfuwen",
	UI_sanshuxingfuwen = "UI_sanshuxingfuwen",
	UI_jyqtx = "UI_jyqtx",
	UI_juanxianchenggong = "UI_juanxianchenggong",
	UI_shenqi_jihuo = "UI_shenqi_jihuo",
	UI_jihuochenggong = "UI_jihuochenggong",
	UI_shenqi_fuling = "UI_shenqi_fuling",
	UI_jiehun = "UI_jiehun",
	UI_Effect_hunjie_bao_aixn_01 = "UI_Effect_hunjie_bao_aixn_01",
	UI_baby_jihuo = "UI_baby_jihuo",
	UI_baby_jinjie = "UI_baby_jinjie",
	UI_goumaichenggong = "UI_goumaichenggong",
	UI_ronglian = "UI_ronglian",
	UI_ronglianchenggong = "UI_ronglianchenggong",

	UI_fuwenchoujian = "UI_fuwenchoujian",
	UI_bizuo_BK = "UI_bizuo_BK",
	UI_bizuo_XBK = "UI_bizuo_XBK",
	UI_fuwenzhanshi_cheng = "UI_fuwenzhanshi_cheng",
	UI_fuwenzhanshi_hong = "UI_fuwenzhanshi_hong",
	UI_fuwenzhanshi_fen = "UI_fuwenzhanshi_fen",
	UI_fuwenzhanshi_zise = "UI_fuwenzhanshi_zise",
	UI_fuwenzhanshi_chengse = "UI_fuwenzhanshi_chengse",
	UI_fuwenzhanshi_hongse = "UI_fuwenzhanshi_hongse",
	UI_fuwenzhanshi_fense = "UI_fuwenzhanshi_fense",
	UI_juebanxianfu = "UI_juebanxianfu",
	UI_qizhenyibao_gaoji = "UI_qizhenyibao_gaoji",
	UI_qizhenyibao_dingji = "ui_qizhenyibao_dingji",
	UI_tower = "UI_tower",
	UI_zhushenta = "UI_zhushenta",
	UI_sjfuli_lingqu = "UI_sjfuli_lingqu",
	UI_sevendays_lingqu = "UI_sevendays_lingqu",
	UI_qifuchenggong = "UI_qifuchenggong",
	UI_tongqianqifu = "UI_tongqianqifu",

	UI_duihua = "UI_duihua",
	UI_renwu = "UI_renwu",
	UI_renwuwancheng = "ui_renwuwancheng",
	UI_tubiao1 = "ui_tubiao1",
	UI_ddk = "UI_ddk",
	UI_xdk = "UI_xdk",
	UI_wpk_hongse = "UI_wpk_hongse",
	UI_wpk_zise = "UI_wpk_zise",
	UI_wpk_chengse = "UI_wpk_chengse",
	UI_wpk_fense = "UI_wpk_fense",

	UI_pzk_zi = "UI_pzk_zi",
	UI_pzk_hong = "UI_pzk_hong",
	UI_pzk_cheng = "UI_pzk_cheng",
	UI_pzk_fen = "UI_pzk_fen",

	UI_choujiangbaodian = "UI_choujiangbaodian",
	UI_hechengchenggong = "UI_hechengchenggong",
	UI_lmtx = "UI_lmtx",
	UI_cg = "UI_cg",
	UI_songhuabian_homeigu = "UI_songhua_Z",
	UI_songhuaxinxing_hong = "UI_songhua_Z",
	UI_songhua999 = "UI_songhua_G",

	effect_yanhua_zise = "eff_yanhua_fen_D",
	effect_yanhua_da = "eff_yanhua_fen_G",
	UI_dianjijinru = "UI_dianjijinru",
	UI_ywtx = 'UI_ywtx',
	UI_effect_skill = 'UI_effect_skill',
	UI_fuwenfenjie_g_01 = 'UI_fuwenfenjie_g_01',
	UI_fuwenfenjie_g_02 = 'UI_fuwenfenjie_g_02',
	UI_fuwenfenjie_g_03 = 'UI_fuwenfenjie_g_03',

	UI_title_eff_0 = "UI_ptchenghao",
	UI_title_eff_1 = "UI_gjchenghao",

	ui_bianshenqian = "UI_bsan",
	ui_bianshenhou = "UI_bsan_1",
	ui_bianshenbaodian = "ui_bianshenbaodian",
	ui_jinenghebing = "ui_jinenghebing",
	UI_qifutongqian = "UI_yaoqianshu",
	UI_qifujingyan = "UI_qifu_guang",
	UI_SXCG = "UI_SXCG",
	UI_SJcg = "UI_SJcg",
	UI_SJCG_F2 = "UI_SJCG_F2",
	UI_SJiCG_F2 = "UI_SJiCG_F2",

	UI_PetSkill = "UI_PetSkill",
	UI_JuanXian_1 = "UI_juanxianchenggong_di",
	UI_JuanXian_2 = "UI_juanxianchenggong_zhong",
	UI_JuanXian_3 = "UI_juanxianchenggong_gao",

	UI_vip_jin = "UI_vip_jin",
	UI_vip_hui = "UI_vip_hui",
	UI_juexingchenggong = "UI_juexingchenggong",
	UI_jichengchenggong = "UI_jichengchenggong",

	UI_fulingjindutiao = "UI_fulingjindutiao",
	UI_fulingshuxing = "UI_fulingshuxing",
	UI_fulingchenggong = "UI_fulingchenggong",
	UI_tishitexiao_T = "UI_tishitexiao_T",
	UI_jinyanqiu_zhakai = "UI_jinyanqiu_zhakai",
	UI_jinyanqiu_guangqiu = "UI_jinyanqiu_guangqiu",
    UI_xianyu_guangdian = "UI_xianyu_guangdian",
	UI_jiesuochenggong = "UI_jiesuochenggong",
	UI_longhunjindutiao = "UI_longhunjindutiao",
	UI_HC2xian = "UI_hechengmianban_2_F2",
	UI_HCxian1 = "UI_hechengmianban_F2",
	UI_jinyanqiu_wpk = "UI_jinyanqiu_wpk",
	UI_jbxl = "UI_jbxl",
	UI_jbxl_1 = "UI_jbxl_1",
    UI_yuanbao_zhakai_mao = "UI_yuanbao_zhakai_mao",

	-- UI_kuafu_3v3_01 = "UI_kuafu_3v3_01",
	UI_bdjn_jiesou = "UI_bdjn_jiesou",
	UI_chuanjianjuese = "UI_chuanjianjuese",
	lige_liuguang = "lige_liuguang",
	pojun_liuguang = "pojun_liuguang",
	cangqiong_liuguang = "cangqiong_liuguang",
	qianji_liuguang = "qianji_liuguang",

	UI_lianhua_zi = "UI_lianhua_zi",
	UI_lianhua_cheng = "UI_lianhua_cheng",
	UI_lianhua_hong = "UI_lianhua_hong",
	UI_lianhua_fen = "UI_lianhua_fen",
	UI_lianhua_cai = "UI_lianhua_cai",

	UI_hudie_zi = "UI_hudie_zi",
	UI_hudie_cheng = "UI_hudie_cheng",
	UI_hudie_hong = "UI_hudie_hong",
	UI_hudie_fen = "UI_hudie_fen",
	UI_hudie_cai = "UI_hudie_cai",
	UI_fuzhirongjie = "fuzhirongjie",

	UI_duanweitisheng = "UI_duanweitisheng",
	UI_duanweixiajiang = "UI_duanweixiajiang",
	ui_lygbowen = "ui_lygbowen",
	ui_lingquyeqian = "ui_lingquyeqian",
	UI_renyichongzhi = "UI_renyichongzhi",
	UI_leijichongzhi = "UI_leijichongzhi",
	UI_xingxing_jiemian = "UI_xingxing_jiemian",
	UI_xingxing_fense = "UI_xingxing_fense",
	UI_xingxing_jinse = "UI_xingxing_jinse",
	UI_shuxingdan = "UI_shuxingdan",
	UI_yxbk = "UI_yxbk",
	UI_jiNeng_up = "ui_jineng_up",
	UI_skill_icon_gq_01 = "UI_skill_icon_gq_01",
	UI_role_talent_levelup_01 = "UI_role_talent_levelup_01",
    UI_shuijingqiu_shanguang = "UI_shuijingqiu_shanguang",
    UI_kapaijihuo_lan = "UI_kapaijihuo_lan",
    UI_kapaijihuo_zi = "UI_kapaijihuo_zi",
    UI_kapaijihuo_cheng = "UI_kapaijihuo_cheng",
    UI_kapaijihuo_hong = "UI_kapaijihuo_hong",
    UI_kapaijihuo_fen = "UI_kapaijihuo_fen",
    UI_kapai_fen = "UI_kapai_fen",
    UI_kapai_hong = "UI_kapai_hong",
    UI_kapai_lan = "UI_kapai_lan",
    UI_kapai_zi = "UI_kapai_zi",
    UI_kapai_cheng = "UI_kapai_cheng",
    UI_kapai_jin = "UI_kapai_jin",
    UI_kapai_xuancai = "UI_kapai_xuancai",
    UI_fenjielu = "UI_fenjielu",
    UI_fenjie1 = "UI_fenjie1",
    UI_fenjie2 = "UI_fenjie2",
    UI_hechengmianban_3_F2 = "UI_hechengmianban_3_F2",
    UI_fscg = "UI_fscg",
    UI_xbcg = "UI_xbcg",
    UI_yuanxing_SC = "UI_yuanxing_SC",
    UI_bianqiang = "UI_bianqiang",
	UI_qhtx_cz_F2 = "UI_qhcg_00",			-- 强化成功 带锤子
	UI_xilian_bd_F2 = "UI_xilian_baodian", 	-- 洗炼成功爆点
	UI_qhtx_F2 = "UI_qhcg_02",				-- 强化成功
	UI_xlcg_F2 = "UI_xlcg",					-- 洗炼成功
	UI_sxcg_F2 = "UI_sxcg_00",				-- 升星成功
	UI_sxsb_F2 = "UI_sxsb",					-- 升星失败
	UI_spcg_F2 = "UI_spcg",					-- 升品成功
	UI_spsb_F2 = "UI_spsb",					-- 升品失败
	UI_SXCG_F2 = "UI_SXCG_F2",
	UI_TPCG_F2 = "UI_TPCG_F2",

	UI_husong_baozha = "UI_husong_baozha",  --护送爆炸特效

    UI_tiansheng_huohua = "UI_tiansheng_huohua",
    UI_tiansheng_juqi = "UI_tiansheng_juqi",
    UI_tianshen_jiesuo = "UI_tianshen_jiesuo",
	UI_common_cg_A3 = "UI_cuihuochenggong_lhl",	-- 成功特效 （需加文字组装）
	UI_common_sb_A3 = "UI_jinglianshibai_lhl",			-- 失败特效	（需加文字组装）
	UI_longhun_baodian = "UI_longhun_baodian",
    UI_tianshen_shengji = "UI_tianshen_shengji",
    UI_daoju_biankuang_liziguang = "UI_daoju_biankuang_liziguang_01",
    UI_zhuangbeihuo = "UI_zhuangbeihuo",
    UI_Vip_up_jingse = "UI_guizu_shengji_jinse",
    UI_Vip_invaild = "UI_guizu_shengji_yinse",
    UI_baguashangguang = "UI_baguashangguang",

    UI_baojiatexiao = "UI_baojiatexiao",
    UI_zhuanpanka_gj = "UI_zhuanpanka_gj",
    UI_zhuanpanka_huitan_lv = "UI_zhuanpanka_huitan_lv",
    UI_zhuanpanka_huitan_lan = "UI_zhuanpanka_huitan_lan",
    UI_zhuanpanka_huitan_zi = "UI_zhuanpanka_huitan_zi",
    UI_zhuanpanka_huitan_cheng = "UI_zhuanpanka_huitan_cheng",
    UI_zhuanpanka_huitan_hong = "UI_zhuanpanka_huitan_hong",
    UI_zhuanpanka_huitan_fen = "UI_zhuanpanka_huitan_fen",
    UI_zhuanpanka_huitan_jin = "UI_zhuanpanka_huitan_jin",
    UI_zhuanpanka_huitan_cai = "UI_zhuanpanka_huitan_cai",
    UI_rengjidan_01 = "UI_rengjidan_01",
	UI_mobai_01 = "UI_mobai_01",

	UI_shaoguang_02 = "UI_shaoguang_02",
	UI_baokai_xuanwo_02 = "UI_baokai_xuanwo_02",
	UI_baoshi_xiangqian_01 = "UI_baoshi_xiangqian_01",
	UI_shengyin_baozha = "UI_shengyin_baozha",

	UI_tzjmqh_lhl = "UI_tzjmqh_lhl",
	UI_tzjmbaozha_lhl = "UI_tzjmbaozha_lhl",

	UI_ningqidianji_lhl = "UI_ningqidianji_lhl",
	UI_jiantou_lhl = "UI_jiantou_lhl",

	UI_Remind_BaseCell_Effect = "UI_kuang_fang",			-- 物品框通用可领取特效.
	UI_Remind_Circle_BaseCell_Effect = "UI_kuang_yuan",		-- 圆形物品框通用可领取特效.

	UI_bybt_huangjin = "UI_bybt_huangjin",			--百亿补贴黄金
	UI_bybt_heijin = "UI_bybt_heijin",				--百亿补贴黑金
	UI_bybt_liujin = "UI_bybt_liujin",				--百亿补贴鎏金

	UI_zhendizhan_shuibowen = "UI_zhendizhan_shuibowen",
	UI_huanshou_choujiang = "UI_huanshou_choujiang",		--幻兽抽奖
	UI_huanshou_qiehuan = "UI_huanshou_qiehuan",			--幻兽抽奖切换
}

BaseCell_Ui_Effect = {
	--[3] = 'UI_wupinkuang_zise',		--普通紫色
	[4] = 'UI_wupinkuang_l2_cheng',		--普通橙色
	[5] = 'UI_wupinkuang_l2_hong',		--普通红色
	[6] = 'UI_wupinkuang_l3_fen',		--普通粉色
	[7] = 'UI_wupinkuang_l3_jin',		--普通金色
	[8] = 'UI_wupinkuang_l3_cai',		--普通炫彩
	[9] = "UI_wupinkuang_l3_heijin",
	[10] = "UI_wupinkuang_l3_qin",

}

BaseCell_Ui_Circle_Effect = {
	-- [1] = "UI_wupinyuankuang_lvse",		--普通绿色
	-- [2] = "UI_wupinyuankuang_lanse",	--普通蓝色
	-- [3] = "UI_wupinyuankuang_zise",		--普通紫色
	[4] = 'UI_wupinkuang_yuan_l2_cheng',		--普通橙色
	[5] = 'UI_wupinkuang_yuan_l2_hong',		--普通红色
	[6] = 'UI_wupinkuang_yuan_l3_fen',		--普通粉色
	[7] = 'UI_wupinkuang_yuan_l3_jin',		--普通金色
	[8] = 'UI_wupinkuang_yuan_l3_cai',		--普通炫彩
}

UIEffectName = {
    f_dujie               = "a1_f_dujie",
	f_hecheng             = "a3_f_hecheng",
	f_shengpin            = "a1_f_shengpin",
	f_jinglian            = "a3_f_jinglian",
	f_shengxing           = "a1_f_shengxing",
	f_lianhua             = "a1_f_lianhua",
	f_xiulian             = "a1_f_xiulian",
	f_jinhua              = "a1_f_jinhua",
	f_qianghuabaokai      = "UI_xiguang_baokai",
	f_tupo                = "a1_f_tupo",
	f_fumo                = "a1_f_fumo",
	f_tongmai			  = "a2_f_tongmai",
	f_xuexi				  = "a1_f_xuexi",
	f_rulinsb			  = "a2_f_rulinsb",
	f_shangzhen			  = "a3_ty_szsb",
	f_bslx_hdcw           = "a3_boss_lx_hdcw",


	s_duanzao             = "a3_s_lianzhi",
	s_dujie               = "a1_s_dujie",
	s_fuling              = "a1_s_fuling",
	s_fuling_attr_add     = "a1_s_fuling_attr_add",
	s_hecheng             = "a3_s_hecheng",
	s_jicheng             = "a1_s_jicheng",
	s_jihuo               = "a3_s_jihuo",
	s_keyin               = "a1_s_keyin",  
	s_jinglian            = "a3_s_jinglian",
	s_jinjie              = "a1_s_jinjie",
	s_juanxian            = "a1_s_jxcg",
	s_juexing             = "a1_s_juexing",
	s_qianghua            = "a3_s_cuhuo",
	s_qianghua1           = "a3_ty_jfcg",
	s_ronglian            = "a1_s_ronglian",
	s_shengji             = "a3_s_shengji",
	s_shengpin            = "a1_s_shengpin",
	s_shengxing           = "a1_s_shengxing",
	s_xiangqian           = "a1_s_xiangqian",
	s_zhuzhan             = "a1_s_zhuzhan",
	s_xilian              = "a1_s_xilian",
	s_shengjie            = "a1_s_shengjie",
	s_vip_double          = "a1_s_vipdouble",
	s_renwu               = "a2_s_renwu",
	s_goumai              = "a1_s_goumai",
    s_fenjiecg            = "a1_s_fenjiecg",
	s_tupo                = "a3_ty_tupo",
	s_zhulin              = "a1_s_zhulin",
	s_keming              = "a1_s_keming",
	s_jiefeng             = "a1_s_jiefeng",
	s_xiulian             = "a1_s_xiulian",
	s_jinhua              = "a1_s_jinhua",
	s_zhuanzhi            = "a1_s_zhuanzhi",
	s_fumo                = "a1_s_fumo",
	s_ningju			  = "a1_s_ningju",
	s_tongmai			  = "a2_s_tongmai",
	s_feisheng			  = "a1_s_feisheng",
	s_xuexi				  = "a1_s_xuexi",
	s_rulincg			  = "a2_s_rulincg",
	ty_shengji            = "a3_ty_djts",
	s_jinsheng            = "a3_s_jinsheng",
	ty_zhuoling           = "a3_ty_zhuoling",
	s_shangzhen			  = "a3_ty_szcg",
	s_bslx_hdzq           = "a3_boss_lx_hdzq",
	s_djts                = "a3_ty_djts",
	s_jsts                = "a3_ty_jsts",
}

--灵犀六芒图气泡特效
XIANLINGGUZHEN_BUBBLE_EFFECT = {
	[1] = "UI_rongjie_qiu_lvse",
	[2] = "UI_rongjie_qiu_lanse",
	[3] = "UI_rongjie_qiu_zise",
	[4] = "UI_rongjie_qiu_chengse",
	[5] = "UI_rongjie_qiu_hongse",
	[6] = "UI_rongjie_qiu_fense",
	[7] = "UI_rongjie_qiu_huangse",
	[8] = "UI_rongjie_qiu_qicai",
}

--变身枚举
BIANSHEN_OPERA_TYPE = {
    BIANSHEN_OPERA_TYPE_ALL_INFO = 0,
    BIANSHEN_OPERA_TYPE_UPGRADE = 1,            -- 升级    param1 物品id
    BIANSHEN_OPERA_TYPE_UPGRADE_MAGIC = 2,      -- 升级幻装  param1 形象索引 param2 碎片索引
    BIANSHEN_OPERA_TYPE_USE_IMAGE = 3,          -- 使用形象  param1 形象id
    BIANSHEN_OPERA_TYPE_UNUSE_IMAGE = 4,        -- 还原形象  param1 形象id
    BIANSHEN_OPERA_TYPE_RECAST_ONE = 5,         -- 重铸一次  param1 重铸类型 param2 形象索引 param3 碎片索引
    BIANSHEN_OPERA_TYPE_RECAST_TEN = 6,         -- 重铸十次  param1 重铸类型 param2 形象索引 param3 碎片索引
    BIANSHEN_OPERA_TYPE_TUPO = 7,               -- 突破    param1 形象索引 param2 碎片索引
    BIANSHEN_OPERA_TYPE_BIANSHEN = 8,           -- 请求变身
}

--重铸类型
BIANSHEN_RECAST_TYPE = {
  BIANSHEN_RECAST_TYPE_LOW = 0,              -- 初级重铸
  BIANSHEN_RECAST_TYPE_HIGH = 1,             -- 高级重铸

  BIANSHEN_RECAST_TYPE_MAX = 2,
}



--烟花庆典
RA_YANHUA_QINGDIAN_OPERA_TYPE = {
	CHOU_ONE = 1,	--抽奖1次
	CHOU_TEN = 2,	--抽奖10次
}

KFPVP_TYPE = {
	ONE = 1,		-- 1v1
	MORE = 2, 		-- 3v3
}

COST_REASON_TYPE = {
  COST_REASON_INVALID = 0,            		-- 无效
  COST_GIFT = 1,                  			-- 打开礼包消耗
  COST_CREATE_GUILD = 2,					-- 创建帮派消耗
  COST_PAIMAI = 3,							-- 市场拍卖
  COST_NEW_PAIMAI = 10,						-- 市场拍卖2
  ADD_PICK_COIN = 1001,          			-- 增加拾取铜币
  CREATE_GUILD_FAIL = 1002,					-- 创建帮派失败
  QIFU_ADD_COIN = 1004,						-- 祈福获得
  COIN_FB_ADD_MONEY = 1003,        			-- 铜币副本增加
  GOLD_MAIN_TASK_MONEY = 1005,				-- 主线任务获得元宝
  YUANBAO_REDPAPER_CHANG = 1006,			-- 红包元宝获得
  YUANBAO_QIFU_CHANG = 1007,				-- 祈福元宝获得
  YUANBAO_GUILDSALARY_CHANG = 1008,			-- 仙盟工资元宝获得
  YUANBAO_HUMO_CHANG = 1009,				-- 好友虎摸元宝获得
  YUANBAOKA_CHANGE = 1010,					-- 使用元宝卡获得
  SILVER_ADD_FETCH_TOTAL_REDPAPER = 1011,	-- 领取红包累积元宝
  SILVER_ADD_ACTIVE_ROLL = 1012,			-- 活跃转盘获得元宝
  SUIT_EQUIP_ADD_COIN = 1013,				-- 套装技能增加铜币
  GOLD_ADD_MERGE_ZCMIAIMIAO = 1016,			-- 合服活动招财喵喵获得仙玉
  DianZan_Add_Money = 1020,                 -- 排行榜点赞金币增加
  GOLD_ADD_FORTUNE_CAT = 1021,			    -- 运营活动-招财猫
  BIND_GOLD_ADD_FIRST_BOSS_PERSON_KILL = 1022,	-- BOSS个人首杀
  REBATE_DAY_ACTIVITY = 1023,	            -- 七日返利宝箱
  GOLD_ADD_CROSS_GOLD_ZHUANGPAN = 1024,		-- 仙玉转盘
  GOLD_ADD_WEALTH_GOD_DRAW = 1025,	        -- 喜迎财神 - 抽奖
};

-- 巡游类型
CruiseType = {
	JuPai = 0,			-- 举牌
	HuaTong = 1,		-- 花童
	HuaJiao = 2,		-- 花轿
	-- QiaoFu = 1,
	LaBa = 4,			-- 喇叭
	QiaoLuo = 5,		-- 敲锣
}

-- 任务剧情文字显示类型
TaskStoryTextType = {
	None = 0,
	CaiJi = 1,          -- 采集
	Accept = 2,			-- 接受任务
	Commit = 3,			-- 完成任务
	PassFuBen = 4,		-- 通过副本
}



--聊天频道屏蔽
CHAT_PINGBI_CUSTOM_TYPE = {
  ENUM_CUSTOM_TYPE_CHAT_TEAM = 0,    -- 队伍 屏蔽
  ENUM_CUSTOM_TYPE_CHAT_WORLD = 1,  -- 世界 屏蔽
  ENUM_CUSTOM_TYPE_CHAT_GUILD = 2,  -- 仙盟工会 屏蔽
  ENUM_CUSTOM_TYPE_CHAT_SYSTEM = 3, --  系统 屏蔽
  ENUM_CUSTOM_TYPE_CHAT_CROSS = 4,  -- 跨服信息 屏蔽
}

ChatChannelCustomEnum = {
	[CHANNEL_TYPE.TEAM] = CHAT_PINGBI_CUSTOM_TYPE.ENUM_CUSTOM_TYPE_CHAT_TEAM,
	[CHANNEL_TYPE.WORLD] = CHAT_PINGBI_CUSTOM_TYPE.ENUM_CUSTOM_TYPE_CHAT_WORLD,
	[CHANNEL_TYPE.GUILD] = CHAT_PINGBI_CUSTOM_TYPE.ENUM_CUSTOM_TYPE_CHAT_GUILD,
	[CHANNEL_TYPE.SYSTEM] = CHAT_PINGBI_CUSTOM_TYPE.ENUM_CUSTOM_TYPE_CHAT_SYSTEM,
	[CHANNEL_TYPE.CROSS] = CHAT_PINGBI_CUSTOM_TYPE.ENUM_CUSTOM_TYPE_CHAT_CROSS,
}
CustomEnumChatChannel = {
	[CHAT_PINGBI_CUSTOM_TYPE.ENUM_CUSTOM_TYPE_CHAT_TEAM] = CHANNEL_TYPE.TEAM,
	[CHAT_PINGBI_CUSTOM_TYPE.ENUM_CUSTOM_TYPE_CHAT_WORLD] =  CHANNEL_TYPE.WORLD,
	[CHAT_PINGBI_CUSTOM_TYPE.ENUM_CUSTOM_TYPE_CHAT_GUILD] =  CHANNEL_TYPE.GUILD,
	[CHAT_PINGBI_CUSTOM_TYPE.ENUM_CUSTOM_TYPE_CHAT_SYSTEM] =  CHANNEL_TYPE.SYSTEM,
	[CHAT_PINGBI_CUSTOM_TYPE.ENUM_CUSTOM_TYPE_CHAT_CROSS] =  CHANNEL_TYPE.CROSS,
}

TEAM_INVITE_TYPE = {
	FRIEND = 1,
	NEAR = 2,
	GUILD = 3,
	MARRY = 4,
	CHAT = 5,
}

--疯狂摇钱树
RA_SHAKEMONEY_OPERA_TYPE = {
	RA_SHAKEMONEY_OPERA_TYPE_QUERY_INFO = 0,				-- 请求信息
	RA_SHAKEMONEY_OPERA_TYPE_FETCH_GOLD = 1,				-- 领取元宝
	RA_SHAKEMONEY_OPERA_TYPE_MAX        = 2,
}

--膜拜     key:scene_id  value:activity_type
WORSHIP_SCENE_TYPE = {
	[1401] = 7,
	[770] = 3083,
}

OA_LUCK_CHARM_OP_TYPE = {
    INFO = 1, 									--活动信息
    DRAW = 2,									--抽奖, param1数量
    LEIJI_REWARD = 3,							--领取累计奖励
    RECORD = 4,								--记录
    NoTween = 5,								--是否需要动画 param1 标识
}

--功能预告枚举
ADVANCE_NOTICE_TYPE = {
	ADVANCE_NOTICE_TYPE_LEVEL = 0,				--等级功能预告
	ADVANCE_NOTICE_TYPE_DAY = 1,				--天数功能预告
}

ADVANCE_NOTICE_OPERATE_TYPE = {
  ADVANCE_NOTICE_GET_INFO = 0,					--等级功能预告奖励信息
  ADVANCE_NOTICE_FETCH_REWARD = 1,				--等级功能预告领取奖励
  ADVANCE_NOTICE_DAY_GET_INFO = 2,				--天数功能预告奖励信息
  ADVANCE_NOTICE_DAY_FETCH_REWARD = 3,			--天数功能预告领取奖励
}

TITLE_NOTICE_TYPE = {
	TYPE_INVALID = 0, 				-- 不通知
	TYPE_VIP_EXPERIENCE = 1, 		-- vip体验
	TYPE_OGA_GODS_RANK = 2,			-- 封神榜称号获得
	TITLE_NOTICE_TYPE_GUILD_BATTLE = 3,	-- 仙盟战
}

SHOP_CONVERT_TYPE = {
    WEEKLY_LIMIT_BUY = 1,                -- 每周限购
    BATTFIELD_HONOR = 2,                -- 荣誉兑换
    CROSSHNOR = 3,                  -- 跨服荣誉兑换
    FASHION_APPEARANCE = 4,              -- 时尚外观
    CHESTSHOP_SCORE = 5,                -- 寻宝积分
    STORAGE_SCORE = 6,                -- 仙盟仓库兑换
    DAY_GOLD_BUY = 7,                  -- 元宝每日限购
}

-- 寻宝类型
CHESTSHOP_TYPE = {
	TYPE_EQUIP = 1,   	 	--装备寻宝
	TYPE_DIANFENG = 2,		--巅峰寻宝
	TYPE_ZHIZUN = 3,		--至尊寻宝
	TYPE_FENGSHEN = 4,		--封神寻宝
	TYPE_MAX_COUNT = 4 		--寻宝种类个数
}

--副本进入是否合并次数类型
FB_COMBINE_TYPE = {
	WUJINJITAN = 0,
	YUANGU = 1,
	KILL_GOD_TOWER = 2,
	LINGHUN_GUANGCHANG = 3,
	MANG_HUANG_GU_DIAN = 4,-- 蛮荒古殿
	COPPER_FB = 5, --金蟾宝库
	TEAM_COMMON_BOSS_FB1 = 6,	-- 幻兽副本
	TEAM_COMMON_BOSS_FB2 = 7,  -- 女神副本
	TEAM_COMMON_BOSS_FB3 = 8,  -- 武魂副本
	TEAM_COMMON_TOWER_FB1 = 9, -- 符文塔
}

MANHUANGGUDIAN_REQ_TYPE = {
	BASE_INFO = 1, 		--请求个人信息
	RANK_INFO = 2, 		--请求排行榜信息
}
MANHUANGGUDIAN_OPERA_TYPE = {
	FETCH_REWARD = 1,		 	-- 请求领取首胜奖励 param = wave
	AUTO_FETCH = 2,		-- 请求一键领取首胜奖励
	BUY_TIMES = 3,			-- 购买次数
}


MANHUANGGUDIAN_FLAG = {
	NOT_FETCH = 0, 	--未领取
	FETCH = 1, 		--领取
}

SceneObjDetailLevel = {
	Low = 1,
	Middle = 2,
	High = 3,
}
CROSS_3V3OPERA_TYPE = {
    CROSS_3V3OPERA_TYPE_INFO = 0,
    CROSS_3V3OPERA_TYPE_BUY_TIMES = 1,
  }

HideEffectNPCList = {
	[104] = true,
	[106] = true,
	[109] = true,
	[302] = true,
	[406] = true,
	[501] = true,
	[510] = true,
	[608] = true,
	[709] = true,

	[205] = true,
	[325] = true,

	[202] = true,
	[404] = true,
	[701] = true,
	[704] = true,

	[308] = true,
	[313] = true,
	[322] = true,
}

--特惠礼包
RA_PREFERENTIAL_GIFT_OPERA_TYPE = {
    RA_PREFERENTIAL_GIFT_OPERA_TYPE_QUERY_INFO = 0,      --请求信息
    RA_PREFERENTIAL_GIFT_OPERA_TYPE_BUY = 1,
    RA_PREFERENTIAL_GIFT_OPERA_TYPE_MAX = 2,
}

--特殊处理挂机卡
SPECIAL_GUAJICARD = {
    IDONE = 22537,
    IDTWO = 22538,
}

--炫装白送返回枚举
DAZZLE_SUIT_REWARD_FLAG_TYPE =
{
  DAZZLE_SUIT_REWARD_FLAG_TYPE_NONE = 0,   			-- 没有购买
  DAZZLE_SUIT_REWARD_FLAG_TYPE_HAD_BUY = 1,    		-- 已购买
  DAZZLE_SUIT_REWARD_FLAG_TYPE_HAD_FETCH = 2,   	-- 已领取返还奖励
}

--炫装白送操作枚举
DAZZLE_SUIT_OPERA_TYPE =
{
	DAZZLE_SUIT_OPERA_TYPE_INFO = 0,     -- 信息
	DAZZLE_SUIT_OPERA_TYPE_BUY = 1,         -- 购买
	DAZZLE_SUIT_OPERA_TYPE_FETCH = 2,       -- 领取返还
}

--天天返利
DAYDAYFANLI = {
	RA_CHONGZHI_DAY_REWARD_OPERA_TYPE_QUERY_INFO = 0,   --请求信息
	RE_CHONGZHI_DAY_REWARD_OPERA_TYPE_RANK_REWARD = 1,  --领取奖励 param1 index

	RA_CHONGZHI_DAY_REWARD_OPERA_TYPE_MAX = 2,
}

--跨服云购请求操作码
CS_CROSS_RA_CLOUDBUY_OP_TYPE = {
    CS_CROSS_RA_CLOUDBUY_OP_TYPE_INFO = 0,           -- 请求个人购买信息 返回 8521
    CS_CROSS_RA_CLOUDBUY_OP_TYPE_BUY = 1,            -- 请求购买 返回 8521 8522
 }
--跨服云购返回操作码
CROSS_RA_CLOUDBUY_OPERA_REQ = {
    CROSS_RA_CLOUDBUY_OPERA_REQ_INVALID = 0,
    CROSS_RA_CLOUDBUY_OPERA_REQ_BUY_RECORD = 1,        -- 购买记录 返回 14271
    CROSS_RA_CLOUDBUY_OPERA_REQ_REWARD_RECORD = 2,      -- 中奖记录 返回 14272
    CROSS_RA_CLOUDBUY_OPERA_REQ_GET_ALL_SHOP_INFO = 3,    -- 获取全部信息 返回 14273
    CROSS_RA_CLOUDBUY_OPERA_REQ_SINGLE_SHOP_INFO = 4,    -- 单个专场信息 返回 14274

    CROSS_RA_CLOUDBUY_OPERA_REQ_MAX = 5,
  }

CROSS_RA_CLOUD_BUY_SHOP_TYPE = 2 		--云购专场
CROSS_RA_CLOUD_BUY_REWARD_SEQ_MAX = 20 	--云购专场大奖档次

-- 小目标操作类型
RA_SMALL_GOAL_OPERATE_TYPE = {
	RA_QUERY_INFO = 0,		-- 请求信息
	RANK_REWARD = 1,		-- 领取排行奖励 param1 rush_type
	CAPABILITY_TARGET = 2,	-- 领取目标奖励 param1 capality_goal
	DISCOUNT_BUY = 3,		-- 购买 param1 seq
	SCORE_CONVERT = 4		-- 兑换 param1 seq
}

-- 小目标刷新类型
RA_SMALL_GOAL_FLUSH_TYPE = {
	RANK = 1,		-- 排行
	GOAL_INFO = 2,	-- 协议信息
}

-- 开服激战操作类型
RA_FIERCE_FIGHTING_OPERA_TYPE = {
	RA_QUERY_INFO = 0,		-- 请求信息
	RED_BAG = 1,			-- 全服红包 param1 boss_id
	KILL_REWARD = 2,		-- 击杀奖励 param1 boss_id
}
-- 红包操作类型
RED_PAPER_ENUM = {
	MAX_GUILD_PAPER_RECORD_COUNT = 20,		-- 红包领取最大记录
	MAX_GUILD_PAPER_COUNT = 50,				-- 仙盟红包最大数量
	MAX_SYSTEM_REDPAPER_COUNT = 50,         -- 最大系统红包数量
	MAX_WORLD_PAPER_RECORD_COUNT = 80,      -- 世界红包最大记录
}
REDPAPER_OPERA_TYPE = {
  	REDPAPER_OPERA_TYPE_SELF_INFO = 0,      	 --个人红包信息
    REDPAPER_OPERA_TYPE_GET_TOTAL = 1,    		 -- 领取累计奖励
    REDPAPER_OPERA_TYPE_DISTRIBUTE_SYSTEM = 2, 	 -- 系统红包
    REDPAPER_OPERA_TYPE_DISTRIBUTE = 3,      	 -- 手动红包 param1 0是世界红包 param2红包索引
    REDPAPER_OPERA_TYPE_RECEIVE_GUILD = 4,    	 -- 领取仙盟红包 param1:索引
    REDPAPER_OPERA_TYPE_RECEIVE_WORLD = 5,    	 -- 领取世界红包 param1:索引
    REDPAPER_OPERA_TYPE_GET_GUILD_REDPAPER_INF0 = 6,  -- 请求仙盟红包全部信息
    REDPAPER_OPERA_TYPE_GET_WORLD_REDPAPER_INF0 = 7,  -- 请求世界红包全部信息
    REDPAPER_OPERA_TYPE_GRATFUL = 8,                  -- 红包感言
    REDPAPER_OPERA_TYPE_GUILD_SYSTEM_DISTRIBUTE = 9,  -- 发放仙盟系统红包--盟主和代理盟主可发，param1:索引
    REDPAPER_OPERA_TYPE_GUILD_SYSTEM_INFO = 10,       -- 仙盟系统红包信息

}
GUILD_REDPAPER_TYPE = {                          -- 仙盟红包类型枚举
	GUILD_REDPAPER_TYPE_1 = 1,
	GUILD_REDPAPER_TYPE_2 = 2,
	GUILD_REDPAPER_TYPE_3 = 3,
	GUILD_REDPAPER_TYPE_4 = 4,
	GUILD_REDPAPER_TYPE_5 = 5,
	GUILD_REDPAPER_TYPE_6 = 6,
	GUILD_REDPAPER_TYPE_7 = 7,
	GUILD_REDPAPER_TYPE_8 = 8,
	GUILD_REDPAPER_TYPE_9 = 9,
	GUILD_REDPAPER_TYPE_10 = 10,
	GUILD_REDPAPER_TYPE_11 = 11,
}


-- 活跃转盘操作类型
RA_ACTIVE_ROLL_OP_TYPE = {
	RA_ACTIVE_ROLL_OP_TYPE_INFO = 0,				-- 请求信息
	RA_ACTIVE_ROLL_OP_TYPE_FETCH_TASK_REWARD = 1,	-- 领取任务
	RA_ACTIVE_ROLL_OP_TYPE_ROLL = 2,				-- 抽奖
}

-- 超值必买操作类型
RA_WORTH_BUY_OPERA_TYPE = {
	RA_QUERY_INFO = 0,		-- 请求信息
	BUY = 1,			-- 购买 param1 seq
	HAD_CHECK = 2,		-- 重置new标记
	OFF_SALE = 3,		-- 下架
}

--心魔副本技能  标记
XINMO_FUBEN_SKILL_POS =
{
	XINMO_SKILL = 1,
	BEAST_SKILL = 2,
}

--隐藏福利请求枚举
HIDDEN_WELFARE_OP_TYPE =
{
    HIDDEN_WELFARE_OP_TYPE_ALL_INFO = 0,      -- 全部信息
    HIDDEN_WELFARE_OP_TYPE_FETCH = 1,       -- 领取奖励 param1 -- 任务索引
    HIDDEN_WELFARE_OP_TYPE_CUR_STAGE_INFO = 2,    -- 当前阶段信息
}

-- 多处调用  修改【注意】
REWARD_STATE_TYPE =
{
	UNDONE = 0,				-- 未达成
	CAN_FETCH = 1,			-- 可领取
	FINISH = 2,				-- 已领取
	MISSED = 3,				-- 已错过
	READY = 4,				-- 准备中
}

-- 多处调用  修改【注意】
GOODS_STATE_TYPE =
{
  UNACT = 0,		-- 未激活
  READY_ACT = 1,	-- 准备激活
  CAN_ACT = 2,		-- 可激活
  NORMAL = 3,		-- 已激活
  USING = 4,		-- 使用中
}

--用来控制文本、按钮颜色
RICH_CONTENT_TYPE = {
	NONE = 0,
	MAIN_UI = 1,
	CHAT_WINDOW = 2,
	Mail = 3,
	BagRongLian = 4,

    ParseRoleColor = 5,
    ParseZhanDuiRoleColor = 6,
    ParseSystemTips = 7,
}

--套装类型 卓越/完美/史诗
SUIT_TYPE =
{
	SUIT_TYPE_ZHUMO = 0,
	SUIT_TYPE_ZHUXIAN = 1,
	SUIT_TYPE_ZHUSHEN = 2,
}

--印记枚举
IMPRESSION_TYPE =
{
	TYPE_LEVEL_ONE   = 1,	--完美
	TYPE_LEVEL_TWO   = 2,	--史诗
	TYPE_LEVEL_THREE = 3,	--传说
	TYPE_LEVEL_FOUR  = 4,	--永恒
}

SHENLING_OPERA_TYPE = {
	SHENLING_OPERA_TYPE_PET_ALL_INFO = 0,      		-- 请求全部信息
	SHENLING_OPERA_TYPE_ACTIVE_SHENLING_CANLING = 1,   -- 激活残灵 param1 残灵id
	SHENLING_OPERA_TYPE_ACTIVE_TOTAL_STAR = 2,   	-- 点亮星级
	SHENLING_OPERA_TYPE_PROMOTE_PERFECT_DEGREE = 3, -- 提升完美度，param1是神灵id、param2是神格id parma3消耗的装备个数 index_list消耗装备的背包索引列表
	SHENLING_OPERA_TYPE_SHENGE_BLESS = 4,      		-- 神格祝福，param1是神灵id、param2是神格id
	SHENLING_OPERA_TYPE_SHENGE_SINGLE_INFO = 5,    	-- 单个神格信息，param1是神灵id、param2是神格id
	SHENLING_OPERA_TYPE_ACTIVE_SHENLING = 6,		--  激活神灵 param1 神灵id
}

-- 奇遇礼包操纵类型
RA_SPECIAL_GIFT_OPERA_TYPE = {
	RA_SPECIAL_GIFT_OPERA_TYPE_QUERY_INFO = 0,			-- 请求信息
	RA_SPECIAL_GIFT_OPERA_TYPE_RANK_REWARD = 1,				-- 领取奖励 param1 index
	RA_SPECIAL_GIFT_OPERA_TYPE_MAX = 2,
}

RA_SUPERGIFT_OP_TYPE = {
    RA_SUPERGIFT_OP_TYPE_INFO = 0, --活动信息
    RA_SUPERGIFT_OP_TYPE_FETCH = 1, --购买礼包
    RA_SUPERGIFT_OP_TYPE_FETCH_EXTRA = 2,--额外奖励
}

BOSS_PLAN_OPERA_TYPE = {
    BOSS_PLAN_OPERA_TYPE_INFO = 0,
    BOSS_PLAN_OPERA_TYPE_BUY = 1,
    BOSS_PLAN_OPERA_TYPE_FETCH = 2,
 }

 BOSS_PLAN_REWARD_TYPE = {
 	KELINGQU = 0,
    WEIDACHENG = 1,
    YILINGQU = 2,
 }

SHOP_TYPE = {
	SHOP = 1,       --商城表
	EXCHANGE = 2, 	--兑换商城表
}

RESET_POST_TYPE = {
    DEFAULT = 0,
    SLOW = 1,
    ASSIST = 2,     --组队协助
}

--个人boss
PERSON_BOSS_OPERATE_REQ_TYPE =
  {
    INFO = 0,
    BUY_EXTRA_TIMES = 1,
  }

--boss协助请求
GUILD_ASSIST_OPERATE_TYPE =
  {
    INFO_REQ = 0,       							-- 个人信息请求
    CALL_HELP = 1,     								-- 请求协助
    GO_TO_HELP = 2,     							-- 前往协助 param_1:角色ID, param_2:场景ID, param_3:怪物BossID
    CANCEL_HELP = 3,     							-- 取消协助
    THANK_OTHERS = 4,     							-- 感谢别人 param_1:感谢句子param_2:场景ID param_3:怪物BossID
    GET_THANK_REWARD = 5,   						-- 领取感谢 param_1:角色ID  param_2:场景ID param_3:怪物BossID
    CALL_HELP_INFO_REQ = 6,							-- 请求协助呼叫信息列表
    FORCE_ATTACK = 7,							    -- 强制攻击伤害列表的人 param_1:角色ID  param_2:场景ID param_3:怪物BossID
	INVITE_TEAMMATE = 8,							-- 召唤队友
	GOTO_HELP_TEAMMATE = 9,							-- 前往帮助队友 param_1:角色ID	param_2:场景ID param_3:怪物BossID
  }


GUILD_BUILD_TASK_RATE_TYPE = {
 	RATES = 4,
    RATEA = 3,
    RATEB = 2,
    RATEC = 1,
 }
--仙盟建设一些杂项需要
GUILD_BUILD_TASK_OTHER_TYPE = {
	--特殊情况下客户端需要作假展示的id
	CLIENT_SHOW_TASK_ID = 32001,
	VIEWSHOW_MAX_TASKNUM = 6, --界面展示的固定任务数量
	GUILD_TASK_VIP_TYPE = 22,--仙盟建设的vip特权获取类型
	GUILD_BUILD_TASK_MINID = 31001,
	GUILD_BUILD_TASK_MAXID = 32000,
	ITEM_EXP_ID = 90050, --固定经验id
}

EQUIPMENT_BOARDCAST_TYPE ={
    EQUIPMENT_BOARDCAST_TYPE_INVALID = 0,
    EQUIPMENT_BOARDCAST_TYPE_BAPTIZE = 1,            -- 装备洗炼
    EQUIPMENT_BOARDCAST_TYPE_COMPOSE = 2,            -- 装备合成
    EQUIPMENT_BOARDCAST_TYPE_CHEST = 3,              -- 装备寻宝
    EQUIPMENT_BOARDCAST_TYPE_STRENGTH = 4,            -- 装备强化
    EQUIPMENT_BOARDCAST_TYPE_MONSTER_DROP = 5,          -- 怪物掉落
    EQUIPMENT_BOARDCAST_TYPE_GUILD_PUTITEM = 6,          -- 帮派捐献
    EQUIPMENT_BOARDCAST_TYPE_MAX = 7,					--真炼
  };

  PET_OPERA_TYPE = {
	PET_OPERA_TYPE_PET_SINGLE_INFO_REQ = 0,	-- 请求单个宠物信息, param1是宠物列表index

	PET_OPERA_TYPE_BREAK_PET = 1,			-- 分解宠物，param1是宠物列表index
	PET_OPERA_TYPE_UPLEVEL = 2,			    -- 升级宠物，param1是宠物列表index
	PET_OPERA_TYPE_UPGRADE = 3,			    -- 进阶宠物，param1是宠物列表index
	PET_OPERA_TYPE_PASSIVE_SKILL_UPLEVEL  = 4,	    -- 升级被动技能，param1是宠物列表index, param2是被动技能索引
	PET_OPERA_TYPE_REBORN = 5,			    -- 重生宠物，param1是宠物列表index
	PET_OPERA_TYPE_PREVIEW_REBORN_RESULT  = 6,	-- 预览重生结果, param1是宠物列表index

	PET_OPERA_TYPE_USE_EGG = 10,			-- 使用宠物蛋, param1是宠物背包index，param2是孵化列表index
	PET_OPERA_TYPE_OPEN_EGG = 11,			-- 开宠物蛋，param1是孵化列表index
	PET_OPERA_TYPE_UNLOCK_EGG_SLOT = 12,	-- 解锁宠物蛋孵化位，param1是孵化列表index
	PET_OPERA_TYPE_QUICK_FINISH_EGG = 13,	-- 迅速完成孵化，param1是孵化列表index

	PET_OPERA_TYPE_LEARN_SKILL = 20,		-- 学习技能, param1 0专属 1主动 2被动
	PET_OPERA_TYPE_CALL_PET = 30,			-- 出战宠物，param1是宠物列表index, param2是出战列表index
	PET_OPERA_TYPE_CALL_BACK_PET = 31,		-- 卸下出战宠物，param1是出战列表index
	PET_OPERA_TYPE_UNLOCK_CALL_SLOT = 32,   -- 解锁出战槽, param1是出战槽index
}

 -- 宠物出战类型
 PET_FIGHT_TYPE = {
 	NONE = 0,
 	BEIZHAN = 1,
 	FUZHAN = 2,
 	ZHUZHAN = 3,
}

-- 宠物类型
 PET_TYPE = {
 	DEFENCE = 0,
 	ATTACK = 1,
}
--仙盟工资其他
 GUILD_WAGE_OTHER_TYPE = {
 	MAX_TASK_NUM = 20,
 	--这些任务类型要做淡出判断处理
 	TASK_TYPE1 = 1, --仙盟boss
 	TASK_TYPE11 = 11, --仙盟周任务
 	TASK_TYPE12 = 12, --仙盟建设任务

}

--仙盟工资任务状态
 GUILD_WAGE_TASK_STATES = {
 	LOCK = -1,
  	YILINGQU = 0,
 	WEIWANCHENG = 1,
 	KELINGQU = 2,
}

--仙盟捐献
 GUILD_JUANXIAN_OTHER_TYPE = {
 	MAX_JUANXIAN_TYPE = 3, --最大捐献类型3个
 	--道具捐献类型
 	ITEM_JAUNXIAN_TYPE = 1,
}

--结婚预告相关枚举
QINGYUAN_ADVANCE_NOTICE_STATUS = {
	QINGYUAN_ADVANCE_NOTICE_STATUS_INVAILD  = 0, --无
 	QINGYUAN_ADVANCE_NOTICE_STATUS_MARRY = 1, --结婚预告
 	QINGYUAN_ADVANCE_NOTICE_STATUS_BABY = 2, --领娃预告
 	QINGYUAN_ADVANCE_NOTICE_STATUS_END = 3, --预告结束
}

--职业技能操作
CAREER_UPGRADE_OPERA = {
    INFO = 0,      -- 职业进阶信息
    UPLEVEL = 1,  -- 技能升级
    AWAKE = 2,  -- 技能觉醒
    UPGRADE = 3,    -- 职业进阶
    RESET = 4,    -- 重置
    SELECT_TYPE = 5,    -- 选择类型
    UPLEVEL_ONCE = 6,    -- 技能一键升级
};

--寻宝淘宝操作
TAOBAO_OP_TYPE = {
	DRAW = 0,		--抽奖，parma1 层数， param2索引， param3自动购买
	RESET = 1,		--重置 param1 层数
	RECORD = 2,		--记录 param1 层数
	PUTTOPACK = 3, 	--取出到背包 param1背包index，-1代表所有
	SORT = 4, 		--排序
	SHUFFLE = 5, 		--洗牌 param1 层数
	OPNE = 6, 		--开启 param1 层数
}

--天神寻宝操作
TS_XUNBAO_OPERA_TYPE = {
	LAYER_INFO = 1,	--挡位信息, param1档位
    DRAW = 2,  --抽奖 param1档位，param2索引，param3自动购买
    RESET_LAYRE = 3, -- 重置奖池， param1档位
    DRAW_RECORD = 4, --查看记录，param1档位
    All_DRAW = 5,  --一键抽奖 param1档位，param2自动购买
    EXCHANGE_ITEM = 6, -- 兑换物品
    EXCHANGE_INFO = 7, -- 兑换物品信息
}

--问挂操作 6843
WENGUA_OPERA_TYPE = {
    QUERY_INFO = 1,								-- 请求信息
    DRAW = 2,									-- 卜挂（抽奖），param1是否自动购买, param2一键卜卦标记
    BUY_OPEN_POOL = 3,							-- 购买打开奖池，param1是奖池id， param2是否自动购买
    BREAK = 4,									-- 一键分解
    TAKE = 5,									-- 一键提取
    QUERY_SERVER_RECORD = 6,					-- 请求全服记录
    ONEC_KEY_DRAW = 7,							-- 一键卜卦的标记
};

--boss掉落归属类型drop_type
BOSS_DROP_OWN_TYPE =
{
    BOSS_DROP_OWN_TYPE_NONE = 0,							-- 无归属（谁都可以检）
    BOSS_DROP_OWN_TYPE_MAX_HURT_ROLE = 1,					-- 最高伤害个人归属掉落
    BOSS_DROP_OWN_TYPE_MAX_HURT_TEAM = 2,					-- 最高伤害队伍归属掉落
    BOSS_DROP_OWN_TYPE_OWNER_ROLE = 3,						-- boss归属者
    BOSS_DROP_UNIQUE_TYPE = 4,								-- 独立掉落
    BOSS_DROP_OWN_TYPE_TEAM_ASSIST = 5,						-- 伤害最高协助队伍归属掉落
    BOSS_DROP_OWN_TYPE_PRESET_ROLE = 6,						-- 预设给的玩家
    BOSS_DROP_OWN_TYPE_HURT_RANK = 7,						-- 排名前N队伍归属掉落
    BOSS_DROP_OWN_TYPE_MAX = 8,
};


--副本使用道具类型
FB_USE_ITEM = {
    REBIRTHBOSS  = 1,
    INVOKEBOSS = 2,
	RebirthBossCommon = 3,
}

--减少疲劳类型
DEC_BOSS_TIRE_TYPE = {
	WORLD_BOSS = 1,
	CROSS_BOSS = 2,
	SACRED_BOSS = 3,
}

--寻宝背包装备排序
TAOBAO_BAG_SUBTYPE = {
	[105] = 10,
	[106] = 9,
	[107] = 8,
	[108] = 7,
	[109] = 6,
	[100] = 5,
	[101] = 4,
	[102] = 3,
	[103] = 2,
	[104] = 1,
}

-- 图鉴类型
CARD_TYPE =
{
    CARD_TYPE_BOSS = 0,          -- boss图鉴
    CARD_TYPE_PET = 1,           -- 宠物图鉴
    CARD_TYPE_TIANSHEN = 2,      -- 天神图鉴
};

-- 图鉴操作类型
TUJIAN_OP_TYPE = {
	TUJIAN_OP_TYPE_ACTIVE = 1,							-- 激活请求，param1是图鉴索引
	TUJIAN_OP_TYPE_UPLEVEL = 2,							-- 升级请求，param1是图鉴索引
	TUJIAN_OP_TYPE_UPSTAR = 3,							-- 升星请求，param1是图鉴索引
	TUJIAN_OP_TYPE_BREAK = 4,                           -- 分解请求，param1是分解道具id，param2是分解数量
	TUJIAN_OP_TYPE_ACTIVE_GROUP = 5,                    -- 激活组合请求，param1是组合索引
	TUJIAN_OP_TYPE_UPGRADE = 6,							-- 升阶请求，param1是图鉴索引
};

-- 修真之路请求类型
XIUZHENZHILU_OPEN_TYPE = {
	XIUZHENZHILU_OPEN_TYPE_TASK_REWARD = 0, --领取任务奖励,parm1任务seq
	XIUZHENZHILU_OPEN_TYPE_PROGRESS_REWARD = 1, --领取进度奖励，parm1 index
	XIUZHENZHILU_OPEN_TYPE_FINAL_REWARD = 2, --领取最终奖励
	XIUZHENZHILU_OPEN_TYPE_ACTIVE_CHAPTER_SKILL = 3, --激活章节技能
	XIUZHENZHILU_OPEN_TYPE_BUY_ITEM = 4,			--修真之路购买物品
}

XIUZHENZHILU_TASK_STATES = {
	YLQ = 2, --已领取
	WDC = 1, --未达成
	KLQ = 0, --可领取
}

XIUZHENZHILU_OTHER = {
	MAX_OPEN_DAY = 7, --功能页签最大数量1-7
	MAX_INFO_NUM = 249,--服务器下发的最大数据数量
	MAX_NATURE_NUM = 2,--服务器下发的属性数量
}

-- 藏宝湾操作类型
WABAO_OP =
  {
    WABAO_OP_USE_BAOTU = 1,                -- 使用宝图, param1是宝图品质
    WABAO_OP_DO_TASK = 2,                -- 请求执行一次挖宝任务
    WABAO_OP_DO_WABAO = 3,                -- 请求挖宝, param1是挖宝类型
    WABAO_OP_ACCEPT_TASK = 4,              -- 接挖宝任务
    WABAO_OP_CONFIRM_ENTER_TEAM_FB = 5,        -- 请求准备
    WABAO_OP_QUERY_TEAM_FB_OPEN_INFO = 6,      -- 请求组队副本开启协议
	WABAO_OP_LEAVE_WABAO_SCENE = 7,				-- 离开挖宝场景
    WABAO_OP_GET_TEAM_STATUS = 8,				--前往挖宝判断所有队友是否符合要求
    WABAO_OP_CONTINUE_CHALLENGE = 9,			--继续挑战
  }

WABAO_TYPE =
  {
    WABAO_TYPE_BAOTU = 1,                -- 宝图挖宝
    WABAO_TYPE_TASK = 2,                -- 任务挖宝

  }

--藏宝湾组队通知类型
WabaoTeamFBNotifyReason =
  {
    OpenFB = 1,          -- 打开副本
    ConfirmEnter = 2,    -- 确认进入
    Cancel = 3,			 -- 取消
    Query = 4,			 -- 请求
  }

  --	MAX_LEIJI_NUM = 3, -- 累计奖励最大数目0-3



  --每周必买请求枚举
PERIOD_OP_TYPE =
{
	PERIOD_OP_TYPE_ALL_INFO = 0,							---- 全部信息
	PERIOD_OP_TYPE_BUY = 1,								--	-- 购买
	PERIOD_OP_TYPE_FETCH_EXTRA_REWARD = 2,				--	-- 领取额外奖励
	PERIOD_OP_TYPE_FETCH_REWARD = 3,					-- 领取奖励
}

--充值类型
GET_GOLD_REASON =
{
	GET_GOLD_REASON_CHONGZHI = 0,    					--充值
	GET_GOLD_REASON_TOUZIJIHUA = 1,  					--投资计划（月卡）
	GET_GOLD_REASON_YIBENWANLI_DAILY_GIFT = 9,			-- 一本万利每日礼包
	GET_GOLD_REASON_YUAN_SHEN_ZHAO_HUAN = 11,			--元神召唤-特典充值 直购
	GET_GOLD_REASON_SHEN_JI_BAI_LIAN = 12,				--仙器百炼-特典充值 直购
	GET_GOLD_REASON_ZHI_GOU = 13,						--直购返利礼包
	GET_GOLD_REASON_TB_DEZG_RMB_BUY = 221,				--百亿补贴-大额直购
	GET_GOLD_REASON_TBS_TROLLEY_RMB_BUY = 236,			--百亿补贴-购物车
	GET_GOLD_REASON_TONGPIAO = 238,                     --通票
}

-- 化鲲操作类型
HUAKUN_OPERA_TYPE =
  {
    HUAKUN_OPERA_TYPE_INFO = 0,          -- 请求全部信息
    HUAKUN_OPERA_TYPE_UPLEVEL = 1,          -- 请求升级 param1: 鲲id
    HUAKUN_OPERA_TYPE_UPGRADE = 2,          -- 请求升阶 param1: 鲲id
    HUAKUN_OPERA_TYPE_UPSTAR = 3,          -- 请求升星 param1: 鲲id
    HUAKUN_OPERA_TYPE_DUHUA = 4,          -- 渡化  param1:渡化槽 parma2背包索引
    HUAKUN_OPERA_TYPE_DUHUA_SPEEDUP = 5,      -- 渡化加速 param1:渡化槽
    HUAKUN_OPERA_TYPE_DUHUA_SLOT_OPEN = 6,      -- 渡化槽解锁 param1: 渡化槽
    HUAKUN_OPERA_TYPE_XUEMAI_ACTIVE = 7,      -- 血脉激活 param1: id param2 分支
    HUAKUN_OPERA_TYPE_XUEMAI_CHANGE = 8,      -- 血脉置换
 	HUAKUN_OPERA_TYPE_ACTIVE_KUN = 9,		  -- 请求激活 param1: 鲲id
 	HUAKUN_OPERA_TYPE_FETCH_DUHUA_REWARD = 10, -- 领取渡化奖励, param1:槽索引
  }

MAX_XL_ATTR_NUM = 5				--洗练属性最大条数
KUN_DFF_MAX_ATTR_SKILL_NUM = 4 	--鲲的属性技能条数

--龙魂槽位开启类型
LONGHUN_OPEN_TYPE = {
	ROLE_LEVEL = 1, --人物等级
	ROLE_ZHUAN = 2, --人物转数
	ITEM = 3, --道具开启
}

--龙魂属性
LONGHUN_ATTR_TYPE = {
	ALL_NATURE = 0, --龙魂属性
	STSRT_NATURE = 1, --单个星级属性
	STSRT_ALL_NATURE = 2, --所有星级属性
}

--龙魂物品tips打开
LONGHUN_TIPSOPEN_TYPE = {
	BAG = 0, --龙魂背包
	LONGHUN_CAO = 1,--龙魂槽
}

--仙盟技能杂烩枚举
GUILD_SKILL_TYPE = {
	MAX_INFO = 20, --服务器下发的最大信息数量1-20
	MAX_SKILL_NUM = 18,--当前最大的技能数量
}

-- 八卦操作类型
BAGUA_OPER_TYPE = {
	TYPE_1 = 1,      -- 上卦 @param1 背包索引 @param2 阵眼id @param3 卦位索引 0~2
	TYPE_2 = 2,      -- 下卦 @param1 阵眼id @param2 卦位索引 0~2
	TYPE_3 = 3,      -- 升级阵眼 @param1 阵眼id
	TYPE_4 = 4,      -- 升级卦象 @param1 阵眼id @param2 卦位索引 0~2
	TYPE_5 = 5,      -- 兑换 @param1 兑换的商品索引  rune_id
	TYPE_6 = 6,      -- 使用道具开启阵眼 @param1 阵眼id
}

--进入组队目标，队伍成员条件不足
TEAM_FB_CAN_NOT_ENTER_TYPE = {
	TEAM_FB_CAN_NOT_ENTER_TYPE_ENTER_TIMES = -1,  	-- 进入次数不足
	TEAM_FB_CAN_NOT_ENTER_TYPE_LEVEL = -2,      	-- 等级未达到
	TEAM_FB_CAN_NOT_ENTER_TYPE_ITEM = -3,      		-- 物品材料（门票）不足
	TEAM_FB_CAN_NOT_ENTER_TYPE_GOLD = -4,      		-- 合并次数花费元宝不足
	TEAM_FB_CAN_NOT_ENTER_TYPE_OFFLINE = -5,    	-- 不在线
	TEAM_FB_CAN_NOT_ENTER_TYPE_IN_FB = -6,			-- 在副本中
	TEAM_FB_CAN_NOT_ENTER_TYPE_STATUS = -100,    	-- 当前状态不能进入
	TEAM_FB_CAN_NOT_ENTER_TYPE_OTHER = -1000,    	-- 其他
}

--全城热恋
PERFERT_QINGREN_ENUM = {
	--RAND_ACTIVITY_LOVING_CITY_TASK_MAX = 20,						--完美情人最大任务数量
	 RA_ACTIVE_LOVING_CITY_OP_TYPE_INFO = 0,						--完美情人信息
	 RA_ACTIVE_LOVING_CITY_OP_TYPE_FETCH_TASK_REWARD = 1,			-- -- 领取任务
	 RA_ACTIVE_LOVING_CITY_OP_TYPE_FETCH_PROCESS_REWARD = 2,		--领取进度奖励
	 RA_ACTIVE_LOVING_CITY_OP_TYPE_FETCH_IMAGE = 3,          			-- 兑换形象物品
}
--甜言蜜语
TIANYAN_MIYU_ENUM = {
	PROFESS_REQ_TYPE_ACTIVE = 0,    ---- 表白
    PROFESS_REQ_TYPE_PASSIVE = 1,    ---- 被表白
    PROFESS_REQ_TYPE_ACTIVE_OTHER = 2,  ---- 查看他人表白 param:uid
    PROFESS_REQ_TYPE_PASSIVE_OTHER = 3,  ---- 查看他人被表白 param:uid
    PROFESS_REQ_TYPE_GLOBAL_SINGLE = 4,  ---- 公共表白单个类型信息 param:类型
    PROFESS_REQ_TYPE_GLOBAL_ALL = 5,  ---- 公共表白全部信息
}

MONSTER_JUMP_FLAG = {
	HAS_JUMP = 1,	--已经跳跃
	WAIT_JUMP = 2,	--等待跳跃
}

--护送认任务结束类型
HUSONG_END_TYPE = {
	FREE_END = 1, --免费类型结束
	ITEM_END = 2, --道具类型结束
	BINDGOLD_BUY_END = 3, --购买道具刷新结束(绑元购买道具)
	GOLDBUY_END = 4, --购买道具刷新结束(仙玉购买道具)
	ITEM_BINDGOLD_BUY_END = 5, --消耗道具和绑元
	ITEM_GOLD_BUY_END = 6, --消耗道具和仙玉
	ALL_MONEY_BUY_END =7, --消耗绑玉和仙玉
	ALL_TYPE_END = 8,--消耗道具和绑玉和仙玉
}

-- 天神神通被动技能类型定义
TS_ST_PASS_TYPE = {
	TSSTPASSTYPE_ATTR_ADD = 0,							-- 增加固定属性
	TSSTPASSTYPE_BIAN_ON_HP_ADD = 1,					-- 变身时，X1% 概率对天神生命额外增加 X2%, 每隔 X3 秒最多触发一次
	TSSTPASSTYPE_BIAN_ON_ZENGSHANG = 2,					-- 召唤变身时，有 X1% 概率提升自身 增伤（最终伤害增加） X2%，持续 X3 秒，每隔 X4 秒最多触发一次
	TSSTPASSTYPE_BIAN_DIS_HP_RECOVER = 3,				-- 变身消失时，有 X1% 概率回复自身生命 X2%，每隔 X3 秒最多触发一次
	TSSTPASSTYPE_BIAN_ON_SKILL_HURT_ADD = 4,			-- 天神出场技能，召唤变身时，有 X1% 概率对周围目标造成的技能伤害比例额外增加 X2% ，每隔 X2 秒最多触发一次
	TSSTPASSTYPE_BIAN_ING_HP_WUDI = 5,					-- 变身时，且自身血量低于 X1%，有 X2% 的概率给自身附加无敌效果，持续 X3 秒，每隔 X4 秒最多触发一次
	TSSTPASSTYPE_ATTR_PER_ADD = 6,						-- 天神属性加成万分比 param1：属性id param2:万分比
	TSSTPASSTYPE_ALLATTR_PER_ADD = 7,					-- 天神全属性加成万分比 paramb：万分比
}

TIANSHEN_ROAD_LOGIN_BTN_STATE = {
	WEIDA_TIANSHU = 1,	        --未达到天数
	HAS_NORMAL_REWARD = 2,		--普通奖励可领取
	HAS_SPECIAL_REWARD = 3,		--特俗奖励可领取
	HAS_ALL_REWARD = 4,
	VIP_REWARD = 5,		        --已领取普通奖励，vip奖励条件未达到
	YILINGQU = 6,		        --已领取所有奖励
}

TIANSHEN_THEME_LOGIN_GIFT_OP_TYPE = {
	INFO = 1,				-- 活动信息
	COMMON_REWARD = 2,		-- 领取普通奖励
	SPECIAL_REWARD = 3,	-- 领取特殊奖励
}

TIANSHEN_THEME_SHOUCHONG_OP_TYPE = {
	TYPE_INFO = 1,	-- 活动信息
	TYPE_DRAW = 2,	-- 领取奖励
}

WOYAOSHENQI_OP_TYPE = {
	INFO = 1, 						-- param = 0
	TASK_REWARD = 2, 				-- param = 任务ID
	DANGWEI_REWARD = 3, 			-- param = 档位ID
	HECHENG_SHENQI = 4,				-- 合成神器神器ID
	BUYLIBAO = 5,					-- 购买礼包
	WOYAOSHENQI_CHANGE_JIGSAW = 6,	-- 交换拼图 param1: 拼图索引1 param2: 拼图索引2
}

--我要仙器枚举
WOYAOXIANQI_OP_TYPE = {
	INFO = 1, 					--param = 0
	TASK_REWARD = 2, 			-- param = 任务ID
	DANGWEI_REWARD = 3, 		--param = 档位ID
	HECHENG_SHENQI = 4,			--合成神器神器ID
	BUYLIBAO = 5,				--购买礼包
}

--龙魂冲榜信息请求参数
RA_XIANQI_RANK_OP_TYPE = {
	TYPE_INFO = 1, 				-- 1: 玩家当前信息
	TYPE_RANK = 2, 				-- 2: 获取排行榜信息
	TYPE_RANK_REWARD = 3, 	-- 3: 获取参与奖励, param = 奖励索引
}

TIANSHEN_THEME_CHONGBANG_OP_TYPE = {
	TYPE_INFO = 1, 				-- 1:玩家当前信息
	TYPE_RANK = 2, 				-- 2: 获取排行榜信息
	TYPE_ZHANLI_REWARD = 3, 	--3: 获取战力排行榜奖励, param = 奖励索引
}

--龙魂冲榜信息请求参数
LONGHUN_CHONGBANG_OP_TYPE = {
	TYPE_INFO = 1, 				-- 1: 玩家当前信息
	TYPE_RANK = 2, 				-- 2: 获取排行榜信息
	TYPE_RANK_REWARD = 3, 	-- 3: 获取参与奖励, param = 奖励索引
}

SUPREME_FIELDS_NUMTYPE =
{
	MAX_FOOT_LIGHT = 39,								-- 服务端定义领域最大数
	COUNT_FOOT_LIGHT_STONE = 5,							-- 五行孔位
	MAX_FOOT_SKILL_NUM = 8,								-- 最大技能孔位（0开始）
	MAX_FOOT_ATTR_NUM = 8,								-- 孔位属性数
}

Supreme_Operation_Index =
{
	REQUEST_DATA = 1,								-- 请求数据
	SLOT_ACT = 2,									-- 五行孔激活 
	SLOT_UPGRADE = 3,								-- 五行孔升级
	SUPREME_TRANSFIG = 4,							-- 幻化
	SKILL_SLOT_UNLOCK = 5,							-- 技能孔解锁
	EQUIP_SKILL = 6,								-- 技能上阵
	Activity_Sell = 7,								-- 领域活动免费领取
	CANCEL_SUPREME_TRANSFIG = 8,					-- 取消幻化
	GET_STAR_ATTR = 9,								-- 请求所有领域的五行通天星级
	GET_TOTAL_STAR_ATTR = 10,						-- 请求所有领域的五行通天总星级
	ACTIVATE_STAR_ATTR = 11,						-- 激活五行通天星级属性加成
	ACTIVATE_TOTA_STAR_ATTR = 12,					-- 激活五行通天总星级属性加成
}

CangMingBuy_Activity_Index = {
	Activity_Sell = 1,								-- 沧溟直购免费领取
}

TIANSHEN_THEME_LEICHONG_OP_TYPE = {
	TYPE_INFO = 1, -- 1:获取当前信息
	TYPE_DRAW = 2, -- 2: 领取奖励 param = 档位
}

-- 道具使用类型
Item_Use_Type =
{
	Gold = 2,					-- 金币
	Title = 3,					-- 金币
	BIND_XIANYU = 8,			-- 绑玉
	ShengWang = 25,				-- 声望
	XianYu = 56,				-- 仙玉卡
	XianYu2 = 67,				-- 仙玉卡(这个使用不计入充值)
	Use_Type_62 = 62,			-- 功能开启
	ZHI_SHENG_DAN = 6,			-- 直升丹
	ROLE_EXP_DAN = 84,			-- 人物经验丹
	LUCKY_VALUE = 149,          -- 幸运值
	BACKGROUND = 153,			-- 奇境
	WUHUNZHENSHEN = 156,		-- 武魂真身
	MECHA = 162,                -- 机甲
}

NO_LONGER_TYPE =
{
  NO_LONGER_TYPE_INVALID = 0,
  NO_LONGER_TYPE_INVITE_ME_TEAM = 1,    -- 不再接受邀请我进他的队
  NO_LONGER_TYPE_JOIN_MY_TEAM = 2,      -- 不再同意进我队
  NO_LONGER_TYPE_ACCEPT_ASSIST = 3,     -- 不再接受他 前往邀请
  NO_LONGER_TYPE_BEING_ASSIST = 4,      -- 不再接受他 合并
}

MARRY_OTHER_TYPE = {
	MAX_NUM = 30,--服务器下发的最大数据数量
	ITEM_ID_1 = 22100, --真情烟花
	ITEM_ID_2 = 22101, --幸福烟花
	-- ITEM_ID_CH = 22605,--称号
	ITEM_ID_TXS = 12100,--同心锁
    MAX_ID_NUM = 6,--服务器下发最大的id数量
    VIRTUAL_TXS_ID = 90628
}

--福利在线奖励
ONLINE_GIFT_OPERA_TYPE = {
	ONLINE_GIFT_OPERA_TYPE_FETCH = 0, --领取礼包 param1 index索引 param2 奖励索引
	ONLINE_GIFT_OPERA_TYPE_FETCH_ONE_KEY = 1,--一键领取礼包 param1 索引
	ONLINE_GIFT_OPERA_TYPE_ASK_INFO = 2,--请求信息
}

ZHUXIECONST =
{
	ZHUXIE_MAX_BOSS_HURT_RANK_ITEM = 30,
}

BATTLEFLAG_OP_TYPE =
{
	INFO = 0,									-- 战旗信息
	UPGRADE = 1,								-- 升级战旗	param1 = flag_id
	LIGHT_UP = 2,								-- 点亮		param1 = flag_id
	SET_STATE = 3, 								-- 设置状态 param1 = flag_id
}

--战旗信息
BATTLEFLAG_NOTIFY_REASON = {
	BATTLEFLAG_NOTIFY_REASON_LOGIN = 1,            -- 登录下发
	BATTLEFLAG_NOTIFY_REASON_CLIENT_REQUEST = 2,      -- 客户端请求
	BATTLEFLAG_NOTIFY_REASON_UNLOCK_FLAG = 3,        -- 战旗解锁
	BATTLEFLAG_NOTIFY_REASON_UPGRADE_SUCCESS = 4,      -- 升级成功
	BATTLEFLAG_NOTIFY_REASON_UPGRADE_FAILURE = 5,      -- 升级失败
	BATTLEFLAG_NOTIFY_REASON_LIGHT_UP_SUCCESS = 6,      -- 点亮成功
	BATTLEFLAG_NOTIFY_REASON_LIGHT_UP_FAILURE = 7,      -- 点亮失败
	BATTLEFLAG_NOTIFY_REASON_CHANGE_STATE = 8, 		 	-- 新标记改变
}

BATTLEFLAG_ACTIVE_STATE = {
	NOT_ACTIVE = 0,				--未激活
	ACTIVE_NEW = 1,				--已激活___新标记
	ACTIVE_NOT_NEW = 2,			--已激活___没有新标记
}

GROUP_OPERA_TYPE = {
	GROUP_OPERA_TYPE_CREATE = 1,              				-- 创建团队
	GROUP_OPERA_TYPE_APPLY_JOIN = 2,            			-- 申请加入团队
	GROUP_OPERA_TYPE_APPROVAL_APPLY_JOIN = 3,        		-- 审批加入团队 param1是申请人uid
	GROUP_OPERA_TYPE_INVITE = 4,              				-- 邀请加入团队 param1是邀请人uid
	GROUP_OPERA_TYPE_INVITE_ACK = 5, 						-- 回应邀请 param1是否同意
	GROUP_OPERA_TYPE_LEAVE = 6,                				-- 离开团队
	GROUP_OPERA_TYPE_KICK_MEMBER = 7,            			-- 踢出团队 param1是要踢的玩家uid
	--GROUP_OPERA_TYPE_QUERY_INFO_LIST = 10,          		-- 请求团队信息列表

}

GROUP_TYPE = {
	GROUP_TYPE_ZHANDUI3V3 = 1,                				-- 3V3战队
}

ZHANDUI3V3_OPERA =
{
	RejectApplyJoin  = 1,          		-- 今日拒绝玩家申请加入 param1是目标玩家uid
	QueryAllLog  = 2,					-- 请求所有日志信息
	QueryMyZhanduiInfo  = 3,			-- 请求战队信息
	QueryInviteList = 4,				-- 请求邀请列表
	QueryAppliedZhandui = 5,         	-- 请求已申请的战队
	ChangeCaptain = 6,         			-- 转让队长 param1是目标队长id
	QueryAllZhanduiList = 7,        	-- 请求战队列表
	QueryAppliedRole  = 8,        		-- 请求申请本战队的玩家（队长用）
	QueryCross3V3Rank  = 9,            	-- 请求排名
	FetchMatchTimesReward  = 10,     	-- 领取阶段奖励，param1发阶段奖励的配置索引
	ChooseZhanduiLingpai = 11,         	-- 选择战队令牌，param1是战队令牌配置id
    ChangeZhanduiLingpaiName = 12,      -- 修改战队令牌名字

}

-- 下发战队信息协议的原因
NotifyZhanduiInfoReason =
{
	NoReason = 0,            	-- 没原因
	Login = 1,              	-- 登陆
	JoinZhandui = 2,          	-- 玩家自己加入战队
	ModifyName = 3,            	-- 修改名字
	ModifyNotice = 4,          	-- 修改公告
	LeaveZhandui = 5, 			-- 玩家自己离开战队
	Query = 6, 					-- 客户端请求
	BeKicked = 7, 				-- 玩家自己被踢出
	NewMemberJoin = 8,          -- 新成员加入
  	OtherMemberLeave = 9,       -- 其他成员离队
	ChangeCaptain = 10, 		-- 换队长
	ChangeScore = 11,           -- 积分变更
	ChangeLingpaiName = 12, 	--修改战队令牌名字
	AgreeJoinZhandui = 13,      -- 同意加入战队（申请，然后被同意，这种情况不弹出面板）
}

--加入战队列表排序条件
ZHANDUI_LIST_SORT_CONDITION = {
	NONE = 0,					--不排序
	UP_CAPABILITY = 1, 			--战力从高到低
	UP_SOCRE = 2, 				--积分从高到低
}

--跨服3V3匹配信息下发原因
Cross3V3MatchingInfoNotifyReason = {
	StartMatching = 1,							-- 开始匹配
	CancelMatching = 2,							-- 取消匹配
	TimeOut = 3,								-- 匹配超时
	MatchingSucc = 4,							-- 匹配成功
	ClientReady = 5,							-- 客户端准备好

}

-- 跨服3V3操作类型
Cross3V3OperaType = {
	StartMatch = 1,								-- 开始匹配
	CancelMatch = 2,							-- 取消匹配
	EnterPKRoom = 3,							-- 进比赛房
	ClientReady = 4, 							-- 客户端准备好
}

EnumCross3V3RankType = {
	CurSeason = 0,					--当前赛季
	LastSeason = 1, 				--上个赛季
}

EnumDuanweiChangeType = {
	Cur = 0, 					--当前段位
	Up = 1, 					--段位提升
	Down = 2, 					--段位下降
}

--红蓝方
EnumKF3V3SideType = {
	RED = 0,
	BLUE = 1,
}
--比赛状态
EnumKF3V3PKStateType = {
	WaitForClientReady = 0,                -- 等待客户端准备完毕
	StartCountDown = 1,
	Fight = 2,
	End = 3,
}

EnumGhostTpye = {
	IsGhost = 1,		--是鬼魂
	NotGhost = 2,		--不是鬼魂
}

EnumDemonsTpye = {
	IsDemons = 1,		--是心魔
	NotDemons = 2,		--不是心魔
}

--同步队伍加入协议原因
NotifyJoinInfoReason = {
	NoReason = 0, 		--没原因
	CrossInviteAutoAddMember = 1, 		--邀请跨服自动组队
	HeBingJoinTeam = 2, 				--合并加入队伍
}

--请求队员位置信息原因
EnumReqTeamMemberPosReason = {
	KF3V3Prepare = 1,
	OtherSceneEnterKF3V3Prepare = 2,
	FollowTeam = 3,
}

TAB_RULE_TYPE = {
	KF3V3 = 1, 			--3V3规则玩法
}

--请求进跨服原因
EnumInviteStartCrossReason = {
	Zhandui3V3Match = 1,								-- 战队3V3匹配
	LoverPKMatch = 2,                                   -- 仙侣PK 仙侣匹配
}
--
EnumInviteStartCrossOperType = {
  	InviteReq = 1,                                      -- 邀请请求 param1 是 邀请的玩家 uid
	InviteAck = 2,                                    	-- 回应邀请 param1 是邀请者的 uid, param2是否拒绝
}

RATSDUOBEI_TASK = {
	FANRENXIUXIAN = 0, 	-- SCENE_TYPE = 40  凡人修仙       修真路
 	FUMOZHANCHUAN = 1, 	-- SCENE_TYPE = 111 伏魔战船       镇妖境
 	FENGSHENDIAN = 2, 		-- SCENE_TYPE = 117 封神殿
 	FUWENFUBEN = 3,  		-- SCENE_TYPE = 119 符文副本   通天路(天神本)
 	SHOUHUXIANLING = 4, 	-- SCENE_TYPE = 84 守护仙灵    苍穹变(宠物本)
 	JINCHANBAOKU = 5, 		-- SCENE_TYPE = 83 金蟾宝库    金币本
 	HAIDIFEIXU = 6,  		-- SCENE_TYPE = 94 海底废墟    幻灵境(装备本)
	QINYUANFUBEN = 8,  --SCENE_TYPE = 18                   情缘副本 1
	SHIJIEMOWANG = 9,  -- SCENE_TYPE = 75                  世界魔王 1
	HONGMENGSHENYU = 10,  -- SCENE_TYPE = 121  鸿蒙神域(跨服) 1
	WABAO = 11,      -- SCENE_TYPE = 115 116  挖宝         天帝陵(龙魂本)
	MOWANGCHAOXUE = 12,  -- SCENE_TYPE = 77  魔王巢穴 1
	MANHUANGUDIAN = 13,  --SCENE_TYPE = 53  蛮荒古殿(跨服)
	JINGJICHANG = 14,  -- SCENE_TYPE = 15  竞技场 1

 	DUOBEI = 100,
}

CHANGE_MODE_TASK_TYPE = {
	INVALID = 0,
	GATHER = 1,									--采集物
	TALK_TO_NPC = 2,							-- NPC
	TALK_IMAGE = 3,								-- 变身
	CHANGE_MODE_TASK_TYPE_FLY = 4,				-- 飞天任务
	MOUNT_HIGH = 5,								-- 高级坐骑
}

ActivityRewardState = {
	BKL = 0,
	KLQ = 1,
	YLQ = 2,
}

ActivityRewardState2 = {
	WLQ = 0,
	YLQ = 1,
}

WoYaoShenQiTaskType = {
	SHENGQI_JIHUO = 1,		--神器激活
	TIANSHEN_HZANLI = 2,	--天神战力
	SHENGQI_SHENGXING = 3,  --神器升星
	SHENGQI_QINAGHUA = 4,   --神器强化
	FULING_SHUXING = 5,     --附灵属性
	FULING_JINDU = 6,		--附灵进度
}

--零元购操作类型
ZERO_BUY_OPERA_TYPE =
	{
		INFO = 0,								-- 请求返回信息
		BUY = 1,								-- 请求够买礼包 param = ID
		FETCH = 2,								-- 请求领取返现 param = ID
		SET_NEW_FLAG = 3,						-- 设置新标记 param = ID
		SEND_LOG_DATA = 4,							-- 请求日志数据
	}

---[[ F2开服礼包
OGA_TEHUI_LIBAO_OPERA = {
	OGA_TEHUI_LIBAO_OPERA_BUY = 0,		-- 购买 p0(礼包index)
}

OGA_TEHUI_LIBAO_INFO = {
	OGA_TEHUI_LIBAO_INFO_ALL = 0,		-- 全部
	OGA_TEHUI_LIBAO_INFO_CHANGE = 1,	-- 变更
}
--]]

XingTianLaiXiStatus = {
	REFRESH_STATUS_REFRESHING = 0,  --刷新中
	REFRESH_STATUS_IDEL = 1,    	--空闲等待刷怪
}

QUANFU_JUANXIAN_OP_TYPE =  {
	TYPE_INFO = 0,
	TYPE_JUANXIAN = 1, 			-- param1 = 捐献次数 1 or 10 param2= 1消耗原料, 2消耗仙玉
	TYPE_ROLE_REWARD = 2, 		-- 个人累计捐献奖励 param1 = 奖励索引
	TYPE_SERVER_REWARD = 3 		-- 每日全服奖励 param1 = 奖励索引
}

CS_RA_BUDDHABAG_OPERA_TYPE = {
	CS_RA_BUDDHABAG_OPERA_TYPE_INFO = 0,
	CS_RA_BUDDHABAG_OPERA_TYPE_BUY = 1
}

JUANXINA_STUFF_TYPE = {
	TYPE_ITEM = 1,			--道具
	TYPE_XIANAYU = 2,		--仙玉
}

--一本万利
YIBENWANLI_OPERA_TYPE = {
		YIBENWANLI_OPERA_INFO = 0,							-- 请求一本万利返回信息
		YIBENWANLI_OPERA_DAILY_FETCH = 1,					-- 请求领取每日礼包 param = seq
		YIBENWANLI_OPERA_RECHARGE_FETCH = 2,				-- 请求领取连续充值奖励
		YIBENWANLI_OPERA_ACCRUED_FETCH = 3,					-- 请求领取累计奖励
	}

--虎摸
HUMO_TYPE = {
	SEND_REQ = 0,--请求信息
	LQ_REWARD = 1, --领奖
	HM_FRIEND = 2, --摸别人

}
 BUFF_FALLING_APPEARAN_TYPE = {

    FALLING_APPEARAN_INVALID = 0,
    FALLING_APPEARAN_GUILD_BATTLE_BUFF = 1,        --仙盟争霸回血buff
    FALLING_APPEARAN_GUILD_BATTLE_BUFF_GONGJI = 2,        --仙盟争霸攻击buff
    FALLING_APPEARAN_GUILD_BATTLE_BUFF_FANGYU = 3,        --仙盟争霸防御buff
    FALLING_APPEARAN_GUILD_BATTLE_BUFF_HUIXUE = 4,        --仙盟争霸回血buff
    FALLING_APPEARAN_GUILD_BATTLE_BUFF_WUDI = 5, 	      --仙盟争霸无敌buff
    FALLING_APPEARAN_GUILD_BATTLE_BUFF_BOOM = 6,          --仙盟争霸爆炸buff
    FALLING_APPEARAN_GUILD_BATTLE_BUFF_ATTACK = 7,        --仙盟争霸攻击buff

  };

NIGHT_FIGHT_FALL_BUFF_TYPE = {
	ADD_HP = 11,				--加血
	ADD_GONGJI = 12,			--加攻击
	ADD_FANGYU = 13,			--加防御
	ADD_WUDI = 14,				--无敌
	ADD_TIAN_XUAN = 15,			--天选之人
}

ROLE_GET_ITEMLIST_TYPE = {
	RA_FIERCE_FIGHTING = 1,
	OA_TIME_LIMIT_DISCOUNT = 12,	-- 限时特惠
	ROLE_GET_ITEMLIST_TYPE_CHAOTIC_GIFT_RMB_BUY = 13,	--洪荒礼包 - 直购奖励
	ROLE_GET_ITEMLIST_TYPE_BEAST = 30,					-- 御兽抽奖
	OGA_DRAW_REWARD = 62,			--  开服活动拓展 - 抽奖奖励
	CROSS_BOSS_STRIKE_RANK_REWARD = 69,   -- 排名奖励
	CROSS_BOSS_STRIKE_GATHER_REWARD = 70, -- 采集奖励
	ROLE_GET_ITEMLIST_TYPE_BOUNTY_LIST_TASK_REWARD = 71,			--// 悬赏单 - 奖励
	ROLE_GET_ITEMLIST_TYPE_BOUNTY_LIST_NOTE_REWARD = 72,			--// 悬赏单 - 手记奖励
	ROLE_GET_ITEMLIST_TYPE_TEAM_COMMON_TOWER_FB_LEVEL_REWARAD = 79, -- 符文塔关卡奖励
	ROLE_GET_ITEMLIST_TYPE_TREASURE_PURSUIT_REWARAD = 80,			-- 天财地宝 - 迷影寻踪奖励
	ROLE_GET_ITEMLIST_TYPE_CANGJINSHANGPU_TEQUAN_REWARAD = 82,		-- 特权恭喜获得弹窗
	ROLE_GET_ITEMLIST_TYPE_TREASURE_PURSUIT_LUCKY_BADGE_REWARAD = 84,			-- 天财地宝 - 迷影寻踪幸运徽章奖励
}

TEMP_BUFF_NAME = {
    [1] = "Effect_huixue",
    [2] = "10016",
    [3] = "Effect_hudun",
    [4] = "10042",
	[5] = "Effect_tianxuan",
}

BEIZHANHAOLI_OP_TYPE = {
	TYPE_INFO = 0,
	TYPE_BUY = 1,     -- 购买礼包 param1 = item_type param2 = libao_id
	TYPE_LIBAO = 2,   -- 领取每日礼包
}

KANJIA_OP_TYPE = {
	TYPE_FAQI_KANJIA = 1,		-- 发起砍价
	TYPE_BANGZHU_KANJIA = 2,    -- 帮助砍价
	TYPE_INFO = 3,				-- 获取所有协助砍价信息
}

ZHANLIBIPIN_OP_TYPE = {
	TYPE_INFO = 0,
	TYPE_ROLE_REWARD = 1,	-- 领取个人奖励 param1 = 奖励索引
	TYPE_SERVER_REWARD = 2,	-- 领取全服奖励 param1 = 奖励索引
	TYPE_ZHANLILIBAO = 3,	-- 购买  param1 = 礼包id
}

-- 天神之路和全民备战和合服活动的多倍来袭任务类型
DUOBEI_LAIXI_TASK_TYPE = {
	TTT = 3,				-- 通天塔
	CQB = 4,				-- 苍穹变
	JBG = 5,				-- 聚宝阁
	HLJ = 6,				-- 幻灵境
	QYFB = 8,				-- 情缘副本
	TDL = 11,				-- 天帝陵
}

PRODUCT_ID_TRIGGER = {
	CLIENT_SHANDIANXIAN_LINE = 100001,					-- 闪电线
	CLIENT_HUIXUE_LINE = 100002,						-- 回血线
	PRODUCT_ID_NEW_PLAYER_WUDI = 10042, 				-- 新手副本用的cg无敌buff
	PRODUCT_ID_DUJIE = 107001, 							-- 渡劫被劈闪电buff
}

AttributeCell_UIType = {
	None = 0,
	TianShenUpgrade = 1,
}

EnumOperateFrequency = {
	EnterFB_FanRenXiuZhen = "EnterFB_FanRenXiuZhen",
}

EnumTianShenHuanHuaOperate = {
	None = 0,
	Active = 1,
	UpStar = 2,
	UpGrade = 3,
	UpSkill = 4,
}

BOSS_RECORD_TYPE = {
	WORLD_BOSS = 1,
    WORLD_SERVER = 2,
    XIANJIE_BOSS = 3,
	EVERYDAY_RECHARGE_BOSS = 4,
}

MONEY_BAR_EFFECT = {
	[GameEnum.NEW_MONEY_BAR.XIANYU] = {ZHAKAI = Ui_Effect.UI_xianyu_zhakai, FLY = Ui_Effect.UI_xianyu_single},
    [GameEnum.NEW_MONEY_BAR.BIND_XIANYU] = {ZHAKAI = Ui_Effect.UI_bangyu_zhakai, FLY = Ui_Effect.UI_bangyu_single},
    [GameEnum.NEW_MONEY_BAR.COIN] = {ZHAKAI = "UI_jingbi_zhakai", FLY = "UI_jinbi_new"},
	[GameEnum.NEW_MONEY_BAR.SILVER_TICKET] = {ZHAKAI = "UI_yuanbao_zhakai_new", FLY = "UI_yuanbao_new"},
	[GameEnum.NEW_MONEY_BAR.RECHARGE_VOLUME] = {ZHAKAI = Ui_Effect.UI_recharge_volume_zhakai, FLY = Ui_Effect.UI_recharge_volume_single}
}

BossRePressType = {
	level = 1,		--等级压制
	cap = 2,		--战力压制
	all = 3,		--等级，战力都压制
}

--货币类型
MoneyType = {
	XianYu = 1, 		-- 仙玉 （旧 元宝）
	BangYu = 2, 		-- 绑玉 （旧 绑元）
	YuanBao = 3, 		-- 元宝 （旧 银票）新增的
	ShengWang = 4, 		-- 声望
	JinBi = 5, 			-- 金币 （旧 铜币）
	ZhanLing = 6, 		-- 战灵
	ZhanHun = 7,	 	-- 战魂
	GuiZuJiYi = 8,		-- 贵族记忆
	ZhenQi = 9,			-- 真气
	GuaXiangJingHua = 10, --卦象精华
	GuildGongXian = 11, -- 仙盟贡献
	GuildJianSheDu = 12, -- 仙盟资金
	HongMengJingHua = 13,-- 鸿蒙精华
	CangJinScore = 14,	--烟雨灵石.
	Chivalrous = 15,    -- 侠义值
	CASH_POINT = 16,     -- 现金点.
}

-- 对应表情
MoneyTypeEmoji = {
	[MoneyType.XianYu] = 221,
	[MoneyType.BangYu] = 224,
	[MoneyType.CangJinScore] = 234,
}

--根据道具ID获取对应表情.
MoneyTypeEmojiByItemID = {
	[22118] = 221,		--灵玉.
	[23629] = 224,		--元宝.
	[36416] = 233,		--铜钱.
	[22099] = 234,		--烟雨灵石.
}

-- 转职龙魂点亮任务类型
TransferTaskOpera = {
	LongPo = 68,
	LongHun = 69,
	LongLing = 70,
}

-- 钱契id
MoneyTicket = {
	[27826] = 27826,
	[27827] = 27827,
	[27828] = 27828,
}

-- 零元购状态
ZeroBuyState = {
	WEISHANGJIA = 0,
	ZHUNBEISHANGJIA = 1,
	WEIGOUMAI = 2,
	YIGOUMAI = 3,
	XIAJIA = 4,
}

--天神8536reason_type 类型
TIANSHEN_REASON_TYPE = {
	NORMAL = 0, --默认
	EQUIP_JICHENG = 1,--装备继承
	EQUIP_TAKE_OFF = 2,--穿脱装备
}

--仙盟邀请回复枚举
GUILD_INVITE_TYPE = {
	YES_REQ = 0, --同意
	NO_REQ = 1,--拒绝
}
--仙盟战结果请求枚举
GUILD_BATTLE_OP_TYPE = {
	RANK_FIGHT_RESULT = 2,--请求战斗结果
}

--货币类型
MONNEY_TYPE_1 = {
	[GameEnum.MONEY_BAR.BIND_GOLD] = "bind_gold",
	[GameEnum.MONEY_BAR.GOLD] = "gold",
	[GameEnum.MONEY_BAR.COIN] = "coin",
	[GameEnum.MONEY_BAR.SILVER_TICKET] = "silver_ticket",
	[GameEnum.MONEY_BAR.SHENGWANG] = "shengwang",
	[GameEnum.MONEY_BAR.CHIVALROUS] = "chivalrous",
	[GameEnum.MONEY_BAR.CASH_POINT] = "cash_point",
}

--货币显示text
MONNEY_TYPE_BAR_TEXT = {
	["bind_gold"] = "Text_2",
	["gold"] = "Text_1",
	["coin"] = "Text_4",
	["silver_ticket"] = "Text_3",
}

--货币显示图标
MONNEY_TYPE_BAR_TEXT_1 = {
	["bind_gold"] = "bangyu_icon",
	["gold"] = "xianyu_icon",
	["coin"] = "tongqian_icon",
	["silver_ticket"] = "yuanbao_icon",
	["shengwang"] = "shengwang_icon",
}

--货币显示group
MONNEY_TYPE_BAR_TEXT_GROUP = {
	["bind_gold"] = "BindGold",
	["gold"] = "Gold",
	["coin"] = "Coin",
	["silver_ticket"] = "sliver_ticket",
	["shengwang"] = "shengwang",
	["chivalrous"] = "chivalrous",
	["cash_point"] = "cash_point",
}


WARDROBE_OP_TYPE = {
	WARDROBE_OPERATE_TYPE_REQ_INFO = 1, -- 基础信息
	WARDROBE_OPERATE_TYPE_ACTIVE_PART = 2, -- 激活部位 param1:seq param2:part
}

HUANHUA_FETTER_OPERATE_TYPE = {
	INFO = 1,                        -- 基础信息
	ACTIVE_PART = 2,				 -- 激活部位 param1:index param2:part
	FETCH_REWARD = 4,				 -- 领取奖励 param1:seq   
}
WARDROBE_ENUM = {
	MAX_SUIT_COUNT = 100,				-- 衣橱套装数
	MAX_SUIT_PART_COUNT = 10,			-- 套装件数
}

WARDROBE_PART_TYPE = {
	FASHION = 1,
	MOUNT = 2,
	LING_CHONG = 3,
	HUA_KUN = 4,
	XIAN_WA = 5,
}

WARDROBE_THEME_TYPE = {
	WARDROBE = 1,				--角色衣橱
	HMGOD = 2,					--鸿蒙神藏
	GUIXUDREAM = 3,				--归墟梦演
	CUSTOMIZED_SUIT = 4,		--定制技能
} 

WARDROBE_NEW_PART_TYPE = {
	MAX_SHIZHUANG_PART = 14,											-- 最大部位数
	MAX_SHIZHUANG_PROJECT_COUNT = 3,									-- 最大方案数
	FORGE_OPER_TYPE_UP_STA = 1,											--// 升星 param1: seq param2: is_use_item
	FORGE_OPER_TYPE_UP_GRADE = 2,										--// 升阶 param2: seq
	FORGE_OPER_TYPE_PUT_ON_STONE = 3,									--// 镶嵌天石 param1: seq param2: stone_seq
	FORGE_OPER_TYPE_COMPOSE_STONE = 4,									--// 合成天石 param1: seq param1: stone_part
	MAX_STONE_PART_COUNT = 3,											--最大镶嵌孔位
	MAX_SHIZHUANG_FORGE_COUNT = 200,									--最大时装能打造个数			
}
----------------------------------衣橱end----------------------------------------

-----------------------------------战令-----------------------------------------
ZhanLing = {
	MaxLevel = 120,
	MaxDailyRankTaskCount = 15,
	MaxWeekRankTaskCount = 10,
	MaxLevelBuyType = 10,
	MaxRoundExchangeLimitCount = 10,		-- 周期最大 物品种类 限制数
	MaxDailyExchangeLimitCount = 10,		-- 每日最大 物品种类 限制数
}

ZhanLingType = {
	normal = 1,						-- 普通战令
	high = 2,						-- 高级战令
	max = 2,
}

ZhanLingTaskClass = {
	Daily = 1,
	Week = 2,
}

ZhanLingShowType = {
	NoLimit = 0,					-- 不限制
	DailyLimit = 1,					-- 每日限制
	RoundLimit = 2,					-- 活动期间限制
}

ZhanLingRewardFetchState = {
	None = 0,						-- 不可领取
	CanFetch = 1,					-- 可领取
	FetchNormal = 2,				-- 已领取普通奖励
	FetchAll = 3,					-- 已领取普通 + 高级奖励
}

ZhanLingOperType = {
	FetchTaskExp = 1,				-- 领取完成任务经验, param1：任务id param2:任务类型
	FlushTask = 2,					-- 刷新任务, param1：任务id  param2:任务类型
	BuyHighZhanLing = 3,			-- 购买高级战令
	FetchLevelReward = 4,			-- 领取等级奖励, param1:level
	GetAllReward = 5,				-- (一键)获取所有可领取奖励
	BuyExp = 6,						-- 购买经验, param1:档位
	RechangeGoods = 7,				-- 兑换物品, param1,商品序号, param2:数量
	GetInfo = 8,					-- 请求战令信息
	ExchangeInfo = 9,				-- 请求商店兑换信息
	BuyExpInfo = 10,				-- 请求购买经验信息
	ReadNewRound = 11,				-- 已读新的一轮
	OneKeyFetchTaskExp = 12,		-- 一键领取任务经验
}

ZhanLingTaskType = {
	BATTLE_LINGPAI_TASK_TYPE_GUILD_SPEAK = 1,					-- 仙盟发言X次
	BATTLE_LINGPAI_TASK_TYPE_WORLD_SPEAK = 2,					-- 世界发言X次
	BATTLE_LINGPAI_TASK_TYPE_HUG = 3,							-- 熊抱X次
	BATTLE_LINGPAI_TASK_TYPE_QINGYUAN_FB = 4,					-- 进入情侣副本X 次
	BATTLE_LINGPAI_TASK_TYPE_CONFESSION = 5,					-- 任意表白X次
	BATTLE_LINGPAI_TASK_TYPE_SEND_FLOWER = 6,					-- 任意送花X次
	BATTLE_LINGPAI_TASK_TYPE_KILL_WORLD_BOSS = 7,				-- 击杀X个世界魔王（世界BOSS）
	BATTLE_LINGPAI_TASK_TYPE_KILL_VIP_BOSS = 8,					-- 击杀X个魔王巢穴（VIPBOSS）
	BATTLE_LINGPAI_TASK_TYPE_KILL_PERSON_BOSS = 9,				-- 击杀X个专属魔王（个人BOSS）
	BATTLE_LINGPAI_TASK_TYPE_KILL_DABAO_BOSS = 10,				-- 击杀X个神魔禁地（打宝BOSS）
	BATTLE_LINGPAI_TASK_TYPE_KILL_SHENYUAN_BOSS = 11,			-- 击杀X个深渊魔王（深渊boss）
	BATTLE_LINGPAI_TASK_TYPE_KILL_CROSS_BOSS = 12,				-- 击杀X个蛮荒神兽（跨服BOSS）
	BATTLE_LINGPAI_TASK_TYPE_USE_TREASURE_MAP = 13,				-- 使用Y色藏宝图X次
	BATTLE_LINGPAI_TASK_TYPE_ENTER_TIANXIANGE_FB_X = 14,		-- 通关修真路X次（天仙阁）
	BATTLE_LINGPAI_TASK_TYPE_ENTER_COIN_FB_X = 15,				-- 进入聚宝阁X次（铜币本）
	BATTLE_LINGPAI_TASK_TYPE_ENTER_NEW_PET_FB_X = 16,			-- 进入苍穹变X次（宠物本）
	BATTLE_LINGPAI_TASK_TYPE_ENTER_BAGUA_FB_X = 17,				-- 进入通天塔X次（八卦迷阵）
	BATTLE_LINGPAI_TASK_TYPE_ENTER_WABAO_FB_X = 18,				-- 进入天帝陵X次（挖宝）
	BATTLE_LINGPAI_TASK_TYPE_COMPLETE_XUANSHANG_TASK_X = 19,	-- 完成赏金任务X次
	BATTLE_LINGPAI_TASK_TYPE_EXP_QIFU_X = 20,					-- 经验祈福X次
	BATTLE_LINGPAI_TASK_TYPE_GOLD_QIFU_X = 21,					-- 仙玉祈福X次
	BATTLE_LINGPAI_TASK_TYPE_JOIN_HOTSPRING_X = 22,				-- 参与温泉X次
	BATTLE_LINGPAI_TASK_TYPE_JOIN_GUILD_DINNER_X = 23,			-- 参与仙盟晚宴X次
	BATTLE_LINGPAI_TASK_TYPE_DOUBLE_HUSONG_X = 24,				-- 双倍护送X次
	BATTLE_LINGPAI_TASK_TYPE_JOIN_GUILD_GUARD_X = 25,			-- 参与仙盟守卫X次
	BATTLE_LINGPAI_TASK_TYPE_JOIN_XIULUO_SHILIAN_X = 26,		-- 参与修罗试炼X次（青云之颠 - 跨服修罗塔）
	BATTLE_LINGPAI_TASK_TYPE_JOIN_YONGYE_X = 27,				-- 参与永夜战场X次（夜战云巅）
	BATTLE_LINGPAI_TASK_TYPE_JOIN_CROSS_1V1_X = 28,				-- 参与跨服1v1 X次
	BATTLE_LINGPAI_TASK_TYPE_JOIN_CROSS_3V3_X = 29,				-- 参与跨服3v3 X次
	BATTLE_LINGPAI_TASK_TYPE_JOIN_GUILD_BATTLE_X = 30,			-- 参与仙盟战 X次
	BATTLE_LINGPAI_TASK_TYPE_SEND_FLOWER_X = 31,				-- 赠送X朵玫瑰
	BATTLE_LINGPAI_TASK_TYPE_SEND_CHAMPAGNE_X = 32,				-- 赠送X个香槟
	BATTLE_LINGPAI_TASK_TYPE_JOIN_CHALLENGE_FIELD_X = 33,		-- 参加竞技场X次
	BATTLE_LINGPAI_TASK_TYPE_COMPLETE_ZHENYAOJING_FB_X = 34,	-- 完成镇妖境副本X次（灵魂广场）
	BATTLE_LINGPAI_TASK_TYPE_HUANLINGJING_FB_X = 35,			-- 完成幻灵镜副本X次（远古装备本）
	BATTLE_LINGPAI_TASK_TYPE_SEND_ROCKET_X = 38,				-- 赠送X个火箭
	BATTLE_LINGPAI_TASK_TYPE_SEND_YOUTING_X = 39,				-- 赠送X个游艇
	-- 40 完成X个国家版图任务
	BATTLE_LINGPAI_TASK_TYPE_EQUPT_DRAW_X = 41,					-- 进行装备抽奖X次
	BATTLE_LINGPAI_TASK_TYPE_POSY_DRAW_X = 42,					-- 进行铭文抽奖X次
	BATTLE_LINGPAI_TASK_TYPE_JOIN_ETERNAL_NIGHT_X = 43,			-- 参与诛仙战场X次（永夜之巅）
}
-----------------------------------战令end-----------------------------------------

--宝宝激活卡显示
BABY_CARD_SHOW = {
	ONE_CON = 1, --单个条件
	ALL_CON = 2, --所有条件
}

SpecialItemId =
{
	Exp = 90708,
}

-- 客户端用的物品类型，用于减少判断逻辑
CLIENT_ITEM_TYPE = {
	AWAKE_SKILL = 1,    -- 觉醒技能书
}

--夜战云巅 属性飘字
NIGHT_FIGHT_FALL_BUFF_TYPE_VIEW = {
	INVALID = 0,
	ADD_HP = 1,				--加血
	ADD_ATTR_1 = 2,			--全属性伤害加成
	ADD_ATTR_2 = 3,			--全属性伤害减免
	ADD_WUDI = 4,			--无敌
}

XUNYULU_OPERA_TYPE = {
	YPE_REQ_INFO = 0,
	TYPE_REWARD = 1,			--param:id
}

ONLINE_TYPE = {
	ONLINE_TYPE_OFFLINE = 0,			-- 离线
	ONLINE_TYPE_ONLINE = 1,				-- 在线
	ONLINE_TYPE_REST = 2,				-- 离线挂机
	ONLINE_TYPE_CROSS = 3,				-- 跨服
}

GUILD_FB_PASS_STATUS = {
	STATUS_NORMAL = 0,					-- 正常开启
	STATUS_PASS = 1,					-- 通关
	PASS_STATUS_FAIL = 2,				-- 通关失败
}

--组队邀请的类型
GUILD_ASSIST_REASON_TYPE = {
	NORMAL = 0,
	HURT = 1,
}

--市场请求一种类型物品列表
MARKET_AUCTION_ITEM_TYPE = {
	AUCTION_TYPE_WORLD = 0,
	AUCTION_TYPE_GUILD = 1,
	AUCTION_TYPE_SELF = 2,
	AUCTION_TYPE_FOCUS = 3,
}

ZHAN_LING_TEXT_COLOR_TYPE = {
	ONE = 1,
	TWO = 2,
}

LOGIN_FLAG_TEXT_COLOR_TYPE = {
	ONE = 1,                        --选服推荐标签
	TWO = 2,                        --选服新服标签
}

VIP_EXP_CHANGE_TYPE = { --vip经验增加原因
	VIP_EXP_CAHNGE_TYPE_ITEM_USE = 0, 								--道具增加
	VIP_EXP_CAHNGE_TYPE_GOLD_COST = 1,								--仙玉消耗
	ADD_VIP_EXP_TYPE_WORLDBOSS = 2, 								--世界魔王
	ADD_VIP_EXP_TYPE_ZHUANGSHUBOSS = 3,								-- 专属魔王boss
	ADD_VIP_EXP_TYPE_MWCXBOSS = 4,									--   魔王巢穴boss
	ADD_VIP_EXP_TYPE_GOLD_COST = 5,									--   元宝消耗
	ADD_VIP_EXP_TYPE_ITEM_COST = 6,									--   物品消耗
	ADD_VIP_EXP_TYPE_MAX = 7,
}

ATTACK_TYPE = {
	MONSTER = 1,
	ROLE = 2,
}

CAREER_SERIOUS_TYPE =
{
	FIRE = 1,				-- 火
	ICE = 2,				-- 冰
	THUNDER = 3,			-- 雷
}

ADJUST_CAMERA_TYPE = {
	SCENE_POS = 1,			-- 场景摄像机，场景指定位置触发，不恢复
	TASK = 2,				-- 任务摄像机，根据配置判断恢复不恢复，任务触发设置和恢复
	NO_CAN_CHANGE = 3,		-- 副本摄像机，玩家不可调整摄像机，会强制切换为锁定，离开场景后恢复进入场景前的参数，由场景ID驱动
	FUNBEN = 4,				-- 副本摄像机，进入不会强制切换摄像机模式，玩家调整摄像机之后清除缓存摄像机数据，离开场景有缓存数据就恢复之前的参数，但是距离会重置为默认距离，由场景ID驱动
	XUNYOU = 5,				-- 巡游摄像机，不恢复, 主要用来拦截其他摄像机的调整
	CLICK_FIX = 6,			-- 玩家手动点击切换摄像机模型，不恢复，主要用来清除那些优先级比较低的摄像机缓存数据
	AREA = 7,				-- 区域摄像机，进入指定区域触发，离开区域恢复
	SKILL = 8,				-- 技能摄像机，释放技能时 or 维持某个状态时触发， 结束时恢复（场景转换不受影响）
	CAN_CHANGE = 9,			-- 副本摄像机，玩家可以调整摄像机，进入切换成自由，玩家调整摄像机不会清除缓存数据，离开场景恢复
	UP_MOUNT = 10,			-- 坐骑摄像机，上坐骑调整距离，切坐骑更新，下坐骑恢复
	NORMAL = 11,			-- 玩家普通的调整摄像机
}

TASK_CAMERA_TYPE = {
	CAN_ACCEPT = 1,
	CAN_COMMIT = 2,
	FINISH = 3,
}

BOSS_TASK_TYPE = {
	WORLD = 1,
	PERSON = 2,
	DABAO = 3,
	VIP = 4,
	MANHUANG = 5,
	MAX = 4,
}

--需要自动标识的任务类型
NEED_AUTO_TASK_FLAG = {
	[GameEnum.TASK_TYPE_ZHU] = true,								--主线
	[GameEnum.TASK_TYPE_RI] = true,								--日常
	[GameEnum.TASK_TYPE_HU] = true,								--护送
	[GameEnum.TASK_TYPE_MENG] = true,								--仙盟
	[GameEnum.TASK_TYPE_CAMP] = true,								--阵营任务
	[GameEnum.TASK_TYPE_HUAN] = true,								--跑环任务
	[GameEnum.TASK_TYPE_SHANG] = true,							--悬赏任务
	[GameEnum.TASK_TYPE_ZHUAN] = true,							--转职任务
	[GameEnum.TASK_TYPE_GUILD_BUILD] = true,						--仙盟建设
	[GameEnum.TASK_TYPE_GUAJI] = true,							--挂机任务
	[GameEnum.TASK_TYPE_LING] = true,							--护送灵石
	[GameEnum.TASK_TYPE_TREASURE_MAP] = true,					--藏宝图
}

RELATION_TYPE = {
	FRIEDN = 1,     --好友
	GUILD = 2,      --盟友
	ZHANDUI3V3 = 3, --3V3战友
}

NO_LONGER_RECORD_TYPE = {
	TODAY = 0,          -- 今日
	LOGIN = 1,          -- 本次登录
	MAX = 2,
}

--本次登录阻断类型
LOGIN_NO_LONGER_TYPE = {
	LOGIN_NO_LONGER_TYPE_INVALID = 0,
	LOGIN_NO_LONGER_TYPE_INVITE_ME_TEAM = 1,        --不再接受他组队邀请
	LOGIN_NO_LONGER_TYPE_JOIN_MY_TEAM = 2,          --不再统一他进队申请
	LOGIN_NO_LONGER_TYPE_ACCEPT_MERGE = 3,          --不再接受他队伍合并
}

-- 市场操作
AUCTIONITEM_OP_TYPE = {
	AUCTIONITEM_OP_TYPE_NONE = 0,
	AUCTIONITEM_OP_TYPE_ADD = 1,					-- 上架物品，param1:knapsack_type param2:knapsack_index param3:item_num param4:total_price param5 password
	AUCTIONITEM_OP_TYPE_REMOVE = 2,					-- 撤回已上架物品， param1:auction_index

	AUCTIONITEM_OP_TYPE_DIRECTYBUG = 4,				-- 购买物品，param1:auction_index param2:password
	AUCTIONITEM_OP_TYPE_YAOHE = 5, 					-- 吆喝(叫卖)，param1:auction_index
	AUCTIONITEM_OP_TYPE_INFO = 6,                   -- 拍卖绑玉信息
	AUCTIONITEM_OP_TYPE_JUMP_FLAG = 7,				-- 跳转标记  param1：0: 不勾选 1: 勾选
}

--市场拍卖请求操作枚举
NEW_AUCTION_OPERATE_TYPE = {
	REQ_ITEM_INFO = 1,			-- 拍卖物品信息
	BID = 2,						-- 竞拍
	AUTO_BID = 3,					-- 自动竞拍
	REQ_LOG_INFO = 4,				-- 日志信息
	REQ_BID_RECORD_INFO = 5,		-- 竞拍信息
}

MARKET_GOODS_TYPE = {
	WORLD = 0,
	SELF = 2,
	WORLD_NUM = 4, 		-- 全部商品对应子类的数量
}

MARKET_PASSWORD_DROPDOWN_TYPE = {
	NONE = 0, 										-- 不限制
	NO_PASSWORD = 1, 								-- 没有密码
	HAS_PASSWORD = 2, 								-- 有密码的商品
}

-- 市场求购操作
AUCTIONWANTED_OP_TYPE = {
	AUCTIONWANTED_OP_TYPE_NONE = 0,
	AUCTIONWANTED_OP_TYPE_ADD = 1,					-- 申请求购 param1:item_id param2:item_star param3:item_num param4:price
	AUCTIONWANTED_OP_TYPE_REMOVE = 2,				-- 撤销求购 param1:wanted_index
	AUCTIONWANTED_OP_TYPE_RESPONSE = 3,				-- 完成他人的求购 param1:wanted_index param2:knapsack_type param2:knapsack_index
	AUCTIONWANTED_OP_TYPE_REQ_INFO = 4,				-- 请求求购列表信息
}

-- 市场求购信息更新类型
MARKET_WANT_UPDATE_TYPE = {
	ADD = 1, 										-- 新增
	REMOVE = 2, 									-- 移除
}

PPSY_OPERA_TYPE = {
	POSY_OPERATOR_TYPE_BASE_INFO = 0,	-- 基本信息
	POSY_OPERATOR_TYPE_UP_LEVEL = 1,	-- 升级 p1(slot) 特殊槽不能升级
	POSY_OPERATOR_TYPE_COMPOSE = 2,		-- 合成 p1(item_id)
	POSY_OPERATOR_TYPE_BAG_INFO = 3,	-- 背包信息
	POSY_OPERATOR_TYPE_PUT_SLOT = 4,	-- 装备 p1(slot) p2(背包位置) p3(is_speical)
	POSY_OPERATOR_TYPE_DOWN_SLOT = 5,	-- 卸下 p1(slot)
	POSY_OPERATOR_TYPE_DE_COMPOSE_INDEX = 6,	-- 分解p1(背包位置)
	POSY_OPERATOR_TYPE_DE_COMPOSE_QUAILITY = 7,	-- 分解p1(quality)

	POSY_FROM_NORMAL_SLOT = 0,
	POSY_FROM_SPECIAL_SLOT = 1,
	POSY_FROM_BAG = 2,
}
EVERYDAY_RECHARGE_TYPR = {
	OPER_TYPE_LEICHONG_INFO = 0,						--充值信息.
	OPER_TYPE_RECHARGE_REWARD = 1,						--领取每日充值奖励.
	OPER_TYPE_GET_LEIJI_CHONGZHI_REWARD = 2,			--累充天数奖励.
	OPER_TYPE_ONE_KEY_GET_LEIJI_REWARD = 3,				--领取今日所有的奖励.
	OPER_TYPE_GET_LEIJI_CHONGZHI_FREE_REWARD = 4,		--领取免费奖励, paraml: 0：秒杀礼包免费奖励；1：秒杀礼包超级返利；2：每日礼包免费奖励.
	OPER_TYPE_GET_LEIJI_CHONGZHI_RAPIDLY_REWARD = 5,	--领取秒杀礼包奖励, paraml：id.
	OPER_TYPE_GET_RAPIDLY_REWARD_RECORD_INFO= 6,		--秒杀礼包记录信息.
}

BUY_VIPTIME_CARD_TYPE = {
	OP_TYPE_ACTIVE_VIP_CARD = 1, 			-- 请求激活vip卡
	OP_TYPE_FETCH_DAILY_REWARD = 2,			-- 领取每日礼包
	OP_TYPE_BUY_VIP_ZERO_BUY = 3,			-- 购买VIP零购
	OP_TYPE_FETCH_RETURN_GOLD = 4,			-- VIP零元购返现
	OP_TYPE_RECEIVE_ZERO_BUY_REWARD = 5, 	-- 领取VIP零元奖励
	OP_TYPE_FETCH_INITIAL_VIP = 6, 	        -- 领取初始vip
}

INVEST_CARD_TYPE_MAX = 2

OGA_RUSH_RANK_OP_TYPE = {
	RUSH_RANK_REWARD_TYPE_GOAL_FETCH = 1,			-- 请求信息
	RUSH_RANK_REWARD_TYPE_DAILY_FETCH = 2,			-- 领取礼包，参数1是比拼类型，参数2是奖励索引
	RUSH_RANK_REWARD_TYPE_DAY_BUY = 3,				-- 购买日常礼包, 参数1是比拼类型，参数2是礼包索引
	-- RUSH_RANK_REWARD_TYPE_RANK_FETCH = 4,			-- 排名奖励领取
	RUSH_RANK_LIMIT_TIME_BUY = 4,					-- 购买限时礼包 参数1是活动类型 参数2是天数 参数3是礼包id
}

LIFESOUL_OPERATOR_TYPE =
{
	LIFESOUL_OPERATOR_TYPE_BASE_INFO = 0,	-- 基本信息
	LIFESOUL_OPERATOR_TYPE_UP_LEVEL = 1,	-- 升级 p1(slot) 特殊槽不能升级
	LIFESOUL_OPERATOR_TYPE_BAG_INFO = 2,	-- 背包信息
	LIFESOUL_OPERATOR_TYPE_PUT_SLOT = 3,	-- 装备 p1(slot) p2(背包位置) p3(is_speical)
	LIFESOUL_OPERATOR_TYPE_DOWN_SLOT = 4,	-- 卸下 p1(slot)
	LIFESOUL_OPERATOR_TYPE_LOCK_EVEN = 5,	-- 锁 - 解锁
}

MAX_LIFESOUL_SLOT_COUNT = 6
MAX_LIFESOUL_SPECIAL_SLOT_COUNT = 1
LIFESOUL_INDEX_TYPE_SLOT = 0
LIFESOUL_INDEX_TYPE_SPECIAL_SLOT = 1
LIFESOUL_INDEX_TYPE_BAG = 2
LONGHUN_OPEN_FROM_BAG = 1
LONGHUN_OPEN_FROM_EQUIP = 2

OGA_BOSS_HUNTER_REQ = {
	OGA_BOSS_HUNTER_REQ_QUERY_GUILD_RANK = 1,		-- 请求帮派排名
	OGA_BOSS_HUNTER_REQ_QUERY_SELF_GUILD_RANK = 2	-- 请求自己帮派排名
}

WABAO_EVENT_TYPE = {  --挖宝事件类型
	WABAO_EVENT_INVALID = 0,
	WABAO_EVENT_ENTET_PERSONFB = 1,			--进入单人副本
	WABAO_EVENT_ENTET_REWARD_MONEY = 2,		--奖励某种货币 p1 名字 p2 数量
	WABAO_EVENT_ENTET_REWARD_ITEM = 3,		--奖励物品
	WABAO_EVENT_ENTET_MONSTER = 4,			--挖出怪物
	WABAO_EVENT_ENTET_NOTHING = 5,			--什么都没有
	WABAO_EVENT_ENTET_OPEN_TEAMFB = 6,		--开启组队副本

}

CS_RAXIANLINGZHEN_OPERA_TYPE = {			--灵犀六芒图操作类型
	CS_RA_XIANLINGZHEN_OPERA_TYPE_INFO = 0,
	CS_RA_XIANLINGZHEN_OPERA_TYPE_USE = 1,
	CS_RA_XIANLINGZHEN_OPERA_TYPE_WORLD_INFO = 2,
	CS_RA_XIANLINGZHEN_OPERA_TYPE_CHUAN_WEN = 3,
}

RA_HAPPY_MONDAY_OP = { 						--周一运程操作类型
	RA_HAPPY_MONDAY_OP_REQ_INFO = 1,					-- 请求信息
	RA_HAPPY_MONDAY_OP_FETCH_DRAW_TIMES = 2,			-- 领取抽奖次数，参数1是任务类型
	RA_HAPPY_MONDAY_OP_CHOOSE_REWARD = 3,				-- 选择奖励，param1-4 分别代码四个档位，其比特位为1则表示选择其中的奖励
	RA_HAPPY_MONDAY_OP_DRAW = 4,						-- 进行一次抽奖
}

LOGIN_LUXURY_GIFT_OP = {					-- 登录豪礼操作类型
	FETCH_REWARD = 0,
	ALL_INFO = 1,
}

-- 主界面活动按钮流光特效类型
MAIN_ACT_LIUGUANG_TYPE = {
	NOEN = 0, 			-- 没有特效
	ALWAYS = 1, 		-- 常驻
	SOMETIME = 2, 		-- 点击消失
}

-- 主界面活动按钮流光特效类型
MAIN_TOP_BTN_EFFECT_TYPE = {
	ALWAYS = 1, 		-- 常驻
	SOMETIME = 2, 		-- 点击消失
}

-- 采集物状态
GATHER_STATUS = {
	NORMAL = 1, 			-- 采集前
	GATHERING = 2, 			-- 采集中
	END = 3, 				-- 采集后
}

RankGradeEnum = {
	[1] = "bronze",			-- 青铜
	[2] = "sliver",			-- 白银
	[3] = "gold",			-- 黄金
	[4] = "platinum",		-- 铂金
	[5] = "diamond",		-- 钻石
	[6] = "master",			-- 大师
	[7] = "king"			-- 王者
}

-- 新手弹窗触发类型
NOVICE_TRIGGER_TYPE = {
	ACCEPT_TASK = 1, 		-- 接受某个任务
	COMPLETE_TASK = 2, 		-- 完成某个任务
	ENTER_AREA = 3, 		-- 玩家进入某个区域
}

--仙盟奖励分配
GUILD_BATTLE_FENPEI_REWARD_LIMIT_NUM = 5		--仙盟争霸分配最大数
GUILD_BATTLE_REWARD_APPLY_OR_ALLOC = {
	GUILD_BATTLE_REWARD_APPLY = 0, 			--奖励申请
	GUILD_BATTLE_REWARD_ALLOC = 1, 			--奖励分配
	GUILD_BATTLE_REWARD_INFO_SEND = 2, 		--奖励信息发送
	GUILD_BATTLE_REWARD_INFO_IF_MEMBER = 3, 		--奖励信息发送给成员
}


----------------永夜之巅------------
ETERNAL_NIGHT_PARAM = {
	MAX_EQUIP_PART_NUM = 13,
}
----------------永夜之巅END------------


MATCH_TYPE = { --组队匹配类型
	MATCH_TYPE_MIN = 0,
	MATCH_TYPE_NORAML = 1,						-- 匹配玩家，X秒后匹配机器人
	MATCH_TYPE_ONLY_ROBOT = 2,					-- 只匹配机器人
	MATCH_TYPE_MAX = 3,
}

NETWORK_STATE = {
	None = 0,		--无网络
	MOBILE = 1,		--移动网络
	WIFI = 2,		--WiFi网络
	UnKnow = 3,		--未知网络
}

--男剑:1, 女剑:2, 男琴:3, 女琴:4
BASE_TRUE_PROF = {
	Nan_Jian = 1,
	Nv_Jian = 2,
	Nan_Qin = 3,
	Nv_Qin = 4,
}

SKILL_DIR_TYPE = {
	NONE = 0,
	ONE = 1, 			-- 单向
	TWO = 2, 			-- 双向
	CROSS = 3, 			-- 十字
	TOW_CROSS = 4,		-- 米字
}

OBJ_FOLLOW_TYPE = {
	TASK_CHAIN = 1,
	TEAM = 2,
}

SELECT_BOSS_REASON = {
	VIEW = 1,
	ASSIST = 2,
	CONVENE = 3,
}

OBJ_MOVE_REASON = {
	LING_CHONG_FIGHT = 1,
	BEAST_FIGHT = 2,
	TASK_CALLINFO_FIGHT = 3,
}

OBJ_TYPE = {
	OBJ_TYPE_INVALID = 0,
	OBJ_TYPE_ROLE = 1,				-- 角色
	OBJ_TYPE_MONSTER = 2,			-- 怪物
	OBJ_TYPE_FALLINGITEM = 3,		-- 掉落物品
	OBJ_TYPE_GATHER = 4,			-- 采集物品
	OBJ_TYPE_SCENEAREA = 5,			-- 特殊区域
	OBJ_TYPE_EFFECT = 6,			-- 技能效果物品
	OBJ_TYPE_TRIGGER = 7,			-- 触发器
	OBJ_TYPE_BIG_COIN = 8,			-- 大铜币
	OBJ_TYPE_BATTLEFIELD_SHENSHI = 9,	-- 战场神石
	OBJ_TYPE_MONSTER_HALO = 10,			-- 怪物光环
	OBJ_TYPE_MARRY = 11,				-- 结婚模型
	OBJ_TYPE_ROLE_SHADOW = 12,			-- 角色影子
	OBJ_TYPE_WORLDEVENT_OBJ = 13,		-- 世界事件对象
	OBJ_TYPE_PET = 14,					-- 宠物
	OBJ_TYPE_ATTRIBUTE = 15,			-- 属性计算对象 在场景中不存在的对象
	OBJ_TYPE_COUNT = 15,
}

TIANSHEN_SHENSHI_REQ = {
	TIANSHEN_SHENSHI_REQ_PUT = 0,		-- 穿戴 p0(天神) p1(背包index)
	TIANSHEN_SHENSHI_REQ_JIN_SHENG = 1,	-- 晋升 p0(天神)
	TIANSHEN_SHENSHI_REQ_COMPOSE = 2,	-- 合成 p0(天神) p1(itemid)
}

ATTACK_RANGE_TYPE = {
    --正方形，以自己为中心（原备注）
    --玩家位置和技能释放点是方形的中心点
	SQUARE_ME = 1,

    --矩形，以目标为中心（原备注）
    --技能释放点是（以玩家为圆心，攻击距离为半径的）圆里的任意点，再以配置里的攻击范围为矩形的边长
	RECTANGLE_TARGET = 2,

    --圆形，以自己为中心（原备注）
    --玩家位置和技能释放点在圆心
	CIRCULAR_ME = 3,

    --圆形，以目标为中心（原备注）
    --技能的圆心是（以玩家为圆心，攻击距离为半径的）圆里的任意点，再以配置里的攻击范围为半径
	CIRCULAR_TARGET = 4,

    --矩形，以自己为起点（原备注）
	RECTANGLE_ME = 5,

    --扇形，以自己为起点（原备注）
    --以玩家位置为圆心，技能释放点为弧上的中点，圆心角（30至150度），相当于扇形在玩家的正前方中心对称
	SECTOR_ME = 6,

    -- 矩形冲锋
	RECTANGLE_CHONG_FENG = 7,
}


COUNTRY_TASK_QUALITY = {
	BEST = 1,
	MIDDLE = 2,
	LOW = 3,
}

------------运营活动--------------------------------------------
OPERATION_ACTIVITY_FIRST_RECHARGE = {
	YLQ = 2, --已领取
	WDC = 0, --未达成
	KLQ = 1, --可领取
}

OPERATION_ACTIVITY_SHOUCHONG_TYPE = {
	TYPE_INFO = 0,	-- 活动信息
	TYPE_DRAW = 1,	-- 领取奖励
}

OperaActLeiChongRechargeParam = {
    LEICHONG_DANGWEI_MAX = 32,
}

--活动号对应类型
OPERATION_ACTIVITY_OPEN_TYPE = {
	[20018] = 1, --类型1
	[20010] = 2, --类型2
}

OPERATION_EVENT_TYPE =
{
	NONE = 0, --这个活动不需要任何特殊条件
	LEVEL = 1, --等级
	ITEM = 2,--道具
	DAY = 3, -- 天数
}

OPERATION_QUANFU_JUANXIAN = {
	QUANFUJUANXIAN_MAX_SERVER_LIBAO = 64,--全服捐献接收数据数量
	QUANFUJUANXIAN_MAX_ROLE_LIBAO = 64,--累积捐献接收数据数量
	JUAN_XIAN_NUM_1 = 1,
	JUAN_XIAN_NUM_2 = 10,
}

OPERATION_QUANFU_JUANXIAN_OP_TYPE =  {
	TYPE_INFO = 0,
	TYPE_JUANXIAN = 1, 			-- param1 = 捐献次数 1 or 10 param2= 1消耗原料, 2消耗仙玉
	TYPE_ROLE_REWARD = 2, 		-- 个人累计捐献奖励 param1 = 奖励索引
	TYPE_SERVER_REWARD = 3 		-- 每日全服奖励 param1 = 奖励索引
}

OPERATION_REAWAD_STATE = {
	BKL = 0,
	KLQ = 1,
	YLQ = 2,
}

--只是为了区分类型，没有实际意义
OPERATION_REAWAD_TYPE = {
	ONE = 1,
	TWO = 2,
	THREE = 3,
	FOUR = 4,
}

--只是为了区分类型没有任何实际的意义
OPERATION_ACTIVITY_BTN_TYPE = {
	ONE = 1, --类型1
	TWO = 2, --类型2
}

OPERATION_LOGIN_REWARD_BTN_STATE = {
	WEIDA_TIANSHU = 1,	        --未达到天数
	HAS_NORMAL_REWARD = 2,		--普通奖励可领取
	HAS_SPECIAL_REWARD = 3,		--特俗奖励可领取
	HAS_ALL_REWARD = 4,
	VIP_REWARD = 5,		        --已领取普通奖励，vip奖励条件未达到
	YILINGQU = 6,		        --已领取所有奖励
}

OPERATION_LOGIN_GIFT_OP_TYPE = {
	INFO = 1,				-- 活动信息
	COMMON_REWARD = 2,		-- 领取普通奖励
	SPECIAL_REWARD = 3,	-- 领取特殊奖励
}

--天才厨神
TIANCAICHUSHEN_OPERA_TYPE = {
	TIANCAICHUSHEN_OPERA_TYPE_1 = 1,			    -- 排行榜信息
	TIANCAICHUSHEN_OPERA_TYPE_2 = 2,				-- 活动信息
	TIANCAICHUSHEN_OPERA_TYPE_3 = 3,				-- 菜谱信息
	TIANCAICHUSHEN_OPERA_TYPE_4 = 4,				-- 烹饪 param1~3 = item_id
	TIANCAICHUSHEN_OPERA_TYPE_5 = 5,				-- 播放动画动画
	TIANCAICHUSHEN_OPERA_TYPE_6 = 6,				-- 领取解锁全部菜谱奖励
	TIANCAICHUSHEN_OPERA_TYPE_7 = 7,				-- 点击求助 param1 菜谱i
	TIANCAICHUSHEN_OPERA_TYPE_8 = 8,				-- 点击提醒 param1 菜谱i

}
--营活动-天才厨神烹饪返回
TIANCAICHUSHEN_COOK_TYPE = {
	TIANCAICHUSHEN_COOK_TYPE_1 = 1,				    -- 失败
	TIANCAICHUSHEN_COOK_TYPE_2 = 2,					-- 成功但没解锁新菜谱
	TIANCAICHUSHEN_COOK_TYPE_3 = 3,					-- 成功并解锁新菜谱
	TIANCAICHUSHEN_COOK_TYPE_4 = 4,					-- 中级菜谱
	TIANCAICHUSHEN_COOK_TYPE_5 = 5,					-- 次数已满
}

OA_WATER_FLOWER_OPERA_TYPE = {
	OA_WATER_FLOWER_OPERA_TYPE_INFO_REQ = 0,				-- 请求信息
	OA_WATER_FLOWER_OPERA_TYPE_PLANT = 1,					-- 种植, param_0 = 花盆seq
	OA_WATER_FLOWER_OPERA_TYPE_WATER = 2,					-- 浇水, param_0 = 花盆seq
	OA_WATER_FLOWER_OPERA_TYPE_FETCH = 3,					-- 领取, param_0 = 花盆seq
	OA_WATER_FLOWER_OPERA_TYPE_WATER_OTHER = 4,				-- 帮其他人浇水, param_0 = 花盆seq, param_1 = 帮浇对象UID
	OA_WATER_FLOWER_OPERA_TYPE_HELP_OTHER_INFO_REQ = 5,		-- 帮其他人浇水信息
	OA_WATER_FLOWER_OPERA_TYPE_INVITE_HELP_ME = 6,			-- 邀请朋友浇水，param_0 = 朋友UID
	OA_WATER_FLOWER_OPERA_TYPE_OTHER_HELP_ME_INFO_REQ = 7,	-- 请求帮我浇水信息
	OA_WATER_FLOWER_OPERA_TYPE_FETCH_HELP_ME_REWARD = 8,	-- 请求领取帮浇奖励(别人帮我浇水), param_0 = 花盆seq, param_1 = 帮浇对象UID
	OA_WATER_FLOWER_OPERA_TYPE_WATER_LOG_REQ = 9,			-- 请求浇水日志
	OA_WATER_FLOWER_OPERA_TYPE_OTER_INFO_REQ = 10,  		-- 请求其他人的盆栽信息 param_0 = uid
	OA_WATER_FLOWER_OPERA_TYPE_CONFIRM_INVITE = 11, 	 	-- 确认其他人的邀请 param_0 = uid
}

--幸福狂欢
HAPPY_CANIVAL_OPERA_TYPE ={
	HAPPY_CANIVAL_OPERA_TYPE_INFO = 0,
	HAPPY_CANIVAL_OPERA_TYPE_REWARD = 1,
}

--幸福狂欢
HAPPY_CANIVAL_REWARD_TYPE ={
	KLQ = 0,
	WDC = 1,
	YLQ= 2,
}

-- 限时秒杀
OA_TIMED_SPIKE_OPERA_TYPE =
{
	OA_TIMED_SPIKE_OPERA_TYPE_INFO = 0,			-- 限时秒杀信息 param1:type
	OA_TIMED_SPIKE_OPERA_TYPE_BUY = 1,				-- 进行购买		param1:shop_id param2:id
	OA_TIMED_SPIKE_OPERA_TYPE_RECEIVE_GIFT = 2,		-- 领取额度奖励 param1:type param2:index
}

-- 兔女郎
RA_TUNVLANG_OPERA_TYPE = {
	 LAYER_INFO = 1, 							--下发档位信息
	 DRAW = 2,									--抽奖, param1填档位，param2填否自动购买
	 RESET_LAYER = 3,							--重置奖池，param1填挡位
	 RECORD = 4,								--记录，param1填挡位
	 SET_DIR = 5,								--角度
	 DRAW_ONEKEY = 6,							--一键抽奖
}

YUNYINGHUODONG_XIANSHILEICHONG_TYPE = {
	TYPE_INFO = 0, -- 0:获取当前信息
	TYPE_DRAW = 1, -- 1: 领取奖励 param = 档位
}

OperaActCtnRechargeParam = {
	MAX_CTN_DAY_COUNT = 10,			-- 活动最大天数
	MAX_CTN_REWARD_BIT = 128,		-- 活动最大奖励标记
}
OperaActLeiChongRechargeParam = {
	LEICHONG_DANGWEI_MAX = 32,
}

ACT_RECAHREGE_OPERA_TYPE = {
	ACT_RECAHREGE_OPERA_INFO = 0,  --请求信息
	ACT_RECAHREGE_OPERA_DAY_REWARD = 1,			-- 每天奖励奖励领取 param1=领取下标
	ACT_RECAHREGE_OPERA_TOTAL_DAY_REWARD = 2,	-- 累计天数奖励领取 param1=领取下标
}

TIME_LIMITED_OFFER_OPERA_TYPE = {
    TIME_LIMITED_OFFER_INFO = 0,
    TIME_LIMITED_OFFER_GET_REWARD = 1,
    TIME_LIMITED_OFFER_GET_EXTRA_REWARD = 2,
}

OA_TEQUAN_SHOP_OPERATE_TYPE = {
	OA_TEQUAN_SHOP_OPERATE_TYPE_MIN = 0,

	OA_TEQUAN_SHOP_OPERATE_TYPE_INFO = 1,			--请求信息
	OA_TQQUAN_SHOP_OPERATE_TYPE_FETCH_REWARD = 2,	--领取奖励
}

------------运营活动END--------------------------------------------

------------------------------合服活动 ----------------------
MERGE_EVENT_TYPE =
{
	NONE = 0, --这个活动不需要任何特殊条件
	LEVEL = 1, --等级
	ITEM = 2,--道具
}

--猎魔达人操作
PERSON_LIEMO_DAREN_OPERATE_TYPE = {
	INFO = 0,					--客户端请求信息
	FETCH_REWARD = 1,			--领取奖励 param_1: seq
	REQ_RANK = 2, 				--请求个人排行榜信息
}

ZHAOCAIMAO_OP_TYPE = {
    ZHAOCAIMAO_INFO = 0,
    ZHAOCAIMAO_OP_FETCH_LOGIN_REWARD = 1,
    ZHAOCAIMAO_OP_FETCH_CHONGZHI_TASK = 2, -- 任务充值金额
    ZHAOCAIMAO_OP_CHOUJIANG = 3,
    ZHAOCAIMAO_OP_RECORD = 4,
}
--合服预告客户端请求
ROLE_HEFU_YUGAO =
{
	ROLE_HEFU_YUGAO_DATA = 1,  		-- 获取合服预告信息
	ROLE_HEFU_YUGAO_FETCH = 2, 		-- 领取合服
}

MERGE_ACTIVITY_SHOUCHONG_TYPE = {
	TYPE_INFO = 0,	-- 活动信息
	TYPE_DRAW = 1,	-- 领取奖励
	TYPE_RECEIVE = 2,	-- 领取奖励2
}
MERGE_XIANSHILEICHONG_TYPE = {
	TYPE_INFO = 0, -- 0:获取当前信息
	TYPE_DRAW = 1, -- 1: 领取奖励 param = 档位
}
--合服每日首充
MERGE_ACTIVITY_FIRST_RECHARGE = {
	YLQ = 2, --已领取
	WDC = 0, --未达成
	KLQ = 1, --可领取
}

MERGE_JULINGZHUZHEN_OPERA_TYPE = {
	MERGE_JULINGZHUZHEN_OPERA_TYPE_INFO_REQ = 0,				-- 请求信息
	MERGE_JULINGZHUZHEN_OPERA_TYPE_PLANT = 1,					-- 种植
	MERGE_JULINGZHUZHEN_OPERA_TYPE_ZHULING = 2,					-- 注灵
	MERGE_JULINGZHUZHEN_OPERA_TYPE_FETCH = 3,					-- 领取
	MERGE_JULINGZHUZHEN_OPERA_TYPE_ZHULING_OTHER = 4,			-- 帮其他人注灵, param_0 = 花盆seq, param_1 = 帮浇对象UID
	MERGE_JULINGZHUZHEN_OPERA_TYPE_HELP_OTHER_INFO_REQ = 5,		-- 帮其他人注灵信息
	MERGE_JULINGZHUZHEN_OPERA_TYPE_INVITE_HELP_ME = 6,			-- 邀请朋友注灵，param_0 = 朋友UID, param_1 = 1好友，2世界，3仙盟.
	MERGE_JULINGZHUZHEN_OPERA_TYPE_OTHER_HELP_ME_INFO_REQ = 7,	-- 请求帮我注灵信息
	MERGE_JULINGZHUZHEN_OPERA_TYPE_FETCH_HELP_ME_REWARD = 8,	-- 请求领取帮浇奖励(别人帮我注灵), param_0 = 花盆seq, param_1 = 帮浇对象UID
	MERGE_JULINGZHUZHEN_OPERA_TYPE_ZHULING_LOG_REQ = 9,			-- 请求注灵日志
	MERGE_JULINGZHUZHEN_OPERA_TYPE_OTER_INFO_REQ = 10,  		-- 请求其他人的盆栽信息 param_0 = uid
	MERGE_JULINGZHUZHEN_OPERA_TYPE_CONFIRM_INVITE = 11, 	 	-- 确认其他人的邀请 param_0 = uid
}

--MERGE_JULINGZHUZHEN_OPERA_TYPE_INVITE_HELP_ME 邀请朋友注灵.
MERGE_JULINGZHUZHEN_OPERA_INVITE_TYPE = {
	FRIEND = 1,		--好友.
	WORLD = 2,		--世界.
	GUILD = 3,		--仙盟.
}

--合服鸿蒙悟道操作
CSA_EXPBUY_OP = {
	INFO = 0,		--请求活动信息
	FETCH = 1,		--购买经验
}

LINGBAO_STATUS = {
	LINGBAO_STATUS_NOTHING = 0,			-- 什么都没有
	LINGBAO_STATUS_GROWING = 1,			-- 成长期
	LINGBAO_STATUS_RIPE = 2,			-- 已成熟
	LINGBAO_STATUS_FETCH = 3,			-- 已领取
}

--合服烟花庆典操作
CSA_YANHUA_OP = {
	INFO = 0,		--请求活动信息
	BUY = 1,        --购买并抽奖
    --DRAW = 2,       --抽奖
    FETCH = 3,      --领取返利
    RECORD = 4,     --记录
    ANIM_FLAG = 5,  --动画标记
	BAODI_REWARD_INFO = 6,  --请求自选保底信息
	FIRST_ENTER = 7,		--第一次进入
}
LoginYouLiRewardState =
{
	BKL = 0,
	KLQ = 1,
	YLQ = 2,
}
MERGE_LOGIN_GIFT_OP_TYPE = {
	INFO = 1,				-- 活动信息
	COMMON_REWARD = 2,		-- 领取普通奖励
	SPECIAL_REWARD = 3,	-- 领取特殊奖励
}
MERGE_JULINGZHUZHEN_QUERY_TYPE = {
	MERGE_JULINGZHUZHEN_QUERY_TYPE_HELP_INFO = 0, 		-- 请求帮注灵信息
	MERGE_JULINGZHUZHEN_QUERY_TYPE_INVITE = 1, 			-- 邀请帮助注灵
	MERGE_JULINGZHUZHEN_QUERY_TYPE_INFO = 2, 			-- 请求灵宝信息
}

--合服登陆有礼
MERGE_LOGIN_REWARD_BTN_STATE = {
	WEIDA_TIANSHU = 1,	        --未达到天数
	HAS_NORMAL_REWARD = 2,		--普通奖励可领取
	HAS_SPECIAL_REWARD = 3,		--特俗奖励可领取
	HAS_ALL_REWARD = 4,
	VIP_REWARD = 5,		        --已领取普通奖励，vip奖励条件未达到
	YILINGQU = 6,		        --已领取所有奖励
}
--合服招财猫
CSA_ZHAOCAIMAO_OP_TYPE = {
    CSA_ZHAOCAIMAO_INFO = 0,
    CSA_ZHAOCAIMAO_OP_FETCH_LOGIN_REWARD = 1,
    CSA_ZHAOCAIMAO_OP_FETCH_CHONGZHI_TASK = 2, -- 任务充值金额
    CSA_ZHAOCAIMAO_OP_CHOUJIANG = 3,
    CSA_ZHAOCAIMAO_OP_RECORD = 4,
    CSA_ZHAOCAIMAO_OP_FETCH_CHOUJIANG = 5,		-- 领取抽奖奖励
}


MERGE_SPECITAL_RANK_OPERATE = {
	GOLD_RANK_INFO = 64, 		--请求仙玉排行榜信息
}
------------------------------合服活动end ----------------------


-------------------------------节日活动-----------------------
FESTIVAL_XIANSHILEICHONG_TYPE = {
	TYPE_INFO = 0, -- 0:获取当前信息
	TYPE_DRAW = 1, -- 1: 领取奖励 param = 档位
}

FESTIVAL_RANK_OPERATE = {
	GOLD_RANK_INFO = 65, 		--请求仙玉排行榜信息
}

--天才厨神
FA_TIANCAICHUSHEN_OPERA_TYPE = {
	FA_TIANCAICHUSHEN_OPERA_TYPE_1 = 1,			    -- 排行榜信息
	FA_TIANCAICHUSHEN_OPERA_TYPE_2 = 2,				-- 活动信息
	FA_TIANCAICHUSHEN_OPERA_TYPE_3 = 3,				-- 菜谱信息
	FA_TIANCAICHUSHEN_OPERA_TYPE_4 = 4,				-- 烹饪 param1~3 = item_id
	FA_TIANCAICHUSHEN_OPERA_TYPE_5 = 5,				-- 播放动画动画
	FA_TIANCAICHUSHEN_OPERA_TYPE_6 = 6,				-- 领取解锁全部菜谱奖励
	FA_TIANCAICHUSHEN_OPERA_TYPE_7 = 7,				-- 点击求助 param1 菜谱i
	FA_TIANCAICHUSHEN_OPERA_TYPE_8 = 8,				-- 点击提醒 param1 菜谱i

}
--营活动-天才厨神烹饪返回
FA_TIANCAICHUSHEN_COOK_TYPE = {
	FA_TIANCAICHUSHEN_COOK_TYPE_1 = 1,				    -- 失败
	FA_TIANCAICHUSHEN_COOK_TYPE_2 = 2,					-- 成功但没解锁新菜谱
	FA_TIANCAICHUSHEN_COOK_TYPE_3 = 3,					-- 成功并解锁新菜谱
	FA_TIANCAICHUSHEN_COOK_TYPE_4 = 4,					-- 中级菜谱
	FA_TIANCAICHUSHEN_COOK_TYPE_5 = 5,					-- 次数已满
}

-- 限时秒杀
FA_TIMED_SPIKE_OPERA_TYPE =
{
	FA_TIMED_SPIKE_OPERA_TYPE_INFO = 0,			-- 限时秒杀信息 param1:type
	FA_TIMED_SPIKE_OPERA_TYPE_BUY = 1,				-- 进行购买		param1:shop_id param2:id
	FA_TIMED_SPIKE_OPERA_TYPE_RECEIVE_GIFT = 2,		-- 领取额度奖励 param1:type param2:index
}

-------------------------------节日活动end-----------------------

------------任务链--------------------------------------------
TASK_CHAIN_ACTIVITY_TYPE = {
	OPERATION = 0,	--运营活动
	OPENSERVER = 1, --开服活动
}

OPERATION_TASK_CHAIN_ACT_STATUS = {
	STANDY = 0,										-- 任务链子任务备战中
	OPEN = 1, 										-- 任务链子任务进行中
}

OPERATION_TASK_CHAIN_TASK_STATUS = {
	STANDY = 0,
	FOLLOW = 1,
	OPEN = 2,
	CLOSE = 3,
	NO_START = 4,
}

--这个枚举与开服活动得任务连公用，改的时候慎重
OPERATION_TASK_CHAIN_FOLLOW_STATUS = {
	NO = 0,
	FOLLOW = 1,
}

FOLLOW_UI_UNDER_BAR_TYPE = {
	TIP = 0, 													-- 纯文本显示
	TIMER = 1, 													-- 定时器显示,定时器结束移除UI
	SLIDER = 2,													-- 进度显示
}

OPERATION_TASK_CHAIN_HUSONG_STATUS = {
	STANDY = 0, 									-- 任务准备状态
	MOVE = 1,										-- 护送的NPC移动中
	FIGHT = 2,										-- 有需要击杀的怪物
	SUCC = 3, 										-- 护送成功
}


OPERATION_TASK_CHAIN_SKILL_TYPE = {
	NONE = 0,
	GATHER = 1,
	SUBMISSION = 2,
	SKILL = 3,
	CHECK_GATHER = 4,
}

-- 0巡逻中 1追击 2返回
OPERATION_TASK_CHAIN_MONSTER_XUNLUO_TYPE = {
	NONE = -1,
	XUNLUO = 0,
	PURSUE = 1,
	BLACK = 2,
}

-- 状态0正常状态（默认状态），1无敌状态， 2被击中
OPERATION_TASK_CHAIN_SIXTEEN_CELL_ROLE_STATUS = {
	NORMAL = 0,
	INVINCIBLE = 1,
	HURT = 2,
}

-- 状态0预警 1等待
OPERATION_TASK_CHAIN_SIXTEEN_CELL_STATUS = {
	WARN = 0,
	WAIT = 1,
}

OPERATION_TASK_CHAIN_YUN_SONG_ADOID_STATUS = {
	NORMAL = 0,
	FAST = 1,
}

OPERATION_TASK_CHAIN_YUN_SONG_ADOID_RESULT = {
	NORMAL = 0,
	SUCC = 1,
}

OPERA_ACT_MOWANG_BTN_PARAM = {
	ACT = 0, 			--跳转到活动面板
	NormalView = 1, 	--普通界面
}

OPERATION_TASK_CHAIN_YUN_SONG_ADOID_JIGUAN_STATUS = {
	WARN = 0,
	WAIT = 1,
}

OPERATION_TASK_CHAIN_YUN_SONG_ADOID_GATHER_STATUS = {
	NONE = 0,
	SUCC = 1,
	RESET = 2,
}

OPERATION_TASK_CHAIN_TIP_TYPE = {
	CHANGE_IMG = 0,
	FIXED = 1,
}

OPERATION_TASK_CHAIN_WAVE_STATUS = {
	WARN = 0,
	WAIT = 1,
}

OPERATION_TASK_CHAIN_SCHEDULE_TIMER_TYPE = {
	SLIDER = 0,
	TIP = 1,
	LEFT_SLIDER = 2,
}

--主界面加载完回调的key
--之前设计的人就把key定死了是个活动号, 只有一个地方传了活动号进去
--保证这里定义的key不是活动号，不重复就好了
MainUIInitCallBackKey = {
	FanRenXiuZhenTask = "FanRenXiuZhenTask",
	BossAssistInfoView = "BossAssistInfoView",
	BossNormalHurtView = "BossNormalHurtView",
	FuBenExpInfoView = "FuBenExpInfoView",
	TaskChainInfoKey = "TaskChainInfoKey",
	ShenJiGuInfoKey = "ShenJiGuInfoKey",
}

FB_START_DOWN_END_TYPE = {
	FIGHT = 1,
	TASK = 2,
	ACT = 3,
	TASK_CLOSE = 4,
	FEISHENG = 5,
}

-- 停止采集的原因
STOP_GATHER_REASON = {
	NONE = 0, 						-- 未知原因
	ALREADY_GATHER = 1,				-- 被采完了
}

------------任务链END--------------------------------------------

----------天神八卦-------------
TIANSHEN_BAGUA_REQ ={
		TIANSHEN_BAGUA_REQ_SHOP_FRESH = 0,	-- 刷新
		TIANSHEN_BAGUA_REQ_SHOP_BUY = 1,	-- 购买 p0(index)
		TIANSHEN_BAGUA_REQ_PUT = 2,			-- 穿戴 p0(index)
		TIANSHEN_BAGUA_REQ_UP_STAR = 3,		-- 合成 p0(itemid) p1(是否在身上)
	}

----------天神八卦----------

--跨服仙盟战 请求类型
CROSS_XIANMENGZHAN_OPERA_TYPE = {
	--转去跨服处理的
	CROSS_XIANMENGZHAN_OPERA_TYPE_QUERY_MATCH_STATE = 1,	--请求比赛进度
	CROSS_XIANMENGZHAN_OPERA_TYPE_QUERY_GUILD_RANK = 2,		--请求仙盟排名

	-- -- 本服处理的
	CROSS_XIANMENGZHAN_OPERA_TYPE_REQ_ENTER_FB = 100,		--请求进入比赛副本
	CROSS_XIANMENGZHAN_OPERA_TYPE_FORWARD_REWARD = 101,		--领取仙盟战预告奖励
	CROSS_XIANMENGZHAN_OPERA_TYPE_REQ_KEEP_INFO = 102,		--请求连胜信息
}

OBJ_LINR_SPECIAL_KEY = {
	SELECT = "select_odj_line",
}


------------------------新 跨服1v1-----------------------
CROSS_1V1_OPERA_TYPE = {
	-- 转到跨服的请求
	CROSS_1V1_OPERA_TYPE_ENTER_PK_SCENE = 1,											-- 请求进入PK场景
	-- CROSS_1V1_OPERA_TYPE_FETCH_SCORE_REWARD = 2,										-- 领取积分奖励 param1是积分档位
	-- CROSS_1V1_OPERA_TYPE_FETCH_RANK_REWARD = 3,											-- 领取排名奖励 param1是排名档位
	CROSS_1V1_OPERA_TYPE_KNOCKOUT_GUESS_WINNER = 4,										-- 竞猜本轮胜利者 param1是下标索引标记
	CROSS_1V1_OPERA_TYPE_QUERY_SCORE_RANK = 5,											-- 请求排行榜
	CROSS_1V1_OPERA_TYPE_QUERY_KNOCKOUT_MATCH_INFO = 6,									-- 请求淘汰赛对阵表
	CROSS_1V1_OPERA_TYPE_QUERY_PERSON_INFO = 7,											-- 请求个人信息
	CROSS_1V1_OPERA_TYPE_QUERY_MATCH_INFO = 8,											-- 请求比赛信息
	CROSS_1V1_OPERA_TYPE_QUERY_WINNER_INFO = 9,											-- 请求冠军信息‘
	CROSS_1V1_OPERA_TYPE_FETCH_GUESS_REWARD = 10,										-- 领取竞猜奖励，param1是胜利者的 match_index, param2是round

	-- 本服请求
	CROSS_1V1_OPERA_TYPE_FETCH_SCORE_REWARD = 100,										-- 领取积分奖励 param1是积分档位
	CROSS_1V1_OPERA_TYPE_FETCH_RANK_REWARD = 101,										-- 领取排名奖励 param1是排名档位
	CROSS_1V1_OPERA_TYPE_FETCH_MATCH_REWARD = 102,										-- 领取场次奖励 param1是场次档位
}


ATTACK_USE_TYPE = {
	GUAJI = 1,
	RANG = 2,
}

OBJ_SPECIAL_STATE = {
	NONE = 0,
	STRIKE_FLY = 1,					-- 击飞
	ACCUMULATION = 2,				-- 蓄力
	COLLIDE = 3,					-- 冲撞
	TWINKLE = 4,					-- 技能的前置闪烁
	CHONG_FENG = 5,					-- 冲锋
	CRICLE_LIMIT = 6,				-- 限制移动范围
}

STRIKE_FLY_STATE = {
	UP = 1,
	DOWN = 2,
	KEEP = 3,
}

SKILL_BUFF_SHOW = {
	LAYER = 0,
	EFFECT = 1,
	SHAKE = 2,
	EFFECT_MY = 3,
	TIMER = 4,
	STAR = 5,
}

HIT_SHOW_TYPE = {
	REMOVE_BUFF = 1,
}

ATTACK_SKILL_TYPE = {
	NONE = 0,
	TWINKLE_BLACK = 1,			-- 闪烁技能返回
	ACCUMULATION_BACK = 2,		-- 蓄力技能返回
	BACKSTAB_BACK = 3,			-- 背刺技能返回
	BEFOTR_SKILL_CHARGE = 4,	-- 释放技能前冲锋
}

FRONT_SKILL_TYPE = {
	TWINKLE = 0,				-- 前置技能  闪烁
	ACCUMULATION = 1,			-- 前置技能  蓄力
	RELIEVE = 2,				-- 前置技能	 解控
	YUE_JI = 3,					-- 前置技能	 跃击
	BACKSTAB = 4,				-- 前置技能  背刺
	CHARGE = 5,					-- 前置技能  冲锋
}

BLACK_SKILL_TYPE = {
	SCREEN_EFF = 0,				-- 后置效果  屏幕特效
	PULL = 1,					-- 后置效果  拉人
	BACKSTAB_BACK = 2,			-- 后置效果  背刺
}

SCREEN_EFFECT_TARGET_TYPE = {
	SELF = 0,					-- 自己
	OTHER = 1,					-- 对方
	BOTH = 2,					-- 双方
}

SKILL_UI_EFFECT_TYPE = {
	SPLIT_SCREEN = 0,
	DRUNK = 1,
	STAIN = 2,
}

-- 一些技能使用的時候有特殊條件
USE_SKILL_LIMIT = {
	BUFF_LAYER = 1,				-- 技能使用限制 指定BUFF类型达到配置层数
	ACTIVE_USE_SHOW_CHANGE = 2,		-- 只能手动使用，并且打开技能切换列表
}

SCENE_MONSTER_TYPE = {
	MONSTER_TYPE_NORMAL = 0, 				-- 普通怪物
	MONSTER_TYPE_BOSS = 1,					-- BOSS怪物
	MONSTER_TYPE_NPC = 2,					-- npc怪物(不攻击，且不能被攻击)
	MONSTER_TYPE_TIANSHEN = 3,				-- 天神召唤物
	MONSTER_TYPE_CALL = 4,					-- 召唤物（可以被攻击, 强制把选中召唤者的人选中自己)
	MONSTER_TYPE_TASK_CALL = 5,				-- 召唤物（不可以攻击）
	MONSTER_TYPE_MAX = 6,
}

MONSTER_SUB_TYPE = {
	NORMAL = 0,								-- 普通
	TASK = 1,								-- 任务（可以被攻击）
	MAX = 1,
}

WUXING_HURT_TYPE = {
	WUXING_NORMAL = 0,
	WUXING_JIN = 1,
	WUXING_MU = 2,
	WUXING_SHUI = 3,
	WUXING_HUO = 4,
	WUXING_TU =5
}


EXPLODE_REASON = {
	EXPLODE_REASON_ZHUOSHAO = 0,				-- 灼烧
	EXPLODE_REASON_JUHUAPIG = 1,				-- 菊花猪
	EXPLODE_REASON_ROLE_BOMB_SELF = 2,			-- 自爆 对应仙盟战的自爆buff 客户端buff类型 12006
}

ClientBossType = {
	CommonBoss = 0,			--普通Boss
	TSBoss = 1,				--天神Boss
	WeaponAniTSBoss = 2,		--武器带动画的天神Boss
}

-- 属性变化原因
ATTR_NOTIFY_REASON = {
	ATTR_NOTIFY_REASON_NORMAL = 0,												-- 正常属性变动
	ATTR_NOTIFY_REASON_RESET_CHARACTER = 1,										--特殊切换 如上下镖车
	ATTR_NOTIFY_REASON_MOUNT = 2,												-- 上下坐骑
	ATTR_NOTIFY_REASON_LONGJUMP = 3,											-- 长跳
	ATTR_NOTIFY_REASON_EFFECT_BUFF_REEFFECT = 4,								-- 属性buff重新重新生效
	ATTR_NOTIFY_REASON_WITHOUT_NOTIFY = 5,										--不通知
	ATTR_NOTIFY_REASON_MONSTER_CHANGE_HP = 6,									-- 怪物动态血量变化
	ATTR_NOTIFY_REASON_SPECIALSPEED_CHANGE = 7,									-- 特殊速度变化
	ATTR_NOTIFY_REASON_ETERNAL_ATTR_CHANGE = 8,									-- 永夜之巅属性变化
	ATTR_NOTIFY_REASON_GM_PRINT = 9,											-- gm命令打印
	ATTR_NOTIFY_REASON_TIANSHEN_BAGUA_CHANGE = 10,								-- 天神八卦属性变化
	ATTR_NOTIFY_REASON_RECOVER_HP = 11,											-- 技能回血暴击
	ATTR_NOTIFY_REASON_RECOVER_HP_EQUIP_XIANJIE_SKILL = 12,						-- 装备仙戒技能回血
	ATTR_NOTIFY_REASON_QIHUN_WING_HUIXUE = 17,									-- 器魂羽翼回血
    ATTR_NOTIFY_REASON_QIHUN_JIANLING_HUIXUE = 18,								-- 器魂剑灵回血
    ATTR_NOTIFY_REASON_XIAN_JIE_EQUIP_TIANSHU_PART_ACT = 19,				    -- 仙界装备 - 天书碎片激活
}

BUFF_REMOVE_REASON = {
	EFFECT_REMOVE_TIMEOUT = 0,
	EFFECT_REMOVE_CLEAR = 1,
	EFFECT_REMOVE_REPLACE = 2,
	EFFECT_REMOVE_BURST = 3,		-- 星辰印记爆开
}

BUFF_FLUSH_REASON = {
	ADD = 1,
	REMOVE = 2,
	FLUSH = 3,
}

SCENE_EFFECT_TYPE = {
	SCENE_EFFECT_TYPE_INVALID = 0,
	SCENE_EFFECT_TYPE_SPECIAL_FAZHEN = 1,					-- 法阵
	SCENE_EFFECT_TYPE_HURT = 2,								-- 纯伤害
	SCENE_EFFECT_TYPE_TOWERDEFEND_FAZHEN = 3,				-- 塔防法阵
	SCENE_EFFECT_TYPE_SKILL_121 = 4,						-- 技能121法阵
	SCENE_EFFECT_TYPE_SKILL_421 = 5,						-- 技能421法阵
	SCENE_EFFECT_TYPE_CLIENT = 6,							-- 客户端技能特效，只供显示
	SCENE_EFFECT_TYPE_SHIELD = 7,							-- 范围护盾
}

--运势类型
FORTUNE_OPERATE_TYPE =
{
	FORTUNE_OPERATE_TYPE_MIN = 0,

	FORTUNE_OPERATE_TYPE_REQ_INFO = 1,					-- 基础信息
	FORTUNE_OPERATE_TYPE_REFRESH_TYPE = 2,				-- 刷新类型 param1:is_gold
}


-- 骑宠装备背包类型
MOUNT_PET_EQUIP_TYPE = {
	MOUNT = 0, 											-- 坐骑
	PET = 1, 											-- 宠物
	HUAKUN = 2, 										-- 化鲲
}

MOUNT_PET_EQUIP_OPERA_TYPE = {
    MOUNT_PET_EQUIP_OPER_TYPE_PUT_ON = 1,					-- 穿戴装备
	MOUNT_PET_EQUIP_OPER_TYPE_UP_STAR = 2,					-- 升星
	MOUNT_PET_EQUIP_OPER_TYPE_STRENGTHEN = 3,				-- 强化
    MOUNT_PET_EQUIP_OPER_TYPE_SEND_ALL_INFO = 4,	        -- 所有信息。
    MOUNT_PET_EQUIP_OPER_TYPE_REORDER_BAG = 5,				-- 背包排序
    MOUNT_PET_EQUIP_OPER_TYPE_ACTIVE_SUIT = 6,				-- 激活套装
    MOUNT_PET_EQUIP_OPER_TYPE_SUIT_INFO = 7,				-- 套装信息
    MOUNT_PET_EQUIP_OPER_TYPE_ONE_KEY_PUTON = 8,		    -- 一键穿戴
    MOUNT_PET_EQUIP_OPER_TYPE_ONE_KEY_STRENGTH = 9,		    -- 一键强化
}

--掉落极品展示tip类型
DROP_TIPS_SHOW_TYPE = {
	Wing 	= 1, 	--翅膀
	Fabao 	= 2, 	--法宝
	Kun 	= 3, 	--化鲲
	Eqiup 	= 4, 	--装备
}

--每日宝箱协议操作----
DAILY_TREASURE_OPERATE_TYPE =
{
	DAILY_TREASURE_OPERATE_TYPE_MIN = 0,

	DAILY_TREASURE_OPERATE_TYPE_REQ_INFO = 1,			-- 基础信息
	DAILY_TREASURE_OPERATE_TYPE_ALLY_ITEM_INFO = 2,		-- 盟友信息
	DAILY_TREASURE_OPERATE_TYPE_REQ_RECORD = 3,			-- 刷新记录
	DAILY_TREASURE_OPERATE_TYPE_RECEIVE = 4,			-- 领取宝箱
	DAILY_TREASURE_OPERATE_TYPE_HELP = 5,				-- 求助
	DAILY_TREASURE_OPERATE_TYPE_HELP_REFRESH = 6,		-- 刷新盟友宝箱 param1:target_uid
	DAILY_TREASURE_OPERATE_TYPE_ENTER_PASSWARD = 7,			-- 输入密码		param1:value
	DAILY_TREASURE_OPERATE_TYPE_OPEN_TREASURE = 8,			-- 开启宝箱
	DAILY_TREASURE_OPERATE_TYPE_HELP_REFRESH_BE_REQ_INFO = 9,	-- 帮助刷新请求信息
	DAILY_TREASURE_OPERATE_TYPE_HELP_REFRESH_REQ_INFO = 10,		-- 求助刷新请求信息
	DAILY_TREASURE_OPERATE_TYPE_HELP_REFRESH_REQ = 11,			-- 帮助刷新请求	param1:target_uid
	DAILY_TREASURE_OPERATE_TYPE_HELP_REFRESH_ACK = 12,			-- 帮助刷新回应 param1:target_uid param2:is_refuse
	DAILY_TREASURE_OPERATE_TYPE_HELP_REWARD_REQ = 13,			--  添加（感谢）请求
	DAILY_TREASURE_OPERATE_TYPE_HELP_REWARD_ACK = 14,			-- 添加进背包

}

RESET_SPECIAL_TYPE = {
	NORMAL = 1,
	SPEED = 2,
}

-- 跨服小鸭疾走操作类型
CROSS_RACING_OPERA = {
	CROSS_RACING_OPERA_QUERY_INFO = 1,						-- 请求信息（包括轮次信息、玩家信息、鸭子信息）
	CROSS_RACING_OPERA_FETCH_PLAY_COIN = 2,					-- 领取应援币
	CROSS_RACING_OPERA_INSPIRE_DUCK = 3,					-- 鼓舞鸭子（param1:鸭子index）
	CROSS_RACING_OPERA_DISTURB_DUCK = 4,					-- 干扰鸭子（param1:鸭子index）
	CROSS_RACING_OPERA_BET_ONE_DUCK = 5, 					-- 下注鸭子（param1:鸭子index）
	CROSS_RACING_OPERA_CHANGE_MOVE_SPEED = 6, 				-- 设置跟随速度（param1:设置的速度）
	CROSS_RACING_OPERA_ENTER_FROM_HOTSPRING = 7, 			-- 请求进入小鸭疾跑
	CROSS_RACING_OPERA_VOTE = 8, 							-- 投票 param1:duck_index
}

-- 操作鸭子返回
CROSS_RACING_DUCK_OPERA = {
	CROSS_RACING_DUCK_OPERA_INSPIRE = 1, 					-- 鼓舞
	CROSS_RACING_DUCK_OPERA_DISTURB = 2, 					-- 干扰
}

-- 跨服小鸭疾走轮次状态
DUCK_RACE_ROUND_STATE = {
	BET = 1, 												-- 下注阶段
	RACING = 2, 											-- 比赛中
	RESULT = 3,  											-- 结算阶段
}

-- 事件类型
CROSS_RACING_EVENT_TYPE =
{
	CROSS_RACING_EVENT_TYPE_RAND = 1,					-- 随机事件
	CROSS_RACING_EVENT_TYPE_SHORTCUT = 2,				-- 抄近路事件
}

TIAN_SHEN_SHEN_SHI_MODEL_EFFECT = {
	[1] = "UI_zhuanpanka_huitan_lv",
	[2] = "UI_zhuanpanka_huitan_lan",
	[3] = "UI_zhuanpanka_huitan_zi",
	[4] = "UI_zhuanpanka_huitan_cheng",
	[5] = "UI_zhuanpanka_huitan_hong",
	[6] = "UI_zhuanpanka_huitan_fen",
	[7] = "UI_zhuanpanka_huitan_jin",
	[8] = "UI_zhuanpanka_huitan_cai",
}

CROSS_ENTER_OTHER_GS_KEY = {
	CROSS_SPY = 1,											-- 跨服刺探
	DOOR = 3,												-- 走传送门
}

--灵玉请求操作类型
LINGYU_OPERA_TYPE = {
	LINGYU_INLAY = 0,				--灵玉镶嵌 装备部位part_index 灵玉空位slot_index, 背包索引bag_index
	LINGYU_UNINLAY = 1,				--灵玉摘下 part_index,slot_index
	LINGYU_LEVEL_UP = 2,			--灵玉升级 part_index,slot_index
	LINGYU_REFINE_UPLEVEL = 3,		--灵玉精炼升级part_index
	LINGYU_ALL_INFO = 4,			--灵玉当前信息
	LINGYU_ACTIVE_ALL_LEVEL = 5,	--灵玉激活总等级level body_seq
	LINGYU_AUTO_LEVEL_UP = 6,		--灵玉一键升级 part_index
}

--登录返回结果类型
LoginAckResult ={
	LOGIN_RESULT_SUC = 0,
	LOGIN_NO_THREAD = -1,
	LOGIN_SERVER_ERROR = -2,
	LOGIN_RESULT_EXIST = -3,
	LOGIN_SCENE_NOT_EXIST = -4,
	LOGIN_RESULT_NO_GATEWAY = -5,
	LOGIN_RESULT_NO_ROLE = -6,
	LOGIN_THREAD_BUSY = -7,
	LOGIN_LOGIN_FORBID = -8,   -- 角色被封号
	LOGIN_ANTI_WALLOW = -9,
}

ROLEACTION_OPERA_TYPE = {
	ACTIVE = 0,													-- 激活
	UPGRADE = 1,												-- 升级
	PERFORM = 2,												-- 播放
}

JINGJIE_MAT_LEVEL = {
	[0] = 0,
	[32287] = 1,
	[32288] = 2,
	[32289] = 3,
	[99999] = 4,--满级
}


ROLE_GATHER_TYPE = {
	GATHER = 1,
	NPC = 2,
}


CLIENT_FLY_REASON = {
	NONE = 1,
	CORSS_SPY_DEFENSE = 2,
}

-----神机  暗器--------------
HW_CONST_PARAM = {
	HW_ONCE_LAYER_STAR_NUM = 6,
	ACTIVE_SKILL_TYPE = 1,
	PASSIVE_SKILL_TYPE = 2,
	SPECIAL_SKILL_TYPE = 3,
	AWAKEN_SKILL_TYPE = 4,
	EXP_ICON_IDS = {36972, 36974},

	--注灵进度调参数
	ZLProgressMinV = 15,  --开始值
	ZLProgressMaxV = 500,	--最大值
	ZLProgressAddV = 15,	--增加量
	AddSpeedTime = 1,		--间隔秒


	OutUIModelOffY = 2.5,		--外部UI模型Y偏移值
}

-----神机  暗器 END--------------

--神机预告协议操作类型
SHEN_JI_YU_GAO_OP_TYPE = {
	INFO = 0,                      -- 请求信息
	GET_TASK_REWARD = 1,           -- 领任务奖励
	GET_SPECIAL_SALE = 2,          -- 领充值特卖
}

SHEN_JI_YU_GAO_TASK_STATUS = {
	NOT_COMPLETE = 0,       -- 任务未完成
	COMPLETE = 1,           -- 已完成未领取奖励
	HAS_GET_REWARD = 2      -- 已领奖
}

SHEN_JI_YU_GAO_SPECIAL_SALE_STATUS = {
	CLOSE = 0,    -- 特卖未开启
	OPEN = 1,     -- 特卖进行中
	END = 2       -- 特卖结束
}

SHEN_JI_YU_GAO_SPECIAL_SALE_ID_STATUS = {
	NOT_BUY = 0,  -- 未购买
	NOT_GET = 1,  -- 已购买未领奖
	HAS_GET = 2,  -- 已领取
}


--仙器真炼(F1神机百炼)操作类型
MACHINE_OP_TYPE = {
	INFO = 0,		--信息
	DRAW = 1,		--抽奖
	SHOP_CONVERT = 2, --商店兑换 param1 商品索引
	TE_DIAN = 3,          -- 请求特典操作
}

XIANQI_TEDIAN_OP_TYPE = {
	REQ_INFO = 0,                -- 请求特典信息
	GET_TEDIAN_RMB_SHOPITEM = 1,      -- 领取特典子活动rmb购买的商品
	REQ_BUY_TEDIAN_SHOPITEM = 2,         -- 请求购买特典子活动售卖商品（非rmb类型商品）
}

--仙器真炼(F1神机百炼)抽奖类型
MACHINE_DRAW_TYPE = {
	SINGLE = 0,		--单抽
	MULTI = 1,		--n抽
	USE_MULTI_ITEM = 2,   --特殊道具抽
}

--仙器真炼(F1神机百炼)抽奖结果类型
MACHINE_REWARD_TYPE = {
	FIRST_SINGLE = 0,	--首次单抽
	FIRST_N = 1,		--首次n抽
	NO_FIRST = 2,		--普通抽奖
	USE_ITEM = 3,		--特殊道具抽
}

XianQiTeDianSubActId = {
    ProbabilityUp = 1,          --概率提升
    ShiLian1 = 2,               --1折十连
    ShiLian2 = 3,               --2折十连
    ZhenXuan1 = 4,              --元神珍选
    ZhenXuan2 = 5,              --元神珍选
    TeHuiZhaoHuan = 6,          --特惠召唤
}

-- 首充、再充、豪充协议数组长度
MAX_C_Z_SIZE = 4

-- 首充、再充、豪充最大天数
MAX_C_Z_DAY = 3

FUNOPEN_UNOPEN_REASON = {
	OPEN_DAY = 1,
}

ACT_ZHICHONG_OPER_TYPE = {
	ACT_ZHICHONG_OPER_TYPE_FETCH_REWARD = 1,					-- 领取单日奖励
	ACT_ZHICHONG_OPER_TYPE_ONE_KEY_FETCH_ALL_REWARD = 2,		-- 领取所有奖励
	ACT_ZHICHONG_OPER_TYPE_SEND_INFO = 3,						-- 发送信息
}

-- 跟踪角色类型
TRACK_ROLE_TYPE = {
	TEAM = 1,			-- 组队跟随
}

CLIENT_MOVE_REASON = {
	NONE = 1,
	FOLLOW = 2,
	GUAJI = 3,
}

CLIENT_MOVE_REQ_PARAM = {
	NORMAL = 1,
	GUAJI_RECOVERY = 2,       	-- 跟随挂机中，跟随者从视野里消失，重新尝试跟随
}

CARTOON_OPERATE_TYPE = {
	CARTOON_GET_REARD = 1,
}

---------------臻品直购----------------------
PIERRE_DIRECT_PURCHASE_OP_TYPE = {
	GET_DAILY = 1,					-- 领取每日
	GET_ALL = 2,					-- 所有信息
}

---------------跨服组队----------------------
CROSS_TEAM_OPERATE_TYPE = {
    CROSS_TEAM_OPERATE_TYPE_TEAM_LIST = 1,						-- 队伍列表
    CROSS_TEAM_OPERATE_TYPE_DISMISS= 2,						    -- 解散队伍
    CROSS_TEAM_OPERATE_TYPE_KICK_OUT= 3,						-- 踢出队友		param1:member_index
    CROSS_TEAM_OPERATE_TYPE_CHANGE_LEADER= 4,					-- 变更队长		param1:member_index
    CROSS_TEAM_OPERATE_TYPE_CHANGE_MUST_CHECK= 5,				-- 入队验证变更 param1:is_check
    CROSS_TEAM_OPERATE_TYPE_CHANGE_MEMBER_CAN_INVITE= 6,		-- 队员邀请变更 param1:can_invite
    CROSS_TEAM_OPERATE_TYPE_EXIT= 7,							-- 退出队伍
    CROSS_TEAM_OPERATE_TYPE_JOIN= 8,							-- 加入队伍		param:team_index
    CROSS_TEAM_OPERATE_TYPE_TEAM_INFO = 9,                      -- 队伍信息
    CROSS_TEAM_OPERATE_TYPE_TEAM_INVITE_CHANNEL_CHAT = 10,      --10 组队喊话
    CROSS_TEAM_OPERATE_TYPE_NEAR_ROLE = 11,                     -- 请求附近的人
    CROSS_TEAM_OPERATE_TYPE_REQ_LEADER = 12,                    -- 申请队长
    CROSS_TEAM_OPERATE_TYPE_INVITE_SHOUTING = 13,               -- 组队喊话
}

TEAM_OUT_REASON = {
    TEAM_OUT_REASONTEAM_OUT_REASON_DISMISS = 0,	--0 队伍解散
    TEAM_OUT_REASON_KICK_OFF = 1,		-- 被踢出队伍
    TEAM_OUT_REASON_SECEDE = 2,			-- 自己退出
    TEAM_OUT_REASON_TEAM_MERGE = 3,		-- 队伍合并
}

TEAM_JOIN_REASON = {
    TEAM_JOIN_REASON_NoReason = 1,
    TEAM_JOIN_REASON_CrossInviteAutoAddMember = 2,			-- 邀请跨服自动组队
    TEAM_JOIN_REASON_TeamMergeMember = 3,					-- 队伍合并
}

--本次登录阻断类型
CROSS_TEAM_LOGIN_NO_LONGER_TYPE = {
	CROSS_TEAM_LOGIN_NO_LONGER_TYPE_INVALID = 0,
	CROSS_TEAM_LOGIN_NO_LONGER_TYPE_INVITE_ME_TEAM = 1,        --不再接受他组队邀请
	CROSS_TEAM_LOGIN_NO_LONGER_TYPE_JOIN_MY_TEAM = 2,          --不再同意他进队申请
	CROSS_TEAM_LOGIN_NO_LONGER_TYPE_ACCEPT_MERGE = 3,          --不再接受他队伍合并
}
------------新仇人枚举-----------------------
CS_CROSS_FRIEND_REQ_TYPE = {
	CS_CROSS_FRIEND_REQ_ADD_ENEMY = 0,		-- 增加仇人 p1(uid) p2(plat_type)
	CS_CROSS_FRIEND_REQ_REMOVE_ENEMY = 1,	-- 移除仇人 p1(uid) p2(plat_type)
	CS_CROSS_FRIEND_REQ_ENEMY_INFO = 2,		-- 仇人列表
	CS_CROSS_FRIEND_REQ_REMOVE_KILL_RECORD = 3, -- 移除击杀 p1(uid) p2(plat_type) p3(index)
	CS_CROSS_FRIEND_REQ_REMOVE_ALL_KILL_RECORD = 4, -- 移除所有击杀
}

CS_CROSS_FRIEND_CHANGE_TYPE = {
	CHANGE_TYPE_INFO = 0,		-- 信息改变
	CHANGE_TYPE_ADD = 1,		-- 增加
	CHANGE_TYPE_REMOVE = 2,		-- 移除
	CHANGE_TYPE_DEL = 3,		-- 移除2-服务端主动
}

-- 寻宝boss操作类型
TREASURE_BOSS_REQ_TYPE =
{
    REQ_TYPE_INFO = 0,                -- 请求信息
    REQ_TYPE_ENTER = 1,                   --进入场景,param_0 为场景seq
}

-- 天神3v3操作
TianShen3V3OperaType = {
	StartMatch = 1,												-- 开始匹配
	CancelMatch = 2,											-- 取消匹配
}

-- 天神3v3操作类型2
TianShen3V3OperaType2 = {
	FetchMatchReward = 1, 										-- 领取参与奖励(param1:seq)
	SelectTianShen = 2,											-- 选取天神(param1:tianshen_index)
	GetSelfInfo = 3,
	RequestSeasonRank = 4, 										-- 赛季排名信息
}

-- 点位占领状态
TianShen3v3PointState = {
	Unoccupy = 1, 												-- 无人占领
	Occupying = 2, 												-- 抢夺中
	Occupyed = 3, 												-- 已占领
}

---------------仙盟战 请求类型--------------
GUILD_BATTLE_OP_REQ_TYPE =
	{
		GUILD_BATTLE_REWARD_REQ_TYPE_COMMIT_LINGSHI = 0,					-- 提交灵石
		GUILD_BATTLE_REWARD_REQ_TYPE_COMMIT_BIANSHEN = 1,					-- 变身
		GUILD_BATTLE_REWARD_REQ_TYPE_COMMIT_CANCEL= 2,						-- 取消变身
	}

PHONE_REQUEST_TYPE = {
	IS_BIND = "is_bind",								-- 请求是否绑定
	GET_CODE = "get_code",								-- 请求获取验证码
	BIND = "bind",										-- 请求开始绑定手机号码
}

SHAN_HAI_BOSS_ART_EFFECT_STEP = {
	QUESTION = 1,
	ANSWER = 2,
	UPDATE = 3,
	WAIT = 4,
}

SHAN_HAI_BOSS_ART_EFFECT_TYPE = {
	SCENE_EFFECT_RED = 1,
	SCENE_EFFECT_YELLOW = 2,
	SCENE_EFFECT_BLUE = 3,
    SCENE_EFFECT_ANSWER = 4,
    SCENE_EFFECT_RED_PERSON = 5, --有人站在上面
	SCENE_EFFECT_YELLOW_PERSON = 6, --有人站在上面
	SCENE_EFFECT_BLUE_PERSON = 7, --有人站在上面
}

MONSTER_AI_ART_QUESTION_STATUS = {
	MONSTER_AI_ART_QUESTION_STATUS_MIN = 0,
	MONSTER_AI_ART_QUESTION_STATUS_WAIT = 1,
	MONSTER_AI_ART_QUESTION_STATUS_ANSWER = 2,
	MONSTER_AI_ART_QUESTION_STATUS_SUMMARY = 3,
	MONSTER_AI_ART_QUESTION_STATUS_MAX = 4,
}

NEW_CS_RAXIANLINGZHEN_OPERA_TYPE = {			--新灵犀六芒图操作类型
	CS_RA_XIANLINGZHEN_OPERA_TYPE_DRAW = 1, --转
	CS_RA_XIANLINGZHEN_OPERA_TYPE_GET_TASK_REWARD = 2, --领取任务奖励
	CS_RA_XIANLINGZHEN_OPERA_TYPE_ADD_REWARD_COUNT = 3, --补货
	CS_RA_XIANLINGZHEN_OPERA_TYPE_ALL_INFO = 4, --所有信息
	CS_RA_XIANLINGZHEN_OPERA_TYPE_RECORD = 5, --大奖记录
	NEW_LINGXI_OPER_TYPE_TASK_INFO = 6,	--请求任务信息
	FETCH_SERVER_DRAW_TIMES_REWARD = 7, -- 领取全服抽奖次数奖励 p1:奖励索引
	SERVER_DRAW_TIMES_REWARD_INFO  = 8, -- 全服抽奖次数奖励信息 
	FETCH_DAILY_REWARD_INFO = 9,        -- 领取每日奖励
}


-- =========================  跨服仙玉转盘活动  ===================================================================
CS_CROSS_CHANNEL_ACTIVITY_GOLD_ZHUANPAN_OP_TYPE = {
	CS_CROSS_CHANNEL_ACTIVITY_GOLD_ZHUANPAN_OP_TYPE_INFO = 0,				-- 请求个人购买信息
	CS_CROSS_CHANNEL_ACTIVITY_GOLD_ZHUANPAN_OP_TYPE_BUY = 1,				-- 请求购买 p1(抽奖模式)
}

-- =========================  跨服仙玉转盘活动 end ===================================================================

--仙界boss
CROSS_FAIRYLAND_BOSS_OPERATE_TYPE =
{
    CROSS_FAIRYLAND_BOSS_OPERATE_TYPE_MIN = 0,
    CROSS_FAIRYLAND_BOSS_OPERATE_TYPE_BOSS_INFO = 1,			--基础信息		param1:layer
    CROSS_FAIRYLAND_BOSS_OPERATE_TYPE_DROP_HISTORY = 2,			--掉落信息
    CROSS_FAIRYLAND_BOSS_OPERATE_TYPE_BOSS_CONCERN = 3,			--BOSS关注		param1:layer param2:boss_id param3:is_concern
    CROSS_FAIRYLAND_BOSS_OPERATE_TYPE_ENTER_SCENE = 4,			--进入场景
    CROSS_FAIRYLAND_BOSS_OPERATE_TYPE_BASE_INFO = 5,			--基础信息 开启时间
};


-------------------boss协助 新-------------------

MONSTER_ASSIST_REQ =
{
	MONSTER_ASSIST_REQ_INVODE = 0,				-- 发起召集 p0(assist_type) p1(monster_id) p2(monster_key)
	MONSTER_ASSIST_REQ_REPLY_HELP = 1,			-- 回应协助 p0(origin_uid)
	MONSTER_ASSIST_REQ_CANCEL_INVOKE = 2,		-- 取消召集
	MONSTER_ASSIST_REQ_CANCEL_REPLY_HELP = 3,	-- 取消协助
}

XIEZHU_REASON_TYPE =
{
	REASON_TYPE_ADD = 0,				-- 增加召集信息 p0(uid)
	REASON_TYPE_REMOVE_ROLE = 1,		-- 移除召集信息 p0(uid)
	REASON_TYPE_REMOVE_MONSTER = 2,		-- 移除BOSS信息
	REASON_TYPE_DISCONNECT = 3,			-- 协助信息全部断开
	REASON_TYPE_CHANGE = 4,				-- 召集变更
}

-- 协助状态
ASSIST_STATUS =
{
	ASSIST_STATUS_NOTHING = 0,				-- 正常状态
	ASSIST_STATUS_INVOKE = 1,				-- 状态召集
	ASSIST_STATUS_HELP_OTHER = 2,			-- 协助状态
}

-- 协助BOSS类型
ASSIST_BOSS_TYPE =
{
	ASSIST_BOSS_TYPE_CROSS_BOSS = 0,		-- 跨服BOSS  蛮荒神兽
}

--协助者取消协助状态
CANCEL_STATUS_REASON = {
	CANCEL_STATUS_REASON_NOMAL = 0,				--正常
	CANCEL_STATUS_REASON_BOSS_DIE = 1,			--boss死亡
	CANCEL_STATUS_REASON_SELF = 2,				--主动取消
	CANCEL_STATUS_REASON_ATTACK = 3,			--攻击其他boss
	CANCEL_STATUS_REASON_INVOKE = 4,			--召集者取消
	CANCEL_STATUS_REASON_TIMESTAMP = 5,			-- 攻击BOSS超时
	CANCEL_STATUS_REASON_LEAVE_FB= 6			-- 离开副本
}

-------------------boss协助 新 End-------------------

------------------印记转盘 star----------------------
OP_YIN_JI_ZHUAN_PAN_TYPE = {
		OP_YIN_JI_ZHUAN_PAN_TYPE_INFO = 0,				-- 请求印记转盘信息
		OP_YIN_JI_ZHUAN_PAN_TYPE_DRAW = 1,				-- 抽一次
		OP_YIN_JI_ZHUAN_PAN_TYPE_DRAW_MULTI = 2,		-- 抽十次  param11 1:仙玉 0：道具
		OP_YIN_JI_ZHUAN_PAN_TYPE_GET_LEICHOU_REWARD = 3,-- 获取累抽奖励
		OP_YIN_JI_ZHUAN_PAN_TYPE_GET_DAILY_REWARD = 4,	-- 获取每日充值奖励
		OP_YIN_JI_ZHUAN_PAN_TYPE_GET_LOGIN_REWARD = 5,	-- 获取每日登录奖励
}

MAX_YIN_JI_ZHUAN_PAN_REWARD_ID = 500          -- 最大奖励id
MAX_YIN_JI_ZHUAN_PAN_RECORD_REWARD_NUM = 100  --最大抽奖记录条数
MAX_YIN_JI_ZHUAN_PAN_RECHARGE_ID = 100		-- 最大充值id
----------------印记转盘 end-------------------------

ADD_FRIEND_REQ_TYPE = {
	COMMON = 1,			--普通方式
	LIAOYILIAO = 2,		--通过情缘撩一撩添加
}

------------------灵智技能（器魂系统）-----------------
-- 灵智技能类型
LINGZHI_SKILL_TYPE = {
	WING = 0,
	FABAO = 1,
	JIANZHEN = 2,
	SHENBING = 3,
}

-- 灵智技能下发原因
 LINGZHI_SKILL_REASON_TYPE = {
	DEF = 0,										    -- 下发原因- 默认
	RESET = 1,										-- 下发原因- 重置
	SEND_REASON_FETCH_XIULIAN_CHENGJIU = 2,	        -- 领取修炼成就
	SEND_REASON_FETCH_WAIGUAN_CHENGJIU = 3,	        -- 领取外观成就
	SEND_REASON_FUWEN_UPLEVEL = 4,	        --  符文升级
	SEND_REASON_QIHUN_FETCH = 5,			-- 领取器魂
}

-- 灵智虚拟道具
 LINGZHI_SKILL_XUNI_ITEM = {
	[LINGZHI_SKILL_TYPE.WING] = 91237,
	[LINGZHI_SKILL_TYPE.FABAO] = 91238,
	[LINGZHI_SKILL_TYPE.JIANZHEN] = 91240,
	[LINGZHI_SKILL_TYPE.SHENBING] = 91239,
}


-- 请求类型
LINGZHI_SKILL_OPERA_TYPE = {
	LINGZHI_SKILL_OPERA_TYPE_ACTIVE = 1,				-- 器魂开启 param1 器魂类型
	LINGZHI_SKILL_XIULIAN_UPLEVEL = 2,					-- 修炼升级 param1 器魂类型
	LINGZHI_SKILL_FUWEN_ACTIVE = 3,						-- 符文激活 param1 器魂类型 param2索引
	LINGZHI_SKILL_FUWEN_UPLEVEL = 4,					-- 符文升级 param1 器魂类型 param2索引
	LINGZHI_SKILL_FETCH_GRADE_CHENGJIU = 5,				-- 领取等级成就奖励 param1 器魂类型 param2索引
	LINGZHI_SKILL_FETCH_APPERANCE_CHENGJIU = 6,			-- 领取外观激活成就 param1 器魂类型 param2索引
	LINGZHI_SKILL_OPERA_TYPE_RESET_SKILL = 7,			-- 器魂符文重置 param1 器魂类型
}


------------------灵智技能（器魂系统）End-----------------
-- 天下第一比赛类型
WORLDS_NO1_MATCH_TYPE = {
	AUDITION = 1, 											-- 海选
	KNOCKOUT = 2, 											-- 淘汰
}

-- 天下第一请求类型
TIANXIADIYI_PERSON_OPER_TYPE = {
	TIANXIADIYI_PERSON_OPER_TYPE_1 = 0,						-- 请求排行信息   	param1是查看的排行榜下标
	TIANXIADIYI_PERSON_OPER_TYPE_2 = 1, 					-- 竞猜  			param2是竞猜目标的uuid
	TIANXIADIYI_PERSON_OPER_TYPE_3 = 2, 					-- 请求观战  		param2是观战目标的uuid
	TIANXIADIYI_PERSON_OPER_TYPE_4 = 3, 					-- 请求获得上届冠军信息
	TIANXIADIYI_PERSON_OPER_TYPE_5 = 4, 					-- 请求点赞
	TIANXIADIYI_PERSON_OPER_TYPE_6 = 5, 					-- 请求获取点赞信息
	TIANXIADIYI_PERSON_OPER_TYPE_7 = 6, 					-- 请求获取轮次信息
}

-- 天下第一排行类型
WORLDS_RANK_INFO_TYPE = {
	WORLDS_RANK_INFO_TYPE_0 = 0, 							-- 海选排名
	WORLDS_RANK_INFO_TYPE_1 = 1, 							-- 淘汰赛第一轮排名
	WORLDS_RANK_INFO_TYPE_2 = 2, 							-- 淘汰赛第二轮排名
	WORLDS_RANK_INFO_TYPE_3 = 3, 							-- 淘汰赛第三轮排名
}

WORLDS_RANK_INFO_TYPE_TO_MATCH_TYPE = {
	[WORLDS_RANK_INFO_TYPE.WORLDS_RANK_INFO_TYPE_0] = WORLDS_NO1_MATCH_TYPE.AUDITION,
	[WORLDS_RANK_INFO_TYPE.WORLDS_RANK_INFO_TYPE_1] = WORLDS_NO1_MATCH_TYPE.KNOCKOUT,
	[WORLDS_RANK_INFO_TYPE.WORLDS_RANK_INFO_TYPE_2] = WORLDS_NO1_MATCH_TYPE.KNOCKOUT,
	[WORLDS_RANK_INFO_TYPE.WORLDS_RANK_INFO_TYPE_3] = WORLDS_NO1_MATCH_TYPE.KNOCKOUT,
}

WORLDS_RANK_INFO_TYPE_TO_KNOCKOUT_SUBROUND = {
	[WORLDS_RANK_INFO_TYPE.WORLDS_RANK_INFO_TYPE_1] = 1,
	[WORLDS_RANK_INFO_TYPE.WORLDS_RANK_INFO_TYPE_2] = 2,
	[WORLDS_RANK_INFO_TYPE.WORLDS_RANK_INFO_TYPE_3] = 3,
}

KNOCKOUT_SUBROUND_TO_WORLDS_RANK_INFO_TYPE = {
	[1] = WORLDS_RANK_INFO_TYPE.WORLDS_RANK_INFO_TYPE_1,
	[2] = WORLDS_RANK_INFO_TYPE.WORLDS_RANK_INFO_TYPE_2,
	[3] = WORLDS_RANK_INFO_TYPE.WORLDS_RANK_INFO_TYPE_3,
}

WORLDS_NO1_ROUND_STATUS = {
	UNOPENED = 1, 				-- 未开启
	OPENNING = 2, 				-- 开启中
	OVER = 3, 					-- 已结束
}

WORLDS_NO1_SUBROUND_STATUS = {
	UNOPENED = 1, 				-- 未开启
	OPENNING = 2, 				-- 开启中
	OVER = 3, 					-- 已结束
}

-- 子轮阶段
WORLDS_NO1_SUBROUND_STAGE = {
	PREPARE = 1, 				-- 准备阶段
	PK = 2, 					-- 战斗阶段
}

-- 积分增加类型
WORLDS_NO1_SCORE_ADD_TYPE =
{
	AddType_1 = 1,			-- 击杀积分
	AddType_2 = 2,			-- 存活积分
	AddType_3 = 3,			-- 拾取积分
};

LOAD_ASSET_TYPE = {
    TypeUnityTexture = 1,
    TypeUnitySprite = 2,
    TypeUnityMaterial= 3,
    TypeUnityPrefab = 4,
    TypeAudioMixer = 5,
    TypeActorQingGongObject = 6,
    TypeQualityConfig = 7,
    TypeTextAsset = 8,
    TypeShaderVariant = 9,
    TypeRuntimeAnimatorController = 10,
    TypeUnityAudioClip = 11,
    TypeYYPostProcessProfile = 12,
}

FuHuoType = {
    None = 0,
	Common = 1,
	Here = 2,
}

SHENG_ZHUANG_BROWSE = {
	MAX_XIANJIE_EQUIP_RAND_ATTR = 16,						-- 仙界圣装的进阶属性
	MAX_XIANJIE_EQUIP_SHENZHUANG_PART = 16,					-- 仙界圣装部位
	MAX_XIANJIE_EQUIP_SLOT_COUNT = 16,						-- 仙界装备神体数量

}

FESTIVA_LIANCHONG = {
	RA_LIANCHONG_DAY_COUNT = 8, --最大天数
	CS_RA_LIANCHONG_OPERA_FETCH_DAY = 0,			       -- 每日奖励 seq
	CS_RA_LIANCHONG_OPERA_FETCH_TOTAL_DAY = 1,		-- 达标奖励 seq
}
SKILL_SPECIAL_SHOW = {
	WUXING_ICON = 1,
	TIP_IGNORE_WUXING = 2,			-- 技能描述不显示五行切换
	WUXING_ICON_AND_TIP = 3,		-- 主界面技能图标根据五行变化并且技能描述不显示五行切换
}

-- 主界面变强，下发按钮id
BIANQIANG_BOTTOM_ID = {
	FIRST_RECHARGE = 1, 				-- 首充
	EVERYDAY_RECHARGE_LIANCHONG = 2, 	-- 连续充值
	ZERO_BUY_VIP = 3, 					-- 零元购V6
	MONTH_CARD = 4, 					-- 月卡
	RECHARGE_ZHIGOU = 5, 				-- 18元直购
	XIANQI_TEDIAN = 6, 					-- 仙器直购
	LINGZHI_WING = 7,					-- 羽翼器魂
	LINGZHI_FABAO = 8, 					-- 法宝器魂
	LINGZHI_SHENBING = 9, 				-- 神兵器魂
	LINGZHI_JIANZHEN = 10, 				-- 剑阵器魂
	SIXIANG_EXP_POOL = 11, 				-- 四象升级特权
}

--神兽类型
SHENSHOU_QUALITY_TYPE = {
	ALL_QUALITY = 0,                   --全部类型
}

--龙脉商店
LONGMAI_OPERA_TYPE = {
	SHOP_INFO = 1,                      -- 请求龙脉商店信息
	BUY_REWARD = 2,                     -- 购买物品
	FLUSH_SHOP = 3,                     -- 刷新商店
}

--炼丹类型
ALCHEMY_OPERATE_TYPE = {
	INFO = 1,					-- 信息
	PELLET_UP = 2,				--丹药升级
	DANZAO_UNLOCK = 3,			--丹灶解锁
	FURNACE_UP = 4,				--炼丹炉升级
	NORMAL_ARRAY_UP = 5,		--法阵升级
	COMPOS = 6,					--丹药合成
	COMPOS_FINISIH = 7,			--丹药收获
	COMPOS_EXPEDITE = 8,		--炼丹加速 param1:danzao_seq param2:item_id
}

-- 星图秘境类行
SECRET_AREA_TYPE = {
	XingTuMiJing_GetInfo = 0,           --获取基本信息
	XingTuMiJing_EnterScene = 1,		--进入场景
	XingTuMiJing_LevenScene = 2,		--离开场景
	XingTuMiJing_GetTask = 3,           --获取场景信息
}

-- 幸运大礼包请求类型
GIFT_OPERATE_TYPE = {
	CROSS_LUCKY_GIFT_OPERATE_TYPE_INFO = 1,						-- 幸运值信息
	CROSS_LUCKY_GIFT_OPERATE_TYPE_BASE_INFO = 2,				-- 基础信息/活动
	CROSS_LUCKY_GIFT_OPERATE_TYPE_BUY = 3,						-- 购买	  param1:seq param2:mode
	CROSS_LUCKY_GIFT_OPERATE_TYPE_RECEIVE_DAILY_REWARD = 4,		-- 每日奖励领取
	CROSS_LUCKY_GIFT_OPERATE_TYPE_RANK_INFO = 5,				-- 幸运值排名信息
	CROSS_LUCKY_GIFT_OPERATE_TYPE_LUCKY_GRADE_REWARD = 6,		-- 领取档次奖励	param1:seq
	CROSS_LUCKY_GIFT_OPERATE_TYPE_LUCKY_DOUBLE_GRADE = 7,		-- 幸运值加倍
}

-- 幸运大礼包购买模式
GIFT_PRCHASE_OPERATE_TYPE = {
	PRCHASE_MODE_ONE   = 1,
	PRCHASE_MODE_TWO   = 2,
	PRCHASE_MODE_THREE = 3,
}

--跨服鲜花榜请求类型
CROSS_FLOWER_RANK_SEX_TYPE = {
	WOMAN = 0,					--女神榜
	MAN = 1,					--男神榜
}

OA_CAT_VENTURE_OPERTE_TYPE = {
	INFO = 1,                 	-- 活动信息
	STEP = 2,                 	-- 步进
	STEP_REWARD = 3,          	-- 步进奖励 param1:step
	TASK_REWARD = 4,          	-- 任务奖励 param1:task
}

OA_FLOP_DRAW_OPERTE_TYPE = {
	INFO = 1,                 	-- 活动信息
	STEP = 2,                 	-- 步进 param1:AUTO (0:正常 1 一键)
	STEP_REWARD = 3,          	-- 步进奖励 param1:step
	TASK_REWARD = 4,          	-- 任务奖励 param1:task
	STEP_ALL_REWARD = 5,        -- 领取所有奖励
}
-- 五行操作类型
FIVE_ELEMENTS_OPERATE_TYPE = {
	FIVE_ELEMENTS_OPERATE_TYPE_INFO = 1,							-- 信息
	FIVE_ELEMENTS_OPERATE_TYPE_PART_LEVEL_UP = 2,					-- 部位升级		param1:part
	FIVE_ELEMENTS_OPERATE_TYPE_HOLE_LEVEL_UP = 3,					-- 孔位升级		param1:part			param2:hole
	FIVE_ELEMENTS_OPERATE_TYPE_WEAR_STONE = 4,						-- 穿戴宝石		param1:part		五行	param2:hole	 孔位	param3:bag_index   背包位置
	FIVE_ELEMENTS_OPERATE_TYPE_DECOMPOS_STONE = 5,					-- 宝石分解		param1:bag_index	param2:count
	FIVE_ELEMENTS_OPERATE_TYPE_COMPOS_STONE = 6,					-- 宝石合成		param1:item_id		param2:part		param3:hole
	FIVE_ELEMENTS_OPERATE_TYPE_SKILL_AWAKEN = 7,					-- 技能觉醒
	FIVE_ELEMENTS_OPERATE_TYPE_ACTIVE_TALENT = 8,					-- 天赋激活		param1:seq
	FIVE_ELEMENTS_OPERATE_TYPE_DRAW = 9,							-- 抽奖			param1:mode			param2:item_id
	FIVE_ELEMENTS_OPERATE_TYPE_DRAW_JUMP = 10,						-- 奖池跳转		param1:item_id
	FIVE_ELEMENTS_OPERATE_TYPE_DRAW_RECORD_INFO = 11,			    -- 抽奖记录
	FIVE_ELEMENTS_OPERATE_TYPE_HOLE_SUIT_ACTIVE = 12,               -- 激活套装     param1:part			param2:seq
}

WAIST_LIGHT_OPERATE_TYPE = {
	INFO = 1,				  -- 信息
	USE_STONE = 2,			  -- 使用陨石          param1 = 五行id, parame2 = 孔id, param3 = bag_index
	STONE_LEL = 3,            -- 陨石升级          param1 = 五行id, parame2 = 孔id
	USE_WAIST = 4,            -- 使用五行沧溟		param1 = 五行id
	SET_SKILL = 5,            -- 装配技能         	param1 =五行id
	CANCEL_WAIST = 6,         -- 取消使用五行沧溟	 param1 = 五行id
	BUY_FREE = 7,             -- 购买免费商品        param1 = 免费的商品id 

	DAOHUN_ACTIVE = 8,        -- 激活沧溟道魂        param1 = 沧溟id 
	DAOHUN_UPGRADE = 9,       -- 升级沧溟道魂        param1 = 沧溟id 
	QTL_ACTIVE = 10,       	  -- 激活乾天录        	 param1  =  乾天录id 
	QTL_INFO = 11,       	  -- 请求乾天录信息 
}


WAIST_LIGHT_NUMTYPE =
{
	MAX_WAIST_LIGHT = 40,								-- 服务端定义沧溟最大数
	COUNT_WAIST_LIGHT_STONE = 5,						-- 五行沧溟孔位
}

--金石之言
 GOLDEN_TALK_OPERATE_TYPE = {
 	INFO = 1, 														-- 请求信息
 	BUY_FREE = 2, 										-- 免费商品        param1 = 免费的商品id 

 }

--返利活动-绝版赠礼
 EXTINCT_GIFT_OPERATE_TYPE = {
 	INFO = 1, 														-- 请求信息
 	GET_MAIN_TASK_REWARD = 2, 										-- 领取主任务奖励
 	GET_SUB_TASK_REWARD = 3, 										-- 领取子任务奖励 param1:activity_day
 }

 -- 超值赠礼
PREMIUM_GIFT_OPERATE_TYPE = {
	INFO = 1, 														-- 请求信息
	GET_MAIN_TASK_REWARD = 2, 										-- 领取主任务奖励
	GET_SUB_TASK_REWARD = 3, 										-- 领取子任务奖励 param1:activity_day
}

--返利活动-充值立减
RECHARGE_DISCOUNTS_OPERATE_TYPE = {
	INFO = 1, 														-- 请求信息
    GIFT_RANDOM = 2, 												-- 随机优惠		param1:seq
    GIFT_BUY = 3, 													-- 购买礼包		param1:seq
}

--返利活动-烟花抽奖
FIREWORKS_DRAW_OPERATE_TYPE = {
	INFO = 1, 														-- 请求信息
    DRAW = 2, 														-- 进行抽奖 param1:mode
}

OA_LIMIT_RMB_BUY_OPERATE_TYPE = {
	INFO = 1,                                                       -- 信息
}

-- 洪荒好礼 特惠庆典
CHAOTIC_GIFT_OPERATE_TYPE = {
	CHAOTIC_GIFT_OPERATE_TYPE_GIFT_INFO = 1,							-- 豪礼信息
	CHAOTIC_GIFT_OPERATE_TYPE_RMB_BUY_INFO = 2,							-- 直购信息
	CHAOTIC_GIFT_OPERATE_TYPE_SPECIAL_INFO = 3,							-- 特典信息
	CHAOTIC_GIFT_OPERATE_TYPE_BUY_GIFT = 4,								-- 购买豪礼			param1:seq
	CHAOTIC_GIFT_OPERATE_TYPE_RECEIVE_SPECIAL_REWARD = 5,				-- 领取特典奖励		param1:chapter
	VIP_SPECIAL_INFO = 6,						                        -- VIP特典信息
	RECEIVE_VIP_SPECIAL_REWARD = 7,			                            -- 领取VIP特典奖励		param1:chapter
}

--Boss特权 开启/关闭参数
BOSS_DROP_PRIVILEGE_OPEN_TYPE = {
	CLOSE = 0,
	OPEN = 1,
}

--Boss特权 再爆一次
BOSS_DROP_PRIVILEGE_OPERATE_TYPE = {
	INFO = 1, 															-- 信息
    LEVEL_UP = 2, 														-- 特权升级
    SET_STATUS = 3, 													-- 设置特权状态		param1:is_open
}

-- 战神特权
ZHANSHENPRIVILEGE_OPERA_TYPE =
{
	REDUCE_TIMES = 0,			--减少次数
	SET_STATUS = 1,		    	--设置特权状态	p1:(0:关闭, 1:开启)
	FETCH_DAILY_REWARDS = 2,	--领取每日奖励
}

AGENT_DOWN_REWARD_STATUS = {
	CANT_GET = 0,						--渠道下载奖励状态 - 不可领取
	CAN_GET = 1,						--渠道下载奖励状态 - 可领取
	HAS_GET = 2,						--渠道下载奖励状态 - 已领取
}

AGENT_DOWN_OPERATE_TYPE = {
	INFO = 0,			--请求信息
	SET_STATUS = 1,	    -- 设置状态 param1:status
}

OA_HELP_RANK_OPERATE_TYPE = {
	INFO = 1,           -- 请求信息
	BUY = 2,			-- 购买商品 param1:rank_seq  param2:seq
	FREE_GIFT = 3,      -- 领取每日奖励
	ALL_BUY = 4,        -- 一键购买
}

OA_TIANSHEN_RMB_BUY_OPERATE_TYPE = {
	INFO = 1,           -- 请求信息
}

OA_GOD_RMB_BUY_OPERATE_TYPE = {
	INFO = 1,           -- 请求信息
}

OA_FOOT_LIGHT_OPERATE_TYPE = {
	INFO = 1,           -- 请求信息
}

HmGod_TEXT_COLOR_TYPE = {
	ONE = 1,
	TWO = 2,
	THREE = 3,
	FOUR = 4,
	FIVE = 5,
	SIX = 6,
	SEVEN = 7,
}

RELIC_OPERATE_TYPE = {
	RELIC_INFO = 1,							--圣器信息
	POWER_INFO = 2,							--能量信息
	UNLOCK_SEAL = 3,						--解锁封印		param1:seq		param2:index
	RELIC_ACTIVE = 4,						--圣器激活		param1:seq
	EQUIP = 5,								--圣器装备		param1:seq		param2:slot		param3:bag_index
	ACTIVE_SUIT = 6,						--激活套装		param1:seq 
	SLOT_UPLEVEL = 7,						--槽位升级		param1:seq		param2:slot
	SLOT_UPSTAR = 8,						--槽位升星		param1:seq		param2:slot
	SLOT_UPGRADE = 9,						--槽位升阶		param1:seq		param2:slot
	POWER_UPLEVEL = 10,						--能量升级	    param1:type
	POWER_FETCH = 11,						--能量领取		param1:type		param2:index
}

JIEYI_OPERATE_TYPE = {
	JIEYI_OPERATE_TYPE_BASE_INFO = 1,							-- 基础信息     无用预留
	JIEYI_OPERATE_TYPE_JINLAN_UP = 2,							-- 金兰升级
	JIEYI_OPERATE_TYPE_EQUIP_STAR_UP = 3,						-- 装备升星		param1:part		param2:hole
	JIEYI_OPERATE_TYPE_EQUIP_LEVEL_UP = 4,						-- 装备升级		param1:part		param2:hole
	JIEYI_OPERATE_TYPE_EQUIP_SUIT_ACTIVE = 5,					-- 套装激活		param1:part
	JIEYI_OPERATE_TYPE_TEAM_INFO = 6,							-- 队伍信息
	JIEYI_OPERATE_TYPE_TEAM_LIST = 7,							-- 队伍列表
	JIEYI_OPERATE_TYPE_CREATE_TEAM = 8,							-- 创建队伍
	JIEYI_OPERATE_TYPE_TEAM_JOIN_INVITE = 9,					-- 邀请入队		param1:uid
	JIEYI_OPERATE_TYPE_TEAM_JOIN_INVITE_RET = 10,				-- 邀请回应		param1:jieyi_id	param2:invite_uid param3:is_agree
	JIEYI_OPERATE_TYPE_TEAM_JOIN_REQ = 11,						-- 申请入队		param1:jieyi_id
	JIEYI_OPERATE_TYPE_TEAM_JOIN_REQ_RET = 12,					-- 申请回应		param1:req_uid  param2:is_agree
	JIEYI_OPERATE_TYPE_LEAVE_TEAM = 13,							-- 离开队伍
	JIEYI_OPERATE_TYPE_VOTE = 14,								-- 发起投票     param1:be_vote_uid
	JIEYI_OPERATE_TYPE_VOTE_RESULT = 15,						-- 投票结果		param1:is_agree
	BUILD_TASK_REWARD = 16,                                     -- 领取任务奖励	param1：seq
	BUILD_ACTIVE_REWARD = 17,                                   -- 领取活跃奖励	param1：seq
	BUILD_FANLI_LINGYU_REWARD = 18,                             -- 领取返回灵玉奖励	
}

DRAGON_TEMPLE_OPERATE_TYPE = {
	INFO = 1,                           -- 基础信息   
	SOLT_INFO = 2,                      -- 槽位信息
	HANTCH_INFO = 3,                    -- 孵化信息
	DAILY_REWARD = 4,                   -- 每日奖励
	LEVEL_UP = 5,                       -- 升级
	SOLT_ACTIVE = 6,                    -- 槽位激活		param1:solt
	SOLT_LEVEL_UP = 7,                  -- 槽位升级		param1:solt
	SOLT_GRADE_UP = 8,                  -- 槽位进阶		param1:solt
	SUIT_LEVEL_UP = 9,                  -- 套装升级		param1:solt_type
	HANTCH_LEVEL_UP = 10,			    -- 孵化升级		param1:seq
	HANTCH_GRADE_UP = 11,               -- 孵化进阶     -- 弃用状态
	DRAW = 12,                          -- 抽奖			param1:mode			param2:level
	DONATE = 13,                        -- 捐献			param1:seq
	DRAW_RECORD = 14,                   -- 抽奖信息
	RANK_INFO = 15,                     -- 排名信息		param1:rank_type
	DONATE_INFO = 16,					-- 捐献信息
	FETCH_DONATE_TIMES_REWARD = 17,     -- 捐献奖励     param1:seq
	FETCH_HATCH_DAILY_REWARD = 18,	    -- 孵化每日奖励
	HANTCH_DAN_UP = 19,			        -- 孵化丹药升级		param1:seq，param2：丹药下标, param3：使用数量
}

ARTIFACT_OPERATE_TYPE = {
	INFO = 1,							-- 基础信息  
	UPLEVEL = 2,						-- 升级 param1:seq
	STAR_UPLEVEL = 3,					-- 星级 param1:seq
	UP_AWAKE_LEVEL = 4,					-- 觉醒 param1:seq
	APPLY = 5,							-- 上阵 param1:seq param2:位置seq
	ACTIVE = 6,							-- 激活 param1:seq
	GET_GIFT_REWARD = 7,				-- 领取礼包奖励 param1:seq
	UNAPPLY = 8,						-- 下阵 param1:apply_seq
	SEND_GIFT = 9,						-- 送礼物		param1:seq param2: item_id  param3:数量
	FETCH_FAVOR_REWARD = 10,			-- 领取好感奖励	param1:seq param2: favor_level
	TRAVEL = 11,						-- 同游			param1:seq, param2: scene_seq
	FETCH_TRAVEL_REWARD = 12,			-- 领取同游奖励	param1:seq
}

-- 更新类型
ARTIFACT_UPDATE_TYPE = {
	ACTIVE = 0,				-- 激活
	UPLEVEL = 1,			-- 升级
	UP_STAR_LEVEL = 2,		-- 升星
	UP_AWAKE_LEVEL = 3,		-- 觉醒升级
	GET_GIFT_REWARD = 4,	-- 领取礼包
	UPDATE = 5,				-- 刷新
}

DRAGON_TEMPLE_OPERATE_RANK_TYPE = {
	LONGHUN = 1,                        -- 龙魂
	MONEYCONTRIBUTE = 2,                --贡献
}

DRAGON_TEMPLE_OPERATE_DRAW_TYPE = {
	PERSON = 1,                         -- 个人
	SERVER = 2,                			-- 全服
}

OA_DIY_DRAW1_OPERATE_TYPE = {
	INFO = 1,													-- 请求信息
	DRAW = 2,													-- 进行抽奖 param1:mode
	CHOOSE = 3,													-- 选择奖池 param1:index param2:seq
}

OA_GLORY_CRYSTAL_OPERATE_TYPE = {
	INFO = 1,                                                   -- 请求信息
	DRAW = 2,                                                   -- 进行抽奖 param1:mode
	CONVERT = 3,                                                -- 进行兑换 param1:seq, num
	FETCH_TIMES_REWARD = 4,                                     -- 次数奖励 param1:seq
	FETCH_COLLECT_REWARD = 5,                                   -- 收集奖励 param1:task_seq
	DAILY_REWARD = 6,                                   		-- 领取每日任务奖励 param1: task_seq
	RMB_ONEKEY_REWARD = 7,                                   	-- 领取直购一键打包礼包 param1: grade
	TOTAL_RECHARGE_REWARD = 8,                                  -- 领取累计充值奖励 param1:seq
	FREE_REWARD = 9,                                  			-- 领取免费奖励 param1:seq
	BUY_GIFT = 10,                                  			-- 购买礼包 param1:seq
}

WEALTH_GOD_OPERATE_TYPE = {
	WEALTH_GOD_OPERATE_TYPE_INFO = 1,							-- 信息
	WEALTH_GOD_OPERATE_TYPE_POOL_INFO = 2,						-- 奖池信息
	WEALTH_GOD_OPERATE_TYPE_RANK_INFO = 3,					    -- 排名信息
	WEALTH_GOD_OPERATE_TYPE_DRAW = 4,						    -- 抽奖
	REWARD = 5,													-- 赐福领取奖励
	UPLEVEL = 6,												-- 赐福升级
	FETCH_FREE_RMB_BUY = 7,									    -- 领取免费直购		param1:seq
}

-- 虚拟现金类型
VIRTUAL_GOLD_TYPE = {
	REPLACE_COIN = 0,											-- 代币 - 废弃		（充值 or 直购）
	CASH_POINT = 1,												-- 现金点			（充值 or 直购）
	RECHARGE_VOLUME = 2,										-- 充值券			（充值）
	REPLACE_COIN_ITEM = 3,										-- 代币 - 消耗物品	（充值 or 直购）
}

LONGXI_OPERATE_TYPE = {
	LONGXI_OPERATE_TYPE_ITEM_INFO = 1,							-- 信息
	LONGXI_OPERATE_TYPE_LEVEL_UP = 2,							-- 升级		param1:seq
	LONGXI_OPERATE_TYPE_TASK_FINISH = 3,						-- 立即激活	param1:seq
}

LONGXI_ACTIVITY_TYPE = {
	SUPERDRAGONSEAL = 0,								  	    -- 至尊龙玺
	DRAGONKINGTOKEN = 1,                                        -- 龙王令牌
}

OPRECHARGE_RANK = {
	INFO = 1,
	RANK_INFO = 2,
}

-- 首充/等级直购 开关
FIRSTRECHAEGE_AND_LEVELRECHARGE_SWITCH_TYPE= {
	SHOW_FIRST_RECHARGE = 0,       -- 显示首充 隐藏等级直购
	SHOW_LEVEL_RECHARGE = 1,       -- 显示等级直购隐藏首充 
}

CROSS_YANGLONG_OPERATE_TYPE = {
	CROSS_YANGLONGOPERATE_TYPE_RANK_REWARD_INFO = 1,			-- 排名奖励信息
	CROSS_YANGLONGOPERATE_TYPE_FETCH_RANK_REWARD = 2,			-- 领取排名奖励		param1:index
	CROSS_YANGLONGOPERATE_TYPE_SCORE_RANK_INFO = 3,				-- 积分排名信息
	CROSS_YANGLONGOPERATE_TYPE_CONVERT = 4,						-- 兑换				param1:seq
	CROSS_YANGLONGOPERATE_TYPE_BASE_INFO = 5,					-- 玩家基础信息
	CROSS_YANGLONGOPERATE_TYPE_SET_REFRESH_REMIND_FALG = 6,     -- 设置刷新提醒      param1:is_remind
}

EQUIP_YULING_OPERATE_TYPE = {
	EQUIP_YULING_OPERATE_TYPE_PART_INFO = 1,						-- 部位信息
	EQUIP_YULING_OPERATE_TYPE_UNLOCK = 2,							-- 孔位解锁	param1:equip_index param2:hole
	EQUIP_YULING_OPERATE_TYPE_FORGE = 3,							-- 孔位锻造 param1:equip_index param2:hole
}

--许愿仙池
RA_YANHUA_SHENGDIAN3_OP_TYPE = {
	INFO = 0,		--请求活动信息
	BUY = 1,        --购买并抽奖
    --DRAW = 2,       --抽奖
    FETCH = 3,      --领取返利
    RECORD = 4,     --记录
    ANIM_FLAG = 5,  --动画标记
}

RA_JIANGSHAN_RUHUA_OP_TYPE = {
	INFO = 0,		--请求活动信息
	BUY = 1,        --购买并抽奖
    --DRAW = 2,       --抽奖
    FETCH = 3,      --领取返利
    RECORD = 4,     --记录
    ANIM_FLAG = 5,  --动画标记
}

RA_MORANXUANYUAN_OP_TYPE = {
	INFO = 0,		--请求活动信息
	BUY = 1,        --购买并抽奖
    --DRAW = 2,       --抽奖
    FETCH = 3,      --领取返利
    RECORD = 4,     --记录
    ANIM_FLAG = 5,  --动画标记
}

--锻造-铸神
CAST_SOUL_OPERATE_TYPE = {
	INFO = 1, 									-- 信息
    LEVEL_UP = 2, 								-- 铸魂升级		param1:equip_index
    SUIT_LEVEL_UP = 3,							-- 套装升级		param1:seq
    GOD_LEVEL_UP = 4, 							-- 铸神升级		param1:seq
}

--弑天套装
SHITIAN_SUIT_OPERATE_TYPE = {
	INFO = 1, 										--信息
    UNLOCK = 2, 									--部位解锁		param1:suit_seq		param2:part
    STAR_UP = 3, 									--部位升星		param1:suit_seq		param2:part
    REWARD = 4, 									--领取奖励		param1:suit_seq
}

--特惠直购2
OA_LIMIT_RMB_BUY2_OPERATE_TYPE = {
	INFO = 1,										--请求信息
}

WEEKCARD_OPERATE_TYPE = {
	WEEKCARD_OPERATE_TYPE_INFO               = 1,		-- 信息
	WEEKCARD_OPERATE_TYPE_FETCH_DAILY_REWARD = 2,		-- 领取周卡奖励		param1:day
	WEEKCARD_OPERATE_TYPE_FETCH_BUFF_REWARD  = 3,		-- 领取活动奖励
}

--累计登录
LEIJI_LOGIN_OPERATE_TYPE = {
    INFO = 1, 									-- 信息
    FETCH_REWARD = 2, 							-- 领取奖励			param1:day
    FETCH_DAY_REWARD = 3, 						-- 领取天数奖励		param1:seq
}

SYSTEM_FORCESHOW_TASK_STATUS = {
	NONE = 0,                                   -- 未达成
	CAN_FETCH = 1,                              -- 可领取
	HAS_FETCH = 2,                              -- 已领取
}

SYSTEM_FORCESHOW_OPERATE_TYPE = {
	INFO = 1,									-- 信息     param1:seq
	FETCH_TASK_REWARD = 2,				        -- 任务奖励	param1:seq param2:task_id
	FETCH_CHAPTER_REWARD = 3,				    -- 章节奖励	param1:seq param2:chapter
	FETCH_FINISH_REWARD = 4,				    -- 最终奖励	param1:seq
	ALL_INFO = 5,                               -- 全部信息
}

--每日累充2
OA_DAILY_RECHARGE_OPERATE_TYPE = {
	INFO = 1,									-- 请求信息
	FETCH_REWARD = 2,							-- 领取奖励		param1:seq
}

--烟花抽奖2
OA_FIREWORKS_DRAW2_OPERATE_TYPE = {
	INFO = 1,									-- 请求信息
	DRAW = 2,									-- 进行抽奖 param1:mode
}

--特殊抽奖1
OA_SPECIAL_DRAW_OPERATE_TYPE = {
	INFO = 1,									-- 请求信息
	DRAW = 2,									-- 进行抽奖 param1:mode
	TIMES_REWARD = 3,							-- 次数奖励 param1:seq
}

--限时直购3
OA_LIMIT_RMB_BUY3_OPERATE_TYPE = {
	INFO = 1,									-- 请求信息
}

--特惠卖场
OA_LIMIT_RMB_SHOP_OPERATE_TYPE = {
	INFO = 1,									-- 请求信息
}

OA_ACT_COMMON_CONVERT_OPERA_TYPE = {
	INFO = 0,									-- 请求信息
	CONVERT = 1									-- 兑换 param1:seq, param2:num
}

RAND_ACTIVITY_TYPE_CSA_CONVERT_OPERA_TYPE = {
	INFO = 0,									-- 请求信息
	CONVERT = 1									-- 兑换 param1:seq, param2:num
}

-- 捉鬼副本操作
GHOST_FB_OPERATE_TYPE = {
	BASE_INFO = 1,						-- 基础信息
	TASK_INFO = 2,						-- 任务信息
	FETCH_TASK_REWARD = 3,				-- 任务奖励 param1:task_id
	REFRESH_EVENT = 4,					-- 刷新事件
	ENTER_BAOTU_FB = 5,					-- 进入宝图副本	param1:seq
	LEAVE_BAOTU_FB = 6,					-- 离开宝图副本
	TASK_AUTO_FINISH = 7,				-- 任务一键完成
}

-- 捉鬼副本 - 事件状态
GHOST_FB_EVENT_STATUS = {
	NONE = 1,
	ACCEPT = 2,
	FINISH = 3,
}

-- 捉鬼副本 - 目标状态
GHOST_FB_GOAL_STATUS = {
	FIND = 1,							-- 探测
	ARRIVE = 2,							-- 到达目的地
	BOX = 3,							-- 宝箱出现
	MONSTER = 4,						-- 小怪出现
	BOSS = 5,							-- 鬼王出现
	STELE = 6,							-- 鬼王墓碑
}



SIXIANG_QUENCH_OPERATE_TYPE = {
	INFO = 1,									-- 信息
	SOLT_UPGRADE = 2,							-- 槽位进阶	param1:solt
	SKILL_UPLEVEL = 3,							-- 技能升级	param1:solt		param2:seq
	CUT_IRON_UPLEVEL = 4,						-- 琢铁升级	param1:seq		param2:count
}

CHARM_OPERATE_TYPE = {
	SOLT_INFO = 1,							-- 槽位信息
	WEAR_EQUIP = 2,							-- 穿戴装备		param1:solt			param2:bag_index
	COMPOS_EQUIP = 3,						-- 合成装备		param1:item_id		param2:solt  -1 全拿背包的   拿槽位的就发solt
	YINYANG_UPLEVEL = 4,                    -- 阴阳升级
}

CHARM_RATE_TYPE = {
	WING = 1, --1.仙翼进阶属性
	FABAO = 2, --2.法器进阶属性
	SEHNBING = 3, --3.龙脉进阶属性
	JIANZHEN = 4, --4.背饰进阶属性
	LINGCHONG = 5, --5.灵剑进阶属性
	Mount = 6, --6.灵骑进阶属性
	TUJIAN = 7, --7.万图谱属性
	ControlBeasts = 8, --8.幻兽属性
	EQUIP = 9, --9.装备属性
	TIANSHEN = 10, --10.诸神属性
	MINGWEN = 11, --11.魂印属性
	LINGMENG = 12, --12.灵梦属性
	SHENSHOU = 13, --13.天兵属性
	MAX = 13, --最大值

}

NOTCIE_TYPE = {
	MOVE_CHECK_NOT_PASS = 1,	            -- 移动非法
	SKILL_PERFROM_CHECK_NOT_PASS = 2,		-- 技能非法
	HEARTBEAT_ERROR = 3,					-- 心跳错误
}

-- 一线牵
OA_CHAIN_DRAW_OPERTE_TYPE = {
	INFO = 1,										-- 信息同步
	DO_DRAW = 2,									-- 抽奖				param1:is_auto
	DRAW_REWARD = 3,								-- 获取抽奖奖励		param1:seq
	TASK_REWARD = 4,								-- 获取任务奖励		param1:seq
}

EQUIP_COLLECT_OPERATE_TYPE = {
	INFO = 1,                                   -- 信息
	ACTIVE_HOLE = 2,							-- 激活孔位		param1:suit_index	param2:hole	param3:knapsack_index
	SUIT_UPLEVEL = 3,							-- 套装升级		param1:suit_index
}

HUALING_OPERATE_TYPE = {
	INFO    = 1,					-- 信息
	UPLEVEL = 2,				    -- 化灵升级		param1:type		param2:seq		param3:count
}

--藏金商铺
OA_HIDE_GOLD_SHOP_OPERATE_TYPE = {
	INFO = 1,				-- 请求信息
	BUY = 2,					-- 商店购买		param1:seq
	REFRESH = 3,				-- 商店刷新
}

-- 道行
TAOIST_OPERATE_TYPE = {
	INFO 						= 1,					-- 信息
	SLOT_KAIGUANG 				= 2,					-- 开光				param1 = slot param2 = 1使用幸运道具,0 不使用
	WEAR_ALL 					= 3,                    -- 一键穿戴
	SUIT_ITEM 					= 4,                    -- 领取套装奖励
	COMP 						= 5,                    -- 合成               param1 = 目标装备id   num = 数量
	SLOT_ENHANCE 				= 6, 					-- 槽位强化			param1 = slot
	SLOT_ADVANCE 				= 7, 					-- 槽位进阶			param1 = slot
	ENHANCE_GONGMING 			= 8, 					-- 强化共鸣 + 1级别
	SLOT_CARVE 					= 9, 					-- 槽位刻印			param1 = slot param2 = seq
	CARVE_GONGMING 				= 10, 					-- 刻印共鸣
	KAIGUANG_RETURN 			= 11, 					-- 开光使用/重置	param1 = slot param2 = is_use
	USE_STONE 					= 12, 					-- 宝石镶嵌			param1 = hole param2 = bag_index   
	INLAY_UPLEVEL 				= 13, 					-- 符石升级			param1 = hole    --
	TAOIST_INLAY_TYPE_GONGMING 	= 14,                   -- 符石共鸣          param1 = num  -- 4套装还是8套装还是12套装
	RMB_BUY_UP_LEVEL            = 15, 					-- 直购升级
	TOUCH                       = 16,                   -- 抚摸
	TAOIST_OPERATE_QUE          = 17,                   -- 答题              param1 = qu_id  param2 = result
}

--天师下山
OA_LIKE_OPERATE_TYPE = {
	INFO = 1,				-- 请求信息
	DO_LIKE = 2,			-- 点赞
	HOT_VALUE_REWARD = 3,	-- 热度奖励
}

TIANSHEN_RUMO_OPERATE_TYPE = 
{
	TSRM_INFO = 1,						-- 请求入魔信息	p1=天神id(0代表全发)
	TSRM_ACTIVE = 2,					-- 激活			p1=天神id
	TSRM_LLLUSION = 3,					-- 幻化			p1=天神id	p2=幻化id
	TSRM_RABBET_UPGRADE = 4,			-- 槽位升级		p1=天神id	p2=槽位id
	TSRM_UPGRADE = 5,					-- 升星			p1=天神id	p2=幻化id
}

-- 梦灵
DREAM_SPIRIT_OPERATE_TYPE = {
	INFO                = 1,		-- 基础信息
	ITEM_INFO           = 2,		-- 梦灵信息
	BAG_INFO            = 3,		-- 背包信息
	EQUIP               = 4,		-- 梦灵装备		param1:seq		param2:slot		param3:bag_index
	SLOT_UP_LEVEL       = 5,		-- 槽位升级		param1:seq		param2:slot
	EQUIP_STONE         = 6,		-- 使用属性丹	param1:hole
	SLOT_UP_LEVEL_ONE_KEY = 7,      -- 一键升级     param1:seq
}

OA_SHENGSHOU_DRAW_OPERATE_TYPE = {
	DRAW = 1,			-- 抽奖，p1->抽奖数
	GET_FREE_DRAW = 2,  -- 获取每日免费道具
}

TIANSHEN_RUMO_SKILL_OPERATE_TYPE = 
{
	TSRM_CHOESS_SKILL = 1,			-- 选择技能		p1=天神id	p2=p3的大小	p3=已选技能类型（按位存储）
	TSRM_RESET_SKILL = 2,			-- 重置技能		p1=天神id	p2=p3的大小	p3=已选技能类型（按位存储）
}

WEIWODUZUN_OPERTE_TYPE = {
	INFO = 1,                      --全部信息推送
	UPDATE = 2,                    --单个信息推送		param1:task_id
	FEACH_REWARD = 3,              --获取奖励			param1:task_id
}
ATTRIBUTE_STONE_OPERATE_TYPE =
{
	INFO = 1,							-- 信息
	UPGRADE_STONE = 2,					-- 进阶宝石		param1: type	param2:slot_idx   num 
	PET_STONE = 3,						-- 宠物宝石		param1: slot_idx   num
	MOUNT_STONE = 4,					-- 坐骑宝石		param1: slot_idx   num
}

DRAWINGS_COMPOSE_OPERATE_TYPE = {
	INFO = 1,						-- 信息
	USE_CHIP = 2,					-- 使用碎片				param1: type		param2:drawings_id		param3:slot_idx
}

FUXINGGAOZHAO_OPERA_TYPE = {
	DRAW = 0,	 				-- 抽奖 param1 抽奖次数
	ACQUIRE = 1,  				-- 领奖 param1 奖励序号
}

JINGMAI_OPERA_TYPE = 
{
	JINGMAI_OPERA_TYPE_SEND_INFO = 0,			-- 获取经脉信息
	JINGMAI_OPERA_TYPE_UPGRADE = 1,				-- 升级
	JINGMAI_OPERA_TYPE_CONDENSATE = 2,			-- 凝气
	JINGMAI_OPERA_TYPE_GET_SKILL = 3,			-- 领取技能
	--JINGMAI_OPERA_TYPE_MAXIMUM_CONDENSATE = 4,	-- 一键凝气
	JINGMAI_OPERA_TYPE_BREAK_UP = 5,			-- 突破
}

ROLE_OPERA_TYPE = 
{
	OPER_TYPE_UPGRADE = 0, 		-- 武魂升级
	OPER_TYPE_GET_WUHUN = 1, 	-- 武魂激活
	OPER_TYPE_BREAK = 2, 		-- 突破进阶
	OPER_TYPE_LIAN_HUA = 3, 	-- 炼化
	OPER_TYPE_EQUIP_SKILL = 4,	-- 武魂装备上技能
	OPER_TYPE_EQUIP_HUANHUA = 5, -- 武魂幻化 param1 幻化id
	OPER_TYPE_PROPERTY_PELLET = 6, -- 使用属性丹 武魂ID 物品ID
	OPER_TYPE_STARUP = 7,        -- 武魂升星
}

FUXINGGAOZHAO_OPERA_TYPE = {
	DRAW = 0,	 				-- 抽奖 param1 抽奖次数
	ACQUIRE = 1,  				-- 领奖 param1 奖励序号
}

ALL_INFO_RETURN_TYPE =
{
	RETU_TYPE_ALL_WUHUN_INFO = 0, 	-- 返回所有武魂信息
	RETU_TYPE_AURA_INFO = 1, 		-- 返回幻化信息
}

XIUWEI_OPERA_TYPE =
{
	UPGRADE_LEVEL = 0,				-- 升级等级
	UPGRADE_STAGE = 1,				-- 升级境界 是否使用道具 param1:1是用 0是没有
	GET_STAGE_REWARD = 2,			-- 领取境界奖励 param1:stage
	NUQI_BIANSHEN = 3,				-- 怒气变身
	ACTIVE_NUQI_TYPE = 4,			-- 激活境界怒气类型 param1:0人, 1仙, 2魔
	CHOOSE_NUQI_TYPE = 5,			-- 选择境界怒气类型 param1:0人, 1仙, 2魔
	ONE_KEY_USE_EXP_ITEM = 6,		-- 一键使用加经验道具
	EVERY_DAY_REWARD = 7,			-- 领取特权每日奖励
	UPGRADE_STAGE_CLIENT = 8,		-- 升级境界小游戏方式 param1:0失败 1成功 param2:扣掉的修为经验值
	GET_EXP_ADD = 9,				-- 领取修为经验加成 param1:加成类型 param2:seq
	XIUWEI_OPERA_TYPE_GET_INFO = 10, --请求信息
	XIUWEI_OPERA_TYPE_SPECIAL_BIANSHEN = 11, --主线任务特殊变身param1:0人, 1仙, 2魔
	XIUWEI_OPERA_TYPE_NUQI_UPGRADE_SKILL_LEVEL = 12,	-- 升级怒气技能等级 param1:0人, 1仙, 2魔, param2: 技能下标[0,3]
	XIUWEI_OPERA_TYPE_NUQI_UPGRADE_BUFF_LEVEL = 13,	    -- 升级怒气BUFF等级 param1:0人, 1仙, 2魔
	XIUWEI_OPERA_TYPE_FETCH_TASK_REWARD = 14,			-- 任务奖励 paraml:sep
	XIUWEI_OPERA_TYPE_OPEN_NUQI_BIANSHEN = 15,			-- 开启怒气变身
}
--境界 效率加成
CULTIVATION_EXP_ADD_TYPE =
{
	ROLE_LEVEL = 0,         --角色等级
	EQUIP = 1,              --装备战力
	MODIFY = 2,				--神灵战力（天神）
	WING = 3,				--仙翼战力
	FABAO = 4,       		--神器战力
	SHENBING = 5,       	--神武战力
	JIANZHEN = 6,       	--背饰战力
	MOUNT = 7,       		--坐骑战力
	MINGWEN = 8,       		--云纂战力（魂印）
	WUHUN = 9,       		--武魂战力
	BEASTS = 10,       		--驭兽战力
	MONSTER = 11,       	--异兽战力
	APPEARANCE = 12,       	--外观品质*数量
	VIP_LEVEL = 13,       	--vip等级
}

Role_CHARM_RANK_TYPE = 
{
	PERSON_RANK_TYPE_SEND_FLOWER_MALE = 80,         --魅力男榜
	PERSON_RANK_TYPE_SEND_FLOWER_FEMALE = 81,       --魅力女榜
}

--新战斗坐骑
FIGHT_MOUNT2_OPERATE_TYPE = 
{
	INFO = 0,					--返回全部数据
	UPLEVEL = 1,				--战斗坐骑技能升级		param1:seq
	GOON = 2,					-- 幻化战斗坐骑		param1:幻化坐骑（对应坐骑索引 取消幻化-1）
	DRAGON = 3,				    -- 跟随小龙		param1:seq(取消幻化为-1)
	UPGRADE = 4,				-- 形象进阶		param1:seq
	USE_SKILL = 5,			    -- 出战技能	    param1:seq(取消出战-1)
	FIGHT = 6,				    -- 御龙
}

CROSS_DIVINE_DOMAIN_OPERATE_TYPE = {
	BASE_INFO = 1,							-- 基础信息
	ROOM_INFO = 2,							-- 房间信息
	--后面那些基地就没必要请求了，基地的归属不会变化   也不会获得攻城值
	--你要请求哪些城池，城池索引塞到flag里面 flag是位运算过来的 比如你要0123的城池信息 那就把这个flag的0123位置为1
	CITY_INFO = 3,							-- 城池信息	param1:city_flag
	PLAYER_RANK = 4,						-- 玩家排行	param1:type
	SERVER_RANK = 5,						-- 国家排行
	BASE_OTHER_INFO = 6,					-- 其他基础信息
	MONSTER_INFO = 7,						-- 怪物信息	param1:city_seq
	FETCH_SCORE_REWARD = 8,					-- 积分奖励	param1:seq
	FETCH_CAPTURE_REWARD = 9,				-- 占领奖励	param1:city_seq
	WORSHIP = 10,							-- 膜拜		param1:rank	
	FETCH_WORSHIP_REWARD = 11,				-- 被膜拜奖励
	CONVENE_CITY = 12,                       --设置目标param1:city_seq
	CONVERT = 13,                           -- 兑换param1:seq
	ASSEMBLE_CITY = 14,						-- 城池集结	param1:city_seq
	CONCERN_CITY = 15,						-- 城池关注	param1:city_seq		param2:is_concern
	CAMP_INFO = 16,                         -- 前三榜单 param1:seq
}

CROSS_DIVINE_DOMAIN_PERSON_RANK_TYPE = {
	SCORE_RANK = 1,          --分别是当前积分榜  
	TODAY_SCORE_RANK = 2,    --今日增长积分榜
	PRE_SCORE_RANK = 3,     --前一天的结算的榜
} 

WARDROBE_SKILL_TYPE =
{
	WARDROBE_SKILL_TYPE_CHOOSE = 0, 								--客户端请求激活套装选择技能
	WARDROBE_SKILL_TYPE_UPGRADE = 1, 								--客户端请求激活套装技能升级
	WARDROBE_SKILL_TYPE_STAR = 2, 									--客户端请求激活套装技能升星
	WARDROBE_SKILL_TYPE_RESET = 3, 									--客户端请求激活套装技能重置
	WARDROBE_SKILL_TYPE_ATTR_CHOOSE = 4,  							--客户端请求激活套装选择屬性
	WARDROBE_SKILL_TYPE_ATTR_RESET = 5,  							--客户端请求激活套装屬性重置
}

-- 棋盘寻宝
OA_RECHARGE_SCORE_OPERATE_TYPE = 
{
	INFO = 1,
	REWARD = 2,
	QUICK = 3,
}

--长乐未央
HAPPY_FOREVER_TYPE = 
{
	HAPPY_FOREVER_OPERATE_TYPE_CHOOSE = 1,                         --请求选择道具
	HAPPY_FOREVER_OPERATE_TYPE_GET_DAILY_FREE_REWARDS = 2,	       --获取每日免费道具
	HAPPY_FOREVER_OPERATE_TYPE_GET_DAILY_REWARDS = 3,	           --满足某个条件后每日可免费获取原需购买的道具

}

--神怒天诛
OA_SHENNUTIANZHU_OPERATE_TYPE =
{
	INFO = 1,							-- 请求数据
}

-- 秘笈常量定义
ESOTERICA_DEFINE = {
	MAX_NUM = 10,													-- 最大秘笈数
	PART_NUM = 1,													-- 功法数
	MAX_RESLOVE = 50,												-- 单词最大分解数
	EXP_ITEM_ID = 65543,											-- 经验虚拟道具
	MAINUI_SKILL_NUM = 6,											-- 主界面最大展示技能数
	MAINUI_SHOW_SKILL_NUM = 1,                                      -- 主界面展示技能数
}

-- 秘笈操作类型
ESOTERICA_OPERATE_TYPE = {
	INFO = 1,														-- 秘笈信息
	BASE = 2,														-- 基础信息
	WEAR = 3,														-- 穿戴装备		param1:seq  param2:part			param3:bag_index
	UPLEVEL = 4,													-- 秘笈升级		param1:seq	param2:is_auto
	PART_UPLEVEL =5,												-- 部位升级		param1:seq	param2:part
	ACTIVE = 6,														-- 秘笈激活		param1:seq
	SKILL_INFO = 7,													-- 技能信息
	PERFORM_SKILL = 8,												-- 释放技能		param1:seq  param2:target_id	param3:pos_x	param4:pos_y
}

-- 驭兽常量定义
BEAST_DEFINE = 
{
	BEAST_PACK_COUNT_MAX = 30, 										-- 驭兽背包数量
	BEAST_FLAIR_COUNT_MAX = 4, 										-- 资质数量
	BEAST_BREEDING_COUNT_MAX = 4, 									-- 孵化槽数量
	BEAST_BORN_COUNT_MAX = 400, 									-- 最大可拥有已孵化驭兽数量	
	BEAST_FIGHTING_COUNT_MAX = 9, 									-- 驭兽出战数量
	BEAST_PASSIVE_SKILL_COUNT_MAX = 9, 								-- 被动打书技能数量
	BEAST_COMPOSE_PROTOCOL_COUNT_MAX = 3, 							-- 合成协议最大可传输个数
	BEAST_CHANGE_FLAIR_PROTOCOL_COUNT_MAX = 30, 					-- 替换资质协议协议最大可传输个数
	BEAST_CHANGE_STAR_CIRCLE_ATTR_COUNT_MAX	= 4, 					-- 星阵属性最大数量 
	BEAST_CHANGE_STAR_CIRCLE_COUNT_MAX = 9, 						-- 星阵最大数量
	BEAST_CHANGE_TREASURE_COUNT_MAX = 3,							-- 宝物最大数量
	BEAST_STAR_COUNT_MAX = 10,										-- 灵兽星星最大数量
	BEAST_MAIN_BATTLE_COUNT = 3,									-- 灵兽主战位置个数
	BEAST_FLAIR_COUNT = 5,											-- 灵兽资质个数第五个为成长值
	BEASTS_MAX_SKILL_NUM = 3,										-- 主界面驭兽最大技能数
	BEASTS_MAX_REFINE_ATTR_NUM = 6,									-- 灵兽最大资质个数 
	BEASTS_BOUNS_PER = 10000,										-- 资质加成万分比
	BEASTS_BOUNS_PER2 = 100,										-- 资质加成百分比
	MAX_DRAW_ITEM_COUNT = 2,										-- 抽奖类型数据(0,1,2)
	MAX_BOOK_ITEM_COUNT = 99,										-- 幻兽图鉴信息
	MAX_HOLY_SPIRIT_INDEX_COUNT = 40,								-- 圣魂最大数量
	BEASTS_MAX_USE_PELLET = 9,										-- 幻兽最大属性丹个数
}

-- 驭兽操作类型
BEAST_OPERATE_TYPE = 
{
	OPERATE_TYPE_MIN = -1,
	OPERATE_TYPE_BREED = 0, 										-- 开始孵蛋， p1->egg_bag_index[0-299]
	OPERATE_TYPE_ACQUIRE_BRED_BEAST = 1, 							-- 领取孵化好的驭兽， p1->egg_bag_index[0-4]
	OPERATE_TYPE_ACQUIRE_EXP = 2,									-- 获取经验
	OPERATE_TYPE_APPLY_BEAST = 3, 									-- 上阵 p1->已孵化背包下标(0-34), p2 -> 出战位下标(0-8), 
	OPERATE_TYPE_RETREAT_BEAST = 4, 								-- 下阵  p1 -> 出战位下标(0-8), 
	OPERATE_TYPE_RELEASE = 5, 										-- 放生 p1-> 已孵化背包下标
	OPERATE_TYPE_UPGRADE = 6, 										-- 升级 p1->已孵化背包下标(0-34)
	OPERATE_TYPE_SORT_EGG_BGG = 7, 									-- 整理未孵化背包并下发整理后数据
	OPERATE_TYPE_ADD_SKILL = 8, 									-- 打书, p1 -> 驭兽背包下标(0-34) p2 -> 技能书id
	OPERATE_TYPE_STAR_CIRCLE_UPGRADE = 9, 							-- 星阵升星, p1 -> 星阵下标(0-8) p2 -> 属性下标(0-3)
	OPERATE_TYPE_STAR_CIRCLE_ATTR_FLUSH = 10, 						-- 星阵刷属性, p1 -> 星阵下标(0-8) p2 -> 属性下标(0-3)
	OPERATE_TYPE_STAR_CIRCLE_START_NEW_CYCLE = 11,					-- 星阵开新轮回, p1 -> 星阵下标(0-8)
	OPERATE_TYPE_BEAST_KING_UPGRADE = 12, 							-- 兽王等级升级
	OPERATE_TYPE_BEAST_REDUCE_BREEDING_TIME = 13, 					-- 减少孵化时间, p1->孵化槽下标
	OPERATE_TYPE_BEAST_REDUCE_ALL_BREEDING_TIME = 14, 				-- 一键孵化 p1->孵化槽下标
	OPERATE_TYPE_BREEDING_SLOT_RELEASE = 15, 						-- 放生孵化槽的驭兽 p1-> 孵化槽背包下标
	OPERATE_TYPE_SLOT_UNLOCK = 16, 									-- 孔位灵玉解锁 p1->孔位下标(0-n)
	OPERATE_TYPE_SLOT_UPGRADE = 17, 								-- 孔位灵玉升级 p1->孔位下标(0-n)
	OPERATE_TYPE_BEAST_DRAW = 18,									-- 驭兽抽奖 - 抽奖 p1->抽奖模式mode_seq
	OPERATE_TYPE_BEAST_DRAW_SELECT_EXTRA_REWARD = 19, 				-- 驭兽抽奖 - 选择额外大奖 -> 奖励下标(0-n)
	OPERATE_TYPE_BEAST_SORT_BEAST_BAG = 20,							-- 整理孵化背包
	OPERATE_TYPE_REBREED = 21,										-- 重新孵化, p1->孵化槽下标
	OPERATE_TYPE_REFINE = 22,										--// 洗练 param1:index
	OPERATE_TYPE_REFINE_RESULT = 23,								--// 洗练结果 param1:index param2:is_save
	OPERATE_TYPE_REFINE_LEVEL = 24,									--// 洗练升级 param1:index
	OPERATE_TYPE_DRAW_ITEM_INFO = 25,								--// 抽奖信息
	OPERATE_TYPE_DRAW = 26,											--// 抽奖 param1:type param2:mode.
	OPERATE_TYPE_DRAW_RECORD_INFO = 27,								--// 抽奖记录信息 param1:type
	OPERATE_TYPE_DRAW_CONVERT = 28,									--// 抽奖兑换 param1:type
	OPERATE_TYPE_HOODBOOK_INFO = 29,								--// 图鉴信息
	OPERATE_TYPE_FETCH_HOODBOOK_REWARD = 30,						--// 领取图鉴奖励 param1:beast_type
	OPERATE_TYPE_SKIN_INFO = 31,									-- 皮肤信息
	OPERATE_TYPE_SKIN_UPLEVEL  = 32,								-- 皮肤升级 param1:skin_seq
	OPERATE_TYPE_SKIN_USE = 33,										-- 皮肤幻化 param1:bag_index param2:skin_seq
	OPERATE_TYPE_HOLY_CALL = 34,									-- 圣兽召唤 param1:beast_type
	OPERATE_TYPE_HOLY_LINK = 35,									-- 圣兽链接 param1:bag_index param2:link_bag_index
	OPERATE_TYPE_HOLY_SPIRIT_UPLEVEL = 36,							-- 圣魂升级 param1:bag_index param2:index
	OPERATE_TYPE_HOLY_SPIRIT_RESET = 37,							-- 圣魂重置 param1:bag_index
}

CROSS_FLAG_BATTLE_OPERATE_TYPE = {
	MIN = 0,
	ROOM_INFO = 1,			-- 房间信息
	BASE_INFO = 2,			-- 基础信息
	RANK_INFO = 3,			-- 排行信息
	MATCH = 4,				-- 进行匹配
}

CROSS_FLAG_BATTLE_STATE_TYPE = {
	NOMATCH = 1,
	MATCHING = 2,
	MATCHSUC = 3,
}

-- 一生所爱
A_LIFELONG_LOVE_OPERATE_TYPE = {
	TASK_REWARD = 1,					-- 领取任务奖励 param1:任务seq
	ONE_KEY_TASK_REWARD = 2,			-- 一键领取任务奖励
	BIG_REWARD = 3,						-- 领取大奖奖励
}

OA_ODYSSEY_RMB_BUY_TYPE = {
	INFO = 1,
}

DIYCHUANWEN_OPERA_TYPE = {
	INFO = 1,						-- 请求返回信息
	GET_REWARD = 2,				    -- 请求领取档位奖励 param1 = 档位索引
	MODITY_DESC = 3,				-- 请求更改传闻内容 param1 = 类型 paramStr = 内容   （废弃）
	CHOOSE_DESC = 4,				-- 请求选择传闻内容 param1 = 类型 param2 = 文字索引  paramStr = 内容   （param2 为0 重置  -1是自定义paramStr要有内容  其他的模板seq）
	SEND_REWARD = 5,				-- 请求下发打脸奖励 param1 = 档位索引 param2 = 奖励标记（二级制）
	ACTIVE_SKIN = 6,				-- 请求激活皮肤 param1 = 类型 param2 = 皮肤唯一索引
	CHOOSE_SKIN = 7,                -- 请求选择传闻皮肤 param1 = 类型 param2 = 皮肤索引
	Close = 8,                      -- 请求关闭传闻  param1 = 类型 param2 = 0开启 1关闭
}

MECHA_OPERA_TYPE = {
	INFO = 1,					-- 请求返回信息
	ACTIVE = 2,					-- 激活机甲	param1:机甲基础配置mechan_seq
	PART_STAR = 3,				-- 部件升星（激活部件-1升到0星）param1:机甲升星配置seq
	HELP_FIGHT_OPEN = 4,		-- 助战位开启 param1:助战位开启配置seq
	HELP_FIGHT_BATTLE = 5,		-- 上阵助战位 param1:助战位开启配置seq param2:机甲基础配置mechan_seq
	MAIN_FIGHT = 6,				-- 上阵主战位 param1:机甲基础配置mechan_seq
	BIANSHEN = 7,				-- 主战位变身 
	CHOOSE_WING_SKILL = 8,		-- 选择翅膀技能 param1:翅膀相关配置skill_id
	PUTON_PART = 9,				-- 穿戴替换部件 mechan_seq  param_list
	USE_ACTIVE_ITEM = 10,       --分级激活装备  param1：机甲配置的seq   param2：数量
}

-- 自定义飘字类型
FLOAT_WORD_TYPE = {
}

MECHA_PART_TYPE = {
	BODY = 0,   		-- 躯干
	LEFT_HAND = 1, 		-- 左手
	RIGHT_HAND = 2,     -- 右手
	LEFT_FOOT = 3,      -- 左脚
	RIGHT_FOOT = 4,     -- 右脚
	LEFT_WING = 5,      -- 左翼
	RIGHT_WING = 6,     -- 右翼
	WEAPON = 7,         -- 武器
}

DIYCHUANWEN_OPERA_STATUS = {
	NORMAL = 0,              -- 默认状态
	CUSTOMIZED = 1,          -- 定制中是,
	SUC = 2,                 -- 定制成功是
	FAIL = 3,                -- 失败
}

ZHENBAODIAN_OPERA_TYPE =
{
	INFO = 1,						-- 请求返回信息
	GET_HISTORY_REWARD = 2,			-- 请求领取历史档位奖励 param1 = 奖励表索引
	GET_TODAY_REWARD = 3,			-- 请求领取当日档位奖励 param1 = 奖励表索引
	GET_ENTER_GAME_REWARD = 4,      -- 请求领取进游奖励
	BUY_VIP_GIFT = 5,               -- 请求购买vip礼包  param1 = 索引
	GET_HISTORY_RMB_REWARD = 6,     -- 请求领取历史真充返利奖励 20倍
}

CANGJINSHANGPU_OPERA_TYPE = {
	INFO = 1,						-- 请求返回信息
	BUY_SUIT = 2,					-- 请求购买套装商店 param1 = 套装商店配置表索引 param2 数量
	REFRESH_LIMIT_SHOP = 3,			-- 请求刷新限购商店 
	BUY_LIMIT_SHOP = 4,				-- 请求购买限购商店 param1 = 限购商店配置表索引 param2 数量
	BUY_TEQUAN = 5,					-- 请求购买特权 param1 = 限购商店配置表索引
	FETCH_EVERY_DAY_REWARD = 6,		-- 请求领取每日特权奖励 param1 索引
	CHOOSE_ATTR = 7,				-- 请求属性定制 param1属性1 param2 属性2
	RESET_CHOOSE_ATTR = 8,			-- 请求重置属性定制
	SCORE_BUY = 9,                  -- 请求积分直购 param1 = 充值内购表 rmb_type param2 = 充值内购表 rmb_seq param3 = 充值内购表价格
	CONVERT = 10,                   -- 请求兑换 param1 = 兑换表seq
	INVEST = 11,					-- 投资 // 投资 param1: 投资数量
	FETCH_ACTIVE_TEQUAN_REWARD = 12,-- 领取激活特权奖励
	FETCH_INVEST = 13,				-- 领取投资
}

YANYUGE_OPERA_TYPE = {
	INFO = 1,						-- 请求返回信息
	BUY_SUIT = 2,					-- 请求购买套装商店 param1 = 套装商店配置表索引 param2 数量
	REFRESH_LIMIT_SHOP = 3,			-- 请求刷新限购商店 
	BUY_LIMIT_SHOP = 4,				-- 请求购买限购商店 param1 = 限购商店配置表索引 param2 数量
	BUY_TEQUAN = 5,					-- 请求购买特权 param1 = 限购商店配置表索引
	FETCH_EVERY_DAY_REWARD = 6,		-- 请求领取每日特权奖励 param1 索引
	CHOOSE_ATTR = 7,				-- 请求属性定制 param1属性1 param2 属性2
	RESET_CHOOSE_ATTR = 8,			-- 请求重置属性定制
	SCORE_BUY = 9,                  -- 请求积分直购 param1 = 充值内购表 rmb_type param2 = 充值内购表 rmb_seq param3 = 充值内购表价格
	CONVERT = 10,                   -- 请求兑换 param1 = 兑换表seq
	INVEST = 11,					-- 投资 // 投资 param1: 投资数量
	FETCH_ACTIVE_TEQUAN_REWARD = 12,-- 领取激活特权奖励
	FETCH_INVEST = 13,				-- 领取投资
	FETCH_TEQUAN_REWARD = 14,       -- 请求领取特权奖励 param1 = seq
	FETCH_NOBILITY_TASK_REWARD = 15,-- 请求贵族任务奖励 param1: seq
	NOBILITY_TASK = 16,				-- 完成贵族查看界面任务 param1: 参数1
}

YANYUGE_TEQUAN_TYPE = {
	NOEMAL = 1,                    -- 普通特权
	NEZHA = 2,                     -- 哪吒特权
	ZANZHU = 3,                    -- 赞助特权
}

YANYUGE_NOBLE_SHOW_TYPE = {
	ACTIVITY = 1,				   --活动
	SCENE = 2,					   --战场
	OPEN_SERVER = 3,			   --开服
}

YANYUGE_NOBLE_TASK_TYPE = {
	TYPE10 = 10,				   --财神爷兑换灵玉
	TYPE11 = 11,				   --每日消耗灵玉
	TYPE13 = 13,				   --招财进宝
}


-- 灵皇套装拓展
SHITIANSTONE_OPERA_TYPE = {
	TONE_INLAY	  = 0,					--宝石镶嵌 第几套套装suit_seq 装备部位part_index, 宝石空位slot_index, 背包索引bag_index 
	STONE_LEVEL_UP = 1,					--宝石升级suit_seq, part_index, slot_index, 是否要用灵玉购买is_auto_buy
	STONE_ALL_INFO = 2,					--下发宝石当前信息 套装suit_seq, 部位part_index
	EQUIP_LEVEL_UP = 3,					--装备强化升级 套装索引suit_seq, 装备部位part_index
	EQUIP_ALL_INFO = 4,					--下发装备当前强化信息 suit_seq, part_index
	FUMO_LEVEL_UP = 5,					--附魔升级 套装索引suit_seq, 装备部位part_index, 是否使用幸运石is_use
	FUMO_ALL_INFO = 6,					--装备当前附魔信息 suit_seq, part_index
	STONE_ACTIVE = 7,					--激活宝石共鸣 套装索引suit_seq
	STRENGTH_ACTIVE = 8,				--激活强化共鸣 套装索引suit_seq
	FUMO_ACTIVE = 9,					--激活入灵共鸣 套装索引suit_seq
}

-- 万象天引
OA_WANXIAN_TIANYIN_INFO_TYPE = 
{
	INFO = 1,
	REWARD = 2,
	QUICK = 3,
	DAY_REWARD = 4,
}

-- 贯日长虹
RA_GUANRICHANGHONG_OP_TYPE = 
{
	INFO = 0,						-- 请求活动信息
	DRAW = 1,						-- 抽奖
	REWARD = 2,						-- 领取累计奖励
	RECORD_INFO = 3,				-- 记录
	ANIM_FLAG = 4,					-- 记录跳过抽奖动漫标志
	POINT = 5,						-- 积分
}

-- 衣橱新形象
WARD_ROBE_GET_NEW_TYPE = 
{
	NEW_ITEM = 1,
	NEW_SHOW_ID = 2
}

CROSS_CAPABILITY_RANK_OPERATE_TYPE = 
{
	INFO = 1,						--  获取排名信息
}

CAPABILITY_RANK_OPERATE_TYPE = {
	RANK_INFO = 1,					-- 获取排名信息
}

CROSS_REAL_CHARGE_BOSS_OPERATE_TYPE = {
	INFO = 1,							-- 请求特殊boss进度 8745
	BOSS = 2,							-- 某一层boss信息8746 param1  scene_index
	RECORD = 3,                         -- 记录
}

-- 弹窗礼包请求类型
POPUP_GIFT_OPERA_TYPE =
{
	POPUP_GIFT_OPERA_TYPE_MIN = 0,
	POPUP_GIFT_OPERA_TYPE_INFO = 1,						-- 请求信息
	POPUP_GIFT_OPERA_TYPE_POPUP = 2,					-- 请求弹窗 param1 = 弹窗grade
	POPUP_GIFT_OPERA_TYPE_MAX = 10,
}

-- 弹窗礼包请求类型
TAINXIANBAOGE_OPERA_TYPE = 
{
	TAINXIANBAOGE_OPERA_TYPE_MIN = 0,
	TAINXIANBAOGE_OPERA_TYPE_INFO = 1,						-- 请求信息
	TAINXIANBAOGE_OPERA_TYPE_CONVERT = 2,					-- 请求兑换 param1 = seq
}

-- 跨服属性宝石榜
KF_ATTR_STONE_RANK =
{
	KF_ATTR_STONE_RANK_INFO = 1,										-- 请求排名信息
	KF_ATTR_STONE_RANK_PERSON_SCORE_REWARD_FLAG = 2,					-- 请求个人积分奖励信息
	KF_ATTR_STONE_RANK_PERSON_SCORE_REWARD = 3,							-- 领取个人积分奖励 param1 = seq
	LAST_INFO = 4,													-- 昨日排行榜
}

CROSS_1VN_OPERATE_TYPE =
{
	CROSS_1VN_OPERATE_TYPE_MIN = 0,
	CROSS_1VN_OPERATE_TYPE_ROOM_INFO = 1,			-- 房间信息
	CROSS_1VN_OPERATE_TYPE_OTHER_BASE_INFO = 2,		-- 其他基础信息
	CROSS_1VN_OPERATE_TYPE_RANK_INFO = 3,			-- 排行信息
	CROSS_1VN_OPERATE_TYPE_ANSWER = 4,				-- 答题			param1:seq
	CROSS_1VN_OPERATE_TYPE_CHOOSE_TALENT = 5,		-- 选择天赋		param1:seq
	CROSS_1VN_OPERATE_TYPE_REFRESH_TALENT = 6,		-- 刷新天赋
	CROSS_1VN_OPERATE_TYPE_STAGE_GUESS = 7,			-- 竞猜			param1:camp
	CROSS_1VN_OPERATE_TYPE_BUY_TALENT = 8,			-- 购买天赋		param1:seq
	CROSS_1VN_OPERATE_TYPE_CONVERT = 9,				-- 兑换			param1:seq
	CROSS_1VN_OPERATE_TYPE_BASE_INFO = 10,			-- 基础信息
	CROSS_1VN_OPERATE_TYPE_WORSHIP = 11,			-- 膜拜
	CROSS_1VN_OPERATE_TYPE_WORSHIP_INFO = 12,		-- 膜拜信息
	CROSS_1VN_OPERATE_TYPE_FETCH_BE_WORSHIP_REWARD = 13,	-- 领取被膜拜奖励
	CROSS_1VN_OPERATE_TYPE_PERFORM_SKILL = 14,				-- 释放技能		param1:talent_seq param2:target_id	param3:pos_x	param4:pos_y
	CROSS_1VN_OPERATE_TYPE_FETCH_BARRAGE_REWARD = 15,		-- 领取弹幕奖励
	CROSS_1VN_OPERATE_TYPE_MAX = 100,
}

-- 1VN倒计时类型
CROSS_1VN_TIME_DOWN_TYPE = {
	CROSS_1VN_TIME_DOWN_QUESTION	= 1,			--答题倒计时
	CROSS_1VN_TIME_DOWN_TALENT	= 2,				--选择天赋倒计时
	CROSS_1VN_TIME_DOWN_BATTLE	= 3,				--战斗倒计时
}

-- 攒福特权
ZANFUTEQUAN_OPERA_TYPE = 
{
	INFO = 1,												-- 请求信息
	ONLINE_REWARD = 2,										-- 请求在线时长奖励 param1 = seq
	FINAL_REWARD = 3,										-- 请求终身宝箱奖励 param1 = seq
	BACK_REWARD = 4,										-- 请求返利奖励
}

--镜花水月
JINGHUASHUIYUE_OPERA =
{
	INFO = 1,							-- 请求信息
	ACTIVE = 2,							-- 请求激活 套装id
	UPSTAR = 3,							-- 请求升星 套装id
}

-- 仙侣PK匹配信息下发原因
Cross2V2MatchingInfoNotifyReason =
{
	StartMatching = 1,											-- 开始匹配
	MatchingSucc = 2,											-- 匹配成功
	CancelMatching = 3,											-- 个人手动取消匹配
	TeamMemberExitScene = 4,									-- 队友退出场景
	TeamLeaderCancelMatching = 5,								-- 队长取消匹配
	TeamStatusChange = 6,										-- 队伍状态发生变更
}

CROSS_COUBLE_2V2_MATCH_TYPE = {
	SINGLE_PERSON = 0,              --单人
	COUPLE = 1,                     --仙侣
}

CROSS_COUPLE_2V2_OPERATE_TYPE = {
	-- ENTER_STANDY = 1,				-- 进入候场区场景(暂废弃)
	MATCH = 2,						-- 进行匹配              0 单人     1 情侣
	CANCEL_MATCH = 3,				-- 取消匹配
	ROOM_INFO = 4,					-- 请求房间信息
	ENTER_PKROOM = 5,				-- 进入匹配赛房间
	INFO_KNOCKOUT_GUESS_INFO = 6, 	-- 请求淘汰赛个人已竞猜数据
	GUESS_KNOCKOUT = 7,				-- 淘汰赛竞猜，param1 -> round, param2 -> match_index
	GUESS_KNOCKOUT_REWARD = 8,		-- 领取淘汰赛竞猜奖励，param1 -> round, param2 -> match_index
	REQ_LAST_WINNER = 9,			-- 请求上一场比赛的冠军
	SEND_FLOWERS = 10,				-- 送花 param1 -> round, param2 -> index
	QUERY_KNOCKOUT_MATCH_INFO =11, 	-- 请求淘汰赛对阵表信息
	ENTER_KNOCKOUT_PKROOM = 12,		-- 进入淘汰赛房间
	QUERY_RANK = 13,				-- 查询初赛积分排行榜
	QUERY_KNOCKOUT_RANK = 14,		-- 查询淘汰赛积分排行榜
	SEND_FLOWERS_COUNT = 15,		-- 获取淘汰赛总送花数量
	RETURN_STANDBY = 16,            -- 从战斗场景返回准备场景

	ORIGIN_BEGIN = 100,					-- 下面是请求原服处理的协议
	ACQUIRE_MATCH_COUNT_REWARDS = 101,	-- 领取场次奖励
	COUPLE_INFO = 102                   -- 请求双人积分信息
}

CROSS_COUPLE_2V2_ATCH_TYPE = {
	MATCH = 1, -- 匹配赛
	KNOCKOUT = 2, -- 淘汰赛
}

YINIAN_MAGIC_OPERATE_TYPE = {
	INFO = 1,					-- 请求基本信息
	TASK_INFO = 2,			    -- 任务信息
	CHOOSE_TYPE = 3,			-- 选择类型	param1:type
	TASK_REWARD = 4,		    -- 任务奖励	param1:task_id
	UP_LEVE = 5,				-- 升级	
	LEVEL_REWARD = 6,		    -- 等级奖励	param1:stage
	UP_GRADE = 7,               -- 升阶
	USE_GRADE = 8,              -- 幻化阶级 param1:grade
	FETCH_CHONGZHI_EXP_BLESS = 9, -- 领取充值经验祝福值
}

-- 武魂塔请求
WUHUN_TOWER_OPERATE_TYPE =
{
	WUHUN_TOWER_OPERATE_TYPE_MIN = 0,
	WUHUN_TOWER_OPERATE_TYPE_ITEM_INFO = 1,							-- 通关信息
	WUHUN_TOWER_OPERATE_TYPE_DARE = 2,								-- 挑战关卡		param1:type			param2:seq
	WUHUN_TOWER_OPERATE_TYPE_AUTO_DARE = 3,							-- 一键通关		param1:type
	WUHUN_TOWER_OPERATE_TYPE_RANK_INFO = 4,							-- 排行信息		param1:type
	WUHUN_TOWER_OPERATE_TYPE_MAX = 20,
};

-- 跨服仙侣充值榜
CROSS_MARRY_RANK_OPERA =
{
	RANK_INFO = 1,										-- 请求排名信息
}

-- 盲盒卡包操作类型
MYSTERY_BOX_OPERATE_TYPE =
{
	INFO = 1,										-- 信息
	DRAW = 2,										-- 抽奖			param1:抽奖卡包seq param2:抽奖次数(1/10/50)
	DRAW_TASK = 3,									-- 领取抽奖次数奖励	 param1:配置seq
	HANGBOOK_TASK = 4,								-- 领取图鉴任务	 param1:配置seq
	CONVERT = 5,									-- 积分商店兑换	 param1:配置seq
	LOG = 6,										-- 请求抽奖记录
}

-- 奇闻异物
HANDBOOK_OPERA_TYPE = 
{
	INFO = 1,					-- 请求返回信息
	UPGRADE_LEVEL = 2,			-- 请求图鉴升级		param1:seq
	ZHULING = 3,				-- 请求图鉴注灵		param1:seq
	JIBAN = 4,					-- 请求激活羁绊		param1:jiban_seq
}

--武魂魂阵
WUHUN_FRONT_OPERATE_TYPE = 
{
	ALL_INFO = 1,				         -- 请求全部信息
	SOUL_STONE_ACTIVE = 2,		         -- 请求魂石激活 => 武魂id 魂阵index 魂石index
	SOUL_STONE_STAR = 3,			         -- 请求魂石升星 => 武魂id 魂阵index 魂石index
	SOUL_STONE_LEVEL = 4,		         -- 请求魂石升级 => 武魂id 魂阵index 魂石index
	ENGRAVE_SET = 5,				         -- 请求刻印镶嵌 => 武魂id 魂阵index 魂石index 刻印index 物品id
	ENGRAVE_LEVEL = 6,			         -- 请求刻印升级 => 武魂id 魂阵index 魂石index 刻印index
	SOUL_STONE_GRADE = 7,		         -- 请求魂石升品 => 武魂id 魂阵index 魂石index
	SOUL_FRONT_USE = 8,			         -- 请求幻化魂阵 => 武魂id 魂阵index
	SOUL_STONE_LEVEL_ATTR = 9,	         -- 请求魂石等级属性激活 => 武魂id 魂阵index
	ENGRAVE_LEVEL_ATTR = 10,		         -- 请求刻印等级属性激活 => 武魂id 魂阵index
	SOUL_STONE_GRADE_ATTR = 11,	         -- 请求魂石品级属性激活 => 武魂id 魂阵index
	SOUL_FRONT_USE_CANCEL = 12,	         -- 请求还原魂阵 => 武魂id
}

--寻缘
XUNYUAN_OPERATE_TYPE =
{
	RELEASE = 1,            --发布寻缘信息
	MODIFY = 2,				--修改寻缘信息
	DELETE = 3,				--删除寻缘信息
	INFO = 4,       		--请求寻缘信息
}

CROSS_LAND_WAR_OPERATE_TYPE = {
	BASE_INFO = 1,				-- 基础信息
	ROOM_INFO = 2,				-- 房间信息
	RANK_INFO = 3,				-- 排行信息		param1:rank_type	   param2:rank_param 对应的land_seq
	FETCH_CAPTURE_REWARD = 4,	-- 领取占领奖励	param1:land_seq
	ADDED_DEVOTE_REWARD = 5,	-- 激活贡献奖励
	FETCH_DEVOTE_REWARD = 6,	-- 领取贡献奖励
	SHOP_BUY = 7,				-- 商店购买		param1:seq	 param2:times
	SITUATION_INFO = 8,			-- 战况信息	
	FETCH_ORDER_REWARD = 9,		-- 领取战令奖励 param1:seq			param2:fetch_flag     0普通奖励 1高级奖励 2普通+高级   seq发 >=0的时候，只获得对应seq的奖励  <0就是全部都领取
	CAMP_INFO = 10,				--  阵营信息
	LAND_INFO = 11,				-- 领地信息		param1:land_seq	
	MONSTER_RECORD = 12,	    -- 怪物记录		param1:land_seq		param2:monster_seq 
	ENTER_LAND = 13,            -- 切换阵地  param1:land_seq
	LAND_CALL = 14,             -- 阵地集结		param1:land_seq
}

CROSS_LAND_WAR_RANK_TYPE = {
	LAND_DEVOTE = -1,           -- 领地贡献榜
	DEVOTE = 0,                 -- 贡献榜
	KILL = 1,                   -- 击杀榜
	CAMP_DEVOTE = 2,            -- 阵营积分排行榜
}

CROSS_LAND_WAR_SCORE_TYPE = {
	DEVOTE = 0,                 -- 贡献积分
	CAPTURE = 1,                -- 占领积分
	EXCHANGE = 2,               -- 商城使用的兑换积分
}

CROSS_LAND_WAR_SPECIAL_FLAG_TYPE = {
	DEVOTE_ADDED = 0,
	ORDER_ADDED = 1,
}

-- 阵地战阶段
POSITIONAL_WARFARE_STAGE_TYPE = {
	LOCAl 		= 1,
	TWO 		= 2,
	FOUR 		= 3,
	EIGHT 		= 4,
	SIXTEEN 	= 5,
	THIRTY_TWO  = 6,
}

-- 聊天按钮类型
SOCOETY_BTN_TYPE = {
	NORNAL_FACE 		= 1,  -- 普通表情
	BAG 		= 2,   -- 背包
	BAG2 		= 3,   -- 材料
	TOUZI 		= 4,   -- 骰子
	POS 	= 5,       -- 位置
	ZHAOHU  = 6,       -- 招呼(拍一拍)
	SPEAK  = 7,       -- 快捷发言
	BIG_FACE  = 8,  -- 特殊表情
	HONGBAO  = 9,  -- 红包
	ZHAOJI  = 10,  -- 召集
}

-- 紫云秘录操作类型
SECRET_RECORD_OPERATE_TYPE = {
	CHANGE_DAY = 1,  -- 切换天
}

-- 委托任务
ASSIGN_TASK_OPERATE_TASK = {
	INFO = 0,--请求委托任务信息
	ASSIN = 1,--开始派遣任务 param1 = 数组下标[0-4] param_list1外形类型列表 param_list2[外形唯一ID或仙盟上架外形数组下标]
	TASK_REWARD = 2,--领取任务奖励 param1:task_id
	TASK_REFRESH = 3,--刷新任务 param1[0:使用免费刷新 1:使用道具刷新 2:使用仙玉刷新]
	QUICK_FINISH = 4,--快速完成任务 param1-任务id
}

-- 委托任务--仙盟上架外形
ASSIGN_RENT_OPERATE_TASK = {
	INFO = 0,--请求全部仙盟上架外形信息
	SELF_RENT = 1,--请求自己的上架外形信息
	RENT_FASHION = 2,--上架外形 param1--外形类型[1:灵宠;2:坐骑;3:天神化形] param2-外观系统配置唯一索引
	UNRENT_FASHION = 3,--下架外形 自己已上架的列表下标
	GUILD_RECORD = 4,--请求仙盟上架记录
	MY_RECORD = 5,--个人雇佣信息记录
}

-- 天山修炼副本请求（历练副本）
EXP_WEST_OPERATE_TYPE =
{
	EXP_WEST_OPERATE_TYPE_MIN = 0,
	EXP_WEST_OPERATE_TYPE_BASE_INFO = 1,							-- 通关信息
	EXP_WEST_OPERATE_TYPE_DARE = 2,									-- 挑战关卡		param1:level
	EXP_WEST_OPERATE_TYPE_AUTO_DARE = 3,							-- 一键通关
	EXP_WEST_OPERATE_TYPE_RANK_INFO = 4,							-- 排行信息		param1:level
	EXP_WEST_OPERATE_TYPE_CHOOSE_CARD = 5,							-- 抽卡			param1:wave		param2:index
	EXP_WEST_OPERATE_TYPE_USE_SKILL = 6,							-- 使用技能	
	EXP_WEST_OPERATE_TYPE_TIME_INFO = 7,							-- 查看当前的副本通关时间	 param1:level
	EXP_WEST_OPERATE_TYPE_MAX = 999,
}

--竞技场1v1排行榜操作类型.
CHALLENGE_RANK_LIST_TYPE =
{
	CHALLENGE_RANK_LIST_TYPE_LIKE = 1,					--点赞 paraml:uid.
	CHALLENGE_RANK_LIST_TYPE_INFO = 2,					--请求排行榜信息.
	CHALLENGE_RANK_LIST_TYPE_COUNT_REWARD = 3,			--领取次数奖励 paraml:index.
	CHALLENGE_RANK_LIST_TYPE_RANK_REWARD = 4,			--领取排名奖励 paraml:index.
	CHALLENGE_RANK_LIST_TYPE_CHALLENGE_INFO = 5,		--竞技场信息.
	CHALLENGE_RANK_LIST_TYPE_ZUI_QIANG_XIAN_ZUN =  6	--最强仙尊信息
}

--天梯争霸操作类型.
CROSS_CHALLENGE_RANK_LIST_TYPE =
{
	CHALLENGE_RANK_LIST_TYPE_LIKE = 1,								-- 点赞 param1: uid
	CHALLENGE_RANK_LIST_TYPE_INFO = 2,								-- 请求排行榜信息
	CHALLENGE_RANK_LIST_TYPE_COUNT_REWARD = 3,						-- 领取次数奖励 param1: index
	CHALLENGE_RANK_LIST_TYPE_RANK_REWARD = 4,						-- 领取排名奖励 param1: index
	CHALLENGE_RANK_LIST_TYPE_CHALLENGE_INFO = 5,					-- 竞技场信息
	CHALLENGE_RANK_LIST_TYPE_FIGHT_BACK	= 6,                        -- 请求反击信息 uuid	
	CHALLENGE_RANK_LIST_TYPE_SKIP = 7,                              -- 跳过信息
	CHALLENGE_RANK_LIST_TYPE_FIGHT_NUM = 8,                         -- 请求秒杀多次 param1: uuid param2：num
}

--Boss战令操作类型
BOSS_ZHANLING_OPERATE_TYPE =
{
	HUNT_MONSTER_ORDER_OPERA_TYPE_INFO = 1,				-- 请求信息
	HUNT_MONSTER_ORDER_OPERA_TYPE_GET_REWARD = 2,		-- 领取奖励 param1：seq
	HUNT_MONSTER_ORDER_OPERA_TYPE_ONE_KEY = 3,			-- 一键领取
}

--技能突破直购操作类型
OA_SKILL_BREAK_RMB_BUY_OPER_TYPE =
{
	OA_SKILL_BREAK_RMB_BUY_OPER_TYPE_MIN = 0,
	OA_SKILL_BREAK_RMB_BUY_OPER_TYPE_INFO = 1,			-- 请求信息
	OA_SKILL_BREAK_RMB_BUY_OPER_TYPE_MAX = 2,
}

-- 打地鼠操作
WHACK_MOLE_OPERATE_TYPE =
{
	WHACK_MOLE_OPERATE_TYPE_MIN = 0,
	WHACK_MOLE_OPERATE_TYPE_START = 1,					-- 开始
	WHACK_MOLE_OPERATE_TYPE_END = 2,					-- 结束
	WHACK_MOLE_OPERATE_TYPE_TOUCH = 3,					-- 点击		param1:index
	WHACK_MOLE_OPERATE_TYPE_RANK_INFO = 4,				-- 排行榜
	WHACK_MOLE_OPERATE_TYPE_BASE_INFO = 5,				-- 基础信息
	WHACK_MOLE_OPERATE_TYPE_FETCH_JOIN_REWARD = 6,		-- 领取参与奖励
	WHACK_MOLE_OPERATE_TYPE_MAX = 100,
}

--通用记录操作
COMMON_GUIDE_OPERATE_TYPE =
{
	COMMON_GUIDE_OPERATE_TYPE_MIN = 0,
	COMMON_GUIDE_OPERATE_TYPE_INFO = 1,					-- 信息
	COMMON_GUIDE_OPERATE_TYPE_SET = 2,					-- 设置	param1:bit param:is_set
	COMMON_GUIDE_OPERATE_TYPE_MAX = 100,
}

--龙云战令操作.
COUNTRY_OPERATE_TYPE =
{
	COUNTRY_OPERATE_TYPE_MIN = 0,

	COUNTRY_OPERATE_TYPE_BASE_INFO = 1,				-- 基础信息
	COUNTRY_OPERATE_TYPE_TASK_INFO = 2,				-- 任务信息
	COUNTRY_OPERATE_TYPE_ORDER_INFO = 3,			-- 战令信息
	COUNTRY_OPERATE_TYPE_FETCH_TASK_REWARD = 4,		-- 任务奖励		param1:index
	COUNTRY_OPERATE_TYPE_FETCH_ORDER_REWARD = 5,	-- 战令奖励		param1:seq			param2:fetch_flag, 0领取普通奖励, 1领取高级奖励, 3领取普通高级奖励
	COUNTRY_OPERATE_TYPE_FETCH_FAME_REWARD = 6,		-- 名望奖励		param1:index

	COUNTRY_OPERATE_TYPE_MAX = 100,
}

--龙云战令类型.
COUNTRY_OPERATE_SPECIAL_FLAG_TYPE = {
	DEVOTE_ADDED = 0,
	ORDER_ADDED = 1,
}

-- 技能提示展示分类
SKILL_BOX_TYPE = {
	NORMAL = 0,											-- 角色技能
	QI_CHONG_SKILL = 1,									-- 骑宠类型
	TIANSHEN_PASSSKILL = 2,								-- 天神被动
	TIANSHEN_SKILL = 3,									-- 天神主动	
	CUI_ZHUO_SKILL = 4,									-- 淬琢	
	TIANSHEN_MO = 5,									-- 天神入魔	
	TIANSHEN_WUQI = 6,									-- 天神神器
	ARTIFACT_SKILL = 7,									-- 双修
	CONTROL_BEASTS = 8,									-- 幻兽
}

-- 天神宝匣.
TIANSHEN_BOX_OPER_TYPE = {
	DRAW = 1,											-- 抽奖.
	INFO = 2,											-- 信息.
	UPLEVEL = 3,										-- 升品.
}

-- 神灵抽奖.
GODHOOD_DRAW_OPERATE_TYPE = {
	GODHOOD_DRAW_OPERATE_TYPE_INFO = 1,											-- 信息 
	GODHOOD_DRAW_OPERATE_TYPE_DRAW = 2,											-- 抽奖 param1:times 
	GODHOOD_DRAW_OPERATE_TYPE_CONVERT = 3,										-- 兑换 param1:seq  
	GODHOOD_DRAW_OPERATE_TYPE_FETCH_TIMES_REWARD = 4,							-- 次数奖励
	GODHOOD_DRAW_OPERATE_TYPE_RECORD_INFO = 5,									-- 大奖记录
	GODHOOD_DRAW_OPERATE_TYPE_UP_GRADE = 6,									    -- 升档\升品
}

-- 战力福利
CAPABILITY_WEAL_OPER_TYPE = {
	CAPABILITY_WEAL_OPER_TYPE_TASK_REWARD = 1,			-- 领取任务奖励
	CAPABILITY_WEAL_OPER_TYPE_FINAL_REWARD = 2,			-- 领取最终奖励
}

CROSS_NIGHT_FIGHT_OPERATE_TYPE = {
	CROSS_NIGHT_FIGHT_OPERATE_TYPE_INFO = 1,			-- 信息
	CROSS_NIGHT_FIGHT_OPERATE_TYPE_COUNT_REWARD = 2,	-- 次数奖励 param1: index
}

-- 肉身突破
EQUIP_BODY_OPERATE_TYPE = {
	BASE_INFO     = 1,					    -- 基础信息
	BREAK_THROUGH = 2,						-- 突破			param1:seq
}

EQUIP_BODY_TYPE = {
	NORMAL     = 0,					    -- 基础肉身
	SPECIAL    = 1,						-- 特殊肉身 特殊套装
}

--开服投资操作类型
OPENSERVER_INVEST_OPERATE_TYPE =
{
	OGA_INVEST_OPERATE_TYPE_INFO = 1,				-- 请求信息
	OGA_INVEST_OPERATE_TYPE_FETCH_TASK_REWARD = 2,		-- 一键领取 param1：type
}

--珍稀装备位置对应模型ID
RARE_EQUIP_MODEL = {
	[GameEnum.EQUIP_INDEX_TOUKUI] = 16567,		-- 头盔
	[GameEnum.EQUIP_INDEX_YIFU] = 16566,		-- 衣服
	[GameEnum.EQUIP_INDEX_KUZI] = 16568,		-- 裤子
	[GameEnum.EQUIP_INDEX_XIANLIAN] = 16570,	-- 仙链
	[GameEnum.EQUIP_INDEX_XIEZI] = 16569,		-- 鞋子
	[GameEnum.EQUIP_INDEX_XIANZHUI] = 16565,	-- 仙坠
	[GameEnum.EQUIP_INDEX_XIANFU] = 16571,		-- 护腕
}

--转职操作类型
ROLE_ZHUANZHI_OPER_TYPE =
{
	ROLE_ZHUANZHI_OPER_TYPE_FETCH_REWARD = 1,		-- 领取奖励
	ROLE_ZHUANZHI_OPER_TYPE_SEND_TASK_INFO = 2,		-- 发送信息 
	ROLE_ZHUANZHI_OPER_TYPE_LIGHT = 3,				-- 转职点亮 
	ROLE_ZHUANZHI_OPER_TYPE_ONE_KEY_LIGHT = 4,		-- 一键点亮
	ROLE_ZHUANZHI_OPER_TYPE_SEND_BASE_INFO = 5,		-- 发送基础信息
	ROLE_ZHUANZHI_OPER_TYPE_ZHUANZHI = 6,			-- 转职
	ROLE_ZHUANZHI_OPER_TYPE_CHOOSE_TYPE = 7,		-- 选择神魔			param1:type
	
}

ZHUANZHI_GOD_AND_DEMONS_TYPE ={
	NO = -1,          -- 未选择
	GOD = 0,           -- 神
	DEMONS = 1,		-- 魔
}

DISPLAY_SYSTEM_TYPE = {
	Fashion = 1,  	--时装
	Mount = 2,    	--坐骑
	Pet = 3,      	--宠物
	Huakun = 4,   	--化坤
	WuShen = 5,   	--武神
	FaZheng = 6,  	--法阵
	CangMing = 7, 	--沧溟
	TianShen = 8, 	--天神
	Beasts = 9, 	--幻兽
}

-- 与配置表中 soul_ring_seq 对应
SOUL_RING_TYPE = {
	SoulRing1 = 0,                                  -- 魂环1
	SoulRing2 = 1,                                  -- 魂环2
	SoulRing3 = 2,                                  -- 魂环3
	SoulRing4 = 3,                                  -- 魂环4
	SoulRing5 = 4,                                  -- 魂环5
	SoulRing6 = 5,                                  -- 魂环6
	SoulRing7 = 6,                                  -- 魂环7
	SoulRing8 = 7,                                  -- 魂环8
}

-- 魔王仙藏
LORD_EVERYDAY_SHOP_TYPE =
{
	SHOP_FLUSH = 1,       --商店刷新
	SHOP_BUY = 2 ,        --商店购买（param1 商品索引  param2 数量）
}

-- 累计充值操作类型枚举
CUMULATE_RECHARGE_OPERATE_TYPE = {
	FETCH 					= 1,		-- 领取奖励
	MAX 					= 2,
}

-- 时装兑换商店操作
FASHION_EXCHANGE_SHOP_OPER_TYPE = {
	SHAPE_SHOP_OPER_TYPE_BUY = 1,					--购买 param1: seq param2: num.
	SHAPE_SHOP_OPER_TYPE_INFO = 2,					--信息 param1: shop_type.
}

-- 抽奖类弹窗物品角标品质枚举
DRAW_ITEM_CORNER_QUALITY_TYPE = 
{
	NORMAL 		= 0,			-- 普通
	ZHEN_PIN 	= 1,			-- 珍品
	SHENG_PIN 	= 2,			-- 圣品
	SHEN_PIN 	= 3,			-- 神品
}

-- 天神神灵殿枚举
TIANSHEN_HALL_ENUM = {
	MAX_ITEM_COUNT = 8,			-- 最大天神殿个数
	MAX_INDEX_COUNT = 10,		-- 天神殿最大上阵个数
}

-- 充值卷操作请求
VIRUAL_GOLD_OPERA_TYPE = {
	TaskAllGold = 1,        --提取灵玉
	FinishTask = 2,        	--完成任务
	ExchangeGold = 3,       --档位兑换灵玉
}

TIANSHEN_HALL_OPERATE_TYPE = {
	TIANSHEN_HALL_OPERATE_TYPE_MIN = 0,								--
	TIANSHEN_HALL_OPERATE_TYPE_BASE_INFO = 1,							--// 基础信息
	TIANSHEN_HALL_OPERATE_TYPE_ITEM_INFO = 2,							--// 神殿信息
	TIANSHEN_HALL_OPERATE_TYPE_UP_ORDER = 3,							--// 神殿升阶		param1:seq
	TIANSHEN_HALL_OPERATE_TYPE_GO_HALL = 4,								--// 神殿上阵		param1:seq		param2:index  param3:tianshen_index
	TIANSHEN_HALL_OPERATE_TYPE_USE_SEQ = 5,								--// 神殿选择		param1:seq
	TIANSHEN_HALL_OPERATE_TYPE_ACTIVE = 6,									--// 神殿激活		param1:seq
	TIANSHEN_HALL_OPERATE_TYPE_MAX = 100,
}

-- 开服活动扩展 - 操作
OGA_EXTEND_OPERATE_TYPE_ENUM =
{
	FETCH_DAILY_REWARD 		= 1,	-- 领取每日奖励 
	FETCH_TASK_REWARD		= 2,    -- 领取任务奖励 p1:任务seq
	DRAW 					= 3,    -- 抽奖 
	FETCH_CUMULATE_REWARD 	= 4,    -- 领取累充奖励 p1:累充奖励索引
}

-- 开服活动扩展 - 操作
OGA_EXTEND_RMB_TYPE_ENUM =
{
	NORMAL	= 212,	-- 单个购买
	ONE_KEY = 213,	-- 一键购买类型
}

-- 幻梦秘境副本请求（历练活动副本）
OA_DREAM_SECRET_OPERATE_TYPE =
{
	OA_DREAM_SECRET_OPERATE_TYPE_BASE_INFO = 1,					-- 通关信息
	OA_DREAM_SECRET_OPERATE_TYPE_DARE = 2,						-- 挑战关卡
	OA_DREAM_SECRET_OPERATE_TYPE_RANK_INFO = 3,					-- 排行信息
	OA_DREAM_SECRET_OPERATE_TYPE_CHOOSE_CARD = 4,				-- 抽卡			param1:wave			param2:index
	OA_DREAM_SECRET_OPERATE_TYPE_USE_SKILL = 5,					-- 使用技能		param1:target_id	param2:pos_x	param3:pos_y
	OA_DREAM_SECRET_OPERATE_TYPE_BUY_TIME = 6,					-- 购买时间		param1:0:购买一次  2:一键购买
	OA_DREAM_SECRET_OPERATE_TYPE_FETCH_WAVE_REWARD = 7,			-- 领取波次奖励  param1:wave
}

--天财地宝
TREASURE_OPERATE_TYPE =
{
	TREASURE_OPERATE_TYPE_MIN = 0,
	TREASURE_OPERATE_TYPE_LOGIN_GIFT_INFO = 1,					-- 登录有礼 - 信息
	TREASURE_OPERATE_TYPE_LOGIN_GIFT_COMMON_REWARD = 2,			-- 登录有礼 - 领取普通奖励	param2: day_index
	TREASURE_OPERATE_TYPE_SHOUCHONG_INFO = 3,					-- 首充送礼 - 信息
	TREASURE_OPERATE_TYPE_SHOUCHONG_FETCH = 4,					-- 首充送礼 - 领取奖励
	TREASURE_OPERATE_TYPE_LEICHONG_INFO = 5,					-- 累计充值 - 信息
	TREASURE_OPERATE_TYPE_LEICHONG_FETCH = 6,					-- 累计充值 - 领取奖励		param1: grade param2: id
	TREASURE_OPERATE_TYPE_WOYAOSHENQI_INFO = 7,					-- 拼图领奖 - 信息
	TREASURE_OPERATE_TYPE_WOYAOSHENQI_TASK_REWARD = 8,			-- 拼图领奖 - 领取任务奖励	param1: grade param2: id
	TREASURE_OPERATE_TYPE_WOYAOSHENQI_GRADE_REWARD = 9,			-- 拼图领奖 - 领取阶段奖励	param1: grade param2: id
	TREASURE_OPERATE_TYPE_WOYAOSHENQI_COMPOSE = 10,				-- 拼图领奖 - 合成  -- 注意：此请求类型已弃用
	TREASURE_OPERATE_TYPE_WOYAOSHENQI_BUY = 11,					-- 拼图领奖 - 购买礼包		param1: grade
	TREASURE_OPERATE_TYPE_HUNZHEN_SHOP_INFO = 12,				-- 限时抢购 - 信息
	TREASURE_OPERATE_TYPE_HUNZHEN_SHOP_BUY = 13,				-- 限时抢购 - 购买			param1: grade param2: seq param3: count
	TREASURE_OPERATE_TYPE_BASE_INFO = 14,						-- 天财地宝基础信息
	TREASURE_OPERATE_TYPE_CENTER_TRIAL_FB = 15,					-- 进入试炼副本
	TREASURE_OPERATE_TYPE_UPLEVEL_DRAGON_EGG = 16,				-- 升级龙蛋
	TREASURE_OPERATE_TYPE_FETCH_DRAGON_EGG_REWARD = 17,			-- 领取龙蛋奖励
	TREASURE_OPERATE_TYPE_SPECIAL_DROP_INFO = 18,				-- 特殊掉落信息
	TREASURE_OPERATE_TYPE_SPECIAL_FETCH_DAILY_REWARD = 19,		-- 领取每日奖励
	TREASURE_OPERATE_TYPE_WOYAOSHENQI_TASK_CLIENT = 20,			-- 拼图领奖 - 客户端请求	param1: task_param1
	TREASURE_OPERATE_TYPE_FB_BOSS_DEAD_INFO = 21,				-- 试炼副本boss死亡信息
	TREASURE_OPERATE_TYPE_UPSTAR_GIFT_INFO = 22,				-- 升星赠礼消息
	TREASURE_OPERATE_TYPE_UPSTAR_GIFT_FREE_REWARD = 23,			-- 升星赠礼免费奖励 param1: seq
	TREASURE_OPERATE_TYPE_UPSTAR_GIFT_PAY_REWARD = 24,			-- 升星赠礼付费奖励 param1: seq
	TREASURE_OPERATE_TYPE_WALK_TOGETHER_INFO = 25,				-- 携手同行信息
	TREASURE_OPERATE_TYPE_WALK_TOGETHER_REWARD = 26,			-- 携手同行奖励				param1: seq
	TREASURE_OPERATE_TYPE_WALK_TOGETHER_INVITE = 27,			-- 携手同行邀请				param1: uid
	TREASURE_OPERATE_TYPE_WALK_TOGETHER_ACCEPT = 28,			-- 携手同行接受邀请			param1: inviter_uid
	TREASURE_OPERATE_TYPE_WALK_TOGETHER_FRIEND_INFO = 29,		-- 携手同行好友信息
	TREASURE_OPERATE_TYPE_CONVERT_SHOP_INFO = 30,				-- 兑换商店信息
	TREASURE_OPERATE_TYPE_CONVERT_SHOP_CONVERT = 31,			-- 兑换商店兑换				param1: seq param2 : num
	TREASURE_OPERATE_TYPE_FLOWING_INFO = 32,					-- 川流不息信息
	TREASURE_OPERATE_TYPE_FLOWING_REWARD = 33,					-- 领取川流不息奖励			param1: seq
	TREASURE_OPERATE_TYPE_SCHEME_INFO = 34,						-- 奕者谋定信息
	TREASURE_OPERATE_TYPE_SCHEME_CHOOSE_TEAM = 35,				-- 奕者谋定选择阵营			param1: team_seq
	TREASURE_OPERATE_TYPE_PURSUIT_INFO = 36,					-- 迷影寻踪信息
	TREASURE_OPERATE_TYPE_PURSUIT_CHOOSE = 37,					-- 迷影寻踪选择				param1: seq
	TREASURE_OPERATE_TYPE_PURSUIT_TURN_REWARD = 38,				-- 迷影寻踪轮次奖励			param1: turn
	TREASURE_OPERATE_TYPE_PURSUIT_TASK_REWARD = 39,				-- 迷影寻踪任务奖励			param1: seq
	TREASURE_OPERATE_TYPE_PURSUIT_NEXT_TURN = 40,				-- 迷影寻踪下一轮			param1: seq

	CAN_LINGQU = 0,    --可领取
	NOT_OVER = 1,      --未完成
	HAS_LINGQU = 2	  --已领取
}

-- 豪礼万丈
OA_HAOLI_WANZHANG_OPERATE_TYPE = 
{
	OA_HAOLI_WANZHANG_OPERATE_TYPE_INFO = 1,						-- 信息
	OA_HAOLI_WANZHANG_OPERATE_TYPE_REWARD = 2,						-- 领取奖励 param1:seq
	OA_HAOLI_WANZHANG_OPERATE_TYPE_EVERYDAY_REWARD = 3,				-- 每日领取
}

-- 累充豪礼
OA_HAOLI_WANZHANG2_OPERATE_TYPE = 
{
	OA_HAOLI_WANZHANG2_OPERATE_TYPE_INFO = 1,						-- 信息
	OA_HAOLI_WANZHANG2_OPERATE_TYPE_REWARD = 2,						-- 领取奖励 param1:seq
	OA_HAOLI_WANZHANG2_OPERATE_TYPE_EVERYDAY_REWARD = 3,			-- 每日领取
}

--新节日活动 - 节日祈愿
OA_NEWYEAR_PRAYER_OPERATE_TYPE = {
	INFO = 1,						-- 请求信息	
	GET_REWARD = 2,					-- 领取单个祈愿值的奖励		param1: seq
	BUY_PRAYER = 3,					-- 购买祈愿值		       param1: seq
	GET_TASK_REWARD = 4,		    -- 领取任务奖励		       param1: task_id
	ONE_KEY_REWARD = 5,				-- 一键领取
}

NEWYEAR_PRAYER_REWARD_FLAG = {
	NOT_GET = 0,                     -- 未领取
	GET_NORMAL = 1,                  -- 领取了普通奖励
	GET_ADVANCED = 2,                -- 已经领取全部奖励
}

-- 新节日活动-集福活动
OA_COLLECT_CARD_OP_TYPE = {
	INFO = 1,					-- 活动信息
	SEND = 2,					-- 向某人发送道具 param1:uuid, param2:item_id
	GAIN = 3,					-- 向某人索要道具 param1:uuid, param2:item_id
	GET_REWARD = 4,				-- 领取奖励
	LIST_INFO = 5,				-- 他人请求列表的信息
	REFUSE = 6,					-- 拒绝 param1:uuid
}

-- 新节日活动-节日收集
OA_COLLECT_ITEM_OP_TYPE = {
	INFO = 1,					-- 活动信息
	PERSONAL_REWARD = 2,		-- 领取个人奖励
	JIEYI_REWARD = 3,			-- 领取结义奖励
}

-- 新节日活动-特惠商店
OA_NEW_FESTIVAL_TEHUI_SHOP_INFO = {
	INFO = 1,
	XIANYU_BUY = 2,
	EXCHANGE_BUY = 3,			--seq, num
}

-- 新节日活动-消费返利
OA_NEW_FESTIVAL_CONSUME_REBATE_INFO = {
	INFO = 1,
}

-- 新节日活动 - 登录奖励
OA_NEW_FES_ACT_DENGLU_OPERATE_TYPE = {
	INFO = 1,				-- 请求信息
	DRAW = 2,				-- 抽奖
}

-- 新节日活动 - 节日达人排行
OA_NEW_FES_ACT_RANK_OPERATE_TYPE = {
	INFO = 1,				-- 请求排名信息
}

-- 新节日活动 - 节日抽奖
OA_NEW_FES_ACT_SD_OPERATE_TYPE = {
	INFO = 0,				-- 请求信息
	DRAW = 1,				-- 抽奖
	GET_REWARD = 2,			-- 领取累计奖励
}

-- 新节日活动 - 节日活动累充
NA_RECHARGE_SUM_OPERATE_TYPE=
{
	FETCH=0,						--领取累充奖励 
	INFO=1							--请求信息
}

-- 限时狂欢 魔物降临
RA_MOWU_JIANGLIN_OPERATE_TYPE =
{
	RA_MOWU_JIANGLIN_OPERATE_TYPE_BOSS_DEAD_INFO = 1, -- 发送死亡信息
}

LAND_WAR_FB_OPERATE_TYPE = {
	INFO = 1,                    -- 基础信息
	ENTER_SCENE = 2,		     -- 进入场景 param1:scene_id
	FETCH_PASS_REWARD = 3,		 -- 领取通过奖励
}

-- 跨服空战操作类型
CROSS_AIR_WAR_OPERATE_TYPE = 
{
	BASE_INFO				= 1,			-- 基础信息
	AUCTION_INFO			= 2,			-- 竞拍信息
	ROLE_BASE_INFO			= 3,			-- 玩家基础信息
	AUCTION					= 4,			-- 竞拍 param1:index param2:price
	FETCH_SCORE_REWARD		= 5,			-- 领取积分奖励
	FETCH_AUCTION_REWARD	= 6,			--// 领取拍卖奖励
	MONSTER_INFO			= 7,			--// 怪物信息
	STATUS_INFO				= 8,			--// 活动信息
}

-- 跨服红包天降
CROSS_RED_PAPER_FALLING_OPERATE_TYPE = 
{
	CROSS_RED_PAPER_FALLING_OPERATE_TYPE_ROLE_INFO = 1,			-- 获取玩家数据信息
	CROSS_RED_PAPER_FALLING_OPERATE_TYPE_ACTIVITY_INFO = 2,		-- 获取活动数据信息
	CROSS_RED_PAPER_FALLING_OPERATE_TYPE_ADD_ROUND = 3,			-- 增加当前活动轮次
	CROSS_RED_PAPER_FALLING_OPERATE_TYPE_ENTER = 4,				-- 参加活动进入指定场景位置
	CROSS_RED_PAPER_FALLING_OPERATE_TYPE_GET_REWARD = 5,		-- 点击红包
	CROSS_RED_PAPER_FALLING_OPERATE_TYPE_USE_ITEM_ADD_ACTIVITY = 6,			-- 使用道具增加一场新活动
	CROSS_RED_PAPER_FALLING_OPERATE_TYPE_JOIN_GET_REWARD  = 7,				--切换抢红包界面 0: 关闭 1: 打开
}

-- 悬赏任务操作类型
BOUNTY_OPERATE_TYPE =
{
	BOUNTY_OPERATE_TYPE_MIN = 0,
	BOUNTY_OPERATE_TYPE_ACCEPT = 1,				--// 接受悬赏单	param1 索引		
	BOUNTY_OPERATE_TYPE_GET_NOTE_REWARD = 2,	--// 领取手记奖励 param1 索引
	BOUNTY_OPERATE_TYPE_ONE_KEY_FINISH = 3,		--// 一键完成		param1 索引
	BOUNTY_OPERATE_TYPE_INFO = 4,				--// 请求信息
	BOUNTY_OPERATE_TYPE_REFRESH = 5,			--// 刷新手记
	BOUNTY_OPERATE_TYPE_MAX= 100,
}

-- 悬赏任务枚举
BOUNTY_OPERATE_ENUM =
{
	BOUNTY_LIST_MAX_NUM = 32,				--// 悬赏单任务最大个数
	BOUNTY_LIST_SHOW_NUM = 4,				--// 悬赏单列表个数	
	BOUNTY_TASK_STATUS_NOT_ACCEPT = 0,		--// 接取任务状态（未接取）
	BOUNTY_TASK_STATUS_ACCEPT = 1,			--// 接取任务状态（已接取）
	BOUNTY_TASK_STATUS_FINISH = 2,			--// 接取任务状态（已领取）
}

-- boss入侵
CROSS_BOSS_STRIKE_OPERATE_TYPE = {
	BASE_INFO = 1,			-- 基础信息
	ROLE_BASE_INFO = 2,		-- 玩家基础信息
	SET_GUWU_FLAG = 3,      -- 设置自动鼓舞状态 param1:type
	GUWU = 4,               -- 鼓舞				param1:type
	UP_BOSS_COLOR = 5,      -- boss升品			param1: 次数
}

CROSS_BOSS_STRIKE_GUWU_TYPE = {
	GUWU_TYPE_COIN = 0,
	GUWU_TYPE_GOLD = 1,
}

CROSS_BOSS_STRIKE_STATUS = {
	STATUS_END = 0,    -- 场景内活动结束
	STATUS_WAIT_QUESTION = 1,  -- 等待答题
	STATUS_QUESTION = 2,    -- 答题中
	STATUS_QUESTION_SHOW = 3, -- 答题展示 1题后 展示 会切回大体中
	STATUS_WAIT_BOSS = 4,   --等待boss刷新
	STATUS_BOSS = 5,   -- boss 剩余存活时间
	STATUS_BOSS_END = 6   -- boss结束展示  宝箱时间 最后跳回 end
}

-- 百亿补贴
TEN_BILLION_SUBSIDY_OPERATE_TYPE = {
	FETCH_HIGNMEMBER_MEMBER_LEVEL_DAILY_REWARD = 1,												-- 领取高级会员每日奖励
	FETCH_CUMULATE_ORDER_NUM_REWARD = 2,														-- 领取累计订单数量奖励每日奖励(免单券)
	FETCH_FIRST_PAY_SINGLE_REWARD = 3,															-- 领取首付单笔奖励(免单券)
	USE_FREE_OR_DISCOUNT_TICKET_RMB_BUY = 4,													-- 使用免单券或者折扣券（价格减额至0)直购 p1:直购类型 p2:直购索引 ticket_type:[1:免单券;2:折扣券] param4:折扣券索引
	FETCH_RETURN_REWARD = 5,																	-- 领取满额返现奖励
	USE_TRY_TICKET = 6,																			-- 使用试用券 p1:商品索引
	DISCOUNT_EXPEND_ONCE = 7,																	-- 膨胀折扣券一次 p1:折扣券索引（数据索引）
	ONE_KEY_DISCOUNT_EXPEND = 8,																-- 一键膨胀所有折扣券 p1:目标膨胀次数
	FETCH_FIRST_OPEN_VIEW_REWARD = 9,															-- 领取每日首次打开界面奖励
	PAY_ONE_GET_MORE_SHOP_REFRESH = 10,															-- 买一得多商店刷新
	START_TIME_LIMITED_DISCOUNT_SHOP = 11,														-- 开始折扣商店限时折扣计时
	SHARE_HIGH_PRICE_RMB_BUY_HELP = 12,															-- 大额直购分享助力
	HIGH_PRICE_RMB_BUY_HELP = 13,																-- 大额直购助力
	PARTICIPATE_BUY = 14,																		-- 参与拼团
	PARTICIPATE_Info = 15,																		-- 请求拼团总信息
	FIRSTOPENVIEW = 16,																			-- 首次打开界面
	FETCH_PARTICIPATE_REWARD = 17,																-- 领取团购奖励		p1:item_seq
	INVITE_PARTICIPATE_REWARD = 18,																-- 邀请参与拼团		p1:item_seq
	GOLD_SHOP_BUY = 19,																			-- 灵玉商店购买		p1:item_seq p2:buy_count
	SHOP_CART_INFO  = 20,																		-- 购物车信息		
	SHOP_CART   = 21,																			-- 购物车操作		p1:type	p2:seq	p3:num
	SHOP_CART_CHOOSE   = 22,																	-- 购物车勾选		p1:type	p2:seq	p3:is_choose p2传-1时，批量处理
	SHOP_CART_Lock   = 23,																		-- 购物车锁定		
	HIGH_PRICE_FETCH_REDUCE_REWARD = 24,														-- 大额直购减免次数奖励 p1:seq
	ONE_YUAN_REWARD = 25,																		-- 领取一元福利奖励		
	GuideTicket = 26,																			-- 第一次进入界面领取券		
}

BILLION_SUBSIDY_SHOP_TYPE_MAX = 5            	-- 百亿补贴-购物车类型最大数量
BILLION_SUBSIDY_SHOP_TYPE_SEQ_MAX = 64          -- 百亿补贴-购物车每个类型种类最大数量
BILLION_SUBSIDY_SHOP_COUNT_MAX = 128            -- 百亿补贴-购物车单个物品最大数量
BILLION_SUBSIDY_GUIDE_ID = 60 					--百亿补贴引导id
DRPT_MAX_PLAYER_NUM = 5								--百亿补贴多人拼团最大玩家信息数量

-- 折扣券类型
TEN_BILLION_SUBSIDY_DISCOUNT_COUPON_TYPE = {
	FULL_DISCOUNT_COUPON = 1,		-- 满减券
	DIRECT_DISCOUNT_COUPON = 2,		-- 立减券
	FREE_DISCOUNT_COUPON = 3,		-- 免单券
}

MONSTER_QTE_TYPE = {
	ESOTERICAL_SKILL_SLOT = 7,    -- 秘笈技能特殊处理 该技能走qte不走秘笈显示
	MONSTER_QTE_TYPE_ESOTERICA = 1,
	MONSTER_QTE_TYPE_NEWPLAYER_FB = 2,  --新手副本
}

--物品上架货币类型
AUCTION_PRICE_TYPE = {
	SILVER_TICKET = 1, -- 银票
	GOLD = 2,          -- 灵玉      
	GOLD_BIND = 3,     -- 绑玉
}

AUCTION_PRICE_TYPE_ICON = {
	[1] = "a3_huobi_xianyu",  --暂用  这个目前没有货币类型
	[2] = "a3_huobi_xianyu",  -- 灵玉
	[3] = "a3_huobi_bangyu",  -- 绑玉
}

SHIZHUANG_DYE_ENUM = {
	MSG_MAX_SHIZHUANG_DYEING_COUNT = 100;			--// 时装染色最大数量
	MSG_MAX_DYEING_PART_COUNT = 6;					--// 染色部位最大数量
	MSG_CUR_DYEING_PART_COUNT = 4;					--// 染色部位当前数量
	MSG_MAX_DYEING_PROJECT_COUNT = 5;				--// 染色方案最大数量
	MSG_MAX_DYEING_PROJECT_NAME = 12;				--// 时装染色方案名字最大字数
	MSG_MAX_DYEING_PROJECT_INFO = 64;				--// 时装染色方案信息最大字数
	DYEING_INFO = 1,								--// 染色信息
	DYEING_PROJECT_OPEN = 2,						--// 开启染色方案	param1: project_id
	DYEING_SHARE = 3,								--// 分享染色方案	param1: seq param2: project_id
	DYEING_OTHER_INFO = 4,							--// 其他人染色信息 param1: uid
	DYEING_OPER_TYPE_USE_PROJECT = 5,				--// 使用染色方案 param1: seq param2: project_id
}

AUCTION_ITEM_TYPE = {
	ITEM = 0,           -- 道具
	BIND_GOLD = 1,      -- 绑玉
}

ROLE_DIY_APPEARANCE_TYPE = {
	CREATE_ROEL = 1,
	CHANGE_DIY = 2,
}

MONSTER_SEC_KILL_TYPE = {
	MONSTER_SEC_KILL_TYPE_ODDS = 1,
	MONSTER_SEC_KILL_TYPE_PRIVILEGE = 2,
}

GODDESS_BLESSING_BUY_TYPE = {
	CAN_NOT_BUY = 0, --不能购买
	CAN_BUY = 1,	 --可以购买
	HAVE_BUY = 2,	 --已经购买
}

-- 渡劫操作类型
DUJIE_OPERATE_TYPE =
{
	ORDEAL_OPERATE_TYPE_BASE_INFO = 1,				--// 基础信息
	ORDEAL_OPERATE_TYPE_DO_ORDEAL = 2,				--// 进行渡劫
	ORDEAL_OPERATE_TYPE_CANNEL_ORDEAL = 3,				--// 进行渡劫
	ORDEAL_OPERATE_TYPE_USE_SKILL = 4,				--// 使用技能	param:skill_seq

	ORDEAL_OPERATE_TYPE_BODY_BASE_INFO = 5,					-- 灵根基础信息
	ORDEAL_OPERATE_TYPE_BODY_INFO = 6,							-- 灵根信息
	ORDEAL_OPERATE_TYPE_ACTIVE_BODY = 7,						-- 激活灵根 param:body_seq
	ORDEAL_OPERATE_TYPE_ACTIVE_TALENT = 8,						-- 激活天赋 param:body_seq param2:seq
	ORDEAL_OPERATE_TYPE_CHOOSE_TALENT_SKILL = 9,				-- 天赋技能 param:body_seq param2:seq param3:skill_seq
	ORDEAL_OPERATE_TYPE_RESET_TALENT = 10,						-- 重置天赋 param:body_seq
}

DUJIE_BODY_TALENT_TYPE =
{
	ATTR = 0,   --(属性点)蓝点
	EFFECT = 1,   --(效果点)特殊蓝点
	SKILL = 2,   --(技能点)金点
}

DUJIE_DESC_TYPE = {
	TalentType = 1,  -- 天赋点前缀
	Element = 2,	-- 天赋元素名
	SkillName = 3,	-- 灵根技能名字
	Stage = 4,		-- 渡劫阶段名字
}

CONVERT_TYPE = {
	DAILY = 0,			--每日.
	WEEK = 1,			--每周.
	LIFE = 2,			--终身.
	MONTH = 3,          --每月
}

-- 送花协议类型
SEND_FLOWER_OPER_TYPE =
{
	SEND_FLOWER_OPER_TYPE_INFO = 1,					-- 信息
	SEND_FLOWER_OPER_TYPE_RANK_INFO = 2,			-- 排行榜信息 param1: rank_type
	SEND_FLOWER_OPER_TYPE_DAILY_REWARD = 3,			-- 领取每日奖励
}

-- 充值榜协议类型
OA_RECHARGE_RANK_OPERATE_TYPE =
{
	OA_RECHARGE_RANK_OPERATE_TYPE_BASE_INFO = 1,					-- 信息
	OA_RECHARGE_RANK_OPERATE_TYPE_RANK_INFO = 2,					-- 排行榜信息
	OA_RECHARGE_RANK_OPERATE_TYPE_DAILY_REWARD = 3,					-- 领取每日奖励
}

-- 消费榜协议类型
OA_CONSUME_GOLD_RANK_OPERATE_TYPE =
{
	OA_CONSUME_GOLD_RANK_OPERATE_TYPE_BASE_INFO = 1,					-- 信息
	OA_CONSUME_GOLD_RANK_OPERATE_TYPE_RANK_INFO = 2,					-- 排行榜信息
	OA_CONSUME_GOLD_RANK_OPERATE_TYPE_DAILY_REWARD = 3,					-- 领取每日奖励
}

--百倍爆装
HUNDREDFOLD_DROP_OPERATE_TYPE = 
{
	HUNDREDFOL_UP_LEVEL = 1,						--升级
	HUNDREDFOL_REAL_RECHARGE_REWARD = 2,	--领取真充奖励
	HUNDREDFOL_ENTER = 3,						--进入地图
	HUNDREDFOL_FETCH_TARGET_REWARD = 4,			--领取目标奖励
	CS_E_HUNDREDFOLD_DROP_CLIENT_OPERATE_TYPE_RANK_INFO = 5 -- 排行榜信息
}

--雷法
THUNDER_OPERATE_TYPE =
{
	ITEM_INFO = 1,							-- 信息
	PART_UPLEVEL = 2,						-- 槽位升级 param1:seq param2:part
	PART_UPGRADE = 3,						-- 槽位升阶 param1:seq param2:part
	PUT_EQUIP = 4,							-- 穿戴装备 param1:seq param2:part param3:bag_index
	ACTIVE_SUIT = 5,                        -- 激活套装 param1:seq param2:index
} 
-- 主界面宣传图条件
TASK_PANEL_ADVANCE_NOTICE_CONDITION =
{
	LEVEL_UP = 1,						-- 等级直升
	ONE_YUAN_SALE = 2,					-- 一元秒杀
	REBATE_PRIVILEGE = 3,				-- 返利特权
	FUND = 4,							-- 基金
	GODDESS_BLESSING1 = 5,				-- 女神祝福1
	GODDESS_BLESSING2 = 6,				-- 女神祝福2
	BILLIONSUBSIDY = 7,					-- 百亿补贴
	YANYU_PAVILION = 8,					-- 烟雨阁
	SUPREME_PRIVILEGE = 9,				-- 至尊特权
}

-- 幻兽抽奖运营活动
OA_BEAST_DRAW_OPERATE_TYPE = {
	DRAW_ITEM_INFO = 1,					-- 抽奖信息
	DRAW = 2,							-- 抽奖 param1:type param2:mode
	DRAW_RECORD_INFO = 3,				-- 抽奖记录信息 param1:type
	DRAW_CONVERT = 4,					-- 抽奖兑换 param1:type
}

-- 主界面宣传图条件状态
TASK_PANEL_ADVANCE_NOTICE_STATE =
{
	OPEN_STATE = 1,							-- 开启状态
	CLOSE_STATE = 2,							-- 关闭状态
}

--易容
DIY_APPEARANCE_OPER_TYPE = 
{
	ACTIVE_TYPE = 1,							-- 激活 param1: type param2: seq
	USE_PROJECT = 2,							-- 使用方案 param1: sex param2: prof param3: project_id
}

-- 冲榜助力商店类型
HELP_RANK_SHOP_TYPE =
{
	PURCHASE = 1,	-- 直购
	LINGYU = 2,		-- 灵玉
	SCORE = 3,		-- 积分
}

-- 龙神试炼
DRAGON_TRIAL_OPERATE_TYPE = {
	OA_DRAGON_TRIAL_OPERATE_TYPE_INFO = 1,
	OA_DRAGON_TRIAL_OPERATE_TYPE_DARE = 2,
	OA_DRAGON_TRIAL_OPERATE_TYPE_AUTO_DARE = 3,
}

-- 摩天轮货币类型
FORTUNECAT_HUOBI_TYPE = {
	GOLD = 0,
	CANGJING_SCORE = 1,
}

-- 开启功能名, 和功能开启表对应
OPEN_FUN_NAME = {
	yuanshen = "yuanshen",
}

-- 跨服藏宝类型
CROSS_TREASURE_TYPE = {
	CROSS_TREASURE_OPERATE_TYPE_MIN = 0,
	CROSS_TREASURE_OPERATE_TYPE_INFO = 1,			--// 信息			param1: plat_type, param2: server_id	
	CROSS_TREASURE_OPERATE_TYPE_USE = 2,			--// 放置灵珠		param1: item_id
	CROSS_TREASURE_OPERATE_TYPE_ROLE_INFO = 3,		--// 个人信息
	CROSS_TREASURE_OPERATE_TYPE_BASE_INFO = 4,		--// 基础信息
	CROSS_TREASURE_OPERATE_TYPE_BEAST_INFO = 5,		--// 幻兽信息			param1:server_id
	CROSS_TREASURE_OPERATE_TYPE_MAX = 100,
	MAX_OTHER_TREASURE_COUNT = 500,					--最大灵珠个数
	MSG_MAX_TYPE_COUNT = 10,							--玩家采集对应品质信息
	TREASURE_ITEM_TYPE_LINGZHU = 1,			 		--灵珠
	TREASURE_ITEM_TYPE_BOMB = 2,			 		--炸弹
	MAX_LEVEL = 5,									-- 最大等级
}

--活动开服集卡
ACTIVITY_COLLECT_CARD_TYPE = {
	AO_COLLECT_CARD_OPERATE_TYPE_TYPE_MIN = 0,
	AO_COLLECT_CARD_OPERATE_TYPE_TYPE_INFO = 1,
	AO_COLLECT_CARD_OPERATE_TYPE_DRAW = 2,							--// 抽奖			param1:mode
	AO_COLLECT_CARD_OPERATE_TYPE_FETCH_TASK_REWARD = 3,				--// 领取任务奖励 param1:task_id
	AO_COLLECT_CARD_OPERATE_TYPE_FETCH_REWARD = 4,					--// 领取收集类型奖励 param1:seq
	AO_COLLECT_CARD_OPERATE_TYPE_COMPOSE = 5,						--// 合成			param1:seq		param2:count
	AO_COLLECT_CARD_OPERATE_TYPE_SEND = 6,							--// 向某人发送道具 param1:uid param2:seq
	AO_COLLECT_CARD_OPERATE_TYPE_GAIN = 7,							--// 向某人索要道具 param1:uid param2:seq
	AO_COLLECT_CARD_OPERATE_TYPE_LIST_INFO = 8,						--// 列表信息
	AO_COLLECT_CARD_OPERATE_TYPE_REFUS = 9,							--// 拒绝 param1:uid param2:seq
	AO_COLLECT_CARD_OPERATE_TYPE_FETCH_COLLECT_REWARD = 10,			--// 领取收集个数奖励 param1:seq
	MAX_CARD_COUNT = 14,
	MAX_TASK_COUNT = 64,
	REQ_MAX_NUM = 30,
	FIREND_MAX = 100,
	MAX_PRO_REWARD_COUNT = 13,
	MAX_CARD_RECORD_COUNT = 50,										-- 最大抽奖记录个数
}

--新节日活动-集卡
NEW_FESTIVAL_ACTIVITY_COLLECT_CARD_TYPE = {
	AO_COLLECT_CARD_OPERATE_TYPE_TYPE_MIN = 0,
	AO_COLLECT_CARD_OPERATE_TYPE_TYPE_INFO = 1,
	AO_COLLECT_CARD_OPERATE_TYPE_DRAW = 2,							--// 抽奖			param1:mode
	AO_COLLECT_CARD_OPERATE_TYPE_FETCH_TASK_REWARD = 3,				--// 领取任务奖励 param1:task_id
	AO_COLLECT_CARD_OPERATE_TYPE_FETCH_REWARD = 4,					--// 领取收集类型奖励 param1:seq
	AO_COLLECT_CARD_OPERATE_TYPE_COMPOSE = 5,						--// 合成			param1:seq		param2:count
	AO_COLLECT_CARD_OPERATE_TYPE_SEND = 6,							--// 向某人发送道具 param1:uid param2:seq
	AO_COLLECT_CARD_OPERATE_TYPE_GAIN = 7,							--// 向某人索要道具 param1:uid param2:seq
	AO_COLLECT_CARD_OPERATE_TYPE_LIST_INFO = 8,						--// 列表信息
	AO_COLLECT_CARD_OPERATE_TYPE_REFUS = 9,							--// 拒绝 param1:uid param2:seq
	AO_COLLECT_CARD_OPERATE_TYPE_FETCH_COLLECT_REWARD = 10,			--// 领取收集个数奖励 param1:seq
	MAX_CARD_COUNT = 14,
	MAX_TASK_COUNT = 64,
	REQ_MAX_NUM = 30,
	FIREND_MAX = 100,
	MAX_PRO_REWARD_COUNT = 13,
	MAX_CARD_RECORD_COUNT = 50,										-- 最大抽奖记录个数
}

--每日充值
NA_DAILY_RECHARGE_OPERATE_TYPE = {
	NA_DAILY_RECHARGE_OPERATE_TYPE_MIN = 0,

	NA_DAILY_RECHARGE_OPERATE_TYPE_INFO = 1,							-- 信息
	NA_DAILY_RECHARGE_OPERATE_TYPE_REWARD = 2,							-- 领取奖励

	NA_DAILY_RECHARGE_OPERATE_TYPE_MAX = 3,
}

DOUBLE_MOUNT_OPERATE_TYPE = {
	BASE_INFO = 1,							-- 基础信息
	UP_STAR = 2,							--  坐骑升星		param1:seq
	USE_SEQ = 3,							--  坐骑幻化		param1:seq
	INVITE = 4,								--  邀请			param1:plat_type    param2:target_uid
	INVITE_RESULT = 5,						--  邀请操作		param1:plat_type    param2:target_uid	param3:is_argee
	CANCLE_RIDE = 6,                        -- 取消骑乘
	TEAM_FOLLOW_RIDE = 7,				    -- 队伍跟随乘坐
}

MULTI_MOUNT_STATE = {
	NONE = 0,	        -- 无驾驶/无搭乘状态
	DRIZVE = 1,         -- 驾驶状态
	TAKE = 2,           -- 搭乘状态
}

RECHARGE_PANEL_TYPE = {
	LingYu = 1,		-- 灵玉
	CashPoint = 2, 	-- 现金点.
}

BEAST_EQUIP_OPERATE_TYPE =
{
	BEAST_EQUIP_OPERATE_TYPE_MIN = 0,
	BEAST_EQUIP_OPERATE_TYPE_EQUIP_PUT = 1,						--// 内丹镶嵌 p1:出战位 p2:内丹槽位 p3:幻兽装备背包grid_index
	BEAST_EQUIP_OPERATE_TYPE_EQUIP_UN_PUT = 2,					--// 内丹卸下 p1:出战位 p2:内丹槽位
	BEAST_EQUIP_OPERATE_TYPE_EQUIP_UP_LEVEL = 3,				--// 内丹槽位升级 p1:出战位 p2:内丹槽位
	BEAST_EQUIP_OPERATE_TYPE_EQUIP_REFRESH = 4,					--// 内丹洗练 p1:出战位 p2:内丹槽位 p3:幻兽装备背包grid_index p4:锁定的词条flag(二进制低位到高位)
	BEAST_EQUIP_OPERATE_TYPE_EQUIP_INHERITANCE = 5,				--// 内丹传承 p1:出战位 p2:内丹槽位 p3:接受传承幻兽装备背包grid_index p4:给予传承的幻兽装备背包
	BEAST_EQUIP_OPERATE_TYPE_EQUIP_REPLACE = 6,					--// 词条替换 p1:出战位 p2:内丹槽位 p3:幻兽装备背包grid_index
	BEAST_EQUIP_OPERATE_TYPE_MAX = 100,
	MAX_BEAST_EQUIP_DECOMPOSE_COUNT = 50,						--// 分解最大个数
	MAX_BEAST_EQUIP_FIGHT_SLOT_SEQ = 9,							--// 幻兽最大战斗孔位数
	MAX_BEAST_EQUIP_SLOT_SEQ = 5,								--// 幻兽内丹孔位
	MAX_BEAST_EQUIP_WORDS_SEQ = 4,								--// 最大幻兽装备词条序号
	MAX_BEAST_EQUIP_BAG_NUM = 300,								--// 幻兽内丹装备背包最大个数
}