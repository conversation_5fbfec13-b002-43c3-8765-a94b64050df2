 --------------------------------------------------
--充值界面。提供一排充值列表。玩家点击列表项（金额）跳进平台直接充值
--<AUTHOR>
--------------------------------------------------

function RechargeView:InitRechargeView()
	if self.recharge_gridscroll == nil then
		local data_list = RechargeWGData.Instance:GetShowList()
		local cell_counts = #data_list
		self.recharge_gridscroll = AsyncBaseGrid.New()
		local bundle = "uis/view/recharge_ui_prefab"
		local asset = "ph_recharge_item"
		self.recharge_gridscroll:CreateCells({col = 4, cell_count = cell_counts, list_view = self.node_list.ph_recharge_gridscrl,
				assetBundle = bundle, assetName = asset, itemRender = RechargeItem})
		self.recharge_gridscroll:SetStartZeroIndex(false)
		self.recharge_gridscroll:SetSelectCallBack(BindTool.Bind(self.ClickRechargeItemHandler, self))
		self.recharge_gridscroll:SetEndScrolledCallBack(BindTool.Bind(self.PlayShowTween, self))
		self.recharge_gridscroll:SetDataList(data_list)
		self.recharge_max_cell_num = cell_counts
	end

	if self.ph_recharge_cash_point_grid == nil then
		local data_list = RechargeWGData.Instance:GetTongPiaoCfg()
		local cell_counts = #data_list
		self.ph_recharge_cash_point_grid = AsyncBaseGrid.New()
		local bundle = "uis/view/recharge_ui_prefab"
		local asset = "vip_tongpiao_render"
		self.ph_recharge_cash_point_grid:CreateCells({col = 4, cell_count = cell_counts, list_view = self.node_list.ph_recharge_cash_point_grid,
				assetBundle = bundle, assetName = asset, itemRender = RechargeTongPiaoRender})
		self.ph_recharge_cash_point_grid:SetStartZeroIndex(false)
		self.ph_recharge_cash_point_grid:SetSelectCallBack(BindTool.Bind(self.ClickRechargeItemHandler, self))
		self.ph_recharge_cash_point_grid:SetDataList(data_list)
	end

	--默认显示灵玉充值.
	self.node_list.ph_recharge_gridscrl:SetActive(true)
	self.node_list.ph_recharge_cash_point_grid:SetActive(false)

	self.show_recharge_panel_type = RECHARGE_PANEL_TYPE.LingYu

	self.recharge_btn_item_list = AsyncListView.New(RechargePanelBtnItemRender, self.node_list.recharge_btn_list)
	self.recharge_btn_item_list:SetSelectCallBack(BindTool.Bind(self.RechargeToggleCallBack, self))

	self.recharge_play_show_tween = true

	self.node_list.recharge_volume_desc.text.text = Language.RechargeVolume.RechargeVolumeDesc

	self.recharge_volume_change = BindTool.Bind1(self.FlushRechargeVolume, self)
	RoleWGData.Instance:NotifyAttrChange(self.recharge_volume_change, {"recharge_volume"})
	XUI.AddClickEventListener(self.node_list.recharge_volume_btn, BindTool.Bind(self.OnRechargeVolumeBtnClick, self))

	local recharge_volume_is_open = FunOpen.Instance:GetFunIsOpened(FunName.RechargeVolume)
	--self.node_list["recharge_volume"]:SetActive(recharge_volume_is_open)
	self:FlushRechargeVolume()
end

function RechargeView:DeleteRechargeView()
	if self.recharge_gridscroll then
		self.recharge_gridscroll:DeleteMe()
		self.recharge_gridscroll = nil
	end

	if self.ph_recharge_cash_point_grid then
		self.ph_recharge_cash_point_grid:DeleteMe()
		self.ph_recharge_cash_point_grid = nil
	end

	if self.alert_view then
		self.alert_view:DeleteMe()
		self.alert_view = nil
	end

	if self.recharge_btn_item_list then
		self.recharge_btn_item_list:DeleteMe()
		self.recharge_btn_item_list = nil
	end

	if self.recharge_volume_change then
		RoleWGData.Instance:UnNotifyAttrChange(self.recharge_volume_change)
		self.recharge_volume_change = nil
	end

	self.show_recharge_panel_type = RECHARGE_PANEL_TYPE.LingYu
end

function RechargeView:ShowRechargeView()
	--self.recharge_play_show_tween = true
end

function RechargeView:HideRechargeView()
	--self:RestRechargeViewTween()
end

function RechargeView:OnFlushRechargeView(param_t)
	local recharge_list = VipWGData.Instance:GetRechargeTabListCfg()

	local recharge_type = self.show_recharge_panel_type and self.show_recharge_panel_type or RECHARGE_PANEL_TYPE.LingYu
	local need_jump = false
	if param_t then
		for k,v in pairs(param_t) do
			if k == "all" then
				for t,q in pairs(v) do
					if t == "to_ui_param" and q ~= 0 then
						recharge_type = tonumber(q)
						--跳转页签index不允许超出列表长度.
						self.show_recharge_panel_type = recharge_type <= #recharge_list and recharge_type or RECHARGE_PANEL_TYPE.LingYu
						need_jump = true
						break
					end
				end
			end
		end
	end

	self.recharge_btn_item_list:SetDataList(recharge_list)
	if need_jump then
		self.recharge_btn_item_list:JumpToIndex(recharge_type)
	end

	if self.show_recharge_panel_type == RECHARGE_PANEL_TYPE.LingYu then
		local data_list = RechargeWGData.Instance:GetShowList()
		self.recharge_gridscroll:SetDataList(data_list)
	elseif self.show_recharge_panel_type == RECHARGE_PANEL_TYPE.CashPoint then
		local data_list = RechargeWGData.Instance:GetTongPiaoCfg()
		self.ph_recharge_cash_point_grid:SetDataList(data_list)
	end

	self.node_list.ph_recharge_gridscrl:SetActive(self.show_recharge_panel_type == RECHARGE_PANEL_TYPE.LingYu)
	self.node_list.ph_recharge_cash_point_grid:SetActive(self.show_recharge_panel_type == RECHARGE_PANEL_TYPE.CashPoint)
end

function RechargeView:RechargeToggleCallBack(item, cell_index)
	local data = item:GetData()
	self.show_recharge_panel_type = data.recharge_type

	self:OnFlushRechargeView()
end

function RechargeView:PlayShowTween(scroll_obj, start_idx, end_idx)
	if self.recharge_play_show_tween then
		self.recharge_play_show_tween = false
		--self:PlayShowRechargeViewTween()
	end
end

function RechargeView:PlayShowRechargeViewTween()
	local tween_info = UITween_CONSTS.Recharge
	ReDelayCall(self, function()
		local cell_list = self.recharge_gridscroll:GetAllCell()
		for i = 1, #cell_list do
			cell_list[i]:PlayItemTween()
		end
	end, tween_info.DelayTime, "recharge_list_tween_1")

	ReDelayCall(self, function()
		local cell_list = self.recharge_gridscroll:GetAllCell()
		for i=1,#cell_list do
			cell_list[i]:PlayDuobeiTween()
		end
	end, tween_info.Next_time, "recharge_list_tween_2")
end

function RechargeView:RestRechargeViewTween()
	if self.recharge_gridscroll then
		UITween.CleanAllTween(GuideModuleName.Recharge)
		local cell_list = self.recharge_gridscroll:GetAllCell()
		for i=1,#cell_list do
			cell_list[i]:RestTween()
		end
	end
end

function RechargeView:ClickRechargeItemHandler(item)
	local item_data = item:GetData()
	local data = RechargeWGData.Instance:GetData(item_data)
	if nil == item_data or nil == data then
		return
	end
	self:DoRecharge(item_data.money)
end

function RechargeView:DoRecharge(money)
	if money == nil then return end
	RechargeWGCtrl.Instance:Recharge(money, 0, money)
	--self:PlayEffectByRecharge()
end

function RechargeView:PlayEffectByRecharge(money_type)
	local index = self:GetShowIndex()
	if money_type == GameEnum.MONEY_BAR.GOLD and  self.node_list.effect_pos_recharge and index == TabIndex.recharge_cz then
		local bundle_name, asset_name = ResPath.GetEffectUi(Ui_Effect.UI_xianyu_zhakai)
		AudioManager.PlayAndForget(AudioWGData.Instance:GetOtherAutioEffect(AudioUrl.EffectXianYuHuoDe, false, true))
		--EffectManager.Instance:PlaySingleAtTransform(bundle_name, asset_name, self.node_list.qifu_effect_1.transform,1.6,nil,nil,nil,self.OnClickQifuReceive)
		EffectManager.Instance:PlaySingleAtTransform(bundle_name, asset_name, self.node_list.effect_pos_recharge.transform,1.6,nil,nil,nil,BindTool.Bind(self.PlayEffectByRechargeSecond,self,money_type) )
	end
end

function RechargeView:PlayEffectByRechargeSecond(money_type)
	TipWGCtrl.Instance:DestroyFlyEffectByViewName(GuideModuleName.Recharge)
	if self.money_bar then								--GameEnum.MONEY_BAR.GOLD = GameEnum.NEW_MONEY_BAR.XIANYU 类型对不上
		local end_obj = self.money_bar:GetSlicketNode(GameEnum.NEW_MONEY_BAR.XIANYU)

		if end_obj then
		local bundle_name, asset_name = ResPath.GetEffectUi(Ui_Effect.UI_xianyu_single)
			TipWGCtrl.Instance:ShowFlyEffectManager(GuideModuleName.Recharge, bundle_name,
	    asset_name, self.node_list.effect_pos_recharge, end_obj, DG.Tweening.Ease.OutCubic, 0.5, nil, nil, 20, 200)
		end
	end
end

function RechargeView:OnRechargeVolumeBtnClick()
	ViewManager.Instance:Open(GuideModuleName.RechargeVolumeView)
end

function RechargeView:FlushRechargeVolume()
	local fun_open = FunOpen.Instance:GetFunIsOpened(FunName.RechargeVolume)
	if fun_open and self.node_list.recharge_volume_text then
		local recharge_volume_num = RoleWGData.Instance:GetAttr("recharge_volume")
		self.node_list.recharge_volume_text.text.text = recharge_volume_num
	end
end
--------------------------------------------------
--RechargeItem
--------------------------------------------------
RechargeItem = RechargeItem or BaseClass(BaseGridRender)

function RechargeItem:__init()

end

function RechargeItem:__delete()
	
end

function RechargeItem:ClearAllDatas()
	self.node_list.UI_xianyulizi:SetActive(false)
	self.node_list.UI_baoxiang:SetActive(false)
end

function RechargeItem:LoadCallBack()
 --    if self.node_list and self.node_list.item_root then
	--     self.node_list.img_first_double.canvas_group.alpha = 0
	-- 	self.node_list.item_root.canvas_group.alpha = 0
	-- end
end

-- 重写CreateSelectEffect，为了去掉那选中的边框
function RechargeItem:CreateSelectEffect()
end

function RechargeItem:OnFlush()
    local data = RechargeWGData.Instance:GetData(self.data)
	if IsEmptyTable(data) then
		return
	end

    local extra_cfg = RechargeWGData.Instance:GetExtraReward(data.gold)
	local index = self:GetIndex()
	if IsEmptyTable(extra_cfg) then
		return
	end

	local is_first_recharge = RechargeWGData.Instance:GetIsFirstReward(data.gold)
	local reward_type = 0
	local extra_gold = 0
	if is_first_recharge then
		reward_type = extra_cfg.reward_type
		if reward_type == 1 then
			extra_gold = extra_cfg.extra_bind_gold
		elseif reward_type == 4 then
			extra_gold = extra_cfg.extra_gold
		end
	else
		reward_type = extra_cfg.again_reward_type
		extra_gold = extra_cfg.again_reward_param
	end

	self.node_list.layout_frist_double:SetActive(reward_type > 0)
	self.node_list.lbl_money.text.text = RoleWGData.GetPayMoneyStr(data.money, 0, 0)
	 --self.node_list.rich_addgold.text.text = data.description
	 local is_show_ex_score = extra_cfg.cangjinshangpu_score and extra_cfg.cangjinshangpu_score > 0
	self.node_list.ex_score:SetActive(is_show_ex_score)
	self.node_list.score_num.tmp.text = tostring(extra_cfg.cangjinshangpu_score or 0)
	 
	local icon_res_name = ""
	 if reward_type == 1 then
		icon_res_name = ResPath.GetMoneyIcon(2)
	elseif reward_type == 4 then
		icon_res_name = ResPath.GetMoneyIcon(1)
	end

	self.node_list.xuchong:SetActive(not is_first_recharge and reward_type > 0)
	self.node_list.shouchong:SetActive(is_first_recharge)

	local bundle, asset = ResPath.GetCommonIcon(icon_res_name)
	self.node_list.give_gold_icon.image:LoadSprite(bundle, asset)
	self.node_list.lbl_gold.text.text = extra_gold

 	self.node_list.lbl_yuanbao.text.text = data.gold --.. Language.Common.Gold
	local asset_name, bundle_name = ResPath.GetCommonIcon(data.gold_icon)
	self.node_list.img_gold_icon.image:LoadSprite(asset_name, bundle_name, function ()
		self.node_list.img_gold_icon.image:SetNativeSize()
	end)

	self.node_list.UI_xianyulizi:SetActive(true)
	self.node_list.UI_baoxiang:SetActive(index >= 5)
end

function RechargeItem:PlayItemTween()
	local index = self:GetIndex()
	local tween_info = UITween_CONSTS.Recharge.ListCellTween

    if self.node_list and self.node_list.item_root then
	    self.node_list.img_first_double.canvas_group.alpha = 0
		self.node_list.item_root.canvas_group.alpha = 0
	end

	self.cell_delay_key = "RechargeItem" .. index
	ReDelayCall(self, function()
		if self.node_list and self.node_list.item_root then
			UITween.RotateAlphaShow(GuideModuleName.Recharge, self.node_list.item_root, tween_info)
		end
	end, tween_info.NextDoDelay * (index - 1), self.cell_delay_key)
end

function RechargeItem:PlayDuobeiTween()
	UITween.AlphaShow(GuideModuleName.Recharge, self.node_list.img_first_double, 0, 1, 0.3)
end

function RechargeItem:RestTween()
	if self.node_list and self.node_list.item_root then
	    self.node_list.img_first_double.canvas_group.alpha = 1
		self.node_list.item_root.canvas_group.alpha = 1
		self.node_list.item_root.localRotation = Quaternion.Euler(0, 0, 0)
	end
	CancleDelayCall(self, self.cell_delay_key)
end


--------------------------------------------------RechargePanelBtnItemRender--------------------------------------------------------------------------------
RechargePanelBtnItemRender = RechargePanelBtnItemRender or BaseClass(BaseRender)

function RechargePanelBtnItemRender:__init()
	self.select_mark = nil
end

function RechargePanelBtnItemRender:__delete()
	self.select_mark = nil
end

function RechargePanelBtnItemRender:OnSelectChange(is_select)
	if self.select_mark ~= is_select then
		self.node_list.select_bg:SetActive(is_select)
		self.node_list.viph_image:SetActive(is_select)
		self.node_list.vip_image:SetActive(not is_select)
		self.node_list.normal_bg:SetActive(not is_select)
		self.select_mark = is_select
	end
end

function RechargePanelBtnItemRender:OnFlush()
	local data = self:GetData()
	if IsEmptyTable(data) then
		return
	end

	self.node_list.viph_image.text.text = data.name
	self.node_list.vip_image.text.text = data.name

	self.node_list.reward:SetActive(false)
	self.node_list.remind_img:SetActive(false)
end

------------------ RechargeTongPiaoRender 通票item ------------------
RechargeTongPiaoRender = RechargeTongPiaoRender or BaseClass(BaseRender)
function RechargeTongPiaoRender:LoadCallBack()
    XUI.AddClickEventListener(self.node_list.buy_btn, BindTool.Bind(self.OnClickBuyBtn, self))
end

function RechargeTongPiaoRender:OnFlush()
    if not self.data then
        return
    end

	local asset_name, bundle_name = ResPath.GetCommonIcon(self.data.gold_icon)
    XUI.SetNodeImage(self.node_list.icon, asset_name, bundle_name)
    self.node_list.tongpiao_num.text.text = self.data.tongpiao_num
    self.node_list.buy_num.text.text = RoleWGData.GetPayMoneyStr(self.data.price, self.data.rmb_type, self.data.rmb_seq)
end

function RechargeTongPiaoRender:OnClickBuyBtn()
    if not self.data then
        return
    end

    RechargeWGCtrl.Instance:Recharge(self.data.price, self.data.rmb_type, self.data.rmb_seq)
end