return {
	["other"]={
		{start_leftup_y=36,start_rightdown_x=64,start_leftup_x=52,end_aoi_range=30,end_y=232,start_rightdown_y=42,end_x=277,},},
	["skill_default_table"]={skill_idx=1,loss=0,cd_s=0,range=0,desc="使得角色前一位的目标被冰冻住，持续3秒",readEffectPath=0,launchtype=5,action_type=0,skill_name="眩晕",},
	["skill_pos_default_table"]={},
	["reward_exp_list"]={
		{},
		{},
		{},
		{},
		{},
		{},
		{},
		{},
		{},
		{},
		{},
		{},
		{},
		{},
		{},
		{},
		{},
		{},
		{},
		{},
		{},
		{},
		{},
		{},
		{},
		{},
		{},
		{},
		{},
		{},
		{},
		{},
		{},
		{},
		{},
		{},
		{},
		{},
		{},
		{},
		{},
		{},
		{},
		{},
		{},
		{},
		{},
		{},
		{},
		{},
		{},
		{},
		{},
		{},
		{},
		{},
		{},
		{},
		{},
		{},
		{},
		{},
		{},
		{},
		{},
		{},
		{},
		{},
		{},
		{},
		{},
		{},
		{},
		{},
		{},
		{},
		{},
		{},
		{},
		{},
		{},
		{},
		{},
		{},
		{},
		{},
		{},
		{},
		{},
		{},
		{},
		{},
		{},
		{},
		{},
		{},
		{},
		{},
		{},
		{},
		{},
		{},
		{},
		{},
		{},
		{},
		{},
		{},
		{},
		{},
		{},
		{},
		{},
		{},
		{},
		{},
		{},
		{},
		{},
		{},
		{},
		{},
		{},
		{},
		{},
		{},
		{},
		{},
		{},
		{},
		{},
		{},
		{},
		{},
		{},
		{},
		{},
		{},
		{},
		{},
		{},
		{},
		{},
		{},
		{},
		{},
		{},
		{},
		{},
		{},
		{},
		{},
		{},
		{},
		{},
		{},
		{},
		{},
		{},},
	["skill"]={
		{skill_idx=0,loss=3,cd_s=1,desc="给坐骑注入灵力，可以使坐骑的提升速度增加10%。在过载状态（100疲劳）时，无法使用注灵加速",readEffectPath=10057,skill_name="奔腾",},
		{cd_s=4,desc="瞬间向鼠标所指方向跳跃一段距离（空白键有效）。",launchtype=6,skill_name="跳跃",},
		{skill_idx=2,loss=5,cd_s=30,range=20,desc="对目标玩家进行眩晕，持续3秒",},
		{skill_idx=3,desc="在3秒内不受任何负面状态的影响，过载除外",skill_name="无敌",},
		{skill_idx=4,desc="瞬间降低当前的30%疲劳度",readEffectPath=1854,skill_name="唤灵",},
		{skill_idx=5,desc="速度+30%且不产生任何疲劳度，持续5秒",skill_name="极速",},
		{skill_idx=6,desc="丢下一块西瓜皮，踩上的目标将降低80%速度",skill_name="西瓜皮",},
		{skill_idx=7,loss=5,skill_name="冰冻",},},
	["reward_default_table"]={},
	["reward"]={
		{},
		{},
		{},
		{},
		{},
		{},
		{},},
	["speed"]={
		{zhuling_dec_percent=2,add_percent_upper=15,speed_type=0,loss_per_sec=-5,zhuling_dur_s=10,},
		{add_percent_lower=16,zhuling_dur_s=4,},
		{zhuling_dec_percent=10,add_percent_upper=85,add_percent_lower=41,speed_type=2,loss_per_sec=2,},
		{zhuling_dec_percent=20,add_percent_upper=500,add_percent_lower=86,speed_type=3,loss_per_sec=3,zhuling_dur_s=2,},},
	["reward_exp_list_default_table"]={},
	["speed_default_table"]={zhuling_dec_percent=5,add_percent_upper=40,add_percent_lower=0,speed_type=1,loss_per_sec=1,zhuling_dur_s=3,},
	["skill_pos"]={
		{},
		{},
		{},
		{},
		{},
		{},
		{},
		{},
		{},
		{},
		{},
		{},
		{},
		{},
		{},
		{},
		{},
		{},
		{},
		{},
		{},
		{},
		{},
		{},
		{},
		{},
		{},
		{},
		{},
		{},
		{},
		{},
		{},
		{},
		{},
		{},
		{},
		{},
		{},
		{},
		{},
		{},},
}
