{"actorController": {"projectiles": [], "hurts": [], "beHurtEffecct": {"BundleName": "", "AssetName": "", "AssetGUID": "", "IsEmpty": true}, "hurtEffectName": "", "beHurtNodeName": "", "beHurtAttach": false, "hurtEffectFreeDelay": 0.0, "QualityCtrlList": []}, "actorTriggers": {"effects": [], "halts": [], "sounds": [], "cameraShakes": [], "cameraFOVs": [], "sceneFades": [], "footsteps": []}, "actorBlinker": {"blinkFadeIn": 0.0, "blinkFadeHold": 0.0, "blinkFadeOut": 0.0}, "TimeLineList": []}