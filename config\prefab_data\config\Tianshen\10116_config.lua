return {
	actorController = {
		projectiles = {},

		hurts = {},

	},
	actorTriggers = {
		effects = {
			{
				triggerEventName = "combo1_1/begin",
				triggerDelay = 0.0,
				triggerFreeDelay = 0.0,
				effectGoName = "10116_acttack",
				effectAsset = {
					BundleName = "effects/prefab/model/tianshen/10116/10116_acttack_prefab",
					AssetName = "10116_acttack",
				},
				playerAtTarget = false,
				referenceNodeHierarchyPath = "",
				isAttach = false,
				isRotation = true,
				isUseCustomTransform = false,
				offsetPosX = 0.0,
				offsetPosY = 0.0,
				offsetPosZ = 0.0,
				triggerStopEvent = "",
				effectBtnName = "combo1",
				playerAtPos = false,
				ignoreParentScale = true,
			},
			{
				triggerEventName = "combo1_2/begin",
				triggerDelay = 0.0,
				triggerFreeDelay = 0.0,
				effectGoName = "10116_acttack",
				effectAsset = {
					BundleName = "effects/prefab/model/tianshen/10116/10116_acttack_prefab",
					AssetName = "10116_acttack",
				},
				playerAtTarget = false,
				referenceNodeHierarchyPath = "",
				isAttach = false,
				isRotation = true,
				isUseCustomTransform = false,
				offsetPosX = 0.0,
				offsetPosY = 0.0,
				offsetPosZ = 0.0,
				triggerStopEvent = "",
				effectBtnName = "combo2",
				playerAtPos = false,
				ignoreParentScale = true,
			},
			{
				triggerEventName = "combo1_3/begin",
				triggerDelay = 0.0,
				triggerFreeDelay = 0.0,
				effectGoName = "10116_acttack",
				effectAsset = {
					BundleName = "effects/prefab/model/tianshen/10116/10116_acttack_prefab",
					AssetName = "10116_acttack",
				},
				playerAtTarget = false,
				referenceNodeHierarchyPath = "",
				isAttach = false,
				isRotation = true,
				isUseCustomTransform = false,
				offsetPosX = 0.0,
				offsetPosY = 0.0,
				offsetPosZ = 0.0,
				triggerStopEvent = "",
				effectBtnName = "combo3",
				playerAtPos = false,
				ignoreParentScale = true,
			},
			{
				triggerEventName = "attack1/begin",
				triggerDelay = 0.0,
				triggerFreeDelay = 0.0,
				effectGoName = "10116_skill1",
				effectAsset = {
					BundleName = "effects/prefab/model/tianshen/10116/10116_skill1_prefab",
					AssetName = "10116_skill1",
				},
				playerAtTarget = false,
				referenceNodeHierarchyPath = "",
				isAttach = false,
				isRotation = true,
				isUseCustomTransform = false,
				offsetPosX = 0.0,
				offsetPosY = 0.0,
				offsetPosZ = 0.0,
				triggerStopEvent = "",
				effectBtnName = "attack1",
				playerAtPos = false,
				ignoreParentScale = true,
			},
			{
				triggerEventName = "attack2/begin",
				triggerDelay = 0.0,
				triggerFreeDelay = 0.0,
				effectGoName = "10116_skill2",
				effectAsset = {
					BundleName = "effects/prefab/model/tianshen/10116/10116_skill2_prefab",
					AssetName = "10116_skill2",
				},
				playerAtTarget = false,
				referenceNodeHierarchyPath = "",
				isAttach = false,
				isRotation = true,
				isUseCustomTransform = false,
				offsetPosX = 0.0,
				offsetPosY = 0.0,
				offsetPosZ = 0.0,
				triggerStopEvent = "",
				effectBtnName = "attack2",
				playerAtPos = false,
				ignoreParentScale = true,
			},
			{
				triggerEventName = "attack3/begin",
				triggerDelay = 0.0,
				triggerFreeDelay = 0.0,
				effectGoName = "10116_skill3",
				effectAsset = {
					BundleName = "effects/prefab/model/tianshen/10116/10116_skill3_prefab",
					AssetName = "10116_skill3",
				},
				playerAtTarget = false,
				referenceNodeHierarchyPath = "",
				isAttach = false,
				isRotation = true,
				isUseCustomTransform = false,
				offsetPosX = 0.0,
				offsetPosY = 0.0,
				offsetPosZ = 0.0,
				triggerStopEvent = "",
				effectBtnName = "attack3",
				playerAtPos = false,
				ignoreParentScale = true,
			},
			{
				triggerEventName = "rest/begin",
				triggerDelay = 0.4,
				triggerFreeDelay = 0.0,
				effectGoName = "10116_rest",
				effectAsset = {
					BundleName = "effects/prefab/model/tianshen/10116/10116_rest_prefab",
					AssetName = "10116_rest",
				},
				playerAtTarget = false,
				referenceNodeHierarchyPath = "root",
				isAttach = true,
				isRotation = false,
				isUseCustomTransform = true,
				offsetPosX = 0.0,
				offsetPosY = 0.0,
				offsetPosZ = -7.0,
				triggerStopEvent = "",
				effectBtnName = "rest",
				playerAtPos = false,
				ignoreParentScale = false,
			},
		},
		sounds = {
			{
				soundEventName = "combo1_1/begin",
				soundDelay = 0.0,
				soundAudioAsset = {
					BundleName = "audios/sfxs/roleskill/tianshen_10114",
					AssetName = "MingJiangcombo_1_1",
				},
				soundAudioGoName = "MingJiangcombo_1_1",
				soundBtnName = "combo1",
				soundIsMainRole = false,
			},
			{
				soundEventName = "combo1_2/begin",
				soundDelay = 0.0,
				soundAudioAsset = {
					BundleName = "audios/sfxs/roleskill/tianshen_10114",
					AssetName = "MingJiangcombo_1_2",
				},
				soundAudioGoName = "MingJiangcombo_1_2",
				soundBtnName = "combo2",
				soundIsMainRole = false,
			},
			{
				soundEventName = "combo1_3/begin",
				soundDelay = 0.5,
				soundAudioAsset = {
					BundleName = "audios/sfxs/roleskill/tianshen_10114",
					AssetName = "MingJiangcombo_1_3",
				},
				soundAudioGoName = "MingJiangcombo_1_3",
				soundBtnName = "combo3",
				soundIsMainRole = false,
			},
			{
				soundEventName = "attack1/begin",
				soundDelay = 0.0,
				soundAudioAsset = {
					BundleName = "audios/sfxs/roleskill/tianshen_10114",
					AssetName = "MingJiangattack1",
				},
				soundAudioGoName = "MingJiangattack1",
				soundBtnName = "attack1",
				soundIsMainRole = false,
			},
			{
				soundEventName = "attack2/begin",
				soundDelay = 0.0,
				soundAudioAsset = {
					BundleName = "audios/sfxs/roleskill/tianshen_10114",
					AssetName = "MingJiangattack2",
				},
				soundAudioGoName = "MingJiangattack2",
				soundBtnName = "attack2",
				soundIsMainRole = false,
			},
			{
				soundEventName = "attack3/begin",
				soundDelay = 0.0,
				soundAudioAsset = {
					BundleName = "audios/sfxs/roleskill/tianshen_10114",
					AssetName = "MingJiangattack3",
				},
				soundAudioGoName = "MingJiangattack3",
				soundBtnName = "attack3",
				soundIsMainRole = false,
			},
		},
		cameraShakes = {
			{
				CameraShakeBtnName = "attack3",
				eventName = "attack3/begin",
				numberOfShakes = 6,
				distance = 2.0,
				speed = 1000.0,
				delay = 1.0,
				decay = 0.0,
			},
		},
		radialBlurs = {},

	},
}