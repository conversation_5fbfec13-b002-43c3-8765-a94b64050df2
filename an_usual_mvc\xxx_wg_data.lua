XXXWGData = XXXWGData or BaseClass()
--初始化
function XXXWGData:__init()
	if XXXWGData.Instance then
		error("[XXXWGData] Attempt to create singleton twice!")
		return
	end
    -- 单例
	XXXWGData.Instance = self

    self:InitParam()
    self:InitConfig()

    -- 红点注册
    -- 在remind_def.lua 的 RemindName表定义红点名，关注RemindFunName表、 RemindGroud表
    RemindManager.Instance:Register(RemindName.XXX, BindTool.Bind(self.GetRemind, self))
    -- 注册红点涉及的对应物品(仅限人物背包，其他功能的特殊背包除外)，当物品改变时会对相应红点进行Fire()
    self:RegisterXXXRemindInBag()
end

function XXXWGData:__delete()
    -- 红点注销
    RemindManager.Instance:UnRegister(RemindName.XXX)
    XXXWGData.Instance = nil
end

-- 默认参数
function XXXWGData:InitParam()
    self.flag_list = {}
    self.param_2 = 0
	self.param_list = {}
end

-- 初始化配置
function XXXWGData:InitConfig()
    local cfg = ConfigManager.Instance:GetAutoConfig("XXX_auto")
   --[[
        将配置以key存储，一般上当配置很大时采取以下方法 以空间换时间
        当配置表比较少时，还是以遍历的方式获取,减少常驻内存
        {
            ["param_1"] = {
                ["param_2"] = {}
            }
        }
        ListToMap 和 ListToMapList 的区别查看util.lua
   ]] 
   self.xxx_map_cfg = ListToMap(cfg.xxx, "param_1", "param_2")
   self.xxx_map_list_cfg = ListToMapList(cfg.xxx, "param_1", "param_2")
end

function XXXWGData:RegisterXXXRemindInBag()
    -- 红点涉及的对应物品id列表
    local item_id_list = {}
    BagWGCtrl.Instance:RegisterRemindByItemChange(RemindName.XXX, item_id_list)
end

-- 存储协议下发的数据
function XXXWGData:SetXXXInfo(protocol)
    -- bit:d2b(arg, tbl) 将十进制的值，转换成二进制，以table[32] 存储
    self.flag_list = bit:d2b(protocol.param_1)
    self.param_2 = protocol.param_2
	self.param_list = protocol.param_list
end

-- 红点方法
function XXXWGData:GetRemind()
    -- 【常用方法】
    self:GetItemData()
    self:GetRoleData()
    self:GetTimeData()
    self:GetActivityData()
    self:GetAttrData()

    local meet_condition1 = false
    if meet_condition1 then
        return 1
    end

    local meet_condition2 = false
    if meet_condition2 then
        return 1
    end

    return 0
end

-- 物品
function XXXWGData:GetItemData()
    -- 获取物品配置
    local item_id = 0
    local item_cfg, item_type = ItemWGData.Instance:GetItemConfig(item_id)
    -- 获得背包里的物品数量
    local item_num = ItemWGData.Instance:GetItemNumInBagById(item_id)
    --根据物品id获得在【背包】中所有的index
    local index_list = ItemWGData.Instance:GetItemListIndex(item_id)
    --根据物品id获得在【材料背包】中所有的index
    local stuff_index_list = ItemWGData.Instance:GetItemListIndexByStuffBag(item_id)
end

-- 玩家
function XXXWGData:GetRoleData()
    local role_data = RoleWGData.Instance
    -- 玩家原服uid
    -- 玩家uuid
    local role_uuid = role_data:GetUUid()-- uuid = {role_id = uid, plat_type = plat_type}
    -- 玩家平台类型
    local plat_type = role_data:GetPlatType()
    -- 玩家原服id
    local origin_server_id = role_data:GetOriginServerId()
    -- 获取玩家性别、职业
    local role_sex = role_data:GetRoleSex()
    local role_prof = role_data:GetRoleProf()
    -- 获取玩家货币是否足够
    local cost_gold = 0
    local is_enough = role_data:GetIsEnoughUseGold(cost_gold)           -- 灵玉
    is_enough = role_data:GetIsEnoughBindGold(cost_gold)                -- 元宝
    is_enough = role_data:GetIsEnoughAllGold(cost_gold)                 -- 可消耗两种，灵玉 + 元宝
    -- 获取玩家属性
    local role_level = role_data:GetAttr("level")
    -- 巅峰等级
    local is_dianfeng, show_level = role_data:GetDianFengLevel(role_level)
    -- 把等级转换成巅峰等级字符串
    local level_str = role_data:TransToDianFengLevelStr(role_level)
    -- 获取vip
    local vip_level = role_data:GetAttr("vip_level") or 0

    -- 玩家是否在跨服场景
    if IS_ON_CROSSSERVER then
    end
end

-- 时间
function XXXWGData:GetTimeData()
    -- 当前服务器时间戳
    local server_time = TimeWGCtrl.Instance:GetServerTime()
    -- 当前开服天数
    local server_day = TimeWGCtrl.Instance:GetCurOpenServerDay()
    -- 获取当天剩余时间
    local time = TimeUtil.GetTodayRestTime(TimeWGCtrl.Instance:GetServerTime())
end

-- 活动
function XXXWGData:GetActivityData()
    -- 获取活动剩余时间,结束时间（传入活动类型）
    local time, next_time = ActivityWGData.Instance:GetActivityResidueTime(ACTIVITY_TYPE.XXX)
    --获得某个活动是否开启
    local is_open = ActivityWGData.Instance:GetActivityIsOpen(ACTIVITY_TYPE.XXX)
    --获取活动数据
    local act_info = ActivityWGData.Instance:GetActivityStatuByType(ACTIVITY_TYPE.XXX)
end

--配置表属性
function XXXWGData:GetAttrData()
    -- 获得排序好的属性列表(有下一级)
    -- (属性id字符串， 属性value字符串, 属性数量读取开始索引， 属性数量读取结束索引)
    local attr_level_list = EquipWGData.GetSortAttrListHaveNextByTypeCfg(cur_cfg, next_cfg, type_key_str, value_key_str, read_start, read_end)
end

-- equip_body_seq            装备肉身seq   15       件装备一套    每一个seq对应一阶装备
-- equip_body_index          装备肉身对应的 0 - 375  序号 每15件对应一套装备肉身      
-- equip_part                装备          0-15     部位序号
-- equip_type                装备类型     GameEnum.EQUIP_TYPE_TOUKUI/ GameEnum.EQUIP_TYPE_YIFU  头盔/衣服 
-- equip_big_type            普通装备 GameEnum.EQUIP_BIG_TYPE_NORMAL / 仙器装备GameEnum.EQUIP_BIG_TYPE_XIANQI 

-- 转换公式 equip_body_index = equip_body_seq * GameEnum.MAX_EQUIP_BODY_EQUIP_NUM + equip_part

function XXXWGData:EquipBodyData()
    -- 装备肉身index 0 - 375  转部位index  0 - 15
    local equip_part = EquipmentWGData.GetEquipPartByEquipBodyIndex(equip_body_index)

    -- 装备肉身index 转装备肉身seq 和 部位
    local equip_body_seq, equip_part = EquipmentWGData.EquipBodyIndexToEquipBodySeqAndPart(equip_body_index)

    --  获取人物装备在 装备肉身中 0-374 中的index序号
    local equip_body_index = EquipWGData.Instance:GetEquipBodyIndexByItemCfg(item_cfg)

    -- 通过装备类型获得装备 部位  item_cfg.sub_type == GameEnum.EQUIP_TYPE_XIANFU
    local equip_part = EquipWGData:GetEquipIndexByType(item_cfg.sub_type)

    -- 通过装备部位索引 0-9  获得装备类型  GameEnum.EQUIP_TYPE_TOUKUI/ GameEnum.EQUIP_TYPE_YIFU  头盔/衣服 
    local equip_type = EquipWGData.GetEquipTypeByIndex(equip_part)

    -- 通过 equip_type   和装备阶级 计算装备 0 - 375 index
    local equip_body_index = EquipBodyWGData.Instance:CalRoleEquipWearIndex(equip_type, item_cfg.order)
    
    -- 获取装备类型 普通装备 GameEnum.EQUIP_BIG_TYPE_NORMAL / 仙器装备GameEnum.EQUIP_BIG_TYPE_XIANQI
    local equip_big_type = EquipmentWGData.GetEquipSuitTypeByPartType(equip_part)
end