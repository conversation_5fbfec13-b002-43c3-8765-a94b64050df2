return {
	actorController = {
		projectiles = {},

		hurts = {},

		beHurtEffecct = {},

		hurtEffectName = "",
		beHurtNodeName = "",
		beHurtAttach = false,
		hurtEffectFreeDelay = 0.0,
		QualityCtrlList = {},

	},
	actorTriggers = {
		effects = {
			{
				triggerEventName = "attack1/begin",
				triggerDelay = 0.5,
				triggerFreeDelay = 0.0,
				effectGoName = "eff_4024yushou_attack1",
				effectAsset = {
					BundleName = "effects/prefab/model/yushou/4024/eff_4024yushou_attack1_prefab",
					AssetName = "eff_4024yushou_attack1",
				},
				playerAtTarget = false,
				referenceNodeHierarchyPath = "",
				isAttach = false,
				isRotation = true,
				isUseCustomTransform = true,
				offsetPosX = 0.5,
				offsetPosY = 0.0,
				offsetPosZ = -0.5,
				triggerStopEvent = "",
				effectBtnName = "attack1",
				playerAtPos = false,
				ignoreParentScale = false,
			},
			{
				triggerEventName = "rest/begin",
				triggerDelay = 0.2,
				triggerFreeDelay = 0.0,
				effectGoName = "eff_4024yushou_rest",
				effectAsset = {
					BundleName = "effects/prefab/model/yushou/4024/eff_4024yushou_rest_prefab",
					AssetName = "eff_4024yushou_rest",
				},
				playerAtTarget = false,
				referenceNodeHierarchyPath = "root",
				isAttach = true,
				isRotation = false,
				isUseCustomTransform = true,
				offsetPosX = 0.0,
				offsetPosY = 1.0,
				offsetPosZ = -1.5,
				triggerStopEvent = "",
				effectBtnName = "rest",
				playerAtPos = false,
				ignoreParentScale = false,
			},
		},
		halts = {},

		sounds = {},

		cameraShakes = {},

		cameraFOVs = {},

		sceneFades = {},

		footsteps = {},

	},
	actorBlinker = {
		blinkFadeIn = 0.0,
		blinkFadeHold = 0.0,
		blinkFadeOut = 0.0,
	},
	TimeLineList = {},

}