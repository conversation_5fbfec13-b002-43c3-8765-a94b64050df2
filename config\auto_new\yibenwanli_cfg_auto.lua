-- Y-一本万利.xls
local item_table={
[1]={item_id=26200,num=8,is_bind=1},
[2]={item_id=65531,num=300,is_bind=0},
[3]={item_id=26203,num=10,is_bind=1},
[4]={item_id=65531,num=600,is_bind=0},
[5]={item_id=65534,num=60,is_bind=0},
[6]={item_id=27712,num=1,is_bind=1},
[7]={item_id=65531,num=1000,is_bind=0},
[8]={item_id=65534,num=100,is_bind=0},
[9]={item_id=27703,num=5,is_bind=1},
[10]={item_id=27700,num=20,is_bind=1},
[11]={item_id=65531,num=1800,is_bind=0},
[12]={item_id=26353,num=10,is_bind=1},
[13]={item_id=28036,num=3,is_bind=1},
[14]={item_id=65531,num=3000,is_bind=0},
[15]={item_id=43074,num=1,is_bind=1},
[16]={item_id=27703,num=1,is_bind=1},
[17]={item_id=27700,num=5,is_bind=1},
[18]={item_id=22008,num=1,is_bind=1},
[19]={item_id=36553,num=1,is_bind=1},
[20]={item_id=26367,num=2,is_bind=1},
[21]={item_id=26344,num=5,is_bind=1},
[22]={item_id=26358,num=2,is_bind=1},
[23]={item_id=26349,num=5,is_bind=1},
[24]={item_id=36314,num=1,is_bind=1},
[25]={item_id=27820,num=2,is_bind=1},
[26]={item_id=27611,num=5,is_bind=1},
[27]={item_id=26355,num=2,is_bind=1},
[28]={item_id=26346,num=5,is_bind=1},
[29]={item_id=26363,num=1,is_bind=1},
[30]={item_id=27703,num=2,is_bind=1},
[31]={item_id=27900,num=200,is_bind=1},
[32]={item_id=26374,num=2,is_bind=1},
[33]={item_id=26370,num=5,is_bind=1},
[34]={item_id=27802,num=1,is_bind=1},
[35]={item_id=28037,num=1,is_bind=1},
[36]={item_id=27719,num=1,is_bind=1},
[37]={item_id=27861,num=1,is_bind=1},
[38]={item_id=27903,num=3,is_bind=1},
[39]={item_id=36359,num=1,is_bind=1},
[40]={item_id=26368,num=2,is_bind=1},
[41]={item_id=26356,num=2,is_bind=1},
[42]={item_id=22753,num=1,is_bind=1},
[43]={item_id=27821,num=2,is_bind=1},
[44]={item_id=26359,num=2,is_bind=1},
[45]={item_id=26373,num=2,is_bind=1},
[46]={item_id=27726,num=1,is_bind=1},
[47]={item_id=27720,num=1,is_bind=1},
[48]={item_id=28038,num=1,is_bind=1},
[49]={item_id=28039,num=1,is_bind=1},
[50]={item_id=36361,num=1,is_bind=1},
[51]={item_id=36321,num=1,is_bind=1},
[52]={item_id=36318,num=1,is_bind=1},
[53]={item_id=27730,num=1,is_bind=1},
[54]={item_id=36360,num=1,is_bind=1},
[55]={item_id=36319,num=1,is_bind=1},
[56]={item_id=26200,num=1,is_bind=1},
[57]={item_id=26203,num=1,is_bind=1},
}

return {
other={
{}
},

other_meta_table_map={
},
period={
{},
{period=2,daily_open_day=8,daily_close_day=14,recharge_open_day=8,recharge_close_day=21,},
{period=3,daily_open_day=15,daily_close_day=21,recharge_open_day=22,recharge_close_day=35,},
{period=4,daily_open_day=22,daily_close_day=28,recharge_open_day=36,recharge_close_day=49,},
{period=5,daily_open_day=29,daily_close_day=35,recharge_open_day=50,recharge_close_day=63,},
{period=6,daily_open_day=36,daily_close_day=42,recharge_open_day=64,recharge_close_day=77,},
{period=7,daily_open_day=43,daily_close_day=49,recharge_open_day=78,recharge_close_day=91,},
{period=8,daily_open_day=50,daily_close_day=56,recharge_open_day=92,recharge_close_day=105,},
{period=9,daily_open_day=57,daily_close_day=63,recharge_open_day=106,recharge_close_day=119,},
{period=10,daily_open_day=64,daily_close_day=70,recharge_open_day=120,recharge_close_day=133,},
{period=11,daily_open_day=71,daily_close_day=77,recharge_open_day=134,recharge_close_day=147,}
},

period_meta_table_map={
},
daily={
{},
{seq=2,buy_cost=1,reward_item={[0]=item_table[1]},reward_silver_ticket=300,gift_name="先天大礼",gift_value="<color=#5e2c11>礼包价值</color><color=#f80d0d>10元</color>",reward_show={[0]=item_table[2],[1]=item_table[1]},},
{seq=3,buy_cost=6,reward_item={[0]=item_table[3]},reward_gold=60,reward_silver_ticket=600,gift_name="金丹大礼",gift_value="<color=#5e2c11>礼包价值</color><color=#f80d0d>27元</color>",reward_show={[0]=item_table[4],[1]=item_table[5],[2]=item_table[3]},},
{seq=4,buy_cost=10,reward_item={[0]=item_table[6]},reward_gold=100,reward_silver_ticket=1000,gift_name="元婴大礼",gift_value="<color=#5e2c11>礼包价值</color><color=#f80d0d>45元</color>",reward_show={[0]=item_table[7],[1]=item_table[8],[2]=item_table[6]},},
{seq=5,buy_cost=18,reward_item={[0]=item_table[9],[1]=item_table[10]},reward_silver_ticket=1800,gift_name="洞虚大礼",gift_value="<color=#5e2c11>礼包价值</color><color=#f80d0d>68元</color>",reward_show={[0]=item_table[11],[1]=item_table[9],[2]=item_table[10]},},
{seq=6,buy_cost=30,reward_item={[0]=item_table[12],[1]=item_table[13]},reward_silver_ticket=3000,gift_name="渡劫大礼",gift_value="<color=#5e2c11>礼包价值</color><color=#f80d0d>100元</color>",reward_show={[0]=item_table[14],[1]=item_table[12],[2]=item_table[13]},},
{period=2,},
{period=2,},
{period=2,},
{period=2,},
{period=2,},
{period=2,},
{period=3,},
{period=3,},
{period=3,},
{period=3,},
{period=3,},
{period=3,},
{period=4,},
{period=4,},
{period=4,},
{period=4,},
{period=4,},
{period=4,},
{period=5,},
{period=5,},
{period=5,},
{period=5,},
{period=5,},
{period=5,},
{period=6,},
{period=6,},
{period=6,},
{period=6,},
{period=6,},
{period=6,},
{period=7,},
{period=7,},
{period=7,},
{period=7,},
{period=7,},
{period=7,},
{period=8,},
{period=8,},
{period=8,},
{period=8,},
{period=8,},
{period=8,},
{period=9,},
{period=9,},
{period=9,},
{period=9,},
{period=9,},
{period=9,},
{period=10,},
{period=10,},
{period=10,},
{period=10,},
{period=10,},
{period=10,},
{period=11,},
{period=11,},
{period=11,},
{period=11,},
{period=11,},
{period=11,}
},

daily_meta_table_map={
[65]=5,	-- depth:1
[35]=65,	-- depth:2
[36]=6,	-- depth:1
[62]=2,	-- depth:1
[38]=62,	-- depth:2
[41]=35,	-- depth:3
[42]=36,	-- depth:2
[44]=38,	-- depth:3
[47]=41,	-- depth:4
[48]=42,	-- depth:3
[32]=44,	-- depth:4
[50]=32,	-- depth:5
[53]=47,	-- depth:5
[54]=48,	-- depth:4
[56]=50,	-- depth:6
[60]=54,	-- depth:5
[59]=53,	-- depth:6
[66]=60,	-- depth:6
[29]=59,	-- depth:7
[11]=29,	-- depth:8
[30]=66,	-- depth:7
[17]=11,	-- depth:9
[18]=30,	-- depth:8
[20]=56,	-- depth:7
[12]=18,	-- depth:9
[23]=17,	-- depth:10
[14]=20,	-- depth:8
[24]=12,	-- depth:10
[26]=14,	-- depth:9
[8]=26,	-- depth:10
[58]=4,	-- depth:1
[57]=3,	-- depth:1
[10]=58,	-- depth:2
[63]=57,	-- depth:2
[64]=10,	-- depth:3
[9]=63,	-- depth:3
[16]=64,	-- depth:4
[51]=9,	-- depth:4
[15]=51,	-- depth:5
[46]=16,	-- depth:5
[45]=15,	-- depth:6
[21]=45,	-- depth:7
[22]=46,	-- depth:6
[40]=22,	-- depth:7
[39]=21,	-- depth:8
[27]=39,	-- depth:9
[28]=40,	-- depth:8
[34]=28,	-- depth:9
[52]=34,	-- depth:10
[33]=27,	-- depth:10
},
recharge={
{period=1,daily_item={[0]=item_table[15],[1]=item_table[16],[2]=item_table[17],[3]=item_table[18]},accrued_item={[0]=item_table[19]},model_id=20157001,model_scale=0.9,model_pos="0.29|-1.97|0",model_type=6,model_des="7天即送橙色诸神-缚龙刀尊",banner_des=5,},
{period=1,daily_item={[0]=item_table[15],[1]=item_table[20],[2]=item_table[21],[3]=item_table[18]},model_id=20157001,model_scale=0.9,model_pos="0.29|-1.97|0",model_type=6,model_des="7天即送橙色诸神-缚龙刀尊",banner_des=5,},
{day=3,daily_item={[0]=item_table[15],[1]=item_table[22],[2]=item_table[23],[3]=item_table[18]},accrued_item={[0]=item_table[24]},},
{day=4,daily_item={[0]=item_table[15],[1]=item_table[25],[2]=item_table[26],[3]=item_table[18]},},
{day=5,daily_item={[0]=item_table[15],[1]=item_table[27],[2]=item_table[28],[3]=item_table[18]},accrued_item={[0]=item_table[29]},},
{day=6,daily_item={[0]=item_table[15],[1]=item_table[30],[2]=item_table[31],[3]=item_table[18]},},
{day=7,daily_item={[0]=item_table[15],[1]=item_table[32],[2]=item_table[33],[3]=item_table[18]},accrued_item={[0]=item_table[34]},},
{daily_item={[0]=item_table[35],[1]=item_table[16],[2]=item_table[17],[3]=item_table[18]},accrued_item={[0]=item_table[36]},},
{day=2,daily_item={[0]=item_table[35],[1]=item_table[20],[2]=item_table[21],[3]=item_table[18]},},
{day=3,daily_item={[0]=item_table[35],[1]=item_table[27],[2]=item_table[28],[3]=item_table[18]},},
{day=4,daily_item={[0]=item_table[35],[1]=item_table[25],[2]=item_table[26],[3]=item_table[18]},},
{day=5,daily_item={[0]=item_table[35],[1]=item_table[22],[2]=item_table[23],[3]=item_table[18]},accrued_item={[0]=item_table[37]},},
{day=6,daily_item={[0]=item_table[35],[1]=item_table[32],[2]=item_table[33],[3]=item_table[18]},},
{day=7,daily_item={[0]=item_table[35],[1]=item_table[38],[2]=item_table[31],[3]=item_table[18]},accrued_item={[0]=item_table[39]},},
{day=8,},
{day=9,daily_item={[0]=item_table[35],[1]=item_table[40],[2]=item_table[21],[3]=item_table[18]},},
{day=10,daily_item={[0]=item_table[35],[1]=item_table[41],[2]=item_table[28],[3]=item_table[18]},accrued_item={[0]=item_table[42]},},
{day=11,daily_item={[0]=item_table[35],[1]=item_table[43],[2]=item_table[26],[3]=item_table[18]},},
{day=12,daily_item={[0]=item_table[35],[1]=item_table[44],[2]=item_table[23],[3]=item_table[18]},},
{day=13,daily_item={[0]=item_table[35],[1]=item_table[45],[2]=item_table[33],[3]=item_table[18]},},
{day=14,accrued_item={[0]=item_table[46]},},
{period=3,daily_item={[0]=item_table[35],[1]=item_table[16],[2]=item_table[17],[3]=item_table[18]},},
{period=3,daily_item={[0]=item_table[35],[1]=item_table[20],[2]=item_table[21],[3]=item_table[18]},},
{period=3,daily_item={[0]=item_table[35],[1]=item_table[27],[2]=item_table[28],[3]=item_table[18]},},
{period=3,daily_item={[0]=item_table[35],[1]=item_table[25],[2]=item_table[26],[3]=item_table[18]},},
{period=3,daily_item={[0]=item_table[35],[1]=item_table[22],[2]=item_table[23],[3]=item_table[18]},},
{day=6,daily_item={[0]=item_table[35],[1]=item_table[32],[2]=item_table[33],[3]=item_table[18]},},
{period=3,daily_item={[0]=item_table[35],[1]=item_table[38],[2]=item_table[31],[3]=item_table[18]},},
{day=8,accrued_item={[0]=item_table[47]},},
{day=9,daily_item={[0]=item_table[35],[1]=item_table[40],[2]=item_table[21],[3]=item_table[18]},},
{period=3,daily_item={[0]=item_table[35],[1]=item_table[41],[2]=item_table[28],[3]=item_table[18]},},
{day=11,daily_item={[0]=item_table[35],[1]=item_table[43],[2]=item_table[26],[3]=item_table[18]},},
{period=3,daily_item={[0]=item_table[35],[1]=item_table[44],[2]=item_table[23],[3]=item_table[18]},},
{day=13,daily_item={[0]=item_table[35],[1]=item_table[45],[2]=item_table[33],[3]=item_table[18]},},
{period=3,daily_item={[0]=item_table[35],[1]=item_table[38],[2]=item_table[31],[3]=item_table[18]},},
{period=4,daily_item={[0]=item_table[48],[1]=item_table[16],[2]=item_table[17],[3]=item_table[18]},},
{period=4,daily_item={[0]=item_table[48],[1]=item_table[20],[2]=item_table[21],[3]=item_table[18]},},
{day=3,daily_item={[0]=item_table[48],[1]=item_table[27],[2]=item_table[28],[3]=item_table[18]},},
{period=4,daily_item={[0]=item_table[48],[1]=item_table[25],[2]=item_table[26],[3]=item_table[18]},},
{period=4,daily_item={[0]=item_table[48],[1]=item_table[22],[2]=item_table[23],[3]=item_table[18]},},
{period=4,daily_item={[0]=item_table[48],[1]=item_table[32],[2]=item_table[33],[3]=item_table[18]},},
{period=4,daily_item={[0]=item_table[48],[1]=item_table[38],[2]=item_table[31],[3]=item_table[18]},},
{day=8,accrued_item={[0]=item_table[47]},},
{period=4,daily_item={[0]=item_table[48],[1]=item_table[40],[2]=item_table[21],[3]=item_table[18]},},
{period=4,daily_item={[0]=item_table[48],[1]=item_table[41],[2]=item_table[28],[3]=item_table[18]},},
{period=4,daily_item={[0]=item_table[48],[1]=item_table[43],[2]=item_table[26],[3]=item_table[18]},},
{period=4,daily_item={[0]=item_table[48],[1]=item_table[44],[2]=item_table[23],[3]=item_table[18]},},
{period=4,daily_item={[0]=item_table[48],[1]=item_table[45],[2]=item_table[33],[3]=item_table[18]},},
{period=4,daily_item={[0]=item_table[48],[1]=item_table[38],[2]=item_table[31],[3]=item_table[18]},},
{period=5,daily_item={[0]=item_table[48],[1]=item_table[16],[2]=item_table[17],[3]=item_table[18]},},
{day=2,daily_item={[0]=item_table[48],[1]=item_table[20],[2]=item_table[21],[3]=item_table[18]},},
{period=5,daily_item={[0]=item_table[48],[1]=item_table[27],[2]=item_table[28],[3]=item_table[18]},},
{period=5,daily_item={[0]=item_table[48],[1]=item_table[25],[2]=item_table[26],[3]=item_table[18]},},
{period=5,daily_item={[0]=item_table[48],[1]=item_table[22],[2]=item_table[23],[3]=item_table[18]},},
{day=6,daily_item={[0]=item_table[48],[1]=item_table[32],[2]=item_table[33],[3]=item_table[18]},},
{period=5,daily_item={[0]=item_table[48],[1]=item_table[38],[2]=item_table[31],[3]=item_table[18]},},
{day=8,accrued_item={[0]=item_table[47]},},
{period=5,daily_item={[0]=item_table[48],[1]=item_table[40],[2]=item_table[21],[3]=item_table[18]},},
{period=5,daily_item={[0]=item_table[48],[1]=item_table[41],[2]=item_table[28],[3]=item_table[18]},},
{period=5,daily_item={[0]=item_table[48],[1]=item_table[43],[2]=item_table[26],[3]=item_table[18]},},
{period=5,daily_item={[0]=item_table[48],[1]=item_table[44],[2]=item_table[23],[3]=item_table[18]},},
{period=5,daily_item={[0]=item_table[48],[1]=item_table[45],[2]=item_table[33],[3]=item_table[18]},},
{period=5,daily_item={[0]=item_table[48],[1]=item_table[38],[2]=item_table[31],[3]=item_table[18]},},
{period=6,},
{period=6,},
{period=6,daily_item={[0]=item_table[49],[1]=item_table[27],[2]=item_table[28],[3]=item_table[18]},},
{period=6,},
{period=6,daily_item={[0]=item_table[49],[1]=item_table[22],[2]=item_table[23],[3]=item_table[18]},},
{period=6,daily_item={[0]=item_table[49],[1]=item_table[32],[2]=item_table[33],[3]=item_table[18]},},
{period=6,accrued_item={[0]=item_table[50]},},
{period=6,},
{period=6,},
{period=6,daily_item={[0]=item_table[49],[1]=item_table[41],[2]=item_table[28],[3]=item_table[18]},},
{period=6,},
{day=12,daily_item={[0]=item_table[49],[1]=item_table[44],[2]=item_table[23],[3]=item_table[18]},},
{period=6,},
{day=14,accrued_item={[0]=item_table[46]},},
{period=7,accrued_item={[0]=item_table[36]},model_id=10033001,model_scale=1.3,model_pos="0.15|-1.78|0",model_des="14天即送红色宠物-怒焰",banner_des="3|4",},
{period=7,},
{period=7,},
{period=7,},
{period=7,},
{period=7,},
{day=7,accrued_item={[0]=item_table[51]},},
{day=8,accrued_item={[0]=item_table[47]},},
{period=7,day=9,daily_item={[0]=item_table[49],[1]=item_table[40],[2]=item_table[21],[3]=item_table[18]},},
{period=7,},
{day=11,daily_item={[0]=item_table[49],[1]=item_table[43],[2]=item_table[26],[3]=item_table[18]},},
{period=7,},
{day=13,daily_item={[0]=item_table[49],[1]=item_table[45],[2]=item_table[33],[3]=item_table[18]},},
{period=7,},
{period=8,},
{period=8,daily_item={[0]=item_table[49],[1]=item_table[20],[2]=item_table[21],[3]=item_table[18]},},
{period=8,},
{period=8,},
{period=8,},
{period=8,},
{period=8,accrued_item={[0]=item_table[39]},},
{period=8,},
{period=8,},
{period=8,},
{period=8,},
{period=8,},
{period=8,},
{period=8,},
{period=9,},
{day=2,daily_item={[0]=item_table[49],[1]=item_table[20],[2]=item_table[21],[3]=item_table[18]},},
{period=9,},
{period=9,day=4,daily_item={[0]=item_table[49],[1]=item_table[25],[2]=item_table[26],[3]=item_table[18]},model_id=10033001,model_scale=1.3,model_pos="0.15|-1.78|0",model_des="14天即送红色宠物-怒焰",banner_des="3|4",},
{period=9,},
{day=6,daily_item={[0]=item_table[49],[1]=item_table[32],[2]=item_table[33],[3]=item_table[18]},},
{day=7,accrued_item={[0]=item_table[52]},},
{period=9,},
{period=9,},
{period=9,},
{period=9,},
{day=12,daily_item={[0]=item_table[49],[1]=item_table[44],[2]=item_table[23],[3]=item_table[18]},accrued_item={[0]=item_table[37]},},
{period=9,},
{day=14,daily_item={[0]=item_table[49],[1]=item_table[38],[2]=item_table[31],[3]=item_table[18]},accrued_item={[0]=item_table[53]},},
{period=10,accrued_item={[0]=item_table[36]},},
{period=10,},
{period=10,},
{period=10,daily_item={[0]=item_table[49],[1]=item_table[25],[2]=item_table[26],[3]=item_table[18]},},
{period=10,},
{period=10,},
{period=10,day=7,daily_item={[0]=item_table[49],[1]=item_table[38],[2]=item_table[31],[3]=item_table[18]},accrued_item={[0]=item_table[54]},},
{day=8,accrued_item={[0]=item_table[47]},},
{period=10,daily_item={[0]=item_table[49],[1]=item_table[40],[2]=item_table[21],[3]=item_table[18]},},
{period=10,},
{period=10,daily_item={[0]=item_table[49],[1]=item_table[43],[2]=item_table[26],[3]=item_table[18]},},
{period=10,},
{period=10,daily_item={[0]=item_table[49],[1]=item_table[45],[2]=item_table[33],[3]=item_table[18]},},
{period=10,},
{period=11,},
{period=11,},
{day=3,daily_item={[0]=item_table[49],[1]=item_table[27],[2]=item_table[28],[3]=item_table[18]},},
{period=11,},
{day=5,daily_item={[0]=item_table[49],[1]=item_table[22],[2]=item_table[23],[3]=item_table[18]},accrued_item={[0]=item_table[37]},},
{period=11,},
{period=11,day=7,accrued_item={[0]=item_table[55]},},
{period=11,},
{period=11,},
{day=10,daily_item={[0]=item_table[49],[1]=item_table[41],[2]=item_table[28],[3]=item_table[18]},accrued_item={[0]=item_table[42]},},
{period=11,},
{period=11,},
{period=11,},
{period=11,}
},

recharge_meta_table_map={
[64]=120,	-- depth:1
[92]=64,	-- depth:2
[130]=18,	-- depth:1
[93]=9,	-- depth:1
[48]=20,	-- depth:1
[46]=18,	-- depth:1
[44]=16,	-- depth:1
[132]=20,	-- depth:1
[123]=11,	-- depth:1
[39]=11,	-- depth:1
[74]=130,	-- depth:2
[37]=9,	-- depth:1
[128]=16,	-- depth:1
[127]=120,	-- depth:1
[65]=93,	-- depth:2
[67]=123,	-- depth:2
[69]=13,	-- depth:1
[71]=127,	-- depth:2
[72]=128,	-- depth:2
[76]=132,	-- depth:2
[125]=69,	-- depth:2
[121]=65,	-- depth:3
[36]=8,	-- depth:1
[41]=13,	-- depth:1
[95]=67,	-- depth:3
[104]=76,	-- depth:3
[15]=8,	-- depth:1
[102]=74,	-- depth:3
[100]=72,	-- depth:3
[21]=14,	-- depth:1
[19]=12,	-- depth:1
[99]=71,	-- depth:3
[97]=125,	-- depth:3
[10]=17,	-- depth:1
[66]=10,	-- depth:2
[122]=66,	-- depth:3
[68]=12,	-- depth:1
[70]=126,	-- depth:1
[73]=17,	-- depth:1
[75]=68,	-- depth:2
[77]=70,	-- depth:2
[129]=73,	-- depth:2
[124]=68,	-- depth:2
[131]=75,	-- depth:3
[105]=77,	-- depth:3
[103]=131,	-- depth:4
[49]=21,	-- depth:2
[47]=19,	-- depth:2
[101]=129,	-- depth:3
[45]=17,	-- depth:1
[43]=36,	-- depth:2
[42]=126,	-- depth:1
[40]=12,	-- depth:1
[94]=122,	-- depth:4
[98]=126,	-- depth:1
[133]=105,	-- depth:4
[38]=45,	-- depth:2
[96]=124,	-- depth:3
[134]=78,	-- depth:1
[106]=134,	-- depth:2
[86]=109,	-- depth:1
[90]=86,	-- depth:2
[111]=109,	-- depth:1
[88]=86,	-- depth:2
[85]=78,	-- depth:1
[113]=85,	-- depth:2
[118]=90,	-- depth:3
[107]=109,	-- depth:1
[58]=86,	-- depth:2
[81]=109,	-- depth:1
[144]=88,	-- depth:3
[142]=86,	-- depth:2
[141]=113,	-- depth:3
[22]=78,	-- depth:1
[23]=107,	-- depth:2
[139]=111,	-- depth:2
[25]=109,	-- depth:1
[27]=25,	-- depth:2
[83]=139,	-- depth:3
[30]=25,	-- depth:2
[135]=107,	-- depth:2
[137]=81,	-- depth:2
[34]=25,	-- depth:2
[79]=135,	-- depth:3
[146]=118,	-- depth:4
[62]=34,	-- depth:3
[32]=25,	-- depth:2
[60]=32,	-- depth:3
[114]=142,	-- depth:3
[116]=144,	-- depth:4
[53]=109,	-- depth:1
[51]=53,	-- depth:2
[50]=78,	-- depth:1
[55]=53,	-- depth:2
[138]=134,	-- depth:2
[119]=109,	-- depth:1
[143]=134,	-- depth:2
[136]=143,	-- depth:3
[117]=109,	-- depth:1
[145]=117,	-- depth:2
[140]=119,	-- depth:2
[112]=119,	-- depth:2
[2]=9,	-- depth:1
[4]=2,	-- depth:2
[6]=2,	-- depth:2
[24]=136,	-- depth:4
[26]=138,	-- depth:3
[28]=112,	-- depth:3
[29]=22,	-- depth:2
[31]=143,	-- depth:3
[33]=117,	-- depth:2
[35]=119,	-- depth:2
[115]=143,	-- depth:3
[54]=138,	-- depth:3
[52]=136,	-- depth:4
[57]=50,	-- depth:2
[110]=138,	-- depth:3
[108]=136,	-- depth:4
[91]=119,	-- depth:2
[56]=140,	-- depth:3
[87]=115,	-- depth:4
[89]=145,	-- depth:3
[82]=110,	-- depth:4
[80]=108,	-- depth:5
[63]=119,	-- depth:2
[61]=117,	-- depth:2
[59]=143,	-- depth:3
[84]=91,	-- depth:3
[147]=91,	-- depth:3
[7]=1,	-- depth:1
[5]=1,	-- depth:1
[3]=1,	-- depth:1
},
other_default_table={level_limit=1,open_game_day_limit=1,force_flush_day=14,cycle_period=6,limit1=1,limit2=1,limit3=1,limit4=70,limit5=70,limit6=30,},

period_default_table={period=1,daily_open_day=1,daily_close_day=7,recharge_open_day=1,recharge_close_day=7,},

daily_default_table={period=1,seq=1,buy_cost=0,buy_limit=1,reward_item={[0]=item_table[56],[1]=item_table[57]},reward_gold=0,reward_silver_ticket=0,gift_name="免费大礼",gift_value="<color=#5e2c11>礼包价值</color><color=#f80d0d>3元</color>",reward_show={[0]=item_table[56],[1]=item_table[57]},},

recharge_default_table={period=2,day=1,require_gold=300,daily_item={[0]=item_table[49],[1]=item_table[16],[2]=item_table[17],[3]=item_table[18]},accrued_item={},model_id=10028001,model_scale=1.8,model_rotate="0|160|0",model_pos="0.15|-2.2|0",model_type=2,model_des="14天即送红色宠物-哪吒",banner_des="1|2",}

}

