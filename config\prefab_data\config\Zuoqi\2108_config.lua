return {
	actorController = {
		projectiles = {},

		hurts = {},

		beHurtEffecct = {},

		hurtEffectName = "",
		beHurtNodeName = "",
		beHurtAttach = false,
		hurtEffectFreeDelay = 0.0,
		QualityCtrlList = {},

	},
	actorTriggers = {
		effects = {},

		halts = {},

		sounds = {
			{
				soundEventName = "attack1/begin",
				soundDelay = 0.0,
				soundAudioAsset = {
					BundleName = "audios/sfxs/roleskill/zhandouzuoqi_2016",
					AssetName = "zhandouzuoqi_attack1",
				},
				soundAudioGoName = "zhandouzuoqi_attack1",
				soundBtnName = "attack1",
				soundIsMainRole = false,
			},
			{
				soundEventName = "attack2/begin",
				soundDelay = 0.0,
				soundAudioAsset = {
					BundleName = "audios/sfxs/roleskill/zhandouzuoqi_2016",
					AssetName = "zhandouzuoqi_attack2",
				},
				soundAudioGoName = "zhandouzuoqi_attack2",
				soundBtnName = "attack2",
				soundIsMainRole = false,
			},
			{
				soundEventName = "attack3/begin",
				soundDelay = 0.0,
				soundAudioAsset = {
					BundleName = "audios/sfxs/roleskill/zhandouzuoqi_2016",
					AssetName = "zhandouzuoqi_attack4",
				},
				soundAudioGoName = "zhandouzuoqi_attack4",
				soundBtnName = "attack3",
				soundIsMainRole = false,
			},
			{
				soundEventName = "attack4/begin",
				soundDelay = 0.0,
				soundAudioAsset = {
					BundleName = "audios/sfxs/roleskill/zhandouzuoqi_2016",
					AssetName = "zhandouzuoqi_attack3",
				},
				soundAudioGoName = "zhandouzuoqi_attack3",
				soundBtnName = "attack4",
				soundIsMainRole = false,
			},
			{
				soundEventName = "combo1_1/begin",
				soundDelay = 0.0,
				soundAudioAsset = {
					BundleName = "audios/sfxs/roleskill/zhandouzuoqi_2016",
					AssetName = "zhandouzuoqi_combo1_1",
				},
				soundAudioGoName = "zhandouzuoqi_combo1_1",
				soundBtnName = "combo1",
				soundIsMainRole = false,
			},
		},
		cameraShakes = {
			{
				CameraShakeBtnName = "attack1_shake",
				eventName = "attack1/begin",
				numberOfShakes = 15,
				distance = 0.36,
				speed = 150.0,
				delay = 1.4,
				decay = 0.08,
			},
			{
				CameraShakeBtnName = "attack2_shake",
				eventName = "attack2/begin",
				numberOfShakes = 20,
				distance = 0.5,
				speed = 150.0,
				delay = 1.2,
				decay = 0.05,
			},
			{
				CameraShakeBtnName = "attack3_shake",
				eventName = "attack3/begin",
				numberOfShakes = 30,
				distance = 0.8,
				speed = 150.0,
				delay = 0.3,
				decay = 0.08,
			},
			{
				CameraShakeBtnName = "attack4_1shake",
				eventName = "attack4/begin",
				numberOfShakes = 5,
				distance = 0.8,
				speed = 120.0,
				delay = 0.4,
				decay = 0.05,
			},
			{
				CameraShakeBtnName = "attack4_2shake",
				eventName = "attack4/begin",
				numberOfShakes = 10,
				distance = 1.0,
				speed = 100.0,
				delay = 1.5,
				decay = 0.1,
			},
		},
		cameraFOVs = {},

		sceneFades = {},

		footsteps = {},

	},
	actorBlinker = {
		blinkFadeIn = 0.0,
		blinkFadeHold = 0.0,
		blinkFadeOut = 0.0,
	},
	TimeLineList = {},

}