-- C-充值奖励.xls
local item_table={
[1]={item_id=22000,num=1,is_bind=1},
}

return {
reward={
[0]={seq=0,},
[1]={seq=1,chongzhi=300,extra_bind_gold=300,again_reward_param=150,cangjinshangpu_score=300,discretion="首次购买额外赠送\n<color=#ffff00>300元宝</color>",show_discretion="首次购买额外赠送\n<color=#ffff00>300元宝</color>",shenhe_discretion="首次购买额外赠送\n<color=#ffff00>300元宝</color>",},
[2]={seq=2,chongzhi=680,extra_bind_gold=680,again_reward_param=340,cangjinshangpu_score=680,discretion="首次购买额外赠送\n<color=#ffff00>680元宝</color>",show_discretion="首次购买额外赠送\n<color=#ffff00>680元宝</color>",shenhe_discretion="首次购买额外赠送\n<color=#ffff00>680元宝</color>",},
[3]={seq=3,chongzhi=980,extra_bind_gold=980,again_reward_param=490,cangjinshangpu_score=980,discretion="首次购买额外赠送\n<color=#ffff00>980元宝</color>",show_discretion="首次购买额外赠送\n<color=#ffff00>980元宝</color>",shenhe_discretion="首次购买额外赠送\n<color=#ffff00>980元宝</color>",},
[4]={seq=4,chongzhi=1280,extra_bind_gold=1280,again_reward_param=640,cangjinshangpu_score=1280,discretion="首次购买额外赠送\n<color=#ffff00>1280元宝</color>",show_discretion="首次购买额外赠送\n<color=#ffff00>1280元宝</color>",shenhe_discretion="首次购买额外赠送\n<color=#ffff00>1280元宝</color>",},
[5]={seq=5,chongzhi=1980,extra_bind_gold=1980,again_reward_param=990,cangjinshangpu_score=1980,discretion="首次购买额外赠送\n<color=#ffff00>1980元宝</color>",show_discretion="首次购买额外赠送\n<color=#ffff00>1980元宝</color>",shenhe_discretion="首次购买额外赠送\n<color=#ffff00>1980元宝</color>",},
[6]={seq=6,chongzhi=3280,extra_bind_gold=3280,again_reward_param=1640,cangjinshangpu_score=3280,discretion="首次购买额外赠送\n<color=#ffff00>3280元宝</color>",show_discretion="首次购买额外赠送\n<color=#ffff00>3280元宝</color>",shenhe_discretion="首次购买额外赠送\n<color=#ffff00>3280元宝</color>",},
[7]={seq=7,chongzhi=6480,extra_bind_gold=6480,again_reward_param=3240,cangjinshangpu_score=6480,discretion="首次购买额外赠送\n<color=#ffff00>6480元宝</color>",show_discretion="首次购买额外赠送\n<color=#ffff00>6480元宝</color>",shenhe_discretion="首次购买额外赠送\n<color=#ffff00>6480元宝</color>",},
[8]={seq=8,chongzhi=10000,extra_bind_gold=10000,again_reward_param=5000,cangjinshangpu_score=10000,discretion="首次购买额外赠送\n<color=#ffff00>10000元宝</color>",show_discretion="首次购买额外赠送\n<color=#ffff00>10000元宝</color>",shenhe_discretion="首次购买额外赠送\n<color=#ffff00>10000元宝</color>",},
[9]={seq=9,chongzhi=20000,extra_bind_gold=20000,again_reward_param=10000,cangjinshangpu_score=20000,discretion="首次购买额外赠送\n<color=#ffff00>20000元宝</color>",show_discretion="首次购买额外赠送\n<color=#ffff00>20000元宝</color>",shenhe_discretion="首次购买额外赠送\n<color=#ffff00>20000元宝</color>",},
[10]={seq=10,chongzhi=50000,extra_bind_gold=50000,again_reward_param=25000,cangjinshangpu_score=50000,discretion="首次购买额外赠送\n<color=#ffff00>50000元宝</color>",show_discretion="首次购买额外赠送\n<color=#ffff00>50000元宝</color>",shenhe_discretion="首次购买额外赠送\n<color=#ffff00>50000元宝</color>",},
[11]={seq=11,chongzhi=100000,extra_bind_gold=100000,again_reward_param=50000,cangjinshangpu_score=100000,discretion="首次购买额外赠送\n<color=#ffff00>100000元宝</color>",show_discretion="首次购买额外赠送\n<color=#ffff00>100000元宝</color>",shenhe_discretion="首次购买额外赠送\n<color=#ffff00>100000元宝</color>",},
[12]={seq=12,chongzhi=200000,extra_bind_gold=200000,again_reward_param=100000,cangjinshangpu_score=200000,discretion="首次购买额外赠送\n<color=#ffff00>200000元宝</color>",show_discretion="首次购买额外赠送\n<color=#ffff00>200000元宝</color>",shenhe_discretion="首次购买额外赠送\n<color=#ffff00>200000元宝</color>",}
},

reward_meta_table_map={
},
special_chongzhi_reward={
[0]={seq=0,},
[1]={seq=1,},
[2]={seq=2,}
},

special_chongzhi_reward_meta_table_map={
},
daily_fisrt_chongzhi_reward={
[0]={day=0,},
[1]={day=1,},
[2]={day=2,},
[3]={day=3,},
[4]={day=4,},
[5]={day=5,},
[6]={day=6,}
},

daily_fisrt_chongzhi_reward_meta_table_map={
},
other={
{}
},

other_meta_table_map={
},
daily_total_chongzhi_reward={
{},
{seq=1,need_total_chongzhi=2000,},
{seq=2,need_total_chongzhi=5000,},
{seq=3,need_total_chongzhi=10000,},
{seq=4,need_total_chongzhi=20000,},
{stage=2,},
{stage=2,},
{stage=2,},
{stage=2,},
{stage=2,},
{stage=3,},
{stage=3,},
{stage=3,},
{stage=3,},
{stage=3,},
{stage=4,},
{stage=4,},
{stage=4,},
{stage=4,},
{stage=4,}
},

daily_total_chongzhi_reward_meta_table_map={
[18]=3,	-- depth:1
[17]=2,	-- depth:1
[15]=5,	-- depth:1
[14]=4,	-- depth:1
[10]=15,	-- depth:2
[12]=17,	-- depth:2
[19]=14,	-- depth:2
[9]=19,	-- depth:3
[8]=18,	-- depth:2
[7]=12,	-- depth:3
[13]=8,	-- depth:3
[20]=10,	-- depth:3
},
daily_total_chongzhi_stage={
{},
{stage=2,start_day=3656,end_day=3661,},
{stage=3,start_day=3662,end_day=3667,},
{stage=4,start_day=3668,end_day=3673,}
},

daily_total_chongzhi_stage_meta_table_map={
},
total_chongzhi_want_money={
{},
{state=1,charged=300,random_min=200,random_max=300,},
{state=2,charged=500,random_min=300,random_max=450,},
{state=3,charged=1000,random_min=500,random_max=750,},
{state=4,charged=2000,random_min=1000,random_max=1500,}
},

total_chongzhi_want_money_meta_table_map={
},
weekday_total_chongzhi={
{},
{seq=1,need_total_chongzhi=1000,},
{seq=2,need_total_chongzhi=5000,},
{seq=3,need_total_chongzhi=10000,},
{day_of_week=1,},
{day_of_week=1,},
{day_of_week=1,},
{day_of_week=1,},
{day_of_week=2,},
{day_of_week=2,},
{day_of_week=2,},
{day_of_week=2,},
{day_of_week=3,},
{day_of_week=3,},
{day_of_week=3,},
{day_of_week=3,},
{day_of_week=4,},
{day_of_week=4,},
{day_of_week=4,},
{day_of_week=4,},
{day_of_week=5,},
{day_of_week=5,},
{day_of_week=5,},
{day_of_week=5,},
{day_of_week=6,},
{day_of_week=6,},
{day_of_week=6,},
{day_of_week=6,}
},

weekday_total_chongzhi_meta_table_map={
[26]=2,	-- depth:1
[24]=4,	-- depth:1
[18]=26,	-- depth:2
[23]=3,	-- depth:1
[22]=18,	-- depth:3
[20]=24,	-- depth:2
[19]=23,	-- depth:2
[14]=22,	-- depth:4
[15]=19,	-- depth:3
[27]=15,	-- depth:4
[12]=20,	-- depth:3
[11]=27,	-- depth:5
[10]=14,	-- depth:5
[8]=12,	-- depth:4
[7]=11,	-- depth:6
[6]=10,	-- depth:6
[16]=8,	-- depth:5
[28]=16,	-- depth:6
},
reward_default_table={seq=0,chongzhi=60,reward_type=1,extra_bind_gold=60,extra_coin=0,extra_gold=0,again_reward_type=1,again_reward_param=30,cangjinshangpu_score=60,show_rule=0,show_gold=1,discretion="首次购买额外赠送\n<color=#ffff00>60元宝</color>",show_discretion="首次购买额外赠送\n<color=#ffff00>60元宝</color>",shenhe_discretion="首次购买额外赠送\n<color=#ffff00>60元宝</color>",},

special_chongzhi_reward_default_table={seq=0,reward_item=item_table[1],},

daily_fisrt_chongzhi_reward_default_table={day=0,first_recharge=item_table[1],},

other_default_table={},

daily_total_chongzhi_reward_default_table={stage=1,seq=0,need_total_chongzhi=1000,total_recharge=item_table[1],},

daily_total_chongzhi_stage_default_table={stage=1,start_day=3650,end_day=3655,},

total_chongzhi_want_money_default_table={state=0,charged=100,random_min=100,random_max=200,},

weekday_total_chongzhi_default_table={day_of_week=0,seq=0,need_total_chongzhi=500,reward_item=item_table[1],}

}

