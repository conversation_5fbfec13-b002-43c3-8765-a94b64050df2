return {
	actorController = {
		projectiles = {},

		hurts = {},

		beHurtEffecct = {},

		hurtEffectName = "",
		beHurtNodeName = "",
		beHurtAttach = false,
		hurtEffectFreeDelay = 0.0,
		QualityCtrlList = {},

	},
	actorTriggers = {
		effects = {
			{
				triggerEventName = "attack1/begin",
				triggerDelay = 0.6,
				triggerFreeDelay = 0.0,
				effectGoName = "eff_4021yushou_attack1",
				effectAsset = {
					BundleName = "effects/prefab/model/yushou/4021/eff_4021yushou_attack1_prefab",
					AssetName = "eff_4021yushou_attack1",
				},
				playerAtTarget = false,
				referenceNodeHierarchyPath = "",
				isAttach = false,
				isRotation = true,
				isUseCustomTransform = true,
				offsetPosX = 1.0,
				offsetPosY = 1.0,
				offsetPosZ = -1.0,
				triggerStopEvent = "",
				effectBtnName = "attcack1",
				playerAtPos = false,
				ignoreParentScale = false,
			},
			{
				triggerEventName = "attack2/begin",
				triggerDelay = 0.5,
				triggerFreeDelay = 0.0,
				effectGoName = "yushou_skill_3",
				effectAsset = {
					BundleName = "effects/prefab/model/yushou/common/yushou_skill_3_prefab",
					AssetName = "yushou_skill_3",
				},
				playerAtTarget = false,
				referenceNodeHierarchyPath = "",
				isAttach = false,
				isRotation = true,
				isUseCustomTransform = true,
				offsetPosX = 4.0,
				offsetPosY = 1.0,
				offsetPosZ = -3.0,
				triggerStopEvent = "",
				effectBtnName = "attcack2",
				playerAtPos = false,
				ignoreParentScale = false,
			},
			{
				triggerEventName = "rest/begin",
				triggerDelay = 0.6,
				triggerFreeDelay = 0.0,
				effectGoName = "eff_4021yushou_attack1",
				effectAsset = {
					BundleName = "effects/prefab/model/yushou/4021/eff_4021yushou_attack1_prefab",
					AssetName = "eff_4021yushou_attack1",
				},
				playerAtTarget = false,
				referenceNodeHierarchyPath = "root",
				isAttach = true,
				isRotation = false,
				isUseCustomTransform = true,
				offsetPosX = -0.2,
				offsetPosY = 1.0,
				offsetPosZ = 1.5,
				triggerStopEvent = "",
				effectBtnName = "rest",
				playerAtPos = false,
				ignoreParentScale = false,
			},
		},
		halts = {},

		sounds = {},

		cameraShakes = {},

		cameraFOVs = {},

		sceneFades = {},

		footsteps = {},

	},
	actorBlinker = {
		blinkFadeIn = 0.0,
		blinkFadeHold = 0.0,
		blinkFadeOut = 0.0,
	},
	TimeLineList = {},

}