return {
	actorController = {
		projectiles = {},

		hurts = {},

	},
	actorTriggers = {
		effects = {
			{
				triggerEventName = "rest/begin",
				triggerDelay = 0.0,
				triggerFreeDelay = 0.0,
				effectGoName = "10111_skill1",
				effectAsset = {
					BundleName = "effects/prefab/model/tianshen/10111/10111_skill1_prefab",
					AssetName = "10111_skill1",
				},
				playerAtTarget = false,
				referenceNodeHierarchyPath = "hurt_root",
				isAttach = true,
				isRotation = false,
				isUseCustomTransform = true,
				offsetPosX = 0.0,
				offsetPosY = 0.0,
				offsetPosZ = -15.0,
				triggerStopEvent = "",
				effectBtnName = "rest",
				playerAtPos = false,
				ignoreParentScale = false,
			},
			{
				triggerEventName = "attack1/begin",
				triggerDelay = 0.0,
				triggerFreeDelay = 0.0,
				effectGoName = "10111_skill1",
				effectAsset = {
					BundleName = "effects/prefab/model/tianshen/10111/10111_skill1_prefab",
					AssetName = "10111_skill1",
				},
				playerAtTarget = false,
				referenceNodeHierarchyPath = "buff_down",
				isAttach = true,
				isRotation = true,
				isUseCustomTransform = true,
				offsetPosX = 1.5,
				offsetPosY = 0.0,
				offsetPosZ = -8.0,
				triggerStopEvent = "",
				effectBtnName = "attack1",
				playerAtPos = false,
				ignoreParentScale = false,
			},
			{
				triggerEventName = "attack2/begin",
				triggerDelay = 0.0,
				triggerFreeDelay = 0.0,
				effectGoName = "10111_skill2",
				effectAsset = {
					BundleName = "effects/prefab/model/tianshen/10111/10111_skill2_prefab",
					AssetName = "10111_skill2",
				},
				playerAtTarget = false,
				referenceNodeHierarchyPath = "buff_down",
				isAttach = true,
				isRotation = true,
				isUseCustomTransform = true,
				offsetPosX = 1.5,
				offsetPosY = 0.0,
				offsetPosZ = -8.0,
				triggerStopEvent = "",
				effectBtnName = "attack2",
				playerAtPos = false,
				ignoreParentScale = false,
			},
			{
				triggerEventName = "attack3/begin",
				triggerDelay = 0.0,
				triggerFreeDelay = 0.0,
				effectGoName = "10111_skill3",
				effectAsset = {
					BundleName = "effects/prefab/model/tianshen/10111/10111_skill3_prefab",
					AssetName = "10111_skill3",
				},
				playerAtTarget = false,
				referenceNodeHierarchyPath = "buff_down",
				isAttach = true,
				isRotation = true,
				isUseCustomTransform = true,
				offsetPosX = 1.5,
				offsetPosY = 0.0,
				offsetPosZ = -8.0,
				triggerStopEvent = "",
				effectBtnName = "attack3",
				playerAtPos = false,
				ignoreParentScale = false,
			},
			{
				triggerEventName = "combo1_1/begin",
				triggerDelay = 0.0,
				triggerFreeDelay = 0.0,
				effectGoName = "10111_attack1",
				effectAsset = {
					BundleName = "effects/prefab/model/tianshen/10111/10111_attack1_prefab",
					AssetName = "10111_attack1",
				},
				playerAtTarget = false,
				referenceNodeHierarchyPath = "",
				isAttach = false,
				isRotation = true,
				isUseCustomTransform = false,
				offsetPosX = 0.0,
				offsetPosY = 0.0,
				offsetPosZ = 0.0,
				triggerStopEvent = "",
				effectBtnName = "combo1",
				playerAtPos = false,
				ignoreParentScale = false,
			},
		},
		sounds = {
			{
				soundEventName = "combo1_1/begin",
				soundDelay = 0.0,
				soundAudioAsset = {
					BundleName = "audios/sfxs/roleskill/tianshen_10111",
					AssetName = "MingJiangcombo_1_1",
				},
				soundAudioGoName = "MingJiangcombo_1_1",
				soundBtnName = "combo1",
				soundIsMainRole = false,
			},
			{
				soundEventName = "combo1_2/begin",
				soundDelay = 0.0,
				soundAudioAsset = {
					BundleName = "audios/sfxs/roleskill/tianshen_10111",
					AssetName = "MingJiangcombo_1_2",
				},
				soundAudioGoName = "MingJiangcombo_1_2",
				soundBtnName = "combo2",
				soundIsMainRole = false,
			},
			{
				soundEventName = "combo1_3/begin",
				soundDelay = 0.0,
				soundAudioAsset = {
					BundleName = "audios/sfxs/roleskill/tianshen_10111",
					AssetName = "MingJiangcombo_1_3",
				},
				soundAudioGoName = "MingJiangcombo_1_3",
				soundBtnName = "combo3",
				soundIsMainRole = false,
			},
			{
				soundEventName = "attack1/begin",
				soundDelay = 0.0,
				soundAudioAsset = {
					BundleName = "audios/sfxs/roleskill/tianshen_10111",
					AssetName = "MingJiangattack1",
				},
				soundAudioGoName = "MingJiangattack1",
				soundBtnName = "attack1",
				soundIsMainRole = false,
			},
			{
				soundEventName = "attack2/begin",
				soundDelay = 0.0,
				soundAudioAsset = {
					BundleName = "audios/sfxs/roleskill/tianshen_10111",
					AssetName = "MingJiangattack2",
				},
				soundAudioGoName = "MingJiangattack2",
				soundBtnName = "attack2",
				soundIsMainRole = false,
			},
			{
				soundEventName = "attack3/begin",
				soundDelay = 0.0,
				soundAudioAsset = {
					BundleName = "audios/sfxs/roleskill/tianshen_10111",
					AssetName = "MingJiangattack3",
				},
				soundAudioGoName = "MingJiangattack3",
				soundBtnName = "attack3",
				soundIsMainRole = false,
			},
			{
				soundEventName = "combo1_3/begin",
				soundDelay = 0.5,
				soundAudioAsset = {
					BundleName = "audios/sfxs/roleskill/tianshen_10111",
					AssetName = "MingJiangcombo_1_31",
				},
				soundAudioGoName = "MingJiangcombo_1_31",
				soundBtnName = "combo3_1",
				soundIsMainRole = false,
			},
		},
		cameraShakes = {},

		radialBlurs = {},

	},
}