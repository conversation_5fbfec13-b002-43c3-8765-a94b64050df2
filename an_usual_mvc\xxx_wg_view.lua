XXXView = XXXView or BaseClass(SafeBaseView)
function XXXView:__init()
    self.view_style = ViewStyle.Full
    self:SetMaskBg()
    self.default_index = TabIndex.XXX

    --[[
        AddViewResource(index, bundle_name, asset_name, transform_cfg)
        index 界面索引，0为默认值，默认加载显示，其他值，切到对应标签加载显示，其他索引的隐藏 
        bundle_name, asset_name 预制体ab路径
        transform_cfg 预制体的 rect_transform 参数信息
    ]]
    self:AddViewResource(0, "uis/view/common_panel_prefab", "layout_a1_commmon_panel",
                        {vector2 = Vector2(0, 0), sizeDelta = Vector2(1334, 768)})
	self:AddViewResource(TabIndex.xxx, "uis/view/xxx_prefab", "xxx")
	self:AddViewResource(0, "uis/view/common_panel_prefab", "VerticalTabbar")
	self:AddViewResource(0, "uis/view/common_panel_prefab", "HorizontalTabbar")
	self:AddViewResource(0, "uis/view/common_panel_prefab", "layout_a1_commmon_panel_adorn")

    -- 二级标签（水平）信息
    self.tab_sub = {Language.XXX.TabSub1, Language.XXX.TabSub2, Language.XXX.TabSub3}
    -- 界面红点
	self.remind_tab = {
        {RemindName.XXX},
        {},
        {},
	}

    -- 功能引导绑定事件
    self.get_guide_ui_event = BindTool.Bind(self.GetGuideUiCallBack, self)
end

function XXXView:__delete()
    -- do
end

function XXXView:OpenCallBack()
    -- do
end

function XXXView:CloseCallBack()
    -- do
end

function XXXView:LoadCallBack()
    -- self.node_list 创建见父类

    if not self.tabbar then
        -- 创建Tabbar
		self.tabbar = Tabbar.New(self.node_list)
        -- Language.XXX.TabGrop 一级标签（垂直）信息
        local ver_path = nil
        local hor_path = nil
		self.tabbar:Init(Language.XXX.TabGrop, self.tab_sub, ver_path, hor_path, self.remind_tab)
        -- 标签选择回调绑定
		self.tabbar:SetSelectCallback(BindTool.Bind(self.ChangeToIndex, self))
		-- 标签 关联 功能开启
		FunOpen.Instance:RegsiterTabFunUi(GuideModuleName.XXX, self.tabbar)
	end

    -- 创建货币栏
	if not self.money_bar then
		self.money_bar = MoneyBar.New()
		local bundle, asset = ResPath.GetWidgets("MoneyBar")
		local show_params = {
        show_gold = true, show_bind_gold = true,
        show_coin = true, show_silver_ticket = true,
        }
        self.money_bar:SetMoneyShowInfo(0, 0, show_params)
		self.money_bar:LoadAsset(bundle, asset, self.node_list["money_tabar_pos"].transform)
	end

    -- 注册功能引导
    FunctionGuide.Instance:RegisteGetGuideUi(GuideModuleName.BiZuo, self.get_guide_ui_event)
end

-- 通过.New()创建出来的，事件监听，计时器，tween动画都要销毁掉
-- 因为view的销毁只在Ctrl销毁才销毁，当面板关闭时，创建出来而未销毁的还会在内存里，注册监听的事件仍然在执行
function XXXView:ReleaseCallBack()
    -- 销毁 Tabbar
    if self.tabbar then
        self.tabbar:DeleteMe()
        self.tabbar = nil
    end
    
    -- 销毁 MoneyBar
    if self.money_bar then
        self.money_bar:DeleteMe()
        self.money_bar = nil
    end

    -- 注销功能引导
    FunctionGuide.Instance:UnRegiseGetGuideUiByFun(GuideModuleName.XXX, self.get_guide_ui_event)

    -- 销毁计时器
    self:CleanTimer1()
    self:CleanTimer2()
    self:CleanTimer3()

    self:ReleaseXXXView()
end

function XXXView:LoadIndexCallBack(index)
    -- 初始化对应界面索引的面板
    if index == TabIndex.xxx then
        self:InitXXXView()
    end
end

function XXXView:InitXXXView()
    -- 创建物品格子
    self.one_item = ItemCell.New(self.node_list.XXX)
    -- 循环列表
    self.xxx_list = AsyncListView.New(XxxRender, self.node_list.XXX_list_view)
	self.xxx_list:SetSelectCallBack(BindTool.Bind(self.OnSelectXXXHandler, self))
    -- 固定物品列表
    if self.node_list.fixed_item_list and self.fixed_item_list == nil then
        self.fixed_item_list = {}
        local node_num = self.node_list.fixed_item_list.transform.childCount
        for i = 1, node_num do
            self.fixed_item_list[i] = ItemCell.New(self.node_list.fixed_item_list:FindObj("item_" .. i))
        end
    end

    -- 创建模型
    if nil == self.display_model then
		local display_node = self.node_list.xxx
        -- local drag_node = self.node_list.xxx
		self.display_model = RoleModel.New()
        local display_data = {
			parent_node = display_node,
			camera_type = MODEL_CAMERA_TYPE.BASE,
			-- offset_type = MODEL_OFFSET_TYPE.NORMALIZE,
			rt_scale_type = ModelRTSCaleType.M,
			can_drag = true,
		}

		self.display_model:SetRenderTexUI3DModel(display_data)
		-- self.display_model:SetUI3DModel(display_node.transform, drag_node.event_trigger_listener, 1, false, MODEL_CAMERA_TYPE.BASE)
		self:AddUiRoleModel(self.display_model)
	end

    -- 按钮事件绑定
    XUI.AddClickEventListener(self.node_list.btn_xxx, BindTool.Bind(self.OnClickXXX, self))

    -- 界面监听其他功能的红点变化
    local other_remind_list = {RemindName.XXX1, RemindName.XXX2, RemindName.XXX3}
    self.remind_callback = BindTool.Bind(self.OtherRemindCallBack, self)
    for k,v in pairs(other_remind_list) do
        RemindManager.Instance:Bind(self.remind_callback, v)
    end
end

-- 释放
function XXXView:ReleaseXXXView()
    -- 销毁创建的物品格子
    if self.one_item then
        self.one_item:DeleteMe()
        self.one_item = nil
    end

    -- 销毁循环列表
    if self.xxx_list then
		self.xxx_list:DeleteMe()
		self.xxx_list = nil
	end

    -- 销毁固定物品列表
    if self.fixed_item_list then
        for k, v in pairs(self.fixed_item_list) do
            v:DeleteMe()
        end
        self.fixed_item_list = nil
    end

    -- 销毁模型
    if self.display_model then
		self.display_model:DeleteMe()
		self.display_model = nil
	end

    -- 取消红点监听
    RemindManager.Instance:UnBind(self.remind_callback)
end

function XXXView:ShowIndexCallBack(index)
    if index == TabIndex.xxx then
        -- do
    end
end

function XXXView:OnFlush(param_t, index)
    for k,v in pairs(param_t) do
		if k == "all" then
            if index == TabIndex.xxx then
                -- do
                self:FlushXXXView()
            end
        end
    end
end

function XXXView:FlushXXXView()
    -- 赋值
    self.node_list.xxx_toggle.toggle.isOn = false

    local res_id = ""
    -- 文本赋值
    self.node_list.xxx_text.text.text = ""
    -- 进度条
    self.node_list.xxx_slider.slider.value = 0
    -- .slider:DOValue(value, time)
    -- 加载精灵图
    local bundle, asset = ResPath.GetXXXImages(res_id)
    self.node_list.xxx_img.image:LoadSprite(bundle, asset, function()
        self.node_list.xxx_img.image:SetNativeSize()
    end)
    -- .image.enabled = bool
    -- .image.fillAmount = value
    -- 加载纹理图
    self.node_list.xxx_rawimg.raw_image:LoadSprite(bundle, asset, function()
        self.node_list.xxx_rawimg.raw_image:SetNativeSize()
    end)

    -- 改变节点的Transform or RectTransform参数, 通过Transform.XXX() or RectTransform.XXX() 调用
    -- 具体方法见 transfrom.lua 、rect_transform.lua
    RectTransform.SetSizeDeltaXY(self.node_list.xxx.rect, 0, 0)
    -- or
    -- 见 u3dpool.lua
    self.node_list.xxx.rect.sizeDelta = u3dpool.vec2(0, 0)


    -- 单个物品格子刷新数据
    local one_item_data = {item_id = 0, is_bind = 0, num = 1}
    self.one_item:SetData(one_item_data)

    -- 循环列表刷新数据
    local data_list = {}
    self.xxx_list:SetDataList(data_list)
    -- 循环列表跳到对应格子
    self.xxx_list:JumpToIndex(1)

    -- 固定物品列表刷新数据
    for k,v in ipairs(self.fixed_item_list) do
        v:SetData(data_list[k])
    end

    -- 模型刷新数据
    local model_bundle, model_asset = ResPath.GetXXXModel(res_id)
	self.display_model:SetMainAsset(model_bundle, model_asset)

    -- 计时器
    self:FlushTimer()
end

-- 清除倒计时器1
function XXXView:CleanTimer1()
    if self.timer_1 and CountDown.Instance:HasCountDown(self.timer_1) then
        CountDown.Instance:RemoveCountDown(self.timer_1)
        self.timer_1 = nil
    end
end

-- 清除倒计时器2
local time_2_key = "xxx_2_time"
function XXXView:CleanTimer2()
	if CountDownManager.Instance:HasCountDown(time_2_key) then
		CountDownManager.Instance:RemoveCountDown(time_2_key)
	end
end

function XXXView:CleanTimer3()
    if self.timer_3 then
        GlobalTimerQuest:CancelQuest(self.timer_3)
        self.timer_3 = nil
    end
end

function XXXView:FlushTimer()
    local total_time = 999      -- 总时间(秒)
    local interval = 1          -- 回调间隔
    -- 计时器的创建刷新，需要将旧的计时器进行销毁移除
    -- 倒计时器1 (见count_down.lua)
    self:CleanTimer1()
    self.timer_1 = CountDown.Instance:AddCountDown(total_time, interval,
        -- 回调方法
        function(elapse_time, total_time)
        end,
        -- 倒计时完成回调方法
        function()
        end
    )

    -- 倒计时器2 (见countdownManager.lua)
    self:CleanTimer2()
    if total_time > 0 then
        local complete_time = nil -- 完成的时间戳（与 total_time 选一即可）
        CountDownManager.Instance:AddCountDown(time_2_key,
            -- 回调方法
            function()
            end,
            -- 倒计时完成回调方法
            function()
            end,
			complete_time, total_time, interval)
    end

     -- 倒计时器3 (见timerquest.lua)
     self:CleanTimer3()
    local delay_time = 1       -- 延迟时间
    local do_count = 1         -- 执行次数
    local callback = function() end
    -- 相当于一秒后执行一次
    self.timer_3 = GlobalTimerQuest:AddTimesTimer(callback, delay_time, do_count)
    -- 只需延时执行一次的可根据情况调以一下
    -- 1
    self:CleanTimer3()
    GlobalTimerQuest:AddDelayTimer(callback, delay_time)
    -- 2 增加延迟调用，在在obj的生命周期结束后将自动移除
    AddDelayCall(obj, callback, delay_time)
    -- 3 延迟调用，将移除obj持有的上一个同key的延迟
    ReDelayCall(obj, callback, delay_time, key)
    -- 4 延迟调用，如果当前已有，则不移除上次延迟，等上次延迟执行
    TryDelayCall(obj, callback, delay_time, key)
end

-- 循环列表选择回调
function XXXView:OnSelectXXXHandler(cell)
    -- do
end

-- 按钮事件
function XXXView:OnClickXXX()
    print_error("----按钮点击---")
end

-- 红点变化回调
function XXXView:OtherRemindCallBack(remind_name, num)
    if remind_name == "" then
        self.node_list.XXX:SetActive(num > 0)
    end
end

-- 功能引导回调
function XXXView:GetGuideUiCallBack(ui_name, ui_param)
	if ui_name == GuideUIName.Tab and self.tabbar then
		local tab_index = math.floor(TabIndex[ui_param])
		if tab_index == self:GetShowIndex() then
			return NextGuideStepFlag
		else
			self:ShowIndex(tab_index)
			return NextGuideStepFlag
		end
	elseif ui_name == GuideUIName.XXXBtn then
        return -- function()
	end

	return SafeBaseView.GetGuideUiCallBack(self, ui_name, ui_param)
end

-- 打开奖励展示界面
function XXXView:OpenRewardShowView()
    local data_list =
    {
        view_type = RewardShowViewType.Normal,
        reward_item_list = {}
    }
    RewardShowViewWGCtrl.Instance:SetRewardShowData(data_list)
end
