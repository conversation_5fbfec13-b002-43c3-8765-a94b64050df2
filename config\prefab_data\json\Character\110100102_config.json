{"actorController": {"projectiles": [], "hurts": []}, "actorTriggers": {"effects": [{"triggerEventName": "attack1/begin", "triggerDelay": 0.0, "triggerFreeDelay": 0.0, "effectGoName": "skill1", "effectAsset": {"BundleName": "effects/prefab/model/nuqi/mo/skill1_prefab", "AssetName": "skill1", "AssetGUID": "78e5594361a4c484593a666a7a2a8113", "IsEmpty": false}, "playerAtTarget": false, "referenceNodeHierarchyPath": "", "isAttach": false, "isRotation": true, "isUseCustomTransform": false, "offsetPosX": 0.0, "offsetPosY": 0.0, "offsetPosZ": 0.0, "triggerStopEvent": "[none]", "effectBtnName": "attack1", "playerAtPos": false, "ignoreParentScale": false}, {"triggerEventName": "attack2/begin", "triggerDelay": 0.0, "triggerFreeDelay": 0.0, "effectGoName": "skill2", "effectAsset": {"BundleName": "effects/prefab/model/nuqi/mo/skill2_prefab", "AssetName": "skill2", "AssetGUID": "56d82a4c284ce3a4c894e59f08cfc89c", "IsEmpty": false}, "playerAtTarget": false, "referenceNodeHierarchyPath": "", "isAttach": false, "isRotation": true, "isUseCustomTransform": false, "offsetPosX": 0.0, "offsetPosY": 0.0, "offsetPosZ": 0.0, "triggerStopEvent": "[none]", "effectBtnName": "attack2", "playerAtPos": false, "ignoreParentScale": false}, {"triggerEventName": "attack3/begin", "triggerDelay": 0.0, "triggerFreeDelay": 0.0, "effectGoName": "skill3", "effectAsset": {"BundleName": "effects/prefab/model/nuqi/mo/skill3_prefab", "AssetName": "skill3", "AssetGUID": "e9e5c3ba2af4cdd4cb50f885b35e8a7c", "IsEmpty": false}, "playerAtTarget": false, "referenceNodeHierarchyPath": "", "isAttach": false, "isRotation": true, "isUseCustomTransform": false, "offsetPosX": 0.0, "offsetPosY": 0.0, "offsetPosZ": 0.0, "triggerStopEvent": "[none]", "effectBtnName": "attack3", "playerAtPos": false, "ignoreParentScale": false}, {"triggerEventName": "attack4/begin", "triggerDelay": 0.0, "triggerFreeDelay": 0.0, "effectGoName": "skill4", "effectAsset": {"BundleName": "effects/prefab/model/nuqi/mo/skill4_prefab", "AssetName": "skill4", "AssetGUID": "d0a3018be3ba9624bba5886e1985a76b", "IsEmpty": false}, "playerAtTarget": false, "referenceNodeHierarchyPath": "", "isAttach": false, "isRotation": true, "isUseCustomTransform": true, "offsetPosX": 0.0, "offsetPosY": 1.5, "offsetPosZ": 0.0, "triggerStopEvent": "[none]", "effectBtnName": "attack4", "playerAtPos": false, "ignoreParentScale": false}, {"triggerEventName": "combo1_1/begin", "triggerDelay": 0.0, "triggerFreeDelay": 0.0, "effectGoName": "combo1", "effectAsset": {"BundleName": "effects/prefab/model/nuqi/mo/combo1_prefab", "AssetName": "combo1", "AssetGUID": "bbab6ffe5f0da47499a980abc9a954c9", "IsEmpty": false}, "playerAtTarget": true, "referenceNodeHierarchyPath": "", "isAttach": false, "isRotation": true, "isUseCustomTransform": false, "offsetPosX": 0.0, "offsetPosY": 0.0, "offsetPosZ": 0.0, "triggerStopEvent": "[none]", "effectBtnName": "combo1_1", "playerAtPos": true, "ignoreParentScale": false}, {"triggerEventName": "combo1_2/begin", "triggerDelay": 0.0, "triggerFreeDelay": 0.0, "effectGoName": "combo2", "effectAsset": {"BundleName": "effects/prefab/model/nuqi/mo/combo2_prefab", "AssetName": "combo2", "AssetGUID": "d23ab3c26e6945643a216bad81300c83", "IsEmpty": false}, "playerAtTarget": false, "referenceNodeHierarchyPath": "", "isAttach": false, "isRotation": true, "isUseCustomTransform": true, "offsetPosX": 0.0, "offsetPosY": 1.0, "offsetPosZ": 1.0, "triggerStopEvent": "[none]", "effectBtnName": "combo1_2", "playerAtPos": false, "ignoreParentScale": false}, {"triggerEventName": "combo1_3/begin", "triggerDelay": 0.0, "triggerFreeDelay": 0.0, "effectGoName": "combo3", "effectAsset": {"BundleName": "effects/prefab/model/nuqi/mo/combo3_prefab", "AssetName": "combo3", "AssetGUID": "100c1c92c60bebf40a05d4f4611bd248", "IsEmpty": false}, "playerAtTarget": true, "referenceNodeHierarchyPath": "", "isAttach": false, "isRotation": true, "isUseCustomTransform": false, "offsetPosX": 0.0, "offsetPosY": 0.0, "offsetPosZ": 0.0, "triggerStopEvent": "[none]", "effectBtnName": "combo1_3", "playerAtPos": true, "ignoreParentScale": false}, {"triggerEventName": "combo1_4/begin", "triggerDelay": 0.0, "triggerFreeDelay": 0.0, "effectGoName": "combo4", "effectAsset": {"BundleName": "effects/prefab/model/nuqi/mo/combo4_prefab", "AssetName": "combo4", "AssetGUID": "c652e857975d4a04da1a913e29c6da58", "IsEmpty": false}, "playerAtTarget": false, "referenceNodeHierarchyPath": "", "isAttach": false, "isRotation": true, "isUseCustomTransform": false, "offsetPosX": 0.0, "offsetPosY": 0.0, "offsetPosZ": 0.0, "triggerStopEvent": "[none]", "effectBtnName": "combo1_4", "playerAtPos": true, "ignoreParentScale": false}, {"triggerEventName": "transformation/begin", "triggerDelay": 0.0, "triggerFreeDelay": 0.0, "effectGoName": "bianshen", "effectAsset": {"BundleName": "effects/prefab/model/nuqi/mo/bianshen_prefab", "AssetName": "bianshen", "AssetGUID": "bd133e05fb9464b4fb17f44c57edd7eb", "IsEmpty": false}, "playerAtTarget": false, "referenceNodeHierarchyPath": "", "isAttach": false, "isRotation": false, "isUseCustomTransform": false, "offsetPosX": 0.0, "offsetPosY": 0.0, "offsetPosZ": 0.0, "triggerStopEvent": "[none]", "effectBtnName": "transformation", "playerAtPos": false, "ignoreParentScale": false}], "sounds": [{"soundEventName": "attack1/begin", "soundDelay": 0.0, "soundAudioAsset": {"BundleName": "audios/sfxs/roleskill/110100102", "AssetName": "skill1", "AssetGUID": "2516f1c81c7f0e04ca1dd6e5fc832739", "IsEmpty": false}, "soundAudioGoName": "skill1", "soundBtnName": "skill1", "soundIsMainRole": false}, {"soundEventName": "attack2/begin", "soundDelay": 0.0, "soundAudioAsset": {"BundleName": "audios/sfxs/roleskill/110100102", "AssetName": "skill2", "AssetGUID": "2d9d3ec286f970a44bccccba8f84e9d5", "IsEmpty": false}, "soundAudioGoName": "skill2", "soundBtnName": "skill2", "soundIsMainRole": false}, {"soundEventName": "attack3/begin", "soundDelay": 0.0, "soundAudioAsset": {"BundleName": "audios/sfxs/roleskill/110100102", "AssetName": "skill3", "AssetGUID": "5e35d698586539a4f86c7aed19c3fea5", "IsEmpty": false}, "soundAudioGoName": "skill3", "soundBtnName": "skill3", "soundIsMainRole": false}, {"soundEventName": "attack4/begin", "soundDelay": 0.0, "soundAudioAsset": {"BundleName": "audios/sfxs/roleskill/110100102", "AssetName": "skill4", "AssetGUID": "d63349e8455d38b41b2adc9136fcf2ac", "IsEmpty": false}, "soundAudioGoName": "skill4", "soundBtnName": "skill4", "soundIsMainRole": false}, {"soundEventName": "combo1_1/begin", "soundDelay": 0.0, "soundAudioAsset": {"BundleName": "audios/sfxs/roleskill/110100102", "AssetName": "attack1", "AssetGUID": "7a30af24b60cbea4dabe0e1b3db8cbcb", "IsEmpty": false}, "soundAudioGoName": "attack1", "soundBtnName": "attack1", "soundIsMainRole": false}, {"soundEventName": "combo1_2/begin", "soundDelay": 0.0, "soundAudioAsset": {"BundleName": "audios/sfxs/roleskill/110100102", "AssetName": "attack2", "AssetGUID": "f33f1b5be5e35e6449173e67bb62425e", "IsEmpty": false}, "soundAudioGoName": "attack2", "soundBtnName": "attack2", "soundIsMainRole": false}, {"soundEventName": "combo1_3/begin", "soundDelay": 0.0, "soundAudioAsset": {"BundleName": "audios/sfxs/roleskill/110100102", "AssetName": "attack3", "AssetGUID": "20f95109bdb75b5418d76e36c47dabb2", "IsEmpty": false}, "soundAudioGoName": "attack3", "soundBtnName": "attack3", "soundIsMainRole": false}, {"soundEventName": "combo1_4/begin", "soundDelay": 0.0, "soundAudioAsset": {"BundleName": "audios/sfxs/roleskill/110100102", "AssetName": "attack4", "AssetGUID": "ab8211183be804a418ed7fbc7c363c9b", "IsEmpty": false}, "soundAudioGoName": "attack4", "soundBtnName": "attack4", "soundIsMainRole": false}, {"soundEventName": "transformation/begin", "soundDelay": 0.0, "soundAudioAsset": {"BundleName": "audios/sfxs/roleskill/110100101", "AssetName": "tra", "AssetGUID": "87f117226ddb65d4990a5b856d365e4d", "IsEmpty": false}, "soundAudioGoName": "tra", "soundBtnName": "tra", "soundIsMainRole": false}], "cameraShakes": [{"CameraShakeBtnName": "combo1_4", "eventName": "combo1_4/begin", "numberOfShakes": 2, "distance": 0.3, "speed": 100.0, "delay": 0.8, "decay": 0.0}, {"CameraShakeBtnName": "attack2", "eventName": "attack2/begin", "numberOfShakes": 2, "distance": 0.4, "speed": 100.0, "delay": 0.2, "decay": 0.0}, {"CameraShakeBtnName": "attack3", "eventName": "attack3/begin", "numberOfShakes": 1, "distance": 0.4, "speed": 150.0, "delay": 0.6, "decay": 0.0}, {"CameraShakeBtnName": "attack4_1", "eventName": "attack4/begin", "numberOfShakes": 6, "distance": 0.4, "speed": 600.0, "delay": 1.3, "decay": 0.0}, {"CameraShakeBtnName": "attack4_2", "eventName": "attack4/begin", "numberOfShakes": 9, "distance": 0.6, "speed": 180.0, "delay": 1.8, "decay": 0.0}, {"CameraShakeBtnName": "transformation", "eventName": "transformation/begin", "numberOfShakes": 2, "distance": 0.8, "speed": 400.0, "delay": 0.8, "decay": 0.0}], "radialBlurs": [{"btnName": "transformation", "eventName": "transformation/begin", "isRole": true, "referenceNodeHierarchyPath": "root", "delay": 0.1, "strength": 2.0, "riseTime": 0.1, "holdTime": 0.2, "fallTime": 0.0}, {"btnName": "attack4", "eventName": "attack4/begin", "isRole": true, "referenceNodeHierarchyPath": "RoleFace", "delay": 0.1, "strength": 0.5, "riseTime": 0.1, "holdTime": 0.3, "fallTime": 0.0}]}}