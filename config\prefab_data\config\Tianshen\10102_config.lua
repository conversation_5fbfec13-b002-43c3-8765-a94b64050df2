return {
	actorController = {
		projectiles = {},

		hurts = {},

	},
	actorTriggers = {
		effects = {
			{
				triggerEventName = "attack1/begin",
				triggerDelay = 0.0,
				triggerFreeDelay = 0.0,
				effectGoName = "10102_skill01",
				effectAsset = {
					BundleName = "effects/prefab/model/tianshen/10102/10102_skill01_prefab",
					AssetName = "10102_skill01",
				},
				playerAtTarget = false,
				referenceNodeHierarchyPath = "",
				isAttach = false,
				isRotation = true,
				isUseCustomTransform = false,
				offsetPosX = 0.0,
				offsetPosY = 0.0,
				offsetPosZ = 0.0,
				triggerStopEvent = "",
				effectBtnName = "attack1",
				playerAtPos = false,
				ignoreParentScale = false,
			},
			{
				triggerEventName = "attack2/begin",
				triggerDelay = 0.0,
				triggerFreeDelay = 0.0,
				effectGoName = "10102_skill02",
				effectAsset = {
					BundleName = "effects/prefab/model/tianshen/10102/10102_skill02_prefab",
					AssetName = "10102_skill02",
				},
				playerAtTarget = false,
				referenceNodeHierarchyPath = "",
				isAttach = false,
				isRotation = true,
				isUseCustomTransform = false,
				offsetPosX = 0.0,
				offsetPosY = 0.0,
				offsetPosZ = 0.0,
				triggerStopEvent = "",
				effectBtnName = "attack2",
				playerAtPos = false,
				ignoreParentScale = false,
			},
			{
				triggerEventName = "attack3/begin",
				triggerDelay = 0.0,
				triggerFreeDelay = 0.0,
				effectGoName = "10102_skill03",
				effectAsset = {
					BundleName = "effects/prefab/model/tianshen/10102/10102_skill03_prefab",
					AssetName = "10102_skill03",
				},
				playerAtTarget = false,
				referenceNodeHierarchyPath = "",
				isAttach = false,
				isRotation = true,
				isUseCustomTransform = true,
				offsetPosX = 0.0,
				offsetPosY = 0.0,
				offsetPosZ = 0.0,
				triggerStopEvent = "",
				effectBtnName = "attack3",
				playerAtPos = false,
				ignoreParentScale = false,
			},
			{
				triggerEventName = "combo1_1/begin",
				triggerDelay = 0.0,
				triggerFreeDelay = 0.0,
				effectGoName = "10102_attack01",
				effectAsset = {
					BundleName = "effects/prefab/model/tianshen/10102/10102_attack01_prefab",
					AssetName = "10102_attack01",
				},
				playerAtTarget = false,
				referenceNodeHierarchyPath = "",
				isAttach = false,
				isRotation = true,
				isUseCustomTransform = false,
				offsetPosX = 0.0,
				offsetPosY = 0.0,
				offsetPosZ = 0.0,
				triggerStopEvent = "",
				effectBtnName = "combo1",
				playerAtPos = false,
				ignoreParentScale = false,
			},
			{
				triggerEventName = "rest/begin",
				triggerDelay = 0.0,
				triggerFreeDelay = 0.0,
				effectGoName = "10102_skill03",
				effectAsset = {
					BundleName = "effects/prefab/model/tianshen/10102/10102_skill03_prefab",
					AssetName = "10102_skill03",
				},
				playerAtTarget = false,
				referenceNodeHierarchyPath = "root",
				isAttach = true,
				isRotation = false,
				isUseCustomTransform = true,
				offsetPosX = 0.0,
				offsetPosY = 0.0,
				offsetPosZ = -2.0,
				triggerStopEvent = "",
				effectBtnName = "rest",
				playerAtPos = false,
				ignoreParentScale = false,
			},
		},
		sounds = {},

		cameraShakes = {
			{
				CameraShakeBtnName = "combo1",
				eventName = "attack1/begin",
				numberOfShakes = 5,
				distance = 0.1,
				speed = 650.0,
				delay = 0.4,
				decay = 0.0,
			},
			{
				CameraShakeBtnName = "combo2",
				eventName = "attack2/begin",
				numberOfShakes = 5,
				distance = 0.1,
				speed = 650.0,
				delay = 1.0,
				decay = 0.0,
			},
			{
				CameraShakeBtnName = "combo2-2",
				eventName = "attack2/begin",
				numberOfShakes = 25,
				distance = 0.1,
				speed = 650.0,
				delay = 1.3,
				decay = 0.0,
			},
			{
				CameraShakeBtnName = "combo3",
				eventName = "attack3/begin",
				numberOfShakes = 0,
				distance = 0.1,
				speed = 650.0,
				delay = 0.2,
				decay = 0.0,
			},
			{
				CameraShakeBtnName = "combo3-2",
				eventName = "attack3/begin",
				numberOfShakes = 7,
				distance = 0.1,
				speed = 650.0,
				delay = 1.3,
				decay = 0.0,
			},
			{
				CameraShakeBtnName = "attack1",
				eventName = "combo1_1/begin",
				numberOfShakes = 5,
				distance = 0.1,
				speed = 650.0,
				delay = 0.4,
				decay = 0.0,
			},
		},
		radialBlurs = {},

	},
}