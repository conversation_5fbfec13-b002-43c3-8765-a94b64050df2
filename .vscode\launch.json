{
    // 使用 IntelliSense 了解相关属性。 
    // 悬停以查看现有属性的描述。
    // 欲了解更多信息，请访问: https://go.microsoft.com/fwlink/?linkid=830387
    "version": "0.2.0",
    "configurations": [
        {
            "type": "lua",
            "request": "launch",
            "tag": "normal",
            "name": "LuaPanda",
            "description": "通用模式,通常调试项目请选择此模式 | launchVer:3.2.0",
            "cwd": "${workspaceFolder}",
            "luaFileExtension": "lua",
            "connectionPort": 9011,
            "stopOnEntry": false,
            "useCHook": true,
            "autoPathMode": true,
            "distinguishSameNameFile":true
        },
        {
            "type": "lua",
            "request": "launch",
            "tag": "independent_file",
            "name": "LuaPanda-IndependentFile",
            "description": "独立文件调试模式,使用前请参考文档",
            "luaPath": "",
            "packagePath": [],
            "luaFileExtension": "lua",
            "connectionPort": 9011,
            "stopOnEntry": false,
            "useCHook": true,
            "distinguishSameNameFile":true
        }
    ]
}