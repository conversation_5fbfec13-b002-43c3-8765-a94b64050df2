return {
	actorController = {
		projectiles = {},

		hurts = {},

	},
	actorTriggers = {
		effects = {
			{
				triggerEventName = "attack1/begin",
				triggerDelay = 0.0,
				triggerFreeDelay = 0.0,
				effectGoName = "",
				effectAsset = {},

				playerAtTarget = false,
				referenceNodeHierarchyPath = "",
				isAttach = false,
				isRotation = true,
				isUseCustomTransform = false,
				offsetPosX = 0.0,
				offsetPosY = 0.0,
				offsetPosZ = 0.0,
				triggerStopEvent = "",
				effectBtnName = "attack1",
				playerAtPos = false,
				ignoreParentScale = false,
				effectArray = {
					{
						effectGoName = "3102_attack1",
						effectAsset = {
							BundleName = "effects/prefab/role/3102/3102_attack1_prefab",
							AssetName = "3102_attack1",
						},
					},
					{
						effectGoName = "3102_attack1_hong",
						effectAsset = {
							BundleName = "effects/prefab/role/3102/3102_attack1_hong_prefab",
							AssetName = "3102_attack1_hong",
						},
					},
				},
			},
			{
				triggerEventName = "attack3/begin",
				triggerDelay = 0.0,
				triggerFreeDelay = 0.0,
				effectGoName = "",
				effectAsset = {},

				playerAtTarget = false,
				referenceNodeHierarchyPath = "",
				isAttach = false,
				isRotation = true,
				isUseCustomTransform = false,
				offsetPosX = 0.0,
				offsetPosY = 0.0,
				offsetPosZ = 0.0,
				triggerStopEvent = "",
				effectBtnName = "attack3",
				playerAtPos = false,
				ignoreParentScale = false,
				effectArray = {
					{
						effectGoName = "3102_attack3",
						effectAsset = {
							BundleName = "effects/prefab/role/3102/3102_attack3_prefab",
							AssetName = "3102_attack3",
						},
					},
					{
						effectGoName = "3102_attack3_hong",
						effectAsset = {
							BundleName = "effects/prefab/role/3102/3102_attack3_hong_prefab",
							AssetName = "3102_attack3_hong",
						},
					},
				},
			},
			{
				triggerEventName = "attack4/begin",
				triggerDelay = 0.0,
				triggerFreeDelay = 0.0,
				effectGoName = "",
				effectAsset = {},

				playerAtTarget = false,
				referenceNodeHierarchyPath = "",
				isAttach = false,
				isRotation = true,
				isUseCustomTransform = false,
				offsetPosX = 0.0,
				offsetPosY = 0.0,
				offsetPosZ = 0.0,
				triggerStopEvent = "",
				effectBtnName = "attack4",
				playerAtPos = false,
				ignoreParentScale = false,
				effectArray = {
					{
						effectGoName = "3102_attack4",
						effectAsset = {
							BundleName = "effects/prefab/role/3102/3102_attack4_prefab",
							AssetName = "3102_attack4",
						},
					},
					{
						effectGoName = "3102_attack4_hong",
						effectAsset = {
							BundleName = "effects/prefab/role/3102/3102_attack4_hong_prefab",
							AssetName = "3102_attack4_hong",
						},
					},
				},
			},
			{
				triggerEventName = "combo1_1/begin",
				triggerDelay = 0.0,
				triggerFreeDelay = 0.0,
				effectGoName = "",
				effectAsset = {},

				playerAtTarget = false,
				referenceNodeHierarchyPath = "",
				isAttach = false,
				isRotation = true,
				isUseCustomTransform = false,
				offsetPosX = 0.0,
				offsetPosY = 0.0,
				offsetPosZ = 0.0,
				triggerStopEvent = "",
				effectBtnName = "combo1_1",
				playerAtPos = false,
				ignoreParentScale = false,
				effectArray = {
					{
						effectGoName = "3102_combo1",
						effectAsset = {
							BundleName = "effects/prefab/role/3102/3102_combo1_prefab",
							AssetName = "3102_combo1",
						},
					},
					{
						effectGoName = "3102_combo1_hong",
						effectAsset = {
							BundleName = "effects/prefab/role/3102/3102_combo1_hong_prefab",
							AssetName = "3102_combo1_hong",
						},
					},
				},
			},
			{
				triggerEventName = "combo1_2/begin",
				triggerDelay = 0.0,
				triggerFreeDelay = 0.0,
				effectGoName = "",
				effectAsset = {},

				playerAtTarget = false,
				referenceNodeHierarchyPath = "",
				isAttach = false,
				isRotation = true,
				isUseCustomTransform = false,
				offsetPosX = 0.0,
				offsetPosY = 0.0,
				offsetPosZ = 0.0,
				triggerStopEvent = "",
				effectBtnName = "combo1_2",
				playerAtPos = false,
				ignoreParentScale = false,
				effectArray = {
					{
						effectGoName = "3102_combo2",
						effectAsset = {
							BundleName = "effects/prefab/role/3102/3102_combo2_prefab",
							AssetName = "3102_combo2",
						},
					},
					{
						effectGoName = "3102_combo2_hong",
						effectAsset = {
							BundleName = "effects/prefab/role/3102/3102_combo2_hong_prefab",
							AssetName = "3102_combo2_hong",
						},
					},
				},
			},
			{
				triggerEventName = "combo1_3/begin",
				triggerDelay = 0.0,
				triggerFreeDelay = 0.0,
				effectGoName = "",
				effectAsset = {},

				playerAtTarget = false,
				referenceNodeHierarchyPath = "",
				isAttach = false,
				isRotation = true,
				isUseCustomTransform = false,
				offsetPosX = 0.0,
				offsetPosY = 0.0,
				offsetPosZ = 0.0,
				triggerStopEvent = "",
				effectBtnName = "combo1_3",
				playerAtPos = false,
				ignoreParentScale = false,
				effectArray = {
					{
						effectGoName = "3102_combo3",
						effectAsset = {
							BundleName = "effects/prefab/role/3102/3102_combo3_prefab",
							AssetName = "3102_combo3",
						},
					},
					{
						effectGoName = "3102_combo3_hong",
						effectAsset = {
							BundleName = "effects/prefab/role/3102/3102_combo3_hong_prefab",
							AssetName = "3102_combo3_hong",
						},
					},
				},
			},
			{
				triggerEventName = "combo1_4/begin",
				triggerDelay = 0.0,
				triggerFreeDelay = 0.0,
				effectGoName = "",
				effectAsset = {},

				playerAtTarget = false,
				referenceNodeHierarchyPath = "",
				isAttach = false,
				isRotation = true,
				isUseCustomTransform = false,
				offsetPosX = 0.0,
				offsetPosY = 0.0,
				offsetPosZ = 0.0,
				triggerStopEvent = "",
				effectBtnName = "combo1_4",
				playerAtPos = false,
				ignoreParentScale = false,
				effectArray = {
					{
						effectGoName = "3102_combo4",
						effectAsset = {
							BundleName = "effects/prefab/role/3102/3102_combo4_prefab",
							AssetName = "3102_combo4",
						},
					},
					{
						effectGoName = "3102_combo4_hong",
						effectAsset = {
							BundleName = "effects/prefab/role/3102/3102_combo4_hong_prefab",
							AssetName = "3102_combo4_hong",
						},
					},
				},
			},
			{
				triggerEventName = "attack2/begin",
				triggerDelay = 0.0,
				triggerFreeDelay = 0.0,
				effectGoName = "",
				effectAsset = {},

				playerAtTarget = false,
				referenceNodeHierarchyPath = "",
				isAttach = false,
				isRotation = true,
				isUseCustomTransform = false,
				offsetPosX = 0.0,
				offsetPosY = 0.0,
				offsetPosZ = 0.0,
				triggerStopEvent = "",
				effectBtnName = "attack2",
				playerAtPos = false,
				ignoreParentScale = false,
				effectArray = {
					{
						effectGoName = "3102_attack2",
						effectAsset = {
							BundleName = "effects/prefab/role/3102/3102_attack2_prefab",
							AssetName = "3102_attack2",
						},
					},
					{
						effectGoName = "3102_attack2_hong",
						effectAsset = {
							BundleName = "effects/prefab/role/3102/3102_attack2_hong_prefab",
							AssetName = "3102_attack2_hong",
						},
					},
				},
			},
			{
				triggerEventName = "esoterica_attack_1/begin",
				triggerDelay = 0.0,
				triggerFreeDelay = 0.0,
				effectGoName = "",
				effectAsset = {},

				playerAtTarget = false,
				referenceNodeHierarchyPath = "",
				isAttach = false,
				isRotation = true,
				isUseCustomTransform = false,
				offsetPosX = 0.0,
				offsetPosY = 0.0,
				offsetPosZ = 0.0,
				triggerStopEvent = "",
				effectBtnName = "esoterica_attack_1",
				playerAtPos = false,
				ignoreParentScale = false,
				effectArray = {
					{
						effectGoName = "miji_attack01",
						effectAsset = {
							BundleName = "effects/prefab/model/miji/miji_attack01_prefab",
							AssetName = "miji_attack01",
						},
					},
					{
						effectGoName = "",
						effectAsset = {},

					},
				},
			},
			{
				triggerEventName = "esoterica_attack_2/begin",
				triggerDelay = 0.0,
				triggerFreeDelay = 0.0,
				effectGoName = "",
				effectAsset = {},

				playerAtTarget = false,
				referenceNodeHierarchyPath = "",
				isAttach = false,
				isRotation = true,
				isUseCustomTransform = false,
				offsetPosX = 0.0,
				offsetPosY = 0.0,
				offsetPosZ = 0.0,
				triggerStopEvent = "",
				effectBtnName = "esoterica_attack_2",
				playerAtPos = false,
				ignoreParentScale = false,
				effectArray = {
					{
						effectGoName = "xianfa_attack9",
						effectAsset = {
							BundleName = "effects/prefab/model/xianfa/xianfa_attack9_prefab",
							AssetName = "xianfa_attack9",
						},
					},
					{
						effectGoName = "",
						effectAsset = {},

					},
				},
			},
			{
				triggerEventName = "esoterica_attack_3/begin",
				triggerDelay = 0.0,
				triggerFreeDelay = 0.0,
				effectGoName = "",
				effectAsset = {},

				playerAtTarget = false,
				referenceNodeHierarchyPath = "",
				isAttach = false,
				isRotation = true,
				isUseCustomTransform = false,
				offsetPosX = 0.0,
				offsetPosY = 0.0,
				offsetPosZ = 0.0,
				triggerStopEvent = "",
				effectBtnName = "esoterica_attack_3",
				playerAtPos = false,
				ignoreParentScale = false,
				effectArray = {
					{
						effectGoName = "xianfa_attack3",
						effectAsset = {
							BundleName = "effects/prefab/model/xianfa/xianfa_attack3_prefab",
							AssetName = "xianfa_attack3",
						},
					},
					{
						effectGoName = "",
						effectAsset = {},

					},
				},
			},
			{
				triggerEventName = "esoterica_attack_4/begin",
				triggerDelay = 0.0,
				triggerFreeDelay = 0.0,
				effectGoName = "",
				effectAsset = {},

				playerAtTarget = false,
				referenceNodeHierarchyPath = "",
				isAttach = false,
				isRotation = true,
				isUseCustomTransform = false,
				offsetPosX = 0.0,
				offsetPosY = 0.0,
				offsetPosZ = 0.0,
				triggerStopEvent = "",
				effectBtnName = "esoterica_attack_4",
				playerAtPos = false,
				ignoreParentScale = false,
				effectArray = {
					{
						effectGoName = "xianfa_attack1",
						effectAsset = {
							BundleName = "effects/prefab/model/xianfa/xianfa_attack1_prefab",
							AssetName = "xianfa_attack1",
						},
					},
					{
						effectGoName = "",
						effectAsset = {},

					},
				},
			},
			{
				triggerEventName = "esoterica_attack_5/begin",
				triggerDelay = 0.0,
				triggerFreeDelay = 0.0,
				effectGoName = "",
				effectAsset = {},

				playerAtTarget = false,
				referenceNodeHierarchyPath = "",
				isAttach = false,
				isRotation = true,
				isUseCustomTransform = false,
				offsetPosX = 0.0,
				offsetPosY = 0.0,
				offsetPosZ = 0.0,
				triggerStopEvent = "",
				effectBtnName = "esoterica_attack_5",
				playerAtPos = false,
				ignoreParentScale = false,
				effectArray = {
					{
						effectGoName = "xianfa_attack6",
						effectAsset = {
							BundleName = "effects/prefab/model/xianfa/xianfa_attack6_prefab",
							AssetName = "xianfa_attack6",
						},
					},
					{
						effectGoName = "",
						effectAsset = {},

					},
				},
			},
			{
				triggerEventName = "esoterica_attack_6/begin",
				triggerDelay = 0.0,
				triggerFreeDelay = 0.0,
				effectGoName = "",
				effectAsset = {},

				playerAtTarget = false,
				referenceNodeHierarchyPath = "",
				isAttach = false,
				isRotation = true,
				isUseCustomTransform = false,
				offsetPosX = 0.0,
				offsetPosY = 0.0,
				offsetPosZ = 0.0,
				triggerStopEvent = "",
				effectBtnName = "esoterica_attack_6",
				playerAtPos = false,
				ignoreParentScale = false,
				effectArray = {
					{
						effectGoName = "xianfa_attack4",
						effectAsset = {
							BundleName = "effects/prefab/model/xianfa/xianfa_attack4_prefab",
							AssetName = "xianfa_attack4",
						},
					},
					{
						effectGoName = "",
						effectAsset = {},

					},
				},
			},
			{
				triggerEventName = "esoterica_attack_7/begin",
				triggerDelay = 0.0,
				triggerFreeDelay = 0.0,
				effectGoName = "",
				effectAsset = {},

				playerAtTarget = false,
				referenceNodeHierarchyPath = "",
				isAttach = false,
				isRotation = true,
				isUseCustomTransform = false,
				offsetPosX = 0.0,
				offsetPosY = 0.0,
				offsetPosZ = 0.0,
				triggerStopEvent = "",
				effectBtnName = "esoterica_attack_7",
				playerAtPos = false,
				ignoreParentScale = false,
				effectArray = {
					{
						effectGoName = "xianfa_attack8",
						effectAsset = {
							BundleName = "effects/prefab/model/xianfa/xianfa_attack8_prefab",
							AssetName = "xianfa_attack8",
						},
					},
					{
						effectGoName = "",
						effectAsset = {},

					},
				},
			},
			{
				triggerEventName = "attack5/begin",
				triggerDelay = 0.0,
				triggerFreeDelay = 0.0,
				effectGoName = "",
				effectAsset = {},

				playerAtTarget = false,
				referenceNodeHierarchyPath = "",
				isAttach = false,
				isRotation = true,
				isUseCustomTransform = false,
				offsetPosX = 0.0,
				offsetPosY = 0.0,
				offsetPosZ = 0.0,
				triggerStopEvent = "",
				effectBtnName = "attack5",
				playerAtPos = false,
				ignoreParentScale = false,
				effectArray = {
					{
						effectGoName = "dingzhi_attack01",
						effectAsset = {
							BundleName = "effects/prefab/model/dingzhi/dingzhi_attack01_prefab",
							AssetName = "dingzhi_attack01",
						},
					},
					{
						effectGoName = "",
						effectAsset = {},

					},
				},
			},
			{
				triggerEventName = "attack6/begin",
				triggerDelay = 0.0,
				triggerFreeDelay = 0.0,
				effectGoName = "",
				effectAsset = {},

				playerAtTarget = false,
				referenceNodeHierarchyPath = "",
				isAttach = false,
				isRotation = true,
				isUseCustomTransform = false,
				offsetPosX = 0.0,
				offsetPosY = 0.0,
				offsetPosZ = 0.0,
				triggerStopEvent = "",
				effectBtnName = "attack6",
				playerAtPos = false,
				ignoreParentScale = false,
				effectArray = {
					{
						effectGoName = "dingzhi_attack02",
						effectAsset = {
							BundleName = "effects/prefab/model/dingzhi/dingzhi_attack02_prefab",
							AssetName = "dingzhi_attack02",
						},
					},
					{
						effectGoName = "",
						effectAsset = {},

					},
				},
			},
			{
				triggerEventName = "attack7/begin",
				triggerDelay = 0.0,
				triggerFreeDelay = 0.0,
				effectGoName = "",
				effectAsset = {},

				playerAtTarget = false,
				referenceNodeHierarchyPath = "",
				isAttach = false,
				isRotation = true,
				isUseCustomTransform = false,
				offsetPosX = 0.0,
				offsetPosY = 0.0,
				offsetPosZ = 0.0,
				triggerStopEvent = "",
				effectBtnName = "attack7",
				playerAtPos = false,
				ignoreParentScale = false,
				effectArray = {
					{
						effectGoName = "dingzhi_attack05",
						effectAsset = {
							BundleName = "effects/prefab/model/dingzhi/dingzhi_attack05_prefab",
							AssetName = "dingzhi_attack05",
						},
					},
					{
						effectGoName = "",
						effectAsset = {},

					},
				},
			},
			{
				triggerEventName = "fall_rest/begin",
				triggerDelay = 0.0,
				triggerFreeDelay = 0.0,
				effectGoName = "",
				effectAsset = {},

				playerAtTarget = false,
				referenceNodeHierarchyPath = "root",
				isAttach = true,
				isRotation = false,
				isUseCustomTransform = false,
				offsetPosX = 0.0,
				offsetPosY = 0.0,
				offsetPosZ = 0.0,
				triggerStopEvent = "",
				effectBtnName = "fall_rest",
				playerAtPos = false,
				ignoreParentScale = false,
				effectArray = {
					{
						effectGoName = "eff_chuchang_shandian",
						effectAsset = {
							BundleName = "effects/prefab/environment/common/eff_chuchang_shandian_prefab",
							AssetName = "eff_chuchang_shandian",
						},
					},
					{
						effectGoName = "",
						effectAsset = {},

					},
				},
			},
			{
				triggerEventName = "prizedraw/begin",
				triggerDelay = 0.0,
				triggerFreeDelay = 0.0,
				effectGoName = "",
				effectAsset = {},

				playerAtTarget = false,
				referenceNodeHierarchyPath = "root",
				isAttach = true,
				isRotation = false,
				isUseCustomTransform = false,
				offsetPosX = 0.0,
				offsetPosY = 0.0,
				offsetPosZ = 0.0,
				triggerStopEvent = "",
				effectBtnName = "choujiang",
				playerAtPos = false,
				ignoreParentScale = true,
				effectArray = {
					{
						effectGoName = "choujiang_eff",
						effectAsset = {
							BundleName = "effects/prefab/environment/common/choujiang_eff_prefab",
							AssetName = "choujiang_eff",
						},
					},
					{
						effectGoName = "",
						effectAsset = {},

					},
				},
			},
			{
				triggerEventName = "esoterica_attack_8/begin",
				triggerDelay = 0.0,
				triggerFreeDelay = 0.0,
				effectGoName = "",
				effectAsset = {},

				playerAtTarget = false,
				referenceNodeHierarchyPath = "",
				isAttach = false,
				isRotation = false,
				isUseCustomTransform = true,
				offsetPosX = 1.0,
				offsetPosY = 0.0,
				offsetPosZ = -6.0,
				triggerStopEvent = "",
				effectBtnName = "esoterica_attack_8",
				playerAtPos = false,
				ignoreParentScale = false,
				effectArray = {
					{
						effectGoName = "miji_attack08",
						effectAsset = {
							BundleName = "effects/prefab/model/miji/miji_attack08_prefab",
							AssetName = "miji_attack08",
						},
					},
					{
						effectGoName = "",
						effectAsset = {},

					},
				},
			},
			{
				triggerEventName = "esoterica_attack_9/begin",
				triggerDelay = 0.0,
				triggerFreeDelay = 0.0,
				effectGoName = "",
				effectAsset = {},

				playerAtTarget = false,
				referenceNodeHierarchyPath = "",
				isAttach = false,
				isRotation = true,
				isUseCustomTransform = false,
				offsetPosX = 0.0,
				offsetPosY = 0.0,
				offsetPosZ = 0.0,
				triggerStopEvent = "",
				effectBtnName = "esoterica_attack_9",
				playerAtPos = false,
				ignoreParentScale = false,
				effectArray = {
					{
						effectGoName = "xianfa_attack10",
						effectAsset = {
							BundleName = "effects/prefab/model/xianfa/xianfa_attack10_prefab",
							AssetName = "xianfa_attack10",
						},
					},
					{
						effectGoName = "",
						effectAsset = {},

					},
				},
			},
			{
				triggerEventName = "esoterica_attack_10/begin",
				triggerDelay = 0.0,
				triggerFreeDelay = 0.0,
				effectGoName = "",
				effectAsset = {},

				playerAtTarget = false,
				referenceNodeHierarchyPath = "",
				isAttach = false,
				isRotation = true,
				isUseCustomTransform = false,
				offsetPosX = 0.0,
				offsetPosY = 0.0,
				offsetPosZ = 0.0,
				triggerStopEvent = "",
				effectBtnName = "esoterica_attack_10",
				playerAtPos = false,
				ignoreParentScale = false,
				effectArray = {
					{
						effectGoName = "xianfa_attack2",
						effectAsset = {
							BundleName = "effects/prefab/model/xianfa/xianfa_attack2_prefab",
							AssetName = "xianfa_attack2",
						},
					},
					{
						effectGoName = "",
						effectAsset = {},

					},
				},
			},
		},
		sounds = {
			{
				soundEventName = "combo1_1/begin",
				soundDelay = 0.0,
				soundAudioAsset = {
					BundleName = "audios/sfxs/roleskill/role1",
					AssetName = "role1_attack1",
				},
				soundAudioGoName = "role1_attack1",
				soundBtnName = "combo2_1",
				soundIsMainRole = false,
			},
			{
				soundEventName = "combo1_2/begin",
				soundDelay = 0.0,
				soundAudioAsset = {
					BundleName = "audios/sfxs/roleskill/role1",
					AssetName = "role1_attack2",
				},
				soundAudioGoName = "role1_attack2",
				soundBtnName = "combo2_2",
				soundIsMainRole = false,
			},
			{
				soundEventName = "combo1_3/begin",
				soundDelay = 0.0,
				soundAudioAsset = {
					BundleName = "audios/sfxs/roleskill/role1",
					AssetName = "role1_attack3",
				},
				soundAudioGoName = "role1_attack3",
				soundBtnName = "combo2_3",
				soundIsMainRole = false,
			},
			{
				soundEventName = "combo1_4/begin",
				soundDelay = 0.0,
				soundAudioAsset = {
					BundleName = "audios/sfxs/roleskill/role1",
					AssetName = "role1_attack4",
				},
				soundAudioGoName = "role1_attack4",
				soundBtnName = "combo2_4",
				soundIsMainRole = false,
			},
			{
				soundEventName = "attack1/begin",
				soundDelay = 0.0,
				soundAudioAsset = {
					BundleName = "audios/sfxs/roleskill/role1",
					AssetName = "role1_skill1",
				},
				soundAudioGoName = "role1_skill1",
				soundBtnName = "attack1",
				soundIsMainRole = false,
			},
			{
				soundEventName = "attack2/begin",
				soundDelay = 0.0,
				soundAudioAsset = {
					BundleName = "audios/sfxs/roleskill/role1",
					AssetName = "role1_skill2",
				},
				soundAudioGoName = "role1_skill2",
				soundBtnName = "attack2",
				soundIsMainRole = false,
			},
			{
				soundEventName = "attack3/begin",
				soundDelay = 0.0,
				soundAudioAsset = {
					BundleName = "audios/sfxs/roleskill/role1",
					AssetName = "role1_skill3",
				},
				soundAudioGoName = "role1_skill3",
				soundBtnName = "attack3",
				soundIsMainRole = false,
			},
			{
				soundEventName = "attack4/begin",
				soundDelay = 0.0,
				soundAudioAsset = {
					BundleName = "audios/sfxs/roleskill/role1",
					AssetName = "role1_skill4",
				},
				soundAudioGoName = "role1_skill4",
				soundBtnName = "attack4",
				soundIsMainRole = false,
			},
		},
		cameraShakes = {
			{
				CameraShakeBtnName = "attack2_2",
				eventName = "attack2/begin",
				numberOfShakes = 1,
				distance = 0.2,
				speed = 100.0,
				delay = 0.75,
				decay = 0.0,
			},
			{
				CameraShakeBtnName = "attack4_3",
				eventName = "attack4/begin",
				numberOfShakes = 1,
				distance = 1.2,
				speed = 80.0,
				delay = 2.0,
				decay = 0.0,
			},
			{
				CameraShakeBtnName = "combo1_42",
				eventName = "combo1_4/begin",
				numberOfShakes = 1,
				distance = 0.15,
				speed = 80.0,
				delay = 0.6,
				decay = 0.0,
			},
			{
				CameraShakeBtnName = "attack4_1",
				eventName = "attack4/begin",
				numberOfShakes = 1,
				distance = 0.1,
				speed = 100.0,
				delay = 0.5,
				decay = 0.0,
			},
			{
				CameraShakeBtnName = "attack4_2",
				eventName = "attack4/begin",
				numberOfShakes = 1,
				distance = 0.1,
				speed = 100.0,
				delay = 0.7,
				decay = 0.0,
			},
			{
				CameraShakeBtnName = "attack4_4",
				eventName = "attack4/begin",
				numberOfShakes = 1,
				distance = 0.15,
				speed = 100.0,
				delay = 0.8,
				decay = 0.0,
			},
			{
				CameraShakeBtnName = "attack4_5",
				eventName = "attack4/begin",
				numberOfShakes = 1,
				distance = 0.15,
				speed = 100.0,
				delay = 1.0,
				decay = 0.0,
			},
			{
				CameraShakeBtnName = "attack4_6",
				eventName = "attack4/begin",
				numberOfShakes = 1,
				distance = 0.15,
				speed = 100.0,
				delay = 1.6,
				decay = 0.0,
			},
			{
				CameraShakeBtnName = "attack3_1",
				eventName = "attack3/begin",
				numberOfShakes = 1,
				distance = 0.1,
				speed = 70.0,
				delay = 1.1,
				decay = 0.0,
			},
			{
				CameraShakeBtnName = "esoterica_attack_9_1",
				eventName = "esoterica_attack_9/begin",
				numberOfShakes = 1,
				distance = 0.08,
				speed = 100.0,
				delay = 0.2,
				decay = 0.0,
			},
			{
				CameraShakeBtnName = "esoterica_attack_9_2",
				eventName = "esoterica_attack_9/begin",
				numberOfShakes = 1,
				distance = 0.1,
				speed = 100.0,
				delay = 0.8,
				decay = 0.0,
			},
			{
				CameraShakeBtnName = "esoterica_attack_10",
				eventName = "esoterica_attack_10/begin",
				numberOfShakes = 3,
				distance = 0.1,
				speed = 120.0,
				delay = 0.8,
				decay = 0.0,
			},
		},
		radialBlurs = {
			{
				btnName = "attack1",
				eventName = "attack1/begin",
				isRole = true,
				referenceNodeHierarchyPath = "root/Bip001/Bip001 Pelvis/Bip001 Spine/Bip001 Spine1/hurt_middle",
				delay = 0.01,
				strength = 0.1,
				riseTime = 0.1,
				holdTime = 0.2,
				fallTime = 0.05,
			},
			{
				btnName = "attack4",
				eventName = "attack4/begin",
				isRole = true,
				referenceNodeHierarchyPath = "root/Bip001/Bip001 Pelvis/Bip001 Spine/Bip001 Spine1/hurt_middle",
				delay = 2.0,
				strength = 0.6,
				riseTime = 0.1,
				holdTime = 0.3,
				fallTime = 0.1,
			},
		},
	},
}