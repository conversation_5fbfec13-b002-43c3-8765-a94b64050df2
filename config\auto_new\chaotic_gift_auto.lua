-- C-苍穹礼包.xls
local item_table={
[1]={item_id=38310,num=1,is_bind=1},
[2]={item_id=48076,num=1,is_bind=1},
[3]={item_id=26203,num=15,is_bind=1},
[4]={item_id=26415,num=15,is_bind=1},
[5]={item_id=26501,num=1,is_bind=1},
[6]={item_id=26516,num=1,is_bind=1},
[7]={item_id=37801,num=1,is_bind=1},
[8]={item_id=48076,num=2,is_bind=1},
[9]={item_id=26203,num=20,is_bind=1},
[10]={item_id=26415,num=20,is_bind=1},
[11]={item_id=26502,num=1,is_bind=1},
[12]={item_id=26517,num=1,is_bind=1},
[13]={item_id=37901,num=1,is_bind=1},
[14]={item_id=26203,num=30,is_bind=1},
[15]={item_id=26415,num=30,is_bind=1},
[16]={item_id=26503,num=1,is_bind=1},
[17]={item_id=26518,num=1,is_bind=1},
[18]={item_id=38209,num=1,is_bind=1},
[19]={item_id=26355,num=1,is_bind=1},
[20]={item_id=26346,num=5,is_bind=1},
[21]={item_id=26515,num=1,is_bind=1},
[22]={item_id=26121,num=1,is_bind=1},
[23]={item_id=38311,num=1,is_bind=1},
[24]={item_id=26355,num=2,is_bind=1},
[25]={item_id=26356,num=2,is_bind=1},
[26]={item_id=26347,num=2,is_bind=1},
[27]={item_id=37802,num=1,is_bind=1},
[28]={item_id=26357,num=1,is_bind=1},
[29]={item_id=26348,num=1,is_bind=1},
[30]={item_id=37902,num=1,is_bind=1},
[31]={item_id=26357,num=2,is_bind=1},
[32]={item_id=26355,num=4,is_bind=1},
[33]={item_id=26356,num=4,is_bind=1},
[34]={item_id=26348,num=4,is_bind=1},
[35]={item_id=38312,num=1,is_bind=1},
[36]={item_id=26358,num=1,is_bind=1},
[37]={item_id=26349,num=5,is_bind=1},
[38]={item_id=37803,num=1,is_bind=1},
[39]={item_id=26358,num=2,is_bind=1},
[40]={item_id=26359,num=2,is_bind=1},
[41]={item_id=26350,num=2,is_bind=1},
[42]={item_id=37903,num=1,is_bind=1},
[43]={item_id=26360,num=1,is_bind=1},
[44]={item_id=26351,num=1,is_bind=1},
[45]={item_id=38212,num=1,is_bind=1},
[46]={item_id=26360,num=2,is_bind=1},
[47]={item_id=26359,num=4,is_bind=1},
[48]={item_id=26358,num=4,is_bind=1},
[49]={item_id=26351,num=4,is_bind=1},
[50]={item_id=38202,num=1,is_bind=1},
[51]={item_id=26367,num=1,is_bind=1},
[52]={item_id=26344,num=5,is_bind=1},
[53]={item_id=26500,num=1,is_bind=1},
[54]={item_id=38313,num=1,is_bind=1},
[55]={item_id=26367,num=2,is_bind=1},
[56]={item_id=26368,num=2,is_bind=1},
[57]={item_id=37804,num=1,is_bind=1},
[58]={item_id=26369,num=1,is_bind=1},
[59]={item_id=26345,num=1,is_bind=1},
[60]={item_id=22618,num=1,is_bind=1},
[61]={item_id=37904,num=1,is_bind=1},
[62]={item_id=26369,num=2,is_bind=1},
[63]={item_id=26368,num=4,is_bind=1},
[64]={item_id=26367,num=4,is_bind=1},
[65]={item_id=26345,num=4,is_bind=1},
[66]={item_id=22618,num=3,is_bind=1},
[67]={item_id=29749,num=1,is_bind=1},
[68]={item_id=27139,num=1,is_bind=1},
[69]={item_id=27143,num=1,is_bind=1},
[70]={item_id=26348,num=15,is_bind=1},
[71]={item_id=26455,num=1,is_bind=1},
[72]={item_id=46603,num=32,is_bind=1},
[73]={item_id=29750,num=1,is_bind=1},
[74]={item_id=27140,num=1,is_bind=1},
[75]={item_id=27144,num=1,is_bind=1},
[76]={item_id=26348,num=30,is_bind=1},
[77]={item_id=26459,num=1,is_bind=1},
[78]={item_id=46603,num=48,is_bind=1},
[79]={item_id=29751,num=1,is_bind=1},
[80]={item_id=27141,num=1,is_bind=1},
[81]={item_id=27145,num=1,is_bind=1},
[82]={item_id=26348,num=50,is_bind=1},
[83]={item_id=26460,num=1,is_bind=1},
[84]={item_id=46603,num=80,is_bind=1},
[85]={item_id=29752,num=1,is_bind=1},
[86]={item_id=27146,num=1,is_bind=1},
[87]={item_id=27150,num=1,is_bind=1},
[88]={item_id=48118,num=2,is_bind=1},
[89]={item_id=26450,num=1,is_bind=1},
[90]={item_id=46604,num=16,is_bind=1},
[91]={item_id=29753,num=1,is_bind=1},
[92]={item_id=27147,num=1,is_bind=1},
[93]={item_id=27151,num=1,is_bind=1},
[94]={item_id=48118,num=3,is_bind=1},
[95]={item_id=46604,num=32,is_bind=1},
[96]={item_id=29754,num=1,is_bind=1},
[97]={item_id=27148,num=1,is_bind=1},
[98]={item_id=27152,num=1,is_bind=1},
[99]={item_id=48118,num=5,is_bind=1},
[100]={item_id=46604,num=48,is_bind=1},
[101]={item_id=29755,num=1,is_bind=1},
[102]={item_id=27149,num=1,is_bind=1},
[103]={item_id=27153,num=1,is_bind=1},
[104]={item_id=48118,num=10,is_bind=1},
[105]={item_id=46604,num=80,is_bind=1},
[106]={item_id=26570,num=1,is_bind=1},
[107]={item_id=37779,num=1,is_bind=1},
[108]={item_id=38140,num=1,is_bind=1},
[109]={item_id=48187,num=1,is_bind=1},
[110]={item_id=22575,num=1,is_bind=1},
[111]={item_id=26191,num=1,is_bind=1},
[112]={item_id=37623,num=1,is_bind=1},
[113]={item_id=37816,num=1,is_bind=1},
[114]={item_id=37725,num=1,is_bind=1},
[115]={item_id=37818,num=1,is_bind=1},
[116]={item_id=37626,num=1,is_bind=1},
[117]={item_id=37823,num=1,is_bind=1},
[118]={item_id=38201,num=1,is_bind=1},
[119]={item_id=26203,num=5,is_bind=1},
[120]={item_id=26415,num=10,is_bind=1},
[121]={item_id=29748,num=1,is_bind=1},
[122]={item_id=27138,num=1,is_bind=1},
[123]={item_id=27142,num=1,is_bind=1},
[124]={item_id=26348,num=10,is_bind=1},
[125]={item_id=46603,num=16,is_bind=1},
[126]={item_id=37219,num=1,is_bind=1},
[127]={item_id=37746,num=1,is_bind=1},
[128]={item_id=37645,num=1,is_bind=1},
[129]={item_id=37708,num=1,is_bind=1},
[130]={item_id=37808,num=1,is_bind=1},
}

return {
gifts={
{},
{seq=1,name="荣耀黄金",need_gold=1288,reward_item={[0]=item_table[1],[1]=item_table[2],[2]=item_table[3],[3]=item_table[4],[4]=item_table[5],[5]=item_table[6]},model_show_itemid=38310,},
{seq=2,name="璀璨钻石",need_gold=1688,reward_item={[0]=item_table[7],[1]=item_table[8],[2]=item_table[9],[3]=item_table[10],[4]=item_table[11],[5]=item_table[12]},model_show_type=2,model_show_itemid=37801,display_scale=1,},
{seq=3,name="巅峰大师",need_gold=2888,reward_item={[0]=item_table[13],[1]=item_table[8],[2]=item_table[14],[3]=item_table[15],[4]=item_table[16],[5]=item_table[17]},model_show_type=2,model_show_itemid=37901,display_scale=1,},
{seq=4,grade=2,reward_item={[0]=item_table[18],[1]=item_table[19],[2]=item_table[20],[3]=item_table[2],[4]=item_table[21],[5]=item_table[22]},model_show_itemid=38209,},
{seq=5,grade=2,reward_item={[0]=item_table[23],[1]=item_table[24],[2]=item_table[25],[3]=item_table[26],[4]=item_table[2],[5]=item_table[22]},model_show_itemid=38311,},
{seq=6,grade=2,reward_item={[0]=item_table[27],[1]=item_table[28],[2]=item_table[24],[3]=item_table[25],[4]=item_table[29],[5]=item_table[8]},model_show_itemid=37802,},
{seq=7,grade=2,reward_item={[0]=item_table[30],[1]=item_table[31],[2]=item_table[32],[3]=item_table[33],[4]=item_table[34],[5]=item_table[8]},model_show_itemid=37902,},
{seq=8,grade=3,reward_item={[0]=item_table[35],[1]=item_table[36],[2]=item_table[37],[3]=item_table[2],[4]=item_table[21],[5]=item_table[22]},model_show_itemid=38312,},
{seq=9,name="荣耀黄金",grade=3,need_gold=1288,reward_item={[0]=item_table[38],[1]=item_table[39],[2]=item_table[40],[3]=item_table[41],[4]=item_table[2],[5]=item_table[22]},model_show_type=2,model_show_itemid=37803,display_scale=1,},
{seq=10,grade=3,reward_item={[0]=item_table[42],[1]=item_table[43],[2]=item_table[40],[3]=item_table[39],[4]=item_table[44],[5]=item_table[8]},model_show_itemid=37903,},
{seq=11,name="巅峰大师",grade=3,need_gold=2888,reward_item={[0]=item_table[45],[1]=item_table[46],[2]=item_table[47],[3]=item_table[48],[4]=item_table[49],[5]=item_table[8]},model_show_itemid=38212,},
{seq=12,grade=4,reward_item={[0]=item_table[50],[1]=item_table[51],[2]=item_table[52],[3]=item_table[53],[4]=item_table[21],[5]=item_table[8]},model_show_itemid=38202,},
{seq=13,grade=4,reward_item={[0]=item_table[54],[1]=item_table[55],[2]=item_table[56],[3]=item_table[52],[4]=item_table[6],[5]=item_table[8]},model_show_itemid=38313,},
{seq=14,grade=4,reward_item={[0]=item_table[57],[1]=item_table[58],[2]=item_table[56],[3]=item_table[55],[4]=item_table[59],[5]=item_table[60]},model_show_itemid=37804,},
{seq=15,grade=4,reward_item={[0]=item_table[61],[1]=item_table[62],[2]=item_table[63],[3]=item_table[64],[4]=item_table[65],[5]=item_table[66]},model_show_itemid=37904,}
},

gifts_meta_table_map={
[6]=2,	-- depth:1
[14]=2,	-- depth:1
[7]=3,	-- depth:1
[8]=4,	-- depth:1
[11]=3,	-- depth:1
[15]=3,	-- depth:1
[16]=4,	-- depth:1
},
rmb_buy={
{},
{seq=1,rmb_price=320,reward_item={[0]=item_table[67],[1]=item_table[68],[2]=item_table[69],[3]=item_table[70],[4]=item_table[71],[5]=item_table[72]},show_item=item_table[72],name="墨谷螳螂",},
{seq=2,rmb_price=480,reward_item={[0]=item_table[73],[1]=item_table[74],[2]=item_table[75],[3]=item_table[76],[4]=item_table[77],[5]=item_table[78]},show_item=item_table[78],name="游园艳马",},
{seq=3,rmb_price=800,reward_item={[0]=item_table[79],[1]=item_table[80],[2]=item_table[81],[3]=item_table[82],[4]=item_table[83],[5]=item_table[84]},show_item=item_table[84],name="乌泱蟾蜍",},
{seq=4,grade=2,reward_item={[0]=item_table[85],[1]=item_table[86],[2]=item_table[87],[3]=item_table[88],[4]=item_table[89],[5]=item_table[90]},show_item=item_table[90],name="守地狱使者",},
{seq=5,grade=2,rmb_price=320,reward_item={[0]=item_table[91],[1]=item_table[92],[2]=item_table[93],[3]=item_table[94],[4]=item_table[71],[5]=item_table[95]},show_item=item_table[95],name="守天玄武",},
{seq=6,grade=2,rmb_price=480,reward_item={[0]=item_table[96],[1]=item_table[97],[2]=item_table[98],[3]=item_table[99],[4]=item_table[77],[5]=item_table[100]},show_item=item_table[100],name="咆哮金牛",},
{seq=7,grade=2,rmb_price=800,reward_item={[0]=item_table[101],[1]=item_table[102],[2]=item_table[103],[3]=item_table[104],[4]=item_table[83],[5]=item_table[105]},show_item=item_table[105],name="金贵犀牛",}
},

rmb_buy_meta_table_map={
},
special={
{},
{chapter=2,name="【苍穹特典Ⅱ】",reward_item={[0]=item_table[106],[1]=item_table[107],[2]=item_table[108],[3]=item_table[109],[4]=item_table[110]},model_show_type1=3,model_bundle_name1="uis/rawimages/a2_rare_5",model_asset_name1="a2_rare_5.png",model_show_itemid1=26570,model_show_itemid2=37779,model_show_itemid3=38140,}
},

special_meta_table_map={
},
special_task={
{},
{task_id=1,param1=1,task_title="章节任务二",task_decs="购买苍穹直购-上古年兽<color=#006a25>320元</color>",},
{task_id=2,param1=2,task_title="章节任务三",task_decs="购买苍穹直购-上古腾虎<color=#006a25>480元</color>",},
{task_id=3,param1=3,task_title="章节任务四",task_decs="购买苍穹直购-上古八蛇<color=#006a25>800元</color>",},
{task_id=4,chapter=2,param1=4,task_decs="购买苍穹直购-上古熊兽<color=#006a25>160元</color>",},
{task_id=5,param1=5,task_title="章节任务二",task_decs="购买苍穹直购-上古雷兽<color=#006a25>320元</color>",},
{task_id=6,param1=6,task_title="章节任务三",task_decs="购买苍穹直购-上古灵猴<color=#006a25>480元</color>",},
{task_id=7,param1=7,task_title="章节任务四",task_decs="购买苍穹直购-上古虎王<color=#006a25>800元</color>",},
{task_id=8,chapter=3,param1=8,task_decs="购买苍穹直购-上古金鹏<color=#006a25>160元</color>",},
{task_id=9,param1=9,task_title="章节任务二",task_decs="购买苍穹直购-上古饕餮<color=#006a25>320元</color>",},
{task_id=10,param1=10,task_title="章节任务三",task_decs="购买苍穹直购-上古龙神<color=#006a25>480元</color>",},
{task_id=11,param1=11,task_title="章节任务四",task_decs="购买苍穹直购-上古白泽<color=#006a25>800元</color>",},
{task_id=12,chapter=4,param1=12,task_decs="购买苍穹直购-上神刑天<color=#006a25>160元</color>",},
{task_id=13,chapter=4,param1=13,task_decs="购买苍穹直购-神风文远<color=#006a25>320元</color>",},
{task_id=14,chapter=4,param1=14,task_decs="购买苍穹直购-神将飞蓬<color=#006a25>480元</color>",},
{task_id=15,chapter=4,param1=15,task_decs="购买苍穹直购-烈火真君<color=#006a25>800元</color>",},
{task_id=16,chapter=5,param1=16,task_decs="购买苍穹直购-太昊虎将<color=#006a25>160元</color>",},
{task_id=17,chapter=5,param1=17,task_decs="购买苍穹直购-神弓哲别<color=#006a25>320元</color>",},
{task_id=18,chapter=5,param1=18,task_decs="购买苍穹直购-天界霸将<color=#006a25>480元</color>",},
{task_id=19,chapter=5,param1=19,task_decs="购买苍穹直购-七星孔明<color=#006a25>800元</color>",},
{task_id=20,chapter=6,param1=20,task_decs="购买苍穹直购-凤雏庞统<color=#006a25>160元</color>",},
{task_id=21,chapter=6,param1=21,task_decs="购买苍穹直购-春秋左慈<color=#006a25>320元</color>",},
{task_id=22,chapter=6,param1=22,task_decs="购买苍穹直购-血狱魔君<color=#006a25>480元</color>",},
{task_id=23,chapter=6,param1=23,task_decs="购买苍穹直购-智定天下<color=#006a25>800元</color>",},
{task_id=24,chapter=7,param1=24,task_decs="购买苍穹直购-虚界魔怪<color=#006a25>160元</color>",},
{task_id=25,chapter=7,param1=25,task_decs="购买苍穹直购-极恶化身<color=#006a25>320元</color>",},
{task_id=26,chapter=7,param1=26,task_decs="购买苍穹直购-噬骨魔龙<color=#006a25>480元</color>",},
{task_id=27,chapter=7,param1=27,task_decs="购买苍穹直购-地狱之灵<color=#006a25>800元</color>",},
{task_id=28,chapter=8,param1=28,task_decs="购买苍穹直购-域外心魔<color=#006a25>160元</color>",},
{task_id=29,chapter=8,param1=29,task_decs="购买苍穹直购-白虎妖王<color=#006a25>320元</color>",},
{task_id=30,chapter=8,param1=30,task_decs="购买苍穹直购-永恒憎恨<color=#006a25>480元</color>",},
{task_id=31,chapter=8,param1=31,task_decs="购买苍穹直购-饿念吞噬<color=#006a25>800元</color>",},
{task_id=32,chapter=9,param1=32,task_decs="购买苍穹直购-嗜血屠杀<color=#006a25>160元</color>",},
{task_id=33,chapter=9,param1=33,task_decs="购买苍穹直购-招魂冥使<color=#006a25>320元</color>",},
{task_id=34,chapter=9,param1=34,task_decs="购买苍穹直购-贪婪爆龙<color=#006a25>480元</color>",},
{task_id=35,chapter=9,param1=35,task_decs="购买苍穹直购-吞天魔兽<color=#006a25>800元</color>",},
{task_id=36,chapter=10,param1=36,task_decs="购买苍穹直购-熔岩掌控<color=#006a25>160元</color>",},
{task_id=37,chapter=10,param1=37,task_decs="购买苍穹直购-闪电掌控<color=#006a25>320元</color>",},
{task_id=38,chapter=10,param1=38,task_decs="购买苍穹直购-烈火掌控<color=#006a25>480元</color>",},
{task_id=39,chapter=10,param1=39,task_decs="购买苍穹直购-大海掌控<color=#006a25>800元</color>",},
{task_id=40,chapter=11,param1=40,task_decs="购买苍穹直购-怒雷掌控<color=#006a25>160元</color>",},
{task_id=41,chapter=11,param1=41,task_decs="购买苍穹直购-风暴掌控<color=#006a25>320元</color>",},
{task_id=42,chapter=11,param1=42,task_decs="购买苍穹直购-荒芜掌控<color=#006a25>480元</color>",},
{task_id=43,chapter=11,param1=43,task_decs="购买苍穹直购-大地掌控<color=#006a25>800元</color>",},
{task_id=44,chapter=12,param1=44,task_decs="购买苍穹直购-恶念掌控<color=#006a25>160元</color>",},
{task_id=45,param1=45,task_title="章节任务二",task_decs="购买苍穹直购-幽冥掌控<color=#006a25>320元</color>",},
{task_id=46,chapter=12,param1=46,task_decs="购买苍穹直购-恶鬼掌控<color=#006a25>480元</color>",},
{task_id=47,chapter=12,param1=47,task_decs="购买苍穹直购-冥府掌控<color=#006a25>800元</color>",}
},

special_task_meta_table_map={
[43]=3,	-- depth:1
[31]=3,	-- depth:1
[32]=4,	-- depth:1
[34]=2,	-- depth:1
[36]=4,	-- depth:1
[46]=45,	-- depth:1
[38]=2,	-- depth:1
[39]=3,	-- depth:1
[30]=2,	-- depth:1
[44]=4,	-- depth:1
[42]=2,	-- depth:1
[35]=3,	-- depth:1
[40]=4,	-- depth:1
[24]=4,	-- depth:1
[27]=3,	-- depth:1
[6]=5,	-- depth:1
[7]=5,	-- depth:1
[8]=5,	-- depth:1
[10]=9,	-- depth:1
[11]=9,	-- depth:1
[12]=9,	-- depth:1
[14]=2,	-- depth:1
[28]=4,	-- depth:1
[15]=3,	-- depth:1
[18]=2,	-- depth:1
[19]=3,	-- depth:1
[20]=4,	-- depth:1
[22]=2,	-- depth:1
[23]=3,	-- depth:1
[47]=3,	-- depth:1
[26]=2,	-- depth:1
[16]=4,	-- depth:1
[48]=4,	-- depth:1
},
vip_special={
{},
{chapter=2,name="【VIP豪礼Ⅱ】",reward_item={[0]=item_table[111],[1]=item_table[112],[2]=item_table[113],[3]=item_table[109],[4]=item_table[110]},model_show_itemid2=37623,model_show_itemid3=37816,},
{chapter=3,name="【VIP豪礼Ⅲ】",reward_item={[0]=item_table[111],[1]=item_table[114],[2]=item_table[115],[3]=item_table[109],[4]=item_table[110]},model_show_itemid2=37725,model_show_itemid3=37818,},
{chapter=4,name="【VIP豪礼Ⅳ】",reward_item={[0]=item_table[111],[1]=item_table[116],[2]=item_table[117],[3]=item_table[109],[4]=item_table[110]},model_show_itemid2=37626,model_show_itemid3=37823,}
},

vip_special_meta_table_map={
},
vip_special_task={
{task_decs="购买苍穹豪礼-尊贵白银礼包<color=#006a25>688灵玉</color>",},
{task_id=1,param1=1,task_title="章节任务二",task_decs="购买苍穹豪礼-荣耀黄金礼包<color=#006a25>1288灵玉</color>",},
{task_id=2,param1=2,task_title="章节任务三",task_decs="购买苍穹豪礼-璀璨钻石礼包<color=#006a25>1688灵玉</color>",},
{task_id=3,param1=3,task_title="章节任务四",task_decs="购买苍穹豪礼-巅峰大师礼包<color=#006a25>2888灵玉</color>",},
{task_id=4,chapter=2,param1=4,task_decs="购买苍穹豪礼-尊贵白银礼包<color=#006a25>328灵玉</color>",},
{task_id=5,chapter=2,param1=5,},
{task_id=6,chapter=2,param1=6,},
{task_id=7,chapter=2,param1=7,},
{task_id=8,chapter=3,param1=8,task_decs="购买苍穹豪礼-尊贵白银礼包<color=#006a25>328灵玉</color>",},
{task_id=9,chapter=3,param1=9,},
{task_id=10,chapter=3,param1=10,},
{task_id=11,chapter=3,param1=11,},
{task_id=12,chapter=4,param1=12,task_decs="购买苍穹豪礼-尊贵白银礼包<color=#006a25>328灵玉</color>",},
{task_id=13,chapter=4,param1=13,},
{task_id=14,chapter=4,param1=14,},
{task_id=15,chapter=4,param1=15,},
{task_id=16,chapter=5,param1=16,task_decs="购买苍穹豪礼-尊贵白银礼包<color=#006a25>328灵玉</color>",},
{task_id=17,chapter=5,param1=17,},
{task_id=18,chapter=5,param1=18,},
{task_id=19,chapter=5,param1=19,},
{task_id=20,chapter=6,param1=20,task_decs="购买苍穹豪礼-尊贵白银礼包<color=#006a25>688灵玉</color>",},
{task_id=21,chapter=6,param1=21,},
{task_id=22,chapter=6,param1=22,},
{task_id=23,chapter=6,param1=23,},
{task_id=24,chapter=7,param1=24,task_decs="购买苍穹豪礼-尊贵白银礼包<color=#006a25>688灵玉</color>",},
{task_id=25,chapter=7,param1=25,},
{task_id=26,chapter=7,param1=26,},
{task_id=27,chapter=7,param1=27,},
{task_id=28,chapter=8,param1=28,task_decs="购买苍穹豪礼-尊贵白银礼包<color=#006a25>888灵玉</color>",},
{task_id=29,chapter=8,param1=29,},
{task_id=30,chapter=8,param1=30,},
{task_id=31,chapter=8,param1=31,},
{task_id=32,chapter=9,param1=32,task_decs="购买苍穹豪礼-尊贵白银礼包<color=#006a25>888灵玉</color>",},
{task_id=33,chapter=9,param1=33,},
{task_id=34,chapter=9,param1=34,},
{task_id=35,chapter=9,param1=35,},
{task_id=36,chapter=10,param1=36,task_decs="购买苍穹豪礼-尊贵白银礼包<color=#006a25>888灵玉</color>",},
{task_id=37,chapter=10,param1=37,},
{task_id=38,chapter=10,param1=38,},
{task_id=39,chapter=10,param1=39,},
{task_id=40,chapter=11,param1=40,},
{task_id=41,chapter=11,param1=41,},
{task_id=42,chapter=11,param1=42,},
{task_id=43,chapter=11,param1=43,},
{task_id=44,chapter=12,param1=44,},
{task_id=45,chapter=12,param1=45,},
{task_id=46,chapter=12,param1=46,},
{task_id=47,chapter=12,param1=47,},
{task_id=48,chapter=13,param1=48,},
{task_id=49,chapter=13,param1=49,},
{task_id=50,chapter=13,param1=50,},
{task_id=51,chapter=13,param1=51,},
{task_id=52,chapter=14,param1=52,},
{task_id=53,chapter=14,param1=53,},
{task_id=54,chapter=14,param1=54,},
{task_id=55,chapter=14,param1=55,},
{task_id=56,chapter=15,param1=56,},
{task_id=57,chapter=15,param1=57,},
{task_id=58,chapter=15,param1=58,},
{task_id=59,chapter=15,param1=59,},
{task_id=60,chapter=16,param1=60,},
{task_id=61,chapter=16,param1=61,},
{task_id=62,chapter=16,param1=62,},
{task_id=63,chapter=16,param1=63,},
{task_id=64,chapter=17,param1=64,},
{task_id=65,chapter=17,param1=65,},
{task_id=66,chapter=17,param1=66,},
{task_id=67,chapter=17,param1=67,},
{task_id=68,chapter=18,param1=68,task_decs="购买苍穹豪礼-尊贵白银礼包<color=#006a25>688灵玉</color>",},
{task_id=69,chapter=18,param1=69,},
{task_id=70,chapter=18,param1=70,},
{task_id=71,chapter=18,param1=71,},
{task_id=72,chapter=19,param1=72,task_decs="购买苍穹豪礼-尊贵白银礼包<color=#006a25>328灵玉</color>",},
{task_id=73,chapter=19,param1=73,},
{task_id=74,chapter=19,param1=74,},
{task_id=75,chapter=19,param1=75,},
{task_id=76,chapter=20,param1=76,task_decs="购买苍穹豪礼-尊贵白银礼包<color=#006a25>328灵玉</color>",},
{task_id=77,chapter=20,param1=77,},
{task_id=78,chapter=20,param1=78,},
{task_id=79,chapter=20,param1=79,},
{task_id=80,chapter=21,param1=80,task_decs="购买苍穹豪礼-尊贵白银礼包<color=#006a25>328灵玉</color>",},
{task_id=81,chapter=21,param1=81,},
{task_id=82,chapter=21,param1=82,},
{task_id=83,chapter=21,param1=83,},
{task_id=84,chapter=22,param1=84,task_decs="购买苍穹豪礼-尊贵白银礼包<color=#006a25>328灵玉</color>",},
{task_id=85,param1=85,task_title="章节任务二",task_decs="购买苍穹豪礼-荣耀黄金礼包<color=#006a25>648灵玉</color>",},
{task_id=86,param1=86,task_title="章节任务三",task_decs="购买苍穹豪礼-璀璨钻石礼包<color=#006a25>1288灵玉</color>",},
{task_id=87,chapter=22,param1=87,},
{task_id=88,chapter=23,param1=88,task_decs="购买苍穹豪礼-尊贵白银礼包<color=#006a25>688灵玉</color>",},
{task_id=89,chapter=23,param1=89,},
{task_id=90,chapter=23,param1=90,},
{task_id=91,chapter=23,param1=91,},
{task_id=92,chapter=24,param1=92,task_decs="购买苍穹豪礼-尊贵白银礼包<color=#006a25>688灵玉</color>",},
{task_id=93,chapter=24,param1=93,},
{task_id=94,param1=94,task_title="章节任务三",task_decs="购买苍穹豪礼-璀璨钻石礼包<color=#006a25>3288灵玉</color>",},
{task_id=95,param1=95,task_title="章节任务四",task_decs="购买苍穹豪礼-巅峰大师礼包<color=#006a25>6488灵玉</color>",},
{task_id=96,chapter=25,param1=96,task_decs="购买苍穹豪礼-尊贵白银礼包<color=#006a25>888灵玉</color>",},
{task_id=97,param1=97,task_title="章节任务二",task_decs="购买苍穹豪礼-荣耀黄金礼包<color=#006a25>1588灵玉</color>",},
{task_id=98,chapter=25,param1=98,},
{task_id=99,chapter=25,param1=99,},
{task_id=100,chapter=26,param1=100,task_decs="购买苍穹豪礼-尊贵白银礼包<color=#006a25>888灵玉</color>",},
{task_id=101,chapter=26,param1=101,},
{task_id=102,chapter=26,param1=102,},
{task_id=103,chapter=26,param1=103,},
{task_id=104,chapter=27,param1=104,task_decs="购买苍穹豪礼-尊贵白银礼包<color=#006a25>888灵玉</color>",},
{task_id=105,chapter=27,param1=105,},
{task_id=106,chapter=27,param1=106,},
{task_id=107,chapter=27,param1=107,},
{task_id=108,chapter=28,param1=108,},
{task_id=109,chapter=28,param1=109,},
{task_id=110,chapter=28,param1=110,},
{task_id=111,chapter=28,param1=111,},
{task_id=112,chapter=29,param1=112,},
{task_id=113,chapter=29,param1=113,},
{task_id=114,chapter=29,param1=114,},
{task_id=115,chapter=29,param1=115,},
{task_id=116,chapter=30,param1=116,},
{task_id=117,chapter=30,param1=117,},
{task_id=118,chapter=30,param1=118,},
{task_id=119,chapter=30,param1=119,},
{task_id=120,chapter=31,param1=120,},
{task_id=121,chapter=31,param1=121,},
{task_id=122,chapter=31,param1=122,},
{task_id=123,chapter=31,param1=123,},
{task_id=124,chapter=32,param1=124,},
{task_id=125,chapter=32,param1=125,task_decs="购买苍穹豪礼-荣耀黄金礼包<color=#006a25>3288灵玉</color>",},
{task_id=126,chapter=32,param1=126,task_decs="购买苍穹豪礼-璀璨钻石礼包<color=#006a25>6488灵玉</color>",},
{task_id=127,chapter=32,param1=127,task_decs="购买苍穹豪礼-巅峰大师礼包<color=#006a25>8888灵玉</color>",},
{task_id=128,chapter=33,param1=128,},
{task_id=129,chapter=33,param1=129,},
{task_id=130,chapter=33,param1=130,},
{task_id=131,chapter=33,param1=131,},
{task_id=132,chapter=34,param1=132,},
{task_id=133,chapter=34,param1=133,},
{task_id=134,chapter=34,param1=134,},
{task_id=135,chapter=34,param1=135,}
},

vip_special_task_meta_table_map={
[96]=93,	-- depth:1
[95]=93,	-- depth:1
[98]=97,	-- depth:1
[90]=2,	-- depth:1
[92]=96,	-- depth:2
[91]=95,	-- depth:2
[88]=4,	-- depth:1
[87]=85,	-- depth:1
[86]=85,	-- depth:1
[84]=4,	-- depth:1
[83]=87,	-- depth:2
[82]=86,	-- depth:2
[94]=2,	-- depth:1
[128]=4,	-- depth:1
[104]=96,	-- depth:2
[100]=96,	-- depth:2
[127]=3,	-- depth:1
[126]=2,	-- depth:1
[124]=128,	-- depth:2
[123]=127,	-- depth:2
[122]=126,	-- depth:2
[130]=126,	-- depth:2
[120]=128,	-- depth:2
[119]=127,	-- depth:2
[118]=126,	-- depth:2
[131]=127,	-- depth:2
[116]=128,	-- depth:2
[115]=127,	-- depth:2
[114]=126,	-- depth:2
[112]=128,	-- depth:2
[111]=127,	-- depth:2
[110]=126,	-- depth:2
[108]=96,	-- depth:2
[132]=128,	-- depth:2
[107]=95,	-- depth:2
[106]=98,	-- depth:2
[103]=95,	-- depth:2
[102]=98,	-- depth:2
[134]=126,	-- depth:2
[99]=95,	-- depth:2
[80]=4,	-- depth:1
[68]=128,	-- depth:2
[78]=86,	-- depth:2
[38]=98,	-- depth:2
[36]=96,	-- depth:2
[35]=95,	-- depth:2
[34]=98,	-- depth:2
[32]=96,	-- depth:2
[31]=95,	-- depth:2
[30]=98,	-- depth:2
[28]=96,	-- depth:2
[27]=95,	-- depth:2
[26]=2,	-- depth:1
[24]=96,	-- depth:2
[23]=95,	-- depth:2
[22]=2,	-- depth:1
[20]=4,	-- depth:1
[19]=87,	-- depth:2
[18]=86,	-- depth:2
[16]=4,	-- depth:1
[15]=87,	-- depth:2
[14]=86,	-- depth:2
[12]=4,	-- depth:1
[11]=87,	-- depth:2
[10]=86,	-- depth:2
[8]=4,	-- depth:1
[7]=87,	-- depth:2
[6]=86,	-- depth:2
[39]=95,	-- depth:2
[79]=87,	-- depth:2
[40]=96,	-- depth:2
[43]=127,	-- depth:2
[76]=4,	-- depth:1
[75]=87,	-- depth:2
[74]=86,	-- depth:2
[72]=4,	-- depth:1
[71]=3,	-- depth:1
[70]=2,	-- depth:1
[135]=127,	-- depth:2
[67]=127,	-- depth:2
[66]=126,	-- depth:2
[64]=128,	-- depth:2
[63]=127,	-- depth:2
[62]=126,	-- depth:2
[60]=128,	-- depth:2
[59]=127,	-- depth:2
[58]=126,	-- depth:2
[56]=128,	-- depth:2
[55]=127,	-- depth:2
[54]=126,	-- depth:2
[52]=128,	-- depth:2
[51]=127,	-- depth:2
[50]=126,	-- depth:2
[48]=128,	-- depth:2
[47]=127,	-- depth:2
[46]=126,	-- depth:2
[44]=128,	-- depth:2
[42]=126,	-- depth:2
[136]=128,	-- depth:2
},
gifts_default_table={day=9999,seq=0,name="尊贵白银",grade=1,need_gold=688,reward_item={[0]=item_table[118],[1]=item_table[2],[2]=item_table[119],[3]=item_table[120],[4]=item_table[53],[5]=item_table[21]},model_show_type=1,model_bundle_name="",model_asset_name="",model_show_itemid=38201,special_show_name="",display_pos="",display_scale=0.5,show_special_effect="",special_img="",},

rmb_buy_default_table={day=9999,seq=0,grade=1,rmb_type=119,rmb_price=160,buy_times=1,reward_item={[0]=item_table[121],[1]=item_table[122],[2]=item_table[123],[3]=item_table[124],[4]=item_table[89],[5]=item_table[125]},show_item=item_table[125],name="巨象守卫",},

special_default_table={day=9999,chapter=1,name="【苍穹特典Ⅰ】",reward_item={[0]=item_table[126],[1]=item_table[127],[2]=item_table[128],[3]=item_table[109],[4]=item_table[110]},model_show_type1=1,model_bundle_name1="",model_asset_name1="",model_show_itemid1=37219,special_show_name1="",display_pos1="",display_scale1=0.5,show_special_effect1="",special_img1="",model_show_type2=1,model_bundle_name2="",model_asset_name2="",model_show_itemid2=37746,special_show_name2="",display_pos2="",display_scale2=0.5,show_special_effect2="",special_img2="",model_show_type3=1,model_bundle_name3="",model_asset_name3="",model_show_itemid3=37645,special_show_name3="",display_pos3="",display_scale3=0.5,show_special_effect3="",special_img3="",},

special_task_default_table={task_id=0,chapter=1,task_type=7,param1=0,param2=0,task_title="章节任务一",task_decs="购买苍穹直购-上古魔龙<color=#006a25>160元</color>",open_panel="ChaoticPurchaseView",act_type="",},

vip_special_default_table={day=9999,chapter=1,name="【VIP豪礼Ⅰ】",reward_item={[0]=item_table[111],[1]=item_table[129],[2]=item_table[130],[3]=item_table[109],[4]=item_table[110]},model_show_type1=3,model_bundle_name1="uis/rawimages/a1_rare_1",model_asset_name1="a1_rare_1.png",model_show_itemid1=26191,special_show_name1="",display_pos1="0|-15",display_scale1=0.5,show_special_effect1=1,special_img1="",model_show_type2=1,model_bundle_name2="",model_asset_name2="",model_show_itemid2=37708,special_show_name2="",display_pos2="",display_scale2=0.5,show_special_effect2="",special_img2="",model_show_type3=2,model_bundle_name3="",model_asset_name3="",model_show_itemid3=37808,special_show_name3="",display_pos3="",display_scale3=0.8,show_special_effect3="",special_img3="",},

vip_special_task_default_table={task_id=0,chapter=1,task_type=6,param1=0,param2=0,task_title="章节任务一",task_decs="购买苍穹豪礼-尊贵白银礼包<color=#006a25>1588灵玉</color>",open_panel="HongHuangGoodCoremonyView",act_type="",}

}

