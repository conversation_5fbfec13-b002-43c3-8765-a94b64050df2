return {
	actorController = {
		projectiles = {},

		hurts = {},

	},
	actorTriggers = {
		effects = {
			{
				triggerEventName = "attack2/begin",
				triggerDelay = 1.23,
				triggerFreeDelay = 0.0,
				effectGoName = "yushou_skill_3",
				effectAsset = {
					BundleName = "effects/prefab/model/yushou/common/yushou_skill_3_prefab",
					AssetName = "yushou_skill_3",
				},
				playerAtTarget = false,
				referenceNodeHierarchyPath = "root",
				isAttach = true,
				isRotation = false,
				isUseCustomTransform = true,
				offsetPosX = 0.0,
				offsetPosY = 0.0,
				offsetPosZ = 7.0,
				triggerStopEvent = "",
				effectBtnName = "attack2",
				playerAtPos = false,
				ignoreParentScale = false,
			},
			{
				triggerEventName = "attack1/begin",
				triggerDelay = 0.0,
				triggerFreeDelay = 0.0,
				effectGoName = "eff_4038yushou_attcak1",
				effectAsset = {
					BundleName = "effects/prefab/model/yushou/4038/eff_4038yushou_attcak1_prefab",
					AssetName = "eff_4038yushou_attcak1",
				},
				playerAtTarget = false,
				referenceNodeHierarchyPath = "root",
				isAttach = true,
				isRotation = false,
				isUseCustomTransform = false,
				offsetPosX = 0.0,
				offsetPosY = 0.0,
				offsetPosZ = 0.0,
				triggerStopEvent = "",
				effectBtnName = "attack1",
				playerAtPos = false,
				ignoreParentScale = true,
			},
			{
				triggerEventName = "rest/begin",
				triggerDelay = 0.0,
				triggerFreeDelay = 0.0,
				effectGoName = "eff_4038yushou_attcak1",
				effectAsset = {
					BundleName = "effects/prefab/model/yushou/4038/eff_4038yushou_attcak1_prefab",
					AssetName = "eff_4038yushou_attcak1",
				},
				playerAtTarget = false,
				referenceNodeHierarchyPath = "root",
				isAttach = true,
				isRotation = false,
				isUseCustomTransform = true,
				offsetPosX = 0.0,
				offsetPosY = 0.0,
				offsetPosZ = 0.0,
				triggerStopEvent = "",
				effectBtnName = "rest",
				playerAtPos = false,
				ignoreParentScale = true,
			},
		},
		sounds = {},

		cameraShakes = {},

		radialBlurs = {},

	},
}