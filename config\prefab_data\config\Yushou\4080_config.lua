return {
	actorController = {
		projectiles = {},

		hurts = {},

	},
	actorTriggers = {
		effects = {
			{
				triggerEventName = "attack1/begin",
				triggerDelay = 0.0,
				triggerFreeDelay = 0.0,
				effectGoName = "yushou4080_attack",
				effectAsset = {
					BundleName = "effects/prefab/model/yushou/4080/yushou4080_attack_prefab",
					AssetName = "yushou4080_attack",
				},
				playerAtTarget = false,
				referenceNodeHierarchyPath = "buff_down",
				isAttach = true,
				isRotation = false,
				isUseCustomTransform = false,
				offsetPosX = 0.0,
				offsetPosY = 0.0,
				offsetPosZ = 0.0,
				triggerStopEvent = "",
				effectBtnName = "attack1",
				playerAtPos = false,
				ignoreParentScale = false,
			},
			{
				triggerEventName = "attack2/begin",
				triggerDelay = 0.0,
				triggerFreeDelay = 0.0,
				effectGoName = "yushou4080_skill",
				effectAsset = {
					BundleName = "effects/prefab/model/yushou/4080/yushou4080_skill_prefab",
					AssetName = "yushou4080_skill",
				},
				playerAtTarget = false,
				referenceNodeHierarchyPath = "buff_down",
				isAttach = true,
				isRotation = false,
				isUseCustomTransform = true,
				offsetPosX = 0.0,
				offsetPosY = 3.0,
				offsetPosZ = 0.0,
				triggerStopEvent = "",
				effectBtnName = "attack2",
				playerAtPos = false,
				ignoreParentScale = false,
			},
			{
				triggerEventName = "rest/begin",
				triggerDelay = 2.3,
				triggerFreeDelay = 0.0,
				effectGoName = "yushou4080_rest",
				effectAsset = {
					BundleName = "effects/prefab/model/yushou/4080/yushou4080_rest_prefab",
					AssetName = "yushou4080_rest",
				},
				playerAtTarget = false,
				referenceNodeHierarchyPath = "buff_down",
				isAttach = true,
				isRotation = false,
				isUseCustomTransform = true,
				offsetPosX = 0.0,
				offsetPosY = -0.9,
				offsetPosZ = 0.0,
				triggerStopEvent = "",
				effectBtnName = "rest",
				playerAtPos = false,
				ignoreParentScale = false,
			},
		},
		sounds = {
			{
				soundEventName = "attack2/begin",
				soundDelay = 0.5,
				soundAudioAsset = {
					BundleName = "audios/sfxs/skill/4080",
					AssetName = "4080_skill",
				},
				soundAudioGoName = "4080_skill",
				soundBtnName = "skill",
				soundIsMainRole = false,
			},
		},
		cameraShakes = {},

		radialBlurs = {},

	},
}