return {
	actorController = {
		projectiles = {},

		hurts = {},

	},
	actorTriggers = {
		effects = {
			{
				triggerEventName = "attack1/begin",
				triggerDelay = 0.0,
				triggerFreeDelay = 0.0,
				effectGoName = "4050_attack",
				effectAsset = {
					BundleName = "effects/prefab/model/yushou/4050/4050_attack_prefab",
					AssetName = "4050_attack",
				},
				playerAtTarget = false,
				referenceNodeHierarchyPath = "root",
				isAttach = true,
				isRotation = false,
				isUseCustomTransform = false,
				offsetPosX = 0.0,
				offsetPosY = 0.0,
				offsetPosZ = 0.0,
				triggerStopEvent = "",
				effectBtnName = "attack1",
				playerAtPos = false,
				ignoreParentScale = true,
			},
			{
				triggerEventName = "attack2/begin",
				triggerDelay = 0.0,
				triggerFreeDelay = 0.0,
				effectGoName = "4050_skill",
				effectAsset = {
					BundleName = "effects/prefab/model/yushou/4050/4050_skill_prefab",
					AssetName = "4050_skill",
				},
				playerAtTarget = false,
				referenceNodeHierarchyPath = "root",
				isAttach = true,
				isRotation = false,
				isUseCustomTransform = false,
				offsetPosX = 0.0,
				offsetPosY = 0.0,
				offsetPosZ = 0.0,
				triggerStopEvent = "",
				effectBtnName = "attack2",
				playerAtPos = false,
				ignoreParentScale = true,
			},
			{
				triggerEventName = "rest/begin",
				triggerDelay = 0.8,
				triggerFreeDelay = 0.0,
				effectGoName = "4050_rest",
				effectAsset = {
					BundleName = "effects/prefab/model/yushou/4050/4050_rest_prefab",
					AssetName = "4050_rest",
				},
				playerAtTarget = false,
				referenceNodeHierarchyPath = "root",
				isAttach = true,
				isRotation = false,
				isUseCustomTransform = true,
				offsetPosX = 0.0,
				offsetPosY = 0.2,
				offsetPosZ = 0.0,
				triggerStopEvent = "",
				effectBtnName = "rest",
				playerAtPos = false,
				ignoreParentScale = false,
			},
		},
		sounds = {
			{
				soundEventName = "attack2/begin",
				soundDelay = 0.0,
				soundAudioAsset = {
					BundleName = "audios/sfxs/skill/4050",
					AssetName = "4050_skill",
				},
				soundAudioGoName = "4050_skill",
				soundBtnName = "skill",
				soundIsMainRole = false,
			},
		},
		cameraShakes = {},

		radialBlurs = {},

	},
}