{"actorController": {"projectiles": [], "hurts": []}, "actorTriggers": {"effects": [{"triggerEventName": "attack1/begin", "triggerDelay": 0.0, "triggerFreeDelay": 0.0, "effectGoName": "", "effectAsset": {"BundleName": "", "AssetName": "", "AssetGUID": "", "IsEmpty": true}, "playerAtTarget": false, "referenceNodeHierarchyPath": "", "isAttach": false, "isRotation": true, "isUseCustomTransform": false, "offsetPosX": 0.0, "offsetPosY": 0.0, "offsetPosZ": 0.0, "triggerStopEvent": "[none]", "effectBtnName": "attack1", "playerAtPos": false, "ignoreParentScale": false, "effectArray": [{"effectGoName": "3103_attack1", "effectAsset": {"BundleName": "effects/prefab/role/3103/3103_attack1_prefab", "AssetName": "3103_attack1", "AssetGUID": null, "IsEmpty": false}}, {"effectGoName": "3103_attack1_zi", "effectAsset": {"BundleName": "effects/prefab/role/3103/3103_attack1_zi_prefab", "AssetName": "3103_attack1_zi", "AssetGUID": "c0fef64b8dec7be4f9b869d9f0699fc4", "IsEmpty": false}}]}, {"triggerEventName": "attack2/begin", "triggerDelay": 0.0, "triggerFreeDelay": 0.0, "effectGoName": "", "effectAsset": {"BundleName": "", "AssetName": "", "AssetGUID": "", "IsEmpty": true}, "playerAtTarget": false, "referenceNodeHierarchyPath": "", "isAttach": false, "isRotation": true, "isUseCustomTransform": false, "offsetPosX": 0.0, "offsetPosY": 0.0, "offsetPosZ": 0.0, "triggerStopEvent": "[none]", "effectBtnName": "attack2", "playerAtPos": false, "ignoreParentScale": false, "effectArray": [{"effectGoName": "3103_attack2", "effectAsset": {"BundleName": "effects/prefab/role/3103/3103_attack2_prefab", "AssetName": "3103_attack2", "AssetGUID": null, "IsEmpty": false}}, {"effectGoName": "3103_attack2_zi", "effectAsset": {"BundleName": "effects/prefab/role/3103/3103_attack2_zi_prefab", "AssetName": "3103_attack2_zi", "AssetGUID": "5936b92d23fd6d94297d08c83f44a4a5", "IsEmpty": false}}]}, {"triggerEventName": "attack3/begin", "triggerDelay": 0.0, "triggerFreeDelay": 0.0, "effectGoName": "", "effectAsset": {"BundleName": "", "AssetName": "", "AssetGUID": "", "IsEmpty": true}, "playerAtTarget": false, "referenceNodeHierarchyPath": "", "isAttach": false, "isRotation": true, "isUseCustomTransform": false, "offsetPosX": 0.0, "offsetPosY": 0.0, "offsetPosZ": 0.0, "triggerStopEvent": "[none]", "effectBtnName": "attack3", "playerAtPos": false, "ignoreParentScale": false, "effectArray": [{"effectGoName": "3103_attack3", "effectAsset": {"BundleName": "effects/prefab/role/3103/3103_attack3_prefab", "AssetName": "3103_attack3", "AssetGUID": null, "IsEmpty": false}}, {"effectGoName": "3103_attack3_zi", "effectAsset": {"BundleName": "effects/prefab/role/3103/3103_attack3_zi_prefab", "AssetName": "3103_attack3_zi", "AssetGUID": "e1163f1cbd643ad41a4769288d330aad", "IsEmpty": false}}]}, {"triggerEventName": "attack4/begin", "triggerDelay": 0.0, "triggerFreeDelay": 0.0, "effectGoName": "", "effectAsset": {"BundleName": "", "AssetName": "", "AssetGUID": "", "IsEmpty": true}, "playerAtTarget": false, "referenceNodeHierarchyPath": "", "isAttach": false, "isRotation": true, "isUseCustomTransform": false, "offsetPosX": 0.0, "offsetPosY": 0.0, "offsetPosZ": 0.0, "triggerStopEvent": "[none]", "effectBtnName": "attack4", "playerAtPos": false, "ignoreParentScale": false, "effectArray": [{"effectGoName": "3103_attack4", "effectAsset": {"BundleName": "effects/prefab/role/3103/3103_attack4_prefab", "AssetName": "3103_attack4", "AssetGUID": null, "IsEmpty": false}}, {"effectGoName": "3103_attack4_zi", "effectAsset": {"BundleName": "effects/prefab/role/3103/3103_attack4_zi_prefab", "AssetName": "3103_attack4_zi", "AssetGUID": "88ae19402505ee746bd503db25a80de0", "IsEmpty": false}}]}, {"triggerEventName": "combo1_1/begin", "triggerDelay": 0.0, "triggerFreeDelay": 0.0, "effectGoName": "", "effectAsset": {"BundleName": "", "AssetName": "", "AssetGUID": "", "IsEmpty": true}, "playerAtTarget": false, "referenceNodeHierarchyPath": "", "isAttach": false, "isRotation": true, "isUseCustomTransform": false, "offsetPosX": 0.0, "offsetPosY": 0.0, "offsetPosZ": 0.0, "triggerStopEvent": "[none]", "effectBtnName": "combo1_1", "playerAtPos": false, "ignoreParentScale": false, "effectArray": [{"effectGoName": "3103_combo1", "effectAsset": {"BundleName": "effects/prefab/role/3103/3103_combo1_prefab", "AssetName": "3103_combo1", "AssetGUID": null, "IsEmpty": false}}, {"effectGoName": "3103_combo1_zi", "effectAsset": {"BundleName": "effects/prefab/role/3103/3103_combo1_zi_prefab", "AssetName": "3103_combo1_zi", "AssetGUID": "cdfefaaec89396a41870297e6509d8eb", "IsEmpty": false}}]}, {"triggerEventName": "combo1_2/begin", "triggerDelay": 0.0, "triggerFreeDelay": 0.0, "effectGoName": "", "effectAsset": {"BundleName": "", "AssetName": "", "AssetGUID": "", "IsEmpty": true}, "playerAtTarget": false, "referenceNodeHierarchyPath": "", "isAttach": false, "isRotation": true, "isUseCustomTransform": false, "offsetPosX": 0.0, "offsetPosY": 0.0, "offsetPosZ": 0.0, "triggerStopEvent": "[none]", "effectBtnName": "combo1_2", "playerAtPos": false, "ignoreParentScale": false, "effectArray": [{"effectGoName": "3103_combo2", "effectAsset": {"BundleName": "effects/prefab/role/3103/3103_combo2_prefab", "AssetName": "3103_combo2", "AssetGUID": null, "IsEmpty": false}}, {"effectGoName": "3103_combo2_zi", "effectAsset": {"BundleName": "effects/prefab/role/3103/3103_combo2_zi_prefab", "AssetName": "3103_combo2_zi", "AssetGUID": "0e67a835e69071649996a9a880df410f", "IsEmpty": false}}]}, {"triggerEventName": "combo1_3/begin", "triggerDelay": 0.0, "triggerFreeDelay": 0.0, "effectGoName": "", "effectAsset": {"BundleName": "", "AssetName": "", "AssetGUID": "", "IsEmpty": true}, "playerAtTarget": false, "referenceNodeHierarchyPath": "", "isAttach": false, "isRotation": true, "isUseCustomTransform": false, "offsetPosX": 0.0, "offsetPosY": 0.0, "offsetPosZ": 0.0, "triggerStopEvent": "[none]", "effectBtnName": "combo1_3", "playerAtPos": false, "ignoreParentScale": false, "effectArray": [{"effectGoName": "3103_combo3", "effectAsset": {"BundleName": "effects/prefab/role/3103/3103_combo3_prefab", "AssetName": "3103_combo3", "AssetGUID": null, "IsEmpty": false}}, {"effectGoName": "3103_combo3_zi", "effectAsset": {"BundleName": "effects/prefab/role/3103/3103_combo3_zi_prefab", "AssetName": "3103_combo3_zi", "AssetGUID": "036905027aa87a74bb526882d149971d", "IsEmpty": false}}]}, {"triggerEventName": "combo1_4/begin", "triggerDelay": 0.0, "triggerFreeDelay": 0.0, "effectGoName": "", "effectAsset": {"BundleName": "", "AssetName": "", "AssetGUID": "", "IsEmpty": true}, "playerAtTarget": false, "referenceNodeHierarchyPath": "", "isAttach": false, "isRotation": true, "isUseCustomTransform": false, "offsetPosX": 0.0, "offsetPosY": 0.0, "offsetPosZ": 0.0, "triggerStopEvent": "[none]", "effectBtnName": "combo1_4", "playerAtPos": false, "ignoreParentScale": false, "effectArray": [{"effectGoName": "3103_combo4", "effectAsset": {"BundleName": "effects/prefab/role/3103/3103_combo4_prefab", "AssetName": "3103_combo4", "AssetGUID": null, "IsEmpty": false}}, {"effectGoName": "3103_combo4_zi", "effectAsset": {"BundleName": "effects/prefab/role/3103/3103_combo4_zi_prefab", "AssetName": "3103_combo4_zi", "AssetGUID": "c968bd81ae1666c4d8d975fbf2b6f622", "IsEmpty": false}}]}, {"triggerEventName": "esoterica_attack_1/begin", "triggerDelay": 0.0, "triggerFreeDelay": 0.0, "effectGoName": "", "effectAsset": {"BundleName": "", "AssetName": "", "AssetGUID": "", "IsEmpty": true}, "playerAtTarget": false, "referenceNodeHierarchyPath": "", "isAttach": false, "isRotation": true, "isUseCustomTransform": false, "offsetPosX": 0.0, "offsetPosY": 0.0, "offsetPosZ": 0.0, "triggerStopEvent": "", "effectBtnName": "esoterica_attack_1", "playerAtPos": false, "ignoreParentScale": false, "effectArray": [{"effectGoName": "miji_attack01", "effectAsset": {"BundleName": "effects/prefab/model/miji/miji_attack01_prefab", "AssetName": "miji_attack01", "AssetGUID": null, "IsEmpty": false}}, {"effectGoName": "", "effectAsset": {"BundleName": "", "AssetName": "", "AssetGUID": "", "IsEmpty": true}}]}, {"triggerEventName": "esoterica_attack_2/begin", "triggerDelay": 0.0, "triggerFreeDelay": 0.0, "effectGoName": "", "effectAsset": {"BundleName": "", "AssetName": "", "AssetGUID": "", "IsEmpty": true}, "playerAtTarget": false, "referenceNodeHierarchyPath": "", "isAttach": false, "isRotation": true, "isUseCustomTransform": false, "offsetPosX": 0.0, "offsetPosY": 0.0, "offsetPosZ": 0.0, "triggerStopEvent": "[none]", "effectBtnName": "esoterica_attack_2", "playerAtPos": false, "ignoreParentScale": false, "effectArray": [{"effectGoName": "xianfa_attack9", "effectAsset": {"BundleName": "effects/prefab/model/xianfa/xianfa_attack9_prefab", "AssetName": "xianfa_attack9", "AssetGUID": "ed21f6540190afa42a0dc2557588f18a", "IsEmpty": false}}, {"effectGoName": "", "effectAsset": {"BundleName": "", "AssetName": "", "AssetGUID": "", "IsEmpty": true}}]}, {"triggerEventName": "esoterica_attack_3/begin", "triggerDelay": 0.0, "triggerFreeDelay": 0.0, "effectGoName": "", "effectAsset": {"BundleName": "", "AssetName": "", "AssetGUID": "", "IsEmpty": true}, "playerAtTarget": false, "referenceNodeHierarchyPath": "", "isAttach": false, "isRotation": true, "isUseCustomTransform": false, "offsetPosX": 0.0, "offsetPosY": 0.0, "offsetPosZ": 0.0, "triggerStopEvent": "[none]", "effectBtnName": "esoterica_attack_3", "playerAtPos": false, "ignoreParentScale": false, "effectArray": [{"effectGoName": "xianfa_attack3", "effectAsset": {"BundleName": "effects/prefab/model/xianfa/xianfa_attack3_prefab", "AssetName": "xianfa_attack3", "AssetGUID": "52a62e7527880c540a72b41e5898ce87", "IsEmpty": false}}, {"effectGoName": "", "effectAsset": {"BundleName": "", "AssetName": "", "AssetGUID": "", "IsEmpty": true}}]}, {"triggerEventName": "esoterica_attack_4/begin", "triggerDelay": 0.0, "triggerFreeDelay": 0.0, "effectGoName": "", "effectAsset": {"BundleName": "", "AssetName": "", "AssetGUID": "", "IsEmpty": true}, "playerAtTarget": false, "referenceNodeHierarchyPath": "", "isAttach": false, "isRotation": true, "isUseCustomTransform": false, "offsetPosX": 0.0, "offsetPosY": 0.0, "offsetPosZ": 0.0, "triggerStopEvent": "[none]", "effectBtnName": "esoterica_attack_4", "playerAtPos": false, "ignoreParentScale": false, "effectArray": [{"effectGoName": "xianfa_attack1", "effectAsset": {"BundleName": "effects/prefab/model/xianfa/xianfa_attack1_prefab", "AssetName": "xianfa_attack1", "AssetGUID": "23194b7835085324487da9a6e339a81d", "IsEmpty": false}}, {"effectGoName": "", "effectAsset": {"BundleName": "", "AssetName": "", "AssetGUID": "", "IsEmpty": true}}]}, {"triggerEventName": "esoterica_attack_5/begin", "triggerDelay": 0.0, "triggerFreeDelay": 0.0, "effectGoName": "", "effectAsset": {"BundleName": "", "AssetName": "", "AssetGUID": "", "IsEmpty": true}, "playerAtTarget": false, "referenceNodeHierarchyPath": "", "isAttach": false, "isRotation": true, "isUseCustomTransform": false, "offsetPosX": 0.0, "offsetPosY": 0.0, "offsetPosZ": 0.0, "triggerStopEvent": "[none]", "effectBtnName": "esoterica_attack_5", "playerAtPos": false, "ignoreParentScale": false, "effectArray": [{"effectGoName": "xianfa_attack6", "effectAsset": {"BundleName": "effects/prefab/model/xianfa/xianfa_attack6_prefab", "AssetName": "xianfa_attack6", "AssetGUID": "34314d3151a39c14eae2d8e0f5b285b8", "IsEmpty": false}}, {"effectGoName": "", "effectAsset": {"BundleName": "", "AssetName": "", "AssetGUID": "", "IsEmpty": true}}]}, {"triggerEventName": "esoterica_attack_6/begin", "triggerDelay": 0.0, "triggerFreeDelay": 0.0, "effectGoName": "", "effectAsset": {"BundleName": "", "AssetName": "", "AssetGUID": "", "IsEmpty": true}, "playerAtTarget": false, "referenceNodeHierarchyPath": "", "isAttach": false, "isRotation": true, "isUseCustomTransform": false, "offsetPosX": 0.0, "offsetPosY": 0.0, "offsetPosZ": 0.0, "triggerStopEvent": "[none]", "effectBtnName": "esoterica_attack_6", "playerAtPos": false, "ignoreParentScale": false, "effectArray": [{"effectGoName": "xianfa_attack4", "effectAsset": {"BundleName": "effects/prefab/model/xianfa/xianfa_attack4_prefab", "AssetName": "xianfa_attack4", "AssetGUID": "7da6118c654021d48be6517dcf57c258", "IsEmpty": false}}, {"effectGoName": "", "effectAsset": {"BundleName": "", "AssetName": "", "AssetGUID": "", "IsEmpty": true}}]}, {"triggerEventName": "esoterica_attack_7/begin", "triggerDelay": 0.0, "triggerFreeDelay": 0.0, "effectGoName": "", "effectAsset": {"BundleName": "", "AssetName": "", "AssetGUID": "", "IsEmpty": true}, "playerAtTarget": false, "referenceNodeHierarchyPath": "", "isAttach": false, "isRotation": true, "isUseCustomTransform": false, "offsetPosX": 0.0, "offsetPosY": 0.0, "offsetPosZ": 0.0, "triggerStopEvent": "[none]", "effectBtnName": "esoterica_attack_7", "playerAtPos": false, "ignoreParentScale": false, "effectArray": [{"effectGoName": "xianfa_attack8", "effectAsset": {"BundleName": "effects/prefab/model/xianfa/xianfa_attack8_prefab", "AssetName": "xianfa_attack8", "AssetGUID": "0dbff1b2487193c4c8a84818c05d6752", "IsEmpty": false}}, {"effectGoName": "", "effectAsset": {"BundleName": "", "AssetName": "", "AssetGUID": "", "IsEmpty": true}}]}, {"triggerEventName": "attack5/begin", "triggerDelay": 0.0, "triggerFreeDelay": 0.0, "effectGoName": "", "effectAsset": {"BundleName": "", "AssetName": "", "AssetGUID": "", "IsEmpty": true}, "playerAtTarget": false, "referenceNodeHierarchyPath": "", "isAttach": false, "isRotation": true, "isUseCustomTransform": false, "offsetPosX": 0.0, "offsetPosY": 0.0, "offsetPosZ": 0.0, "triggerStopEvent": "", "effectBtnName": "attack5", "playerAtPos": false, "ignoreParentScale": false, "effectArray": [{"effectGoName": "dingzhi_attack01", "effectAsset": {"BundleName": "effects/prefab/model/dingzhi/dingzhi_attack01_prefab", "AssetName": "dingzhi_attack01", "AssetGUID": null, "IsEmpty": false}}, {"effectGoName": "", "effectAsset": {"BundleName": "", "AssetName": "", "AssetGUID": "", "IsEmpty": true}}]}, {"triggerEventName": "attack6/begin", "triggerDelay": 0.0, "triggerFreeDelay": 0.0, "effectGoName": "", "effectAsset": {"BundleName": "", "AssetName": "", "AssetGUID": "", "IsEmpty": true}, "playerAtTarget": false, "referenceNodeHierarchyPath": "", "isAttach": false, "isRotation": true, "isUseCustomTransform": false, "offsetPosX": 0.0, "offsetPosY": 0.0, "offsetPosZ": 0.0, "triggerStopEvent": "", "effectBtnName": "attack6", "playerAtPos": false, "ignoreParentScale": false, "effectArray": [{"effectGoName": "dingzhi_attack02", "effectAsset": {"BundleName": "effects/prefab/model/dingzhi/dingzhi_attack02_prefab", "AssetName": "dingzhi_attack02", "AssetGUID": null, "IsEmpty": false}}, {"effectGoName": "", "effectAsset": {"BundleName": "", "AssetName": "", "AssetGUID": "", "IsEmpty": true}}]}, {"triggerEventName": "attack7/begin", "triggerDelay": 0.0, "triggerFreeDelay": 0.0, "effectGoName": "", "effectAsset": {"BundleName": "", "AssetName": "", "AssetGUID": "", "IsEmpty": true}, "playerAtTarget": false, "referenceNodeHierarchyPath": "", "isAttach": false, "isRotation": true, "isUseCustomTransform": false, "offsetPosX": 0.0, "offsetPosY": 0.0, "offsetPosZ": 0.0, "triggerStopEvent": "", "effectBtnName": "attack7", "playerAtPos": false, "ignoreParentScale": false, "effectArray": [{"effectGoName": "dingzhi_attack05", "effectAsset": {"BundleName": "effects/prefab/model/dingzhi/dingzhi_attack05_prefab", "AssetName": "dingzhi_attack05", "AssetGUID": null, "IsEmpty": false}}, {"effectGoName": "", "effectAsset": {"BundleName": "", "AssetName": "", "AssetGUID": "", "IsEmpty": true}}]}, {"triggerEventName": "fall_rest/begin", "triggerDelay": 0.0, "triggerFreeDelay": 0.0, "effectGoName": "", "effectAsset": {"BundleName": "", "AssetName": "", "AssetGUID": "", "IsEmpty": true}, "playerAtTarget": false, "referenceNodeHierarchyPath": "root", "isAttach": true, "isRotation": false, "isUseCustomTransform": false, "offsetPosX": 0.0, "offsetPosY": 0.0, "offsetPosZ": 0.0, "triggerStopEvent": "", "effectBtnName": "fall_rest", "playerAtPos": false, "ignoreParentScale": false, "effectArray": [{"effectGoName": "eff_chuchang_shandian", "effectAsset": {"BundleName": "effects/prefab/environment/common/eff_chuchang_shandian_prefab", "AssetName": "eff_chuchang_shandian", "AssetGUID": null, "IsEmpty": false}}, {"effectGoName": "", "effectAsset": {"BundleName": "", "AssetName": "", "AssetGUID": "", "IsEmpty": true}}]}, {"triggerEventName": "prizedraw/begin", "triggerDelay": 0.0, "triggerFreeDelay": 0.0, "effectGoName": "", "effectAsset": {"BundleName": "", "AssetName": "", "AssetGUID": "", "IsEmpty": true}, "playerAtTarget": false, "referenceNodeHierarchyPath": "root", "isAttach": true, "isRotation": false, "isUseCustomTransform": false, "offsetPosX": 0.0, "offsetPosY": 0.0, "offsetPosZ": 0.0, "triggerStopEvent": "[none]", "effectBtnName": "choujiang", "playerAtPos": false, "ignoreParentScale": true, "effectArray": [{"effectGoName": "choujiang_eff", "effectAsset": {"BundleName": "effects/prefab/environment/common/choujiang_eff_prefab", "AssetName": "choujiang_eff", "AssetGUID": "07e5ba189c5069e4e9558cb1d0bf3ef3", "IsEmpty": false}}, {"effectGoName": "", "effectAsset": {"BundleName": "", "AssetName": "", "AssetGUID": "", "IsEmpty": true}}]}, {"triggerEventName": "esoterica_attack_8/begin", "triggerDelay": 0.0, "triggerFreeDelay": 0.0, "effectGoName": "", "effectAsset": {"BundleName": "", "AssetName": "", "AssetGUID": "", "IsEmpty": true}, "playerAtTarget": false, "referenceNodeHierarchyPath": "", "isAttach": false, "isRotation": false, "isUseCustomTransform": true, "offsetPosX": 1.0, "offsetPosY": 0.0, "offsetPosZ": -6.0, "triggerStopEvent": "[none]", "effectBtnName": "esoterica_attack_8", "playerAtPos": false, "ignoreParentScale": false, "effectArray": [{"effectGoName": "miji_attack08", "effectAsset": {"BundleName": "effects/prefab/model/miji/miji_attack08_prefab", "AssetName": "miji_attack08", "AssetGUID": "afb7616833ab6f643bb4344338980bd7", "IsEmpty": false}}, {"effectGoName": "", "effectAsset": {"BundleName": "", "AssetName": "", "AssetGUID": "", "IsEmpty": true}}]}, {"triggerEventName": "esoterica_attack_9/begin", "triggerDelay": 0.0, "triggerFreeDelay": 0.0, "effectGoName": "", "effectAsset": {"BundleName": "", "AssetName": "", "AssetGUID": "", "IsEmpty": true}, "playerAtTarget": false, "referenceNodeHierarchyPath": "", "isAttach": false, "isRotation": true, "isUseCustomTransform": false, "offsetPosX": 0.0, "offsetPosY": 0.0, "offsetPosZ": 0.0, "triggerStopEvent": "[none]", "effectBtnName": "esoterica_attack_9", "playerAtPos": false, "ignoreParentScale": false, "effectArray": [{"effectGoName": "xianfa_attack10", "effectAsset": {"BundleName": "effects/prefab/model/xianfa/xianfa_attack10_prefab", "AssetName": "xianfa_attack10", "AssetGUID": "1f207c1e703af0c4cb4667d65040cfbd", "IsEmpty": false}}, {"effectGoName": "", "effectAsset": {"BundleName": "", "AssetName": "", "AssetGUID": "", "IsEmpty": true}}]}, {"triggerEventName": "esoterica_attack_10/begin", "triggerDelay": 0.0, "triggerFreeDelay": 0.0, "effectGoName": "", "effectAsset": {"BundleName": "", "AssetName": "", "AssetGUID": "", "IsEmpty": true}, "playerAtTarget": false, "referenceNodeHierarchyPath": "", "isAttach": false, "isRotation": true, "isUseCustomTransform": false, "offsetPosX": 0.0, "offsetPosY": 0.0, "offsetPosZ": 0.0, "triggerStopEvent": "[none]", "effectBtnName": "esoterica_attack_10", "playerAtPos": false, "ignoreParentScale": false, "effectArray": [{"effectGoName": "xianfa_attack2", "effectAsset": {"BundleName": "effects/prefab/model/xianfa/xianfa_attack2_prefab", "AssetName": "xianfa_attack2", "AssetGUID": "db15a3bdaf4531040a53689e2aca1d1a", "IsEmpty": false}}, {"effectGoName": "", "effectAsset": {"BundleName": "", "AssetName": "", "AssetGUID": "", "IsEmpty": true}}]}], "sounds": [{"soundEventName": "combo1_1/begin", "soundDelay": 0.0, "soundAudioAsset": {"BundleName": "audios/sfxs/roleskill/role3", "AssetName": "role3_attack1", "AssetGUID": null, "IsEmpty": false}, "soundAudioGoName": "role3_attack1", "soundBtnName": "combo1", "soundIsMainRole": false}, {"soundEventName": "combo1_2/begin", "soundDelay": 0.0, "soundAudioAsset": {"BundleName": "audios/sfxs/roleskill/role3", "AssetName": "role3_attack2", "AssetGUID": null, "IsEmpty": false}, "soundAudioGoName": "role3_attack2", "soundBtnName": "combo2", "soundIsMainRole": false}, {"soundEventName": "combo1_3/begin", "soundDelay": 0.0, "soundAudioAsset": {"BundleName": "audios/sfxs/roleskill/role3", "AssetName": "role3_attack3", "AssetGUID": null, "IsEmpty": false}, "soundAudioGoName": "role3_attack3", "soundBtnName": "combo3", "soundIsMainRole": false}, {"soundEventName": "combo1_4/begin", "soundDelay": 0.0, "soundAudioAsset": {"BundleName": "audios/sfxs/roleskill/role3", "AssetName": "role3_attack4", "AssetGUID": null, "IsEmpty": false}, "soundAudioGoName": "role3_attack4", "soundBtnName": "combo4", "soundIsMainRole": false}, {"soundEventName": "attack1/begin", "soundDelay": 0.0, "soundAudioAsset": {"BundleName": "audios/sfxs/roleskill/role3", "AssetName": "role3_skill1", "AssetGUID": null, "IsEmpty": false}, "soundAudioGoName": "role3_skill1", "soundBtnName": "attack1", "soundIsMainRole": false}, {"soundEventName": "attack2/begin", "soundDelay": 0.0, "soundAudioAsset": {"BundleName": "audios/sfxs/roleskill/role3", "AssetName": "role3_skill2", "AssetGUID": null, "IsEmpty": false}, "soundAudioGoName": "role3_skill2", "soundBtnName": "attack2", "soundIsMainRole": false}, {"soundEventName": "attack3/begin", "soundDelay": 0.0, "soundAudioAsset": {"BundleName": "audios/sfxs/roleskill/role3", "AssetName": "role3_skill3", "AssetGUID": null, "IsEmpty": false}, "soundAudioGoName": "role3_skill3", "soundBtnName": "attack3", "soundIsMainRole": false}, {"soundEventName": "attack4/begin", "soundDelay": 0.0, "soundAudioAsset": {"BundleName": "audios/sfxs/roleskill/role3", "AssetName": "role3_skill4", "AssetGUID": null, "IsEmpty": false}, "soundAudioGoName": "role3_skill4", "soundBtnName": "attack4", "soundIsMainRole": false}, {"soundEventName": "attack5/begin", "soundDelay": 0.0, "soundAudioAsset": {"BundleName": "audios/sfxs/roleskill/jineng", "AssetName": "role1_attack5", "AssetGUID": null, "IsEmpty": false}, "soundAudioGoName": "role1_attack5", "soundBtnName": "attack5", "soundIsMainRole": false}, {"soundEventName": "attack6/begin", "soundDelay": 0.0, "soundAudioAsset": {"BundleName": "audios/sfxs/roleskill/jineng", "AssetName": "role1_attack6", "AssetGUID": null, "IsEmpty": false}, "soundAudioGoName": "role1_attack6", "soundBtnName": "attack6", "soundIsMainRole": false}, {"soundEventName": "attack9/begin", "soundDelay": 0.0, "soundAudioAsset": {"BundleName": "audios/sfxs/roleskill/jineng", "AssetName": "role1_attack7", "AssetGUID": null, "IsEmpty": false}, "soundAudioGoName": "role1_attack7", "soundBtnName": "attack9", "soundIsMainRole": false}, {"soundEventName": "attack10/begin", "soundDelay": 0.0, "soundAudioAsset": {"BundleName": "audios/sfxs/roleskill/jineng", "AssetName": "role1_attack8", "AssetGUID": null, "IsEmpty": false}, "soundAudioGoName": "role1_attack8", "soundBtnName": "attack10", "soundIsMainRole": false}, {"soundEventName": "attack11/begin", "soundDelay": 0.0, "soundAudioAsset": {"BundleName": "audios/sfxs/roleskill/jineng", "AssetName": "role1_attack9", "AssetGUID": null, "IsEmpty": false}, "soundAudioGoName": "role1_attack9", "soundBtnName": "attack11", "soundIsMainRole": false}, {"soundEventName": "attack12/begin", "soundDelay": 0.0, "soundAudioAsset": {"BundleName": "audios/sfxs/roleskill/jineng", "AssetName": "role1_attack10", "AssetGUID": null, "IsEmpty": false}, "soundAudioGoName": "role1_attack10", "soundBtnName": "attack12", "soundIsMainRole": false}, {"soundEventName": "esoterica_attack_1/begin", "soundDelay": 0.0, "soundAudioAsset": {"BundleName": "audios/sfxs/roleskill/jineng", "AssetName": "miji1", "AssetGUID": null, "IsEmpty": false}, "soundAudioGoName": "miji1", "soundBtnName": "miji1", "soundIsMainRole": false}, {"soundEventName": "esoterica_attack_2/begin", "soundDelay": 0.0, "soundAudioAsset": {"BundleName": "audios/sfxs/roleskill/jineng", "AssetName": "miji2", "AssetGUID": null, "IsEmpty": false}, "soundAudioGoName": "miji2", "soundBtnName": "miji2", "soundIsMainRole": false}, {"soundEventName": "esoterica_attack_3/begin", "soundDelay": 0.0, "soundAudioAsset": {"BundleName": "audios/sfxs/roleskill/jineng", "AssetName": "miji3", "AssetGUID": null, "IsEmpty": false}, "soundAudioGoName": "miji3", "soundBtnName": "miji3", "soundIsMainRole": false}, {"soundEventName": "esoterica_attack_4/begin", "soundDelay": 0.0, "soundAudioAsset": {"BundleName": "audios/sfxs/roleskill/jineng", "AssetName": "miji4", "AssetGUID": null, "IsEmpty": false}, "soundAudioGoName": "miji4", "soundBtnName": "miji4", "soundIsMainRole": false}, {"soundEventName": "esoterica_attack_5/begin", "soundDelay": 0.0, "soundAudioAsset": {"BundleName": "audios/sfxs/roleskill/jineng", "AssetName": "miji5", "AssetGUID": null, "IsEmpty": false}, "soundAudioGoName": "miji5", "soundBtnName": "miji5", "soundIsMainRole": false}, {"soundEventName": "esoterica_attack_6/begin", "soundDelay": 0.0, "soundAudioAsset": {"BundleName": "audios/sfxs/roleskill/jineng", "AssetName": "miji6", "AssetGUID": null, "IsEmpty": false}, "soundAudioGoName": "miji6", "soundBtnName": "miji6", "soundIsMainRole": false}, {"soundEventName": "esoterica_attack_7/begin", "soundDelay": 0.0, "soundAudioAsset": {"BundleName": "audios/sfxs/roleskill/jineng", "AssetName": "miji7", "AssetGUID": null, "IsEmpty": false}, "soundAudioGoName": "miji7", "soundBtnName": "miji7", "soundIsMainRole": false}], "cameraShakes": [{"CameraShakeBtnName": "attack2", "eventName": "attack2/begin", "numberOfShakes": 1, "distance": 0.2, "speed": 100.0, "delay": 0.8, "decay": 0.0}, {"CameraShakeBtnName": "attack3_1", "eventName": "attack3/begin", "numberOfShakes": 1, "distance": 0.1, "speed": 150.0, "delay": 0.75, "decay": 0.0}, {"CameraShakeBtnName": "attack3_2", "eventName": "attack3/begin", "numberOfShakes": 3, "distance": 0.2, "speed": 60.0, "delay": 1.8, "decay": 0.0}, {"CameraShakeBtnName": "combo1_2", "eventName": "combo1_2/begin", "numberOfShakes": 1, "distance": 0.1, "speed": 100.0, "delay": 0.5, "decay": 0.0}, {"CameraShakeBtnName": "combo1_4", "eventName": "combo1_4/begin", "numberOfShakes": 3, "distance": 0.1, "speed": 150.0, "delay": 0.3, "decay": 0.0}, {"CameraShakeBtnName": "attack4_1", "eventName": "attack4/begin", "numberOfShakes": 5, "distance": 0.15, "speed": 50.0, "delay": 1.0, "decay": 0.0}, {"CameraShakeBtnName": "attack4_2", "eventName": "attack4/begin", "numberOfShakes": 4, "distance": 1.4, "speed": 150.0, "delay": 1.65, "decay": 0.0}, {"CameraShakeBtnName": "esoterica_attack_9_1", "eventName": "esoterica_attack_9/begin", "numberOfShakes": 1, "distance": 0.08, "speed": 100.0, "delay": 0.2, "decay": 0.0}, {"CameraShakeBtnName": "esoterica_attack_9_2", "eventName": "esoterica_attack_9/begin", "numberOfShakes": 1, "distance": 0.1, "speed": 100.0, "delay": 0.8, "decay": 0.0}, {"CameraShakeBtnName": "esoterica_attack_10", "eventName": "esoterica_attack_10/begin", "numberOfShakes": 3, "distance": 0.1, "speed": 120.0, "delay": 0.8, "decay": 0.0}], "radialBlurs": [{"btnName": "attack4", "eventName": "attack4/begin", "isRole": true, "referenceNodeHierarchyPath": "root/Bip001/Bip001 Pelvis/Bip001 Spine/Bip001 Spine1/hurt_middle", "delay": 1.65, "strength": 0.6, "riseTime": 0.1, "holdTime": 0.3, "fallTime": 0.1}]}}