-- H-合服活动-猎魔达人.xls
local item_table={
[1]={item_id=46041,num=1,is_bind=1},
[2]={item_id=50005,num=1,is_bind=1},
[3]={item_id=26344,num=5,is_bind=1},
[4]={item_id=26376,num=5,is_bind=1},
[5]={item_id=22615,num=1,is_bind=1},
[6]={item_id=46042,num=1,is_bind=1},
[7]={item_id=46048,num=1,is_bind=1},
[8]={item_id=26191,num=1,is_bind=1},
[9]={item_id=50006,num=1,is_bind=1},
[10]={item_id=26345,num=1,is_bind=1},
[11]={item_id=26377,num=1,is_bind=1},
[12]={item_id=26503,num=1,is_bind=1},
[13]={item_id=26519,num=1,is_bind=1},
[14]={item_id=50006,num=2,is_bind=1},
[15]={item_id=50005,num=2,is_bind=1},
[16]={item_id=46048,num=2,is_bind=1},
[17]={item_id=26518,num=1,is_bind=1},
[18]={item_id=26504,num=1,is_bind=1},
[19]={item_id=36366,num=1,is_bind=1},
[20]={item_id=26505,num=1,is_bind=1},
[21]={item_id=36366,num=2,is_bind=1},
[22]={item_id=27909,num=4,is_bind=1},
[23]={item_id=26601,num=8,is_bind=1},
[24]={item_id=26602,num=8,is_bind=1},
[25]={item_id=27909,num=3,is_bind=1},
[26]={item_id=26601,num=6,is_bind=1},
[27]={item_id=26602,num=6,is_bind=1},
[28]={item_id=27909,num=2,is_bind=1},
[29]={item_id=26601,num=4,is_bind=1},
[30]={item_id=26602,num=4,is_bind=1},
[31]={item_id=39144,num=1000,is_bind=1},
[32]={item_id=26347,num=1,is_bind=1},
[33]={item_id=26415,num=2,is_bind=1},
[34]={item_id=26346,num=5,is_bind=1},
[35]={item_id=39142,num=1000,is_bind=1},
[36]={item_id=27860,num=1,is_bind=1},
[37]={item_id=26355,num=1,is_bind=1},
[38]={item_id=48103,num=1,is_bind=1},
[39]={item_id=26410,num=1,is_bind=1},
[40]={item_id=26410,num=2,is_bind=1},
[41]={item_id=27861,num=1,is_bind=1},
[42]={item_id=26356,num=1,is_bind=1},
[43]={item_id=39144,num=2000,is_bind=1},
[44]={item_id=39142,num=2000,is_bind=1},
[45]={item_id=26357,num=1,is_bind=1},
[46]={item_id=36366,num=3,is_bind=1},
[47]={item_id=27909,num=5,is_bind=1},
[48]={item_id=26601,num=10,is_bind=1},
[49]={item_id=26602,num=10,is_bind=1},
}

return {
config_param={
{}
},

config_param_meta_table_map={
},
reward_cfg={
{reward_item={[0]=item_table[1],[1]=item_table[2],[2]=item_table[3],[3]=item_table[4]},},
{seq=1,score=25,reward_item={[0]=item_table[5],[1]=item_table[2],[2]=item_table[3],[3]=item_table[4]},},
{seq=2,score=50,reward_item={[0]=item_table[6],[1]=item_table[2],[2]=item_table[3],[3]=item_table[4]},},
{seq=3,score=75,},
{seq=4,score=100,reward_item={[0]=item_table[7],[1]=item_table[5],[2]=item_table[2],[3]=item_table[3],[4]=item_table[4]},},
{seq=5,score=125,reward_item={[0]=item_table[8],[1]=item_table[6],[2]=item_table[9],[3]=item_table[10],[4]=item_table[11]},},
{seq=6,score=150,},
{seq=7,score=175,},
{seq=8,score=200,reward_item={[0]=item_table[12],[1]=item_table[6],[2]=item_table[2],[3]=item_table[3],[4]=item_table[4]},},
{seq=9,score=225,},
{seq=10,score=250,reward_item={[0]=item_table[13],[1]=item_table[5],[2]=item_table[14],[3]=item_table[10],[4]=item_table[11]},},
{seq=11,score=275,reward_item={[0]=item_table[7],[1]=item_table[6],[2]=item_table[15],[3]=item_table[3],[4]=item_table[4]},},
{seq=12,score=300,reward_item={[0]=item_table[16],[1]=item_table[1],[2]=item_table[15],[3]=item_table[3],[4]=item_table[4]},},
{seq=13,score=325,reward_item={[0]=item_table[17],[1]=item_table[5],[2]=item_table[15],[3]=item_table[3],[4]=item_table[4]},},
{seq=14,score=350,reward_item={[0]=item_table[16],[1]=item_table[6],[2]=item_table[15],[3]=item_table[3],[4]=item_table[4]},},
{seq=15,score=375,reward_item={[0]=item_table[18],[1]=item_table[19],[2]=item_table[14],[3]=item_table[10],[4]=item_table[11]},},
{grade=1,},
{grade=1,},
{grade=1,},
{grade=1,},
{grade=1,},
{grade=1,},
{grade=1,},
{grade=1,},
{grade=1,},
{grade=1,},
{grade=1,},
{grade=1,},
{grade=1,},
{grade=1,},
{grade=1,},
{grade=1,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,}
},

reward_cfg_meta_table_map={
[49]=1,	-- depth:1
[17]=49,	-- depth:2
[33]=17,	-- depth:3
[52]=4,	-- depth:1
[36]=52,	-- depth:2
[55]=7,	-- depth:1
[26]=10,	-- depth:1
[58]=26,	-- depth:2
[23]=55,	-- depth:2
[42]=58,	-- depth:3
[39]=23,	-- depth:3
[20]=36,	-- depth:3
[8]=5,	-- depth:1
[50]=2,	-- depth:1
[47]=15,	-- depth:1
[48]=16,	-- depth:1
[45]=13,	-- depth:1
[46]=14,	-- depth:1
[59]=11,	-- depth:1
[53]=5,	-- depth:1
[54]=6,	-- depth:1
[56]=8,	-- depth:2
[57]=9,	-- depth:1
[60]=12,	-- depth:1
[61]=45,	-- depth:2
[62]=46,	-- depth:2
[51]=3,	-- depth:1
[44]=60,	-- depth:2
[32]=48,	-- depth:2
[41]=57,	-- depth:2
[18]=50,	-- depth:2
[19]=51,	-- depth:2
[21]=53,	-- depth:2
[22]=54,	-- depth:2
[24]=56,	-- depth:3
[25]=41,	-- depth:3
[27]=59,	-- depth:2
[28]=44,	-- depth:3
[29]=61,	-- depth:3
[30]=62,	-- depth:3
[31]=47,	-- depth:2
[63]=31,	-- depth:3
[34]=18,	-- depth:3
[35]=19,	-- depth:3
[37]=21,	-- depth:3
[38]=22,	-- depth:3
[40]=24,	-- depth:4
[43]=27,	-- depth:3
[64]=32,	-- depth:3
},
score_cfg={
{},
{boss_type=1,},
{boss_type=2,score=2,},
{boss_type=3,score=10,},
{boss_type=4,score=5,},
{boss_type=5,}
},

score_cfg_meta_table_map={
[6]=4,	-- depth:1
},
rank_cfg={
{},
{rank_hight=4,rank_low=10,reach_value=300,reward_item={[0]=item_table[20],[1]=item_table[21],[2]=item_table[22],[3]=item_table[23],[4]=item_table[24]},},
{rank_hight=11,rank_low=20,reach_value=250,reward_item={[0]=item_table[18],[1]=item_table[19],[2]=item_table[25],[3]=item_table[26],[4]=item_table[27]},},
{rank_hight=21,rank_low=100,reach_value=80,reward_item={[0]=item_table[12],[1]=item_table[5],[2]=item_table[28],[3]=item_table[29],[4]=item_table[30]},},
{grade=1,},
{grade=1,},
{grade=1,},
{grade=1,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,}
},

rank_cfg_meta_table_map={
[6]=2,	-- depth:1
[7]=3,	-- depth:1
[8]=4,	-- depth:1
[10]=6,	-- depth:2
[11]=7,	-- depth:2
[12]=8,	-- depth:2
[14]=10,	-- depth:3
[15]=11,	-- depth:3
[16]=12,	-- depth:3
},
guild_reward_cfg={
{},
{seq=1,score=75,reward_item={[0]=item_table[31],[1]=item_table[32],[2]=item_table[33],[3]=item_table[34]},},
{seq=2,score=150,},
{seq=3,score=225,reward_item={[0]=item_table[35],[1]=item_table[32],[2]=item_table[33],[3]=item_table[34]},},
{seq=4,score=300,reward_item={[0]=item_table[17],[1]=item_table[32],[2]=item_table[33],[3]=item_table[34]},},
{seq=5,score=375,reward_item={[0]=item_table[36],[1]=item_table[19],[2]=item_table[37],[3]=item_table[33],[4]=item_table[34]},},
{seq=6,score=450,reward_item={[0]=item_table[38],[1]=item_table[39],[2]=item_table[32],[3]=item_table[33],[4]=item_table[34]},},
{seq=7,score=525,reward_item={[0]=item_table[38],[1]=item_table[31],[2]=item_table[32],[3]=item_table[33],[4]=item_table[34]},},
{seq=8,score=600,reward_item={[0]=item_table[36],[1]=item_table[40],[2]=item_table[32],[3]=item_table[33],[4]=item_table[34]},},
{seq=9,score=675,reward_item={[0]=item_table[38],[1]=item_table[35],[2]=item_table[32],[3]=item_table[33],[4]=item_table[34]},},
{seq=10,score=750,reward_item={[0]=item_table[41],[1]=item_table[12],[2]=item_table[42],[3]=item_table[33],[4]=item_table[34]},},
{seq=11,score=825,reward_item={[0]=item_table[38],[1]=item_table[19],[2]=item_table[32],[3]=item_table[33],[4]=item_table[34]},},
{seq=12,score=900,reward_item={[0]=item_table[38],[1]=item_table[43],[2]=item_table[32],[3]=item_table[33],[4]=item_table[34]},},
{seq=13,score=975,reward_item={[0]=item_table[38],[1]=item_table[40],[2]=item_table[32],[3]=item_table[33],[4]=item_table[34]},},
{seq=14,score=1050,reward_item={[0]=item_table[38],[1]=item_table[44],[2]=item_table[32],[3]=item_table[33],[4]=item_table[34]},},
{seq=15,score=1125,reward_item={[0]=item_table[45],[1]=item_table[40],[2]=item_table[38],[3]=item_table[33],[4]=item_table[34]},},
{grade=1,},
{grade=1,},
{grade=1,},
{grade=1,},
{grade=1,},
{grade=1,},
{grade=1,},
{grade=1,},
{grade=1,},
{grade=1,},
{grade=1,},
{grade=1,},
{grade=1,},
{grade=1,},
{grade=1,},
{grade=1,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,}
},

guild_reward_cfg_meta_table_map={
[35]=3,	-- depth:1
[19]=35,	-- depth:2
[51]=19,	-- depth:3
[50]=2,	-- depth:1
[44]=12,	-- depth:1
[45]=13,	-- depth:1
[46]=14,	-- depth:1
[47]=15,	-- depth:1
[48]=16,	-- depth:1
[60]=44,	-- depth:2
[53]=5,	-- depth:1
[54]=6,	-- depth:1
[55]=7,	-- depth:1
[56]=8,	-- depth:1
[57]=9,	-- depth:1
[58]=10,	-- depth:1
[59]=11,	-- depth:1
[43]=59,	-- depth:2
[61]=45,	-- depth:2
[62]=46,	-- depth:2
[52]=4,	-- depth:1
[42]=58,	-- depth:2
[32]=48,	-- depth:2
[40]=56,	-- depth:2
[18]=50,	-- depth:2
[20]=52,	-- depth:2
[21]=53,	-- depth:2
[22]=54,	-- depth:2
[23]=55,	-- depth:2
[24]=40,	-- depth:3
[25]=57,	-- depth:2
[26]=42,	-- depth:3
[27]=43,	-- depth:3
[28]=60,	-- depth:3
[29]=61,	-- depth:3
[30]=62,	-- depth:3
[31]=47,	-- depth:2
[63]=31,	-- depth:3
[34]=18,	-- depth:3
[36]=20,	-- depth:3
[37]=21,	-- depth:3
[38]=22,	-- depth:3
[39]=23,	-- depth:3
[41]=25,	-- depth:3
[64]=32,	-- depth:3
},
other_cfg={
{}
},

other_cfg_meta_table_map={
},
config_param_default_table={start_server_day=1,end_server_day=999,grade=0,open_role_level=100,},

reward_cfg_default_table={grade=0,seq=0,score=12,reward_item={[0]=item_table[7],[1]=item_table[1],[2]=item_table[2],[3]=item_table[3],[4]=item_table[4]},},

score_cfg_default_table={boss_type=0,score=8,},

rank_cfg_default_table={grade=0,rank_hight=1,rank_low=3,reach_value=375,reward_item={[0]=item_table[20],[1]=item_table[46],[2]=item_table[47],[3]=item_table[48],[4]=item_table[49]},},

guild_reward_cfg_default_table={grade=0,seq=0,score=24,reward_item={[0]=item_table[39],[1]=item_table[32],[2]=item_table[33],[3]=item_table[34]},},

other_cfg_default_table={}

}

