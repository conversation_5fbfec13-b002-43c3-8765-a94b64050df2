return {
	actorController = {
		projectiles = {},

		hurts = {},

	},
	actorTriggers = {
		effects = {
			{
				triggerEventName = "attack1/begin",
				triggerDelay = 0.0,
				triggerFreeDelay = 0.0,
				effectGoName = "eff_4032yushou_attcak1",
				effectAsset = {
					BundleName = "effects/prefab/model/yushou/4032/eff_4032yushou_attcak1_prefab",
					AssetName = "eff_4032yushou_attcak1",
				},
				playerAtTarget = false,
				referenceNodeHierarchyPath = "root",
				isAttach = true,
				isRotation = false,
				isUseCustomTransform = false,
				offsetPosX = 0.0,
				offsetPosY = -3.0,
				offsetPosZ = 0.0,
				triggerStopEvent = "",
				effectBtnName = "attack1",
				playerAtPos = false,
				ignoreParentScale = true,
			},
			{
				triggerEventName = "rest/begin",
				triggerDelay = 1.0,
				triggerFreeDelay = 0.0,
				effectGoName = "eff_4032yushou_rest",
				effectAsset = {
					BundleName = "effects/prefab/model/yushou/4032/eff_4032yushou_rest_prefab",
					AssetName = "eff_4032yushou_rest",
				},
				playerAtTarget = false,
				referenceNodeHierarchyPath = "root",
				isAttach = true,
				isRotation = false,
				isUseCustomTransform = true,
				offsetPosX = 0.5,
				offsetPosY = -0.5,
				offsetPosZ = -2.0,
				triggerStopEvent = "",
				effectBtnName = "rest",
				playerAtPos = false,
				ignoreParentScale = false,
			},
		},
		sounds = {},

		cameraShakes = {},

		radialBlurs = {},

	},
}