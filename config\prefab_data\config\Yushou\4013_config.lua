return {
	actorController = {
		projectiles = {},

		hurts = {},

	},
	actorTriggers = {
		effects = {
			{
				triggerEventName = "attack2/begin",
				triggerDelay = 1.1,
				triggerFreeDelay = 0.0,
				effectGoName = "yushou_skill_8",
				effectAsset = {
					BundleName = "effects/prefab/model/yushou/common/yushou_skill_8_prefab",
					AssetName = "yushou_skill_8",
				},
				playerAtTarget = false,
				referenceNodeHierarchyPath = "root",
				isAttach = true,
				isRotation = false,
				isUseCustomTransform = true,
				offsetPosX = 0.0,
				offsetPosY = 0.0,
				offsetPosZ = 7.0,
				triggerStopEvent = "",
				effectBtnName = "attack2",
				playerAtPos = false,
				ignoreParentScale = true,
			},
			{
				triggerEventName = "attack1/begin",
				triggerDelay = 0.0,
				triggerFreeDelay = 0.0,
				effectGoName = "eff_4013yushou_attcak1",
				effectAsset = {
					BundleName = "effects/prefab/model/yushou/4013/eff_4013yushou_attcak1_prefab",
					AssetName = "eff_4013yushou_attcak1",
				},
				playerAtTarget = false,
				referenceNodeHierarchyPath = "root",
				isAttach = true,
				isRotation = false,
				isUseCustomTransform = false,
				offsetPosX = 0.0,
				offsetPosY = 0.0,
				offsetPosZ = 0.0,
				triggerStopEvent = "",
				effectBtnName = "attack1",
				playerAtPos = false,
				ignoreParentScale = true,
			},
			{
				triggerEventName = "rest/begin",
				triggerDelay = 2.35,
				triggerFreeDelay = 0.0,
				effectGoName = "eff_4013yushou_rest1",
				effectAsset = {
					BundleName = "effects/prefab/model/yushou/4013/eff_4013yushou_rest1_prefab",
					AssetName = "eff_4013yushou_rest1",
				},
				playerAtTarget = true,
				referenceNodeHierarchyPath = "",
				isAttach = false,
				isRotation = false,
				isUseCustomTransform = false,
				offsetPosX = -10.0,
				offsetPosY = 0.0,
				offsetPosZ = 0.0,
				triggerStopEvent = "",
				effectBtnName = "rest",
				playerAtPos = false,
				ignoreParentScale = false,
			},
		},
		sounds = {
			{
				soundEventName = "attack2/begin",
				soundDelay = 0.0,
				soundAudioAsset = {
					BundleName = "audios/sfxs/skill/4013",
					AssetName = "4013_skill",
				},
				soundAudioGoName = "4013_skill",
				soundBtnName = "skill",
				soundIsMainRole = false,
			},
		},
		cameraShakes = {},

		radialBlurs = {},

	},
}