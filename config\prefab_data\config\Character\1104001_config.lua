return {
	actorController = {
		projectiles = {},

		hurts = {},

		beHurtEffecct = {},

		hurtEffectName = "",
		beHurtNodeName = "",
		beHurtAttach = false,
		hurtEffectFreeDelay = 0.0,
		QualityCtrlList = {},

	},
	actorTriggers = {
		effects = {},

		halts = {},

		sounds = {
			{
				soundEventName = "attack1/begin",
				soundDelay = 0.0,
				soundAudioAsset = {
					BundleName = "audios/sfxs/roleskill/role6",
					AssetName = "role6_skill1",
				},
				soundAudioGoName = "role6_skill1",
				soundBtnName = "attack1",
				soundIsMainRole = false,
			},
			{
				soundEventName = "attack2/begin",
				soundDelay = 0.0,
				soundAudioAsset = {
					BundleName = "audios/sfxs/roleskill/role6",
					AssetName = "role6_skill2",
				},
				soundAudioGoName = "role6_skill2",
				soundBtnName = "attack2",
				soundIsMainRole = false,
			},
			{
				soundEventName = "attack3/begin",
				soundDelay = 0.0,
				soundAudioAsset = {
					BundleName = "audios/sfxs/roleskill/role6",
					AssetName = "role6_skill3",
				},
				soundAudioGoName = "role6_skill3",
				soundBtnName = "attack3",
				soundIsMainRole = false,
			},
			{
				soundEventName = "attack4/begin",
				soundDelay = 0.0,
				soundAudioAsset = {
					BundleName = "audios/sfxs/roleskill/role6",
					AssetName = "role6_skill4",
				},
				soundAudioGoName = "role6_skill4",
				soundBtnName = "attack4",
				soundIsMainRole = false,
			},
			{
				soundEventName = "combo2_1/begin",
				soundDelay = 0.0,
				soundAudioAsset = {
					BundleName = "audios/sfxs/roleskill/role6",
					AssetName = "role6_combo1",
				},
				soundAudioGoName = "role6_combo1",
				soundBtnName = "combo1",
				soundIsMainRole = false,
			},
			{
				soundEventName = "combo2_2/begin",
				soundDelay = 0.0,
				soundAudioAsset = {
					BundleName = "audios/sfxs/roleskill/role6",
					AssetName = "role6_combo1",
				},
				soundAudioGoName = "role6_combo1",
				soundBtnName = "combo2",
				soundIsMainRole = false,
			},
			{
				soundEventName = "combo2_3/begin",
				soundDelay = 0.0,
				soundAudioAsset = {
					BundleName = "audios/sfxs/roleskill/role6",
					AssetName = "role6_combo3",
				},
				soundAudioGoName = "role6_combo3",
				soundBtnName = "combo3",
				soundIsMainRole = false,
			},
			{
				soundEventName = "combo2_4/begin",
				soundDelay = 0.0,
				soundAudioAsset = {
					BundleName = "audios/sfxs/roleskill/role6",
					AssetName = "role6_combo4",
				},
				soundAudioGoName = "role6_combo4",
				soundBtnName = "combo4",
				soundIsMainRole = false,
			},
		},
		cameraShakes = {},

		cameraFOVs = {},

		sceneFades = {},

		footsteps = {},

	},
	actorBlinker = {
		blinkFadeIn = 0.0,
		blinkFadeHold = 0.0,
		blinkFadeOut = 0.0,
	},
	TimeLineList = {},

}