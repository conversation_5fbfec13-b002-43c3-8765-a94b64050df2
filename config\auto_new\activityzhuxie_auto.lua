return {
    other={
        {
            ct_res_id=101,
            tip_res_id=101,
            content="1>战场开启时间：每天18:00-18:30；\r\n\r\n2>活动期间进入战场获取指定战场宝物，可获取相应奖励；\r\n\r\n3>战场BOSS不定时刷新，完成击杀获得额外奖励；\r\n\r\n4>背靠战友，预防偷袭，击杀玩家有机会抢夺战场宝物；"
        }
    },
    task_list={
        {
            task_id=1,
            world_min_level=0,
            world_max_level=100,
            task_type=1,
            param_id=0,
            param_max_value=40,
            shengwang=4000,
            reward_item1={
                item_id=27417,
                num=5,
                is_bind=0
            },
            reward_item2={
                item_id=27418,
                num=5,
                is_bind=0
            },
            reward_item3={
                item_id=27419,
                num=5,
                is_bind=0
            },
            item_name="采集任务",
            task_name="收集采集物",
            event=4,
            x=19,
            y=106
        },
        {
            task_id=2,
            world_min_level=0,
            world_max_level=100,
            task_type=3,
            param_id=0,
            param_max_value=40,
            shengwang=4000,
            reward_item1={
                item_id=27420,
                num=5,
                is_bind=0
            },
            reward_item2={
                item_id=27421,
                num=5,
                is_bind=0
            },
            reward_item3={
                item_id=27422,
                num=5,
                is_bind=0
            },
            item_name="杀怪任务",
            task_name="击杀小怪物",
            event=2,
            x=26,
            y=62
        },
        {
            task_id=3,
            world_min_level=0,
            world_max_level=100,
            task_type=2,
            param_id=0,
            param_max_value=1,
            shengwang=5000,
            reward_item1={
                item_id=27414,
                num=5,
                is_bind=0
            },
            reward_item2={
                item_id=27415,
                num=5,
                is_bind=0
            },
            reward_item3={
                item_id=27416,
                num=5,
                is_bind=0
            },
            item_name="BOSS任务",
            task_name="击杀BOSS",
            event=2,
            x=157,
            y=98
        },
        {
            task_id=4,
            world_min_level=0,
            world_max_level=100,
            task_type=4,
            param_id=0,
            param_max_value=10,
            shengwang=8000,
            reward_item1={
                item_id=27423,
                num=5,
                is_bind=0
            },
            reward_item2={},
            reward_item3={},
            item_name="杀人任务",
            task_name="击杀玩家",
            event=2,
            x=157,
            y=49
        },
        {
            task_id=5,
            world_min_level=101,
            world_max_level=200,
            task_type=1,
            param_id=0,
            param_max_value=40,
            shengwang=4000,
            reward_item1={
                item_id=27417,
                num=5,
                is_bind=0
            },
            reward_item2={
                item_id=27418,
                num=5,
                is_bind=0
            },
            reward_item3={
                item_id=27419,
                num=5,
                is_bind=0
            },
            item_name="采集任务",
            task_name="收集采集物",
            event=4,
            x=19,
            y=106
        },
        {
            task_id=6,
            world_min_level=101,
            world_max_level=200,
            task_type=3,
            param_id=0,
            param_max_value=40,
            shengwang=4000,
            reward_item1={
                item_id=27420,
                num=5,
                is_bind=0
            },
            reward_item2={
                item_id=27421,
                num=5,
                is_bind=0
            },
            reward_item3={
                item_id=27422,
                num=5,
                is_bind=0
            },
            item_name="杀怪任务",
            task_name="击杀小怪物",
            event=2,
            x=26,
            y=62
        },
        {
            task_id=7,
            world_min_level=101,
            world_max_level=200,
            task_type=2,
            param_id=0,
            param_max_value=1,
            shengwang=5000,
            reward_item1={
                item_id=27414,
                num=5,
                is_bind=0
            },
            reward_item2={
                item_id=27415,
                num=5,
                is_bind=0
            },
            reward_item3={
                item_id=27416,
                num=5,
                is_bind=0
            },
            item_name="BOSS任务",
            task_name="击杀BOSS",
            event=2,
            x=157,
            y=98
        },
        {
            task_id=8,
            world_min_level=101,
            world_max_level=200,
            task_type=4,
            param_id=0,
            param_max_value=10,
            shengwang=8000,
            reward_item1={
                item_id=27423,
                num=5,
                is_bind=0
            },
            reward_item2={},
            reward_item3={},
            item_name="杀人任务",
            task_name="击杀玩家",
            event=2,
            x=157,
            y=49
        },
        {
            task_id=9,
            world_min_level=201,
            world_max_level=300,
            task_type=1,
            param_id=0,
            param_max_value=40,
            shengwang=4000,
            reward_item1={
                item_id=27417,
                num=5,
                is_bind=0
            },
            reward_item2={
                item_id=27418,
                num=5,
                is_bind=0
            },
            reward_item3={
                item_id=27419,
                num=5,
                is_bind=0
            },
            item_name="采集任务",
            task_name="收集采集物",
            event=4,
            x=19,
            y=106
        },
        {
            task_id=10,
            world_min_level=201,
            world_max_level=300,
            task_type=3,
            param_id=0,
            param_max_value=40,
            shengwang=4000,
            reward_item1={
                item_id=27420,
                num=5,
                is_bind=0
            },
            reward_item2={
                item_id=27421,
                num=5,
                is_bind=0
            },
            reward_item3={
                item_id=27422,
                num=5,
                is_bind=0
            },
            item_name="杀怪任务",
            task_name="击杀小怪物",
            event=2,
            x=26,
            y=62
        },
        {
            task_id=11,
            world_min_level=201,
            world_max_level=300,
            task_type=2,
            param_id=0,
            param_max_value=1,
            shengwang=5000,
            reward_item1={
                item_id=27414,
                num=5,
                is_bind=0
            },
            reward_item2={
                item_id=27415,
                num=5,
                is_bind=0
            },
            reward_item3={
                item_id=27416,
                num=5,
                is_bind=0
            },
            item_name="BOSS任务",
            task_name="击杀BOSS",
            event=2,
            x=157,
            y=98
        },
        {
            task_id=12,
            world_min_level=201,
            world_max_level=300,
            task_type=4,
            param_id=0,
            param_max_value=10,
            shengwang=8000,
            reward_item1={
                item_id=27423,
                num=5,
                is_bind=0
            },
            reward_item2={},
            reward_item3={},
            item_name="杀人任务",
            task_name="击杀玩家",
            event=2,
            x=157,
            y=49
        },
        {
            task_id=13,
            world_min_level=301,
            world_max_level=400,
            task_type=1,
            param_id=0,
            param_max_value=40,
            shengwang=4000,
            reward_item1={
                item_id=27417,
                num=5,
                is_bind=0
            },
            reward_item2={
                item_id=27418,
                num=5,
                is_bind=0
            },
            reward_item3={
                item_id=27419,
                num=5,
                is_bind=0
            },
            item_name="采集任务",
            task_name="收集采集物",
            event=4,
            x=19,
            y=106
        },
        {
            task_id=14,
            world_min_level=301,
            world_max_level=400,
            task_type=3,
            param_id=0,
            param_max_value=40,
            shengwang=4000,
            reward_item1={
                item_id=27420,
                num=5,
                is_bind=0
            },
            reward_item2={
                item_id=27421,
                num=5,
                is_bind=0
            },
            reward_item3={
                item_id=27422,
                num=5,
                is_bind=0
            },
            item_name="杀怪任务",
            task_name="击杀小怪物",
            event=2,
            x=26,
            y=62
        },
        {
            task_id=15,
            world_min_level=301,
            world_max_level=400,
            task_type=2,
            param_id=0,
            param_max_value=1,
            shengwang=5000,
            reward_item1={
                item_id=27414,
                num=5,
                is_bind=0
            },
            reward_item2={
                item_id=27415,
                num=5,
                is_bind=0
            },
            reward_item3={
                item_id=27416,
                num=5,
                is_bind=0
            },
            item_name="BOSS任务",
            task_name="击杀BOSS",
            event=2,
            x=157,
            y=98
        },
        {
            task_id=16,
            world_min_level=301,
            world_max_level=400,
            task_type=4,
            param_id=0,
            param_max_value=10,
            shengwang=8000,
            reward_item1={
                item_id=27423,
                num=5,
                is_bind=0
            },
            reward_item2={},
            reward_item3={},
            item_name="杀人任务",
            task_name="击杀玩家",
            event=2,
            x=157,
            y=49
        },
        {
            task_id=17,
            world_min_level=401,
            world_max_level=500,
            task_type=1,
            param_id=0,
            param_max_value=40,
            shengwang=4000,
            reward_item1={
                item_id=27417,
                num=5,
                is_bind=0
            },
            reward_item2={
                item_id=27418,
                num=5,
                is_bind=0
            },
            reward_item3={
                item_id=27419,
                num=5,
                is_bind=0
            },
            item_name="采集任务",
            task_name="收集采集物",
            event=4,
            x=19,
            y=106
        },
        {
            task_id=18,
            world_min_level=401,
            world_max_level=500,
            task_type=3,
            param_id=0,
            param_max_value=40,
            shengwang=4000,
            reward_item1={
                item_id=27420,
                num=5,
                is_bind=0
            },
            reward_item2={
                item_id=27421,
                num=5,
                is_bind=0
            },
            reward_item3={
                item_id=27422,
                num=5,
                is_bind=0
            },
            item_name="杀怪任务",
            task_name="击杀小怪物",
            event=2,
            x=26,
            y=62
        },
        {
            task_id=19,
            world_min_level=401,
            world_max_level=500,
            task_type=2,
            param_id=0,
            param_max_value=1,
            shengwang=5000,
            reward_item1={
                item_id=27414,
                num=5,
                is_bind=0
            },
            reward_item2={
                item_id=27415,
                num=5,
                is_bind=0
            },
            reward_item3={
                item_id=27416,
                num=5,
                is_bind=0
            },
            item_name="BOSS任务",
            task_name="击杀BOSS",
            event=2,
            x=157,
            y=98
        },
        {
            task_id=20,
            world_min_level=401,
            world_max_level=500,
            task_type=4,
            param_id=0,
            param_max_value=10,
            shengwang=8000,
            reward_item1={
                item_id=27423,
                num=5,
                is_bind=0
            },
            reward_item2={},
            reward_item3={},
            item_name="杀人任务",
            task_name="击杀玩家",
            event=2,
            x=157,
            y=49
        },
        {
            task_id=21,
            world_min_level=501,
            world_max_level=600,
            task_type=1,
            param_id=0,
            param_max_value=40,
            shengwang=4000,
            reward_item1={
                item_id=27417,
                num=5,
                is_bind=0
            },
            reward_item2={
                item_id=27418,
                num=5,
                is_bind=0
            },
            reward_item3={
                item_id=27419,
                num=5,
                is_bind=0
            },
            item_name="采集任务",
            task_name="收集采集物",
            event=4,
            x=19,
            y=106
        },
        {
            task_id=22,
            world_min_level=501,
            world_max_level=600,
            task_type=3,
            param_id=0,
            param_max_value=40,
            shengwang=4000,
            reward_item1={
                item_id=27420,
                num=5,
                is_bind=0
            },
            reward_item2={
                item_id=27421,
                num=5,
                is_bind=0
            },
            reward_item3={
                item_id=27422,
                num=5,
                is_bind=0
            },
            item_name="杀怪任务",
            task_name="击杀小怪物",
            event=2,
            x=26,
            y=62
        },
        {
            task_id=23,
            world_min_level=501,
            world_max_level=600,
            task_type=2,
            param_id=0,
            param_max_value=1,
            shengwang=5000,
            reward_item1={
                item_id=27414,
                num=5,
                is_bind=0
            },
            reward_item2={
                item_id=27415,
                num=5,
                is_bind=0
            },
            reward_item3={
                item_id=27416,
                num=5,
                is_bind=0
            },
            item_name="BOSS任务",
            task_name="击杀BOSS",
            event=2,
            x=157,
            y=98
        },
        {
            task_id=24,
            world_min_level=501,
            world_max_level=600,
            task_type=4,
            param_id=0,
            param_max_value=10,
            shengwang=8000,
            reward_item1={
                item_id=27423,
                num=5,
                is_bind=0
            },
            reward_item2={},
            reward_item3={},
            item_name="杀人任务",
            task_name="击杀玩家",
            event=2,
            x=157,
            y=49
        },
        {
            task_id=25,
            world_min_level=601,
            world_max_level=700,
            task_type=1,
            param_id=0,
            param_max_value=40,
            shengwang=4000,
            reward_item1={
                item_id=27417,
                num=5,
                is_bind=0
            },
            reward_item2={
                item_id=27418,
                num=5,
                is_bind=0
            },
            reward_item3={
                item_id=27419,
                num=5,
                is_bind=0
            },
            item_name="采集任务",
            task_name="收集采集物",
            event=4,
            x=19,
            y=106
        },
        {
            task_id=26,
            world_min_level=601,
            world_max_level=700,
            task_type=3,
            param_id=0,
            param_max_value=40,
            shengwang=4000,
            reward_item1={
                item_id=27420,
                num=5,
                is_bind=0
            },
            reward_item2={
                item_id=27421,
                num=5,
                is_bind=0
            },
            reward_item3={
                item_id=27422,
                num=5,
                is_bind=0
            },
            item_name="杀怪任务",
            task_name="击杀小怪物",
            event=2,
            x=26,
            y=62
        },
        {
            task_id=27,
            world_min_level=601,
            world_max_level=700,
            task_type=2,
            param_id=0,
            param_max_value=1,
            shengwang=5000,
            reward_item1={
                item_id=27414,
                num=5,
                is_bind=0
            },
            reward_item2={
                item_id=27415,
                num=5,
                is_bind=0
            },
            reward_item3={
                item_id=27416,
                num=5,
                is_bind=0
            },
            item_name="BOSS任务",
            task_name="击杀BOSS",
            event=2,
            x=157,
            y=98
        },
        {
            task_id=28,
            world_min_level=601,
            world_max_level=700,
            task_type=4,
            param_id=0,
            param_max_value=10,
            shengwang=8000,
            reward_item1={
                item_id=27423,
                num=5,
                is_bind=0
            },
            reward_item2={},
            reward_item3={},
            item_name="杀人任务",
            task_name="击杀玩家",
            event=2,
            x=157,
            y=49
        },
        {
            task_id=29,
            world_min_level=701,
            world_max_level=800,
            task_type=1,
            param_id=0,
            param_max_value=40,
            shengwang=4000,
            reward_item1={
                item_id=27417,
                num=5,
                is_bind=0
            },
            reward_item2={
                item_id=27418,
                num=5,
                is_bind=0
            },
            reward_item3={
                item_id=27419,
                num=5,
                is_bind=0
            },
            item_name="采集任务",
            task_name="收集采集物",
            event=4,
            x=19,
            y=106
        },
        {
            task_id=30,
            world_min_level=701,
            world_max_level=800,
            task_type=3,
            param_id=0,
            param_max_value=40,
            shengwang=4000,
            reward_item1={
                item_id=27420,
                num=5,
                is_bind=0
            },
            reward_item2={
                item_id=27421,
                num=5,
                is_bind=0
            },
            reward_item3={
                item_id=27422,
                num=5,
                is_bind=0
            },
            item_name="杀怪任务",
            task_name="击杀小怪物",
            event=2,
            x=26,
            y=62
        },
        {
            task_id=31,
            world_min_level=701,
            world_max_level=800,
            task_type=2,
            param_id=0,
            param_max_value=1,
            shengwang=5000,
            reward_item1={
                item_id=27414,
                num=5,
                is_bind=0
            },
            reward_item2={
                item_id=27415,
                num=5,
                is_bind=0
            },
            reward_item3={
                item_id=27416,
                num=5,
                is_bind=0
            },
            item_name="BOSS任务",
            task_name="击杀BOSS",
            event=2,
            x=157,
            y=98
        },
        {
            task_id=32,
            world_min_level=701,
            world_max_level=800,
            task_type=4,
            param_id=0,
            param_max_value=10,
            shengwang=8000,
            reward_item1={
                item_id=27423,
                num=5,
                is_bind=0
            },
            reward_item2={},
            reward_item3={},
            item_name="杀人任务",
            task_name="击杀玩家",
            event=2,
            x=157,
            y=49
        },
        {
            task_id=33,
            world_min_level=801,
            world_max_level=900,
            task_type=1,
            param_id=0,
            param_max_value=40,
            shengwang=4000,
            reward_item1={
                item_id=27417,
                num=5,
                is_bind=0
            },
            reward_item2={
                item_id=27418,
                num=5,
                is_bind=0
            },
            reward_item3={
                item_id=27419,
                num=5,
                is_bind=0
            },
            item_name="采集任务",
            task_name="收集采集物",
            event=4,
            x=19,
            y=106
        },
        {
            task_id=34,
            world_min_level=801,
            world_max_level=900,
            task_type=3,
            param_id=0,
            param_max_value=40,
            shengwang=4000,
            reward_item1={
                item_id=27420,
                num=5,
                is_bind=0
            },
            reward_item2={
                item_id=27421,
                num=5,
                is_bind=0
            },
            reward_item3={
                item_id=27422,
                num=5,
                is_bind=0
            },
            item_name="杀怪任务",
            task_name="击杀小怪物",
            event=2,
            x=26,
            y=62
        },
        {
            task_id=35,
            world_min_level=801,
            world_max_level=900,
            task_type=2,
            param_id=0,
            param_max_value=1,
            shengwang=5000,
            reward_item1={
                item_id=27414,
                num=5,
                is_bind=0
            },
            reward_item2={
                item_id=27415,
                num=5,
                is_bind=0
            },
            reward_item3={
                item_id=27416,
                num=5,
                is_bind=0
            },
            item_name="BOSS任务",
            task_name="击杀BOSS",
            event=2,
            x=157,
            y=98
        },
        {
            task_id=36,
            world_min_level=801,
            world_max_level=900,
            task_type=4,
            param_id=0,
            param_max_value=10,
            shengwang=8000,
            reward_item1={
                item_id=27423,
                num=5,
                is_bind=0
            },
            reward_item2={},
            reward_item3={},
            item_name="杀人任务",
            task_name="击杀玩家",
            event=2,
            x=157,
            y=49
        },
        {
            task_id=37,
            world_min_level=901,
            world_max_level=1000,
            task_type=1,
            param_id=0,
            param_max_value=40,
            shengwang=4000,
            reward_item1={
                item_id=27417,
                num=5,
                is_bind=0
            },
            reward_item2={
                item_id=27418,
                num=5,
                is_bind=0
            },
            reward_item3={
                item_id=27419,
                num=5,
                is_bind=0
            },
            item_name="采集任务",
            task_name="收集采集物",
            event=4,
            x=19,
            y=106
        },
        {
            task_id=38,
            world_min_level=901,
            world_max_level=1000,
            task_type=3,
            param_id=0,
            param_max_value=40,
            shengwang=4000,
            reward_item1={
                item_id=27420,
                num=5,
                is_bind=0
            },
            reward_item2={
                item_id=27421,
                num=5,
                is_bind=0
            },
            reward_item3={
                item_id=27422,
                num=5,
                is_bind=0
            },
            item_name="杀怪任务",
            task_name="击杀小怪物",
            event=2,
            x=26,
            y=62
        },
        {
            task_id=39,
            world_min_level=901,
            world_max_level=1000,
            task_type=2,
            param_id=0,
            param_max_value=1,
            shengwang=5000,
            reward_item1={
                item_id=27414,
                num=5,
                is_bind=0
            },
            reward_item2={
                item_id=27415,
                num=5,
                is_bind=0
            },
            reward_item3={
                item_id=27416,
                num=5,
                is_bind=0
            },
            item_name="BOSS任务",
            task_name="击杀BOSS",
            event=2,
            x=157,
            y=98
        },
        {
            task_id=40,
            world_min_level=901,
            world_max_level=1000,
            task_type=4,
            param_id=0,
            param_max_value=10,
            shengwang=8000,
            reward_item1={
                item_id=27423,
                num=5,
                is_bind=0
            },
            reward_item2={},
            reward_item3={},
            item_name="杀人任务",
            task_name="击杀玩家",
            event=2,
            x=157,
            y=49
        }
    },
    flush_point={
        {},
        {},
        {},
        {},
        {},
        {},
        {},
        {},
        {},
        {},
        {},
        {},
        {},
        {},
        {},
        {},
        {},
        {},
        {},
        {},
        {},
        {},
        {},
        {},
        {},
        {},
        {},
        {},
        {},
        {},
        {},
        {},
        {},
        {},
        {},
        {},
        {},
        {},
        {},
        {},
        {},
        {},
        {},
        {},
        {},
        {},
        {},
        {},
        {},
        {},
        {},
        {},
        {},
        {},
        {},
        {},
        {},
        {},
        {},
        {},
        {},
        {},
        {},
        {},
        {},
        {},
        {},
        {},
        {},
        {},
        {},
        {},
        {},
        {},
        {},
        {},
        {},
        {},
        {},
        {},
        {},
        {},
        {},
        {},
        {},
        {},
        {},
        {},
        {},
        {},
        {},
        {},
        {},
        {},
        {},
        {},
        {},
        {},
        {},
        {},
        {},
        {},
        {},
        {},
        {},
        {},
        {},
        {},
        {},
        {},
        {},
        {},
        {},
        {},
        {},
        {},
        {},
        {},
        {},
        {},
        {},
        {},
        {},
        {},
        {},
        {},
        {},
        {},
        {},
        {},
        {},
        {},
        {},
        {},
        {},
        {},
        {},
        {},
        {},
        {},
        {},
        {},
        {},
        {},
        {},
        {},
        {},
        {},
        {},
        {},
        {},
        {},
        {},
        {},
        {},
        {},
        {},
        {},
        {},
        {},
        {},
        {},
        {},
        {},
        {},
        {},
        {},
        {},
        {},
        {},
        {},
        {},
        {},
        {},
        {},
        {},
        {},
        {},
        {},
        {},
        {},
        {},
        {},
        {},
        {},
        {},
        {},
        {},
        {},
        {},
        {},
        {},
        {},
        {},
        {},
        {},
        {},
        {},
        {},
        {},
        {},
        {},
        {},
        {},
        {},
        {},
        {},
        {},
        {},
        {},
        {},
        {},
        {},
        {},
        {},
        {},
        {},
        {},
        {},
        {},
        {},
        {},
        {},
        {},
        {},
        {}
    },
    boss={
        {
            boss_index=0,
            boss_id=4041,
            limitLev=0,
            name="魔界邪神",
            item1_id=26020,
            item2_id=26040,
            item3_id=90083,
            item4_id=65535,
            boss_x=133,
            boss_y=114
        },
        {
            boss_index=0,
            boss_id=4042,
            limitLev=101,
            name="魔界邪神",
            item1_id=26020,
            item2_id=26040,
            item3_id=90083,
            item4_id=65535,
            boss_x=133,
            boss_y=114
        },
        {
            boss_index=0,
            boss_id=4043,
            limitLev=201,
            name="魔界邪神",
            item1_id=26021,
            item2_id=26041,
            item3_id=90083,
            item4_id=65535,
            boss_x=133,
            boss_y=114
        },
        {
            boss_index=0,
            boss_id=4044,
            limitLev=301,
            name="魔界邪神",
            item1_id=26022,
            item2_id=26042,
            item3_id=90083,
            item4_id=65535,
            boss_x=133,
            boss_y=114
        },
        {
            boss_index=0,
            boss_id=4045,
            limitLev=401,
            name="魔界邪神",
            item1_id=26023,
            item2_id=26043,
            item3_id=90083,
            item4_id=65535,
            boss_x=133,
            boss_y=114
        },
        {
            boss_index=0,
            boss_id=4046,
            limitLev=501,
            name="魔界邪神",
            item1_id=26024,
            item2_id=26044,
            item3_id=90083,
            item4_id=65535,
            boss_x=133,
            boss_y=114
        },
        {
            boss_index=0,
            boss_id=4047,
            limitLev=601,
            name="魔界邪神",
            item1_id=26025,
            item2_id=26045,
            item3_id=90083,
            item4_id=65535,
            boss_x=133,
            boss_y=114
        },
        {
            boss_index=0,
            boss_id=4048,
            limitLev=701,
            name="魔界邪神",
            item1_id=26026,
            item2_id=26046,
            item3_id=90083,
            item4_id=65535,
            boss_x=133,
            boss_y=114
        },
        {
            boss_index=0,
            boss_id=4049,
            limitLev=801,
            name="魔界邪神",
            item1_id=26027,
            item2_id=26047,
            item3_id=90083,
            item4_id=65535,
            boss_x=133,
            boss_y=114
        },
        {
            boss_index=0,
            boss_id=4050,
            limitLev=901,
            name="魔界邪神",
            item1_id=26020,
            item2_id=26040,
            item3_id=90083,
            item4_id=65535,
            boss_x=133,
            boss_y=114
        },
        {
            boss_index=1,
            boss_id=4051,
            limitLev=0,
            name="魔界邪神",
            item1_id=26020,
            item2_id=26040,
            item3_id=90083,
            item4_id=65535,
            boss_x=87,
            boss_y=82
        },
        {
            boss_index=1,
            boss_id=4052,
            limitLev=101,
            name="魔界邪神",
            item1_id=26020,
            item2_id=26040,
            item3_id=90083,
            item4_id=65535,
            boss_x=87,
            boss_y=82
        },
        {
            boss_index=1,
            boss_id=4053,
            limitLev=201,
            name="魔界邪神",
            item1_id=26021,
            item2_id=26041,
            item3_id=90083,
            item4_id=65535,
            boss_x=87,
            boss_y=82
        },
        {
            boss_index=1,
            boss_id=4054,
            limitLev=301,
            name="魔界邪神",
            item1_id=26022,
            item2_id=26042,
            item3_id=90083,
            item4_id=65535,
            boss_x=87,
            boss_y=82
        },
        {
            boss_index=1,
            boss_id=4055,
            limitLev=401,
            name="魔界邪神",
            item1_id=26023,
            item2_id=26043,
            item3_id=90083,
            item4_id=65535,
            boss_x=87,
            boss_y=82
        },
        {
            boss_index=1,
            boss_id=4056,
            limitLev=501,
            name="魔界邪神",
            item1_id=26024,
            item2_id=26044,
            item3_id=90083,
            item4_id=65535,
            boss_x=87,
            boss_y=82
        },
        {
            boss_index=1,
            boss_id=4057,
            limitLev=601,
            name="魔界邪神",
            item1_id=26025,
            item2_id=26045,
            item3_id=90083,
            item4_id=65535,
            boss_x=87,
            boss_y=82
        },
        {
            boss_index=1,
            boss_id=4058,
            limitLev=701,
            name="魔界邪神",
            item1_id=26026,
            item2_id=26046,
            item3_id=90083,
            item4_id=65535,
            boss_x=87,
            boss_y=82
        },
        {
            boss_index=1,
            boss_id=4059,
            limitLev=801,
            name="魔界邪神",
            item1_id=26027,
            item2_id=26047,
            item3_id=90083,
            item4_id=65535,
            boss_x=87,
            boss_y=82
        },
        {
            boss_index=1,
            boss_id=4060,
            limitLev=901,
            name="魔界邪神",
            item1_id=26020,
            item2_id=26040,
            item3_id=90083,
            item4_id=65535,
            boss_x=87,
            boss_y=82
        },
        {
            boss_index=2,
            boss_id=4061,
            limitLev=0,
            name="魔界邪神",
            item1_id=26020,
            item2_id=26040,
            item3_id=90083,
            item4_id=65535,
            boss_x=30,
            boss_y=57
        },
        {
            boss_index=2,
            boss_id=4062,
            limitLev=101,
            name="魔界邪神",
            item1_id=26020,
            item2_id=26040,
            item3_id=90083,
            item4_id=65535,
            boss_x=30,
            boss_y=57
        },
        {
            boss_index=2,
            boss_id=4063,
            limitLev=201,
            name="魔界邪神",
            item1_id=26021,
            item2_id=26041,
            item3_id=90083,
            item4_id=65535,
            boss_x=30,
            boss_y=57
        },
        {
            boss_index=2,
            boss_id=4064,
            limitLev=301,
            name="魔界邪神",
            item1_id=26022,
            item2_id=26042,
            item3_id=90083,
            item4_id=65535,
            boss_x=30,
            boss_y=57
        },
        {
            boss_index=2,
            boss_id=4065,
            limitLev=401,
            name="魔界邪神",
            item1_id=26023,
            item2_id=26043,
            item3_id=90083,
            item4_id=65535,
            boss_x=30,
            boss_y=57
        },
        {
            boss_index=2,
            boss_id=4066,
            limitLev=501,
            name="魔界邪神",
            item1_id=26024,
            item2_id=26044,
            item3_id=90083,
            item4_id=65535,
            boss_x=30,
            boss_y=57
        },
        {
            boss_index=2,
            boss_id=4067,
            limitLev=601,
            name="魔界邪神",
            item1_id=26025,
            item2_id=26045,
            item3_id=90083,
            item4_id=65535,
            boss_x=30,
            boss_y=57
        },
        {
            boss_index=2,
            boss_id=4068,
            limitLev=701,
            name="魔界邪神",
            item1_id=26026,
            item2_id=26046,
            item3_id=90083,
            item4_id=65535,
            boss_x=30,
            boss_y=57
        },
        {
            boss_index=2,
            boss_id=4069,
            limitLev=801,
            name="魔界邪神",
            item1_id=26027,
            item2_id=26047,
            item3_id=90083,
            item4_id=65535,
            boss_x=30,
            boss_y=57
        },
        {
            boss_index=2,
            boss_id=4070,
            limitLev=901,
            name="魔界邪神",
            item1_id=26020,
            item2_id=26040,
            item3_id=90083,
            item4_id=65535,
            boss_x=30,
            boss_y=57
        }
    },
    monster={
        {
            monster_index=0,
            monster_id=4001,
            limitLev=0,
            count=15,
            reward_item={
                item_id=27420,
                num=1,
                is_bind=0
            },
            shengwang=5
        },
        {
            monster_index=0,
            monster_id=4002,
            limitLev=101,
            count=15,
            reward_item={
                item_id=27420,
                num=1,
                is_bind=0
            },
            shengwang=5
        },
        {
            monster_index=0,
            monster_id=4003,
            limitLev=201,
            count=15,
            reward_item={
                item_id=27420,
                num=1,
                is_bind=0
            },
            shengwang=5
        },
        {
            monster_index=0,
            monster_id=4004,
            limitLev=301,
            count=15,
            reward_item={
                item_id=27420,
                num=1,
                is_bind=0
            },
            shengwang=5
        },
        {
            monster_index=0,
            monster_id=4005,
            limitLev=401,
            count=15,
            reward_item={
                item_id=27420,
                num=1,
                is_bind=0
            },
            shengwang=5
        },
        {
            monster_index=0,
            monster_id=4006,
            limitLev=501,
            count=15,
            reward_item={
                item_id=27420,
                num=1,
                is_bind=0
            },
            shengwang=5
        },
        {
            monster_index=0,
            monster_id=4007,
            limitLev=601,
            count=15,
            reward_item={
                item_id=27420,
                num=1,
                is_bind=0
            },
            shengwang=5
        },
        {
            monster_index=0,
            monster_id=4008,
            limitLev=701,
            count=15,
            reward_item={
                item_id=27420,
                num=1,
                is_bind=0
            },
            shengwang=5
        },
        {
            monster_index=0,
            monster_id=4009,
            limitLev=801,
            count=15,
            reward_item={
                item_id=27420,
                num=1,
                is_bind=0
            },
            shengwang=5
        },
        {
            monster_index=0,
            monster_id=4010,
            limitLev=901,
            count=15,
            reward_item={
                item_id=27420,
                num=1,
                is_bind=0
            },
            shengwang=5
        },
        {
            monster_index=1,
            monster_id=4011,
            limitLev=0,
            count=15,
            reward_item={
                item_id=27421,
                num=1,
                is_bind=0
            },
            shengwang=5
        },
        {
            monster_index=1,
            monster_id=4012,
            limitLev=101,
            count=15,
            reward_item={
                item_id=27421,
                num=1,
                is_bind=0
            },
            shengwang=5
        },
        {
            monster_index=1,
            monster_id=4013,
            limitLev=201,
            count=15,
            reward_item={
                item_id=27421,
                num=1,
                is_bind=0
            },
            shengwang=5
        },
        {
            monster_index=1,
            monster_id=4014,
            limitLev=301,
            count=15,
            reward_item={
                item_id=27421,
                num=1,
                is_bind=0
            },
            shengwang=5
        },
        {
            monster_index=1,
            monster_id=4015,
            limitLev=401,
            count=15,
            reward_item={
                item_id=27421,
                num=1,
                is_bind=0
            },
            shengwang=5
        },
        {
            monster_index=1,
            monster_id=4016,
            limitLev=501,
            count=15,
            reward_item={
                item_id=27421,
                num=1,
                is_bind=0
            },
            shengwang=5
        },
        {
            monster_index=1,
            monster_id=4017,
            limitLev=601,
            count=15,
            reward_item={
                item_id=27421,
                num=1,
                is_bind=0
            },
            shengwang=5
        },
        {
            monster_index=1,
            monster_id=4018,
            limitLev=701,
            count=15,
            reward_item={
                item_id=27421,
                num=1,
                is_bind=0
            },
            shengwang=5
        },
        {
            monster_index=1,
            monster_id=4019,
            limitLev=801,
            count=15,
            reward_item={
                item_id=27421,
                num=1,
                is_bind=0
            },
            shengwang=5
        },
        {
            monster_index=1,
            monster_id=4020,
            limitLev=901,
            count=15,
            reward_item={
                item_id=27421,
                num=1,
                is_bind=0
            },
            shengwang=5
        },
        {
            monster_index=2,
            monster_id=4021,
            limitLev=0,
            count=15,
            reward_item={
                item_id=27422,
                num=1,
                is_bind=0
            },
            shengwang=5
        },
        {
            monster_index=2,
            monster_id=4022,
            limitLev=101,
            count=15,
            reward_item={
                item_id=27422,
                num=1,
                is_bind=0
            },
            shengwang=5
        },
        {
            monster_index=2,
            monster_id=4023,
            limitLev=201,
            count=15,
            reward_item={
                item_id=27422,
                num=1,
                is_bind=0
            },
            shengwang=5
        },
        {
            monster_index=2,
            monster_id=4024,
            limitLev=301,
            count=15,
            reward_item={
                item_id=27422,
                num=1,
                is_bind=0
            },
            shengwang=5
        },
        {
            monster_index=2,
            monster_id=4025,
            limitLev=401,
            count=15,
            reward_item={
                item_id=27422,
                num=1,
                is_bind=0
            },
            shengwang=5
        },
        {
            monster_index=2,
            monster_id=4026,
            limitLev=501,
            count=15,
            reward_item={
                item_id=27422,
                num=1,
                is_bind=0
            },
            shengwang=5
        },
        {
            monster_index=2,
            monster_id=4027,
            limitLev=601,
            count=15,
            reward_item={
                item_id=27422,
                num=1,
                is_bind=0
            },
            shengwang=5
        },
        {
            monster_index=2,
            monster_id=4028,
            limitLev=701,
            count=15,
            reward_item={
                item_id=27422,
                num=1,
                is_bind=0
            },
            shengwang=5
        },
        {
            monster_index=2,
            monster_id=4029,
            limitLev=801,
            count=15,
            reward_item={
                item_id=27422,
                num=1,
                is_bind=0
            },
            shengwang=5
        },
        {
            monster_index=2,
            monster_id=4030,
            limitLev=901,
            count=15,
            reward_item={
                item_id=27422,
                num=1,
                is_bind=0
            },
            shengwang=5
        },
        {
            monster_index=3,
            monster_id=4031,
            limitLev=0,
            count=15,
            reward_item={
                item_id=27423,
                num=1,
                is_bind=0
            },
            shengwang=5
        },
        {
            monster_index=3,
            monster_id=4032,
            limitLev=101,
            count=15,
            reward_item={
                item_id=27423,
                num=1,
                is_bind=0
            },
            shengwang=5
        },
        {
            monster_index=3,
            monster_id=4033,
            limitLev=201,
            count=15,
            reward_item={
                item_id=27423,
                num=1,
                is_bind=0
            },
            shengwang=5
        },
        {
            monster_index=3,
            monster_id=4034,
            limitLev=301,
            count=15,
            reward_item={
                item_id=27423,
                num=1,
                is_bind=0
            },
            shengwang=5
        },
        {
            monster_index=3,
            monster_id=4035,
            limitLev=401,
            count=15,
            reward_item={
                item_id=27423,
                num=1,
                is_bind=0
            },
            shengwang=5
        },
        {
            monster_index=3,
            monster_id=4036,
            limitLev=501,
            count=15,
            reward_item={
                item_id=27423,
                num=1,
                is_bind=0
            },
            shengwang=5
        },
        {
            monster_index=3,
            monster_id=4037,
            limitLev=601,
            count=15,
            reward_item={
                item_id=27423,
                num=1,
                is_bind=0
            },
            shengwang=5
        },
        {
            monster_index=3,
            monster_id=4038,
            limitLev=701,
            count=15,
            reward_item={
                item_id=27423,
                num=1,
                is_bind=0
            },
            shengwang=5
        },
        {
            monster_index=3,
            monster_id=4039,
            limitLev=801,
            count=15,
            reward_item={
                item_id=27423,
                num=1,
                is_bind=0
            },
            shengwang=5
        },
        {
            monster_index=3,
            monster_id=4040,
            limitLev=901,
            count=15,
            reward_item={
                item_id=27423,
                num=1,
                is_bind=0
            },
            shengwang=5
        }
    },
    gather={
        {
            gather_index=0,
            gather_id=180,
            limitLev=0,
            gather_time=10,
            count=15,
            reward_item={
                item_id=27417,
                num=1,
                is_bind=0
            },
            shengwang=8
        },
        {
            gather_index=1,
            gather_id=181,
            limitLev=0,
            gather_time=10,
            count=15,
            reward_item={
                item_id=27418,
                num=1,
                is_bind=0
            },
            shengwang=8
        },
        {
            gather_index=2,
            gather_id=182,
            limitLev=0,
            gather_time=10,
            count=15,
            reward_item={
                item_id=27419,
                num=1,
                is_bind=0
            },
            shengwang=8
        }
    },
    show_reward={
        [1]={
            key=1,
            item_id=90080
        },
        [2]={
            key=2,
            item_id=90081
        },
        [3]={
            key=3,
            item_id=90050
        }
    }
}