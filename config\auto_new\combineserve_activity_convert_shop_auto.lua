-- H-合服活动-兑换商店.xls
local item_table={
[1]={item_id=48478,num=1,is_bind=1},
[2]={item_id=37434,num=1,is_bind=1},
[3]={item_id=37302,num=1,is_bind=1},
[4]={item_id=46396,num=1,is_bind=1},
[5]={item_id=48429,num=1,is_bind=1},
[6]={item_id=48430,num=1,is_bind=1},
[7]={item_id=48431,num=1,is_bind=1},
[8]={item_id=48432,num=1,is_bind=1},
[9]={item_id=48454,num=1,is_bind=1},
[10]={item_id=48134,num=1,is_bind=1},
[11]={item_id=48437,num=1,is_bind=1},
[12]={item_id=48438,num=1,is_bind=1},
[13]={item_id=48440,num=1,is_bind=1},
[14]={item_id=37219,num=1,is_bind=1},
[15]={item_id=37508,num=1,is_bind=1},
[16]={item_id=37414,num=1,is_bind=1},
[17]={item_id=38726,num=1,is_bind=1},
[18]={item_id=37027,num=1,is_bind=1},
[19]={item_id=37408,num=1,is_bind=1},
[20]={item_id=37111,num=1,is_bind=1},
[21]={item_id=37208,num=1,is_bind=1},
[22]={item_id=46386,num=1,is_bind=1},
[23]={item_id=37220,num=1,is_bind=1},
[24]={item_id=37021,num=1,is_bind=1},
[25]={item_id=37606,num=1,is_bind=1},
[26]={item_id=38653,num=1,is_bind=1},
[27]={item_id=38722,num=1,is_bind=1},
[28]={item_id=37716,num=1,is_bind=1},
[29]={item_id=37216,num=1,is_bind=1},
[30]={item_id=37410,num=1,is_bind=1},
[31]={item_id=38654,num=1,is_bind=1},
[32]={item_id=37607,num=1,is_bind=1},
[33]={item_id=38009,num=1,is_bind=1},
[34]={item_id=38505,num=1,is_bind=1},
[35]={item_id=38310,num=1,is_bind=1},
[36]={item_id=37801,num=1,is_bind=1},
[37]={item_id=26191,num=1,is_bind=1},
[38]={item_id=48071,num=1,is_bind=1},
[39]={item_id=47106,num=1,is_bind=1},
[40]={item_id=47098,num=1,is_bind=1},
[41]={item_id=26348,num=1,is_bind=1},
[42]={item_id=26351,num=1,is_bind=1},
[43]={item_id=26354,num=1,is_bind=1},
[44]={item_id=26372,num=1,is_bind=1},
[45]={item_id=26345,num=1,is_bind=1},
[46]={item_id=26377,num=1,is_bind=1},
[47]={item_id=27909,num=1,is_bind=1},
[48]={item_id=27908,num=1,is_bind=1},
[49]={item_id=27612,num=1,is_bind=1},
[50]={item_id=26500,num=1,is_bind=1},
[51]={item_id=26515,num=1,is_bind=1},
[52]={item_id=26149,num=1,is_bind=1},
[53]={item_id=26415,num=1,is_bind=1},
[54]={item_id=48511,num=1,is_bind=1},
[55]={item_id=37463,num=1,is_bind=1},
[56]={item_id=37307,num=1,is_bind=1},
}

return {
period={
{},
{begin_server_day=15,server_day=28,period=2,},
{begin_server_day=29,server_day=42,period=3,},
{begin_server_day=43,server_day=999,period=4,}
},

period_meta_table_map={
},
item={
{reward_item=item_table[1],stuff_count=300,limit_num=3,},
{seq=1,reward_item=item_table[2],stuff_count=400,sort=2,},
{seq=2,stuff_count=126,sort=3,},
{seq=3,reward_item=item_table[3],stuff_count=126,sort=4,},
{seq=4,reward_item=item_table[4],stuff_count=88,sort=5,},
{seq=5,reward_item=item_table[5],stuff_count=15,sort=6,},
{seq=6,reward_item=item_table[6],stuff_count=30,sort=7,},
{seq=7,reward_item=item_table[7],stuff_count=60,sort=8,},
{seq=8,reward_item=item_table[8],stuff_count=75,limit_num=8,sort=9,},
{seq=9,reward_item=item_table[9],stuff_count=40,sort=10,},
{seq=10,reward_item=item_table[10],stuff_count=40,sort=11,},
{seq=11,reward_item=item_table[11],stuff_count=17,limit_num=4,sort=12,},
{seq=12,reward_item=item_table[12],stuff_count=68,sort=13,},
{seq=13,reward_item=item_table[13],stuff_count=51,sort=14,},
{seq=14,reward_item=item_table[14],stuff_count=67,sort=15,},
{seq=15,reward_item=item_table[15],stuff_count=65,sort=16,},
{seq=16,reward_item=item_table[16],stuff_count=51,sort=17,},
{seq=17,reward_item=item_table[17],stuff_count=47,sort=18,},
{seq=18,reward_item=item_table[18],stuff_count=45,sort=19,},
{seq=19,reward_item=item_table[19],stuff_count=43,sort=20,},
{seq=20,reward_item=item_table[20],stuff_count=38,sort=21,},
{seq=21,reward_item=item_table[21],stuff_count=35,sort=22,},
{seq=22,reward_item=item_table[22],stuff_count=34,sort=23,},
{seq=23,reward_item=item_table[23],stuff_count=33,sort=24,},
{seq=24,reward_item=item_table[24],stuff_count=30,sort=25,},
{seq=25,reward_item=item_table[25],stuff_count=25,sort=26,},
{seq=26,reward_item=item_table[26],stuff_count=25,sort=27,},
{seq=27,reward_item=item_table[27],stuff_count=24,sort=28,},
{seq=28,reward_item=item_table[28],stuff_count=18,sort=29,},
{seq=29,reward_item=item_table[29],stuff_count=10,sort=30,},
{seq=30,reward_item=item_table[30],stuff_count=10,sort=31,},
{seq=31,reward_item=item_table[31],stuff_count=10,sort=32,},
{seq=32,reward_item=item_table[32],stuff_count=8,sort=33,},
{seq=33,reward_item=item_table[33],stuff_count=5,sort=34,},
{seq=34,reward_item=item_table[34],stuff_count=4,sort=35,},
{seq=35,reward_item=item_table[35],stuff_count=4,sort=36,},
{seq=36,reward_item=item_table[36],stuff_count=3,sort=37,},
{seq=37,reward_item=item_table[37],stuff_id=50005,sort=38,},
{seq=38,reward_item=item_table[38],stuff_count=3,sort=39,},
{seq=39,reward_item=item_table[39],sort=40,},
{seq=40,reward_item=item_table[40],sort=41,},
{seq=41,reward_item=item_table[41],sort=42,},
{seq=42,reward_item=item_table[42],sort=43,},
{seq=43,reward_item=item_table[43],sort=44,},
{seq=44,reward_item=item_table[44],stuff_count=15,sort=45,},
{seq=45,reward_item=item_table[45],sort=46,},
{seq=46,reward_item=item_table[46],stuff_count=8,sort=47,},
{seq=47,reward_item=item_table[47],stuff_count=5,sort=48,},
{seq=48,reward_item=item_table[48],limit_num=5,sort=49,},
{seq=49,reward_item=item_table[49],stuff_count=3,sort=50,},
{seq=50,reward_item=item_table[50],sort=51,},
{seq=51,reward_item=item_table[51],limit_num=10,sort=52,},
{seq=52,reward_item=item_table[52],sort=53,},
{seq=53,reward_item=item_table[53],stuff_id=50005,limit_num=20,sort=54,},
{period=2,reward_item=item_table[54],},
{period=2,},
{period=2,},
{period=2,},
{period=2,},
{period=2,},
{period=2,},
{period=2,},
{period=2,},
{period=2,},
{period=2,},
{period=2,},
{period=2,},
{period=2,},
{period=2,},
{period=2,},
{period=2,},
{period=2,},
{period=2,},
{period=2,},
{period=2,},
{period=2,},
{period=2,},
{period=2,},
{period=2,},
{period=2,},
{period=2,},
{period=2,},
{period=2,},
{period=2,},
{period=2,},
{period=2,},
{period=2,},
{period=2,},
{period=2,},
{period=2,},
{period=2,},
{period=2,},
{period=2,},
{period=2,},
{period=2,},
{period=2,},
{period=2,},
{period=2,},
{period=2,},
{period=2,},
{period=2,},
{period=2,},
{period=2,},
{period=2,},
{period=2,},
{period=2,},
{period=2,},
{period=2,},
{period=3,},
{period=3,},
{period=3,},
{period=3,},
{period=3,},
{period=3,},
{period=3,},
{period=3,},
{period=3,},
{period=3,},
{period=3,},
{period=3,},
{period=3,},
{period=3,},
{period=3,},
{period=3,},
{period=3,},
{period=3,},
{period=3,},
{period=3,},
{period=3,},
{period=3,},
{period=3,},
{period=3,},
{period=3,},
{period=3,},
{period=3,},
{period=3,},
{period=3,},
{period=3,},
{period=3,},
{period=3,},
{period=3,},
{period=3,},
{period=3,},
{period=3,},
{period=3,},
{period=3,},
{period=3,},
{period=3,},
{period=3,},
{period=3,},
{period=3,},
{period=3,},
{period=3,},
{period=3,},
{period=3,},
{period=3,},
{period=3,},
{period=3,},
{period=3,},
{period=3,},
{period=3,},
{period=3,},
{period=4,},
{period=4,reward_item=item_table[55],},
{period=4,},
{period=4,},
{period=4,},
{period=4,},
{period=4,},
{period=4,},
{period=4,},
{period=4,},
{period=4,},
{period=4,},
{period=4,},
{period=4,},
{period=4,},
{period=4,},
{period=4,},
{period=4,},
{period=4,},
{period=4,},
{period=4,},
{period=4,},
{period=4,},
{period=4,},
{period=4,},
{period=4,},
{period=4,},
{period=4,},
{period=4,},
{period=4,},
{period=4,},
{period=4,},
{period=4,},
{period=4,},
{period=4,},
{period=4,},
{period=4,},
{period=4,},
{period=4,},
{period=4,},
{period=4,},
{period=4,},
{period=4,},
{period=4,},
{period=4,},
{period=4,},
{period=4,},
{period=4,},
{period=4,},
{period=4,},
{period=4,},
{period=4,},
{period=4,},
{period=4,}
},

item_meta_table_map={
[109]=1,	-- depth:1
[111]=3,	-- depth:1
[55]=1,	-- depth:1
[57]=111,	-- depth:2
[163]=55,	-- depth:2
[165]=57,	-- depth:3
[118]=10,	-- depth:1
[173]=11,	-- depth:1
[172]=118,	-- depth:2
[113]=5,	-- depth:1
[112]=4,	-- depth:1
[110]=2,	-- depth:1
[119]=173,	-- depth:2
[175]=13,	-- depth:1
[181]=19,	-- depth:1
[177]=15,	-- depth:1
[178]=16,	-- depth:1
[179]=17,	-- depth:1
[180]=18,	-- depth:1
[182]=20,	-- depth:1
[183]=21,	-- depth:1
[184]=22,	-- depth:1
[185]=23,	-- depth:1
[188]=26,	-- depth:1
[187]=25,	-- depth:1
[186]=24,	-- depth:1
[121]=175,	-- depth:2
[176]=14,	-- depth:1
[122]=176,	-- depth:2
[138]=30,	-- depth:1
[124]=178,	-- depth:2
[164]=2,	-- depth:1
[166]=112,	-- depth:2
[167]=113,	-- depth:2
[145]=37,	-- depth:1
[144]=36,	-- depth:1
[143]=35,	-- depth:1
[142]=34,	-- depth:1
[141]=33,	-- depth:1
[140]=32,	-- depth:1
[139]=31,	-- depth:1
[189]=27,	-- depth:1
[123]=177,	-- depth:2
[137]=29,	-- depth:1
[135]=189,	-- depth:2
[134]=188,	-- depth:2
[133]=187,	-- depth:2
[132]=186,	-- depth:2
[131]=185,	-- depth:2
[130]=184,	-- depth:2
[129]=183,	-- depth:2
[128]=182,	-- depth:2
[127]=181,	-- depth:2
[126]=180,	-- depth:2
[125]=179,	-- depth:2
[136]=28,	-- depth:1
[91]=145,	-- depth:2
[77]=131,	-- depth:3
[89]=143,	-- depth:2
[90]=144,	-- depth:2
[192]=138,	-- depth:2
[193]=139,	-- depth:2
[194]=140,	-- depth:2
[59]=167,	-- depth:3
[58]=166,	-- depth:3
[56]=164,	-- depth:2
[53]=54,	-- depth:1
[52]=54,	-- depth:1
[51]=52,	-- depth:2
[64]=172,	-- depth:3
[195]=141,	-- depth:2
[197]=89,	-- depth:3
[198]=90,	-- depth:3
[199]=91,	-- depth:3
[41]=54,	-- depth:1
[40]=54,	-- depth:1
[38]=25,	-- depth:1
[8]=9,	-- depth:1
[7]=9,	-- depth:1
[6]=9,	-- depth:1
[196]=142,	-- depth:2
[65]=119,	-- depth:3
[191]=137,	-- depth:2
[79]=133,	-- depth:3
[83]=191,	-- depth:3
[84]=192,	-- depth:3
[85]=193,	-- depth:3
[80]=134,	-- depth:3
[190]=136,	-- depth:2
[78]=132,	-- depth:3
[86]=194,	-- depth:3
[76]=130,	-- depth:3
[75]=129,	-- depth:3
[81]=135,	-- depth:3
[74]=128,	-- depth:3
[73]=127,	-- depth:3
[72]=126,	-- depth:3
[71]=125,	-- depth:3
[70]=124,	-- depth:3
[69]=123,	-- depth:3
[68]=122,	-- depth:3
[67]=121,	-- depth:3
[88]=196,	-- depth:3
[87]=195,	-- depth:3
[82]=190,	-- depth:3
[162]=54,	-- depth:1
[161]=53,	-- depth:2
[214]=52,	-- depth:2
[169]=7,	-- depth:2
[203]=41,	-- depth:2
[202]=40,	-- depth:2
[200]=38,	-- depth:2
[160]=214,	-- depth:3
[174]=12,	-- depth:1
[171]=9,	-- depth:1
[170]=8,	-- depth:2
[168]=6,	-- depth:2
[213]=51,	-- depth:3
[159]=213,	-- depth:4
[108]=162,	-- depth:2
[95]=203,	-- depth:3
[92]=200,	-- depth:3
[66]=174,	-- depth:2
[63]=171,	-- depth:2
[62]=170,	-- depth:3
[61]=169,	-- depth:3
[60]=168,	-- depth:3
[50]=52,	-- depth:2
[94]=202,	-- depth:3
[49]=50,	-- depth:3
[47]=49,	-- depth:4
[46]=47,	-- depth:5
[45]=49,	-- depth:4
[44]=45,	-- depth:5
[43]=45,	-- depth:5
[42]=45,	-- depth:5
[39]=54,	-- depth:1
[48]=49,	-- depth:4
[105]=159,	-- depth:5
[216]=108,	-- depth:3
[107]=161,	-- depth:3
[149]=95,	-- depth:4
[106]=160,	-- depth:4
[146]=92,	-- depth:4
[120]=66,	-- depth:3
[117]=63,	-- depth:3
[116]=62,	-- depth:4
[115]=61,	-- depth:4
[114]=60,	-- depth:4
[215]=107,	-- depth:4
[148]=94,	-- depth:4
[96]=42,	-- depth:6
[157]=49,	-- depth:4
[158]=50,	-- depth:3
[212]=158,	-- depth:4
[211]=157,	-- depth:5
[210]=48,	-- depth:5
[209]=47,	-- depth:5
[208]=46,	-- depth:6
[207]=45,	-- depth:5
[206]=44,	-- depth:6
[205]=43,	-- depth:6
[204]=96,	-- depth:7
[154]=208,	-- depth:7
[153]=207,	-- depth:6
[201]=39,	-- depth:2
[152]=206,	-- depth:7
[151]=205,	-- depth:7
[150]=204,	-- depth:8
[104]=212,	-- depth:5
[103]=211,	-- depth:6
[147]=201,	-- depth:3
[102]=210,	-- depth:6
[101]=209,	-- depth:6
[100]=154,	-- depth:8
[99]=153,	-- depth:7
[98]=152,	-- depth:8
[97]=151,	-- depth:8
[93]=147,	-- depth:4
[155]=101,	-- depth:7
[156]=102,	-- depth:7
},
show={
{},
{},
{},
{}
},

show_meta_table_map={
},
period_default_table={begin_server_day=1,server_day=14,period=1,},

item_default_table={period=1,seq=0,reward_item=item_table[56],stuff_id=50006,stuff_count=1,limit_num=1,sort=1,stuff_icon="icon_exchange_shop1_2",},

show_default_table={item_name="<color=#ffffff>残剑玄尺</color>",show_item_id=37712,show_model="",show_model_bundle="",position="0|-0.5|0",rotation="0|0|0",scale=0.8,open_level=100,}

}

