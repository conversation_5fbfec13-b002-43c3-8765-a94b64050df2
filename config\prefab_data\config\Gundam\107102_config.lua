return {
	actorController = {
		projectiles = {},

		hurts = {},

		beHurtEffecct = {},

		hurtEffectName = "",
		beHurtNodeName = "",
		beHurtAttach = false,
		hurtEffectFreeDelay = 0.0,
		QualityCtrlList = {},

	},
	actorTriggers = {
		effects = {},

		halts = {},

		sounds = {
			{
				soundEventName = "attack1_1/begin",
				soundDelay = 0.0,
				soundAudioAsset = {
					BundleName = "audios/sfxs/roleskill/jijia1_1",
					AssetName = "MingJiangattack1",
				},
				soundAudioGoName = "MingJiangattack1",
				soundBtnName = "attack1",
				soundIsMainRole = false,
			},
			{
				soundEventName = "attack1_2/begin",
				soundDelay = 0.0,
				soundAudioAsset = {
					BundleName = "audios/sfxs/roleskill/jijia1_1",
					AssetName = "MingJiangattack2",
				},
				soundAudioGoName = "MingJiangattack2",
				soundBtnName = "attack2",
				soundIsMainRole = false,
			},
			{
				soundEventName = "attack1_3/begin",
				soundDelay = 0.5,
				soundAudioAsset = {
					BundleName = "audios/sfxs/roleskill/jijia1_1",
					AssetName = "MingJiangattack3",
				},
				soundAudioGoName = "MingJiangattack3",
				soundBtnName = "attack3",
				soundIsMainRole = false,
			},
			{
				soundEventName = "combo1_1/begin",
				soundDelay = 0.0,
				soundAudioAsset = {
					BundleName = "audios/sfxs/roleskill/jijia1_1",
					AssetName = "MingJiangcombo1",
				},
				soundAudioGoName = "MingJiangcombo1",
				soundBtnName = "combo1",
				soundIsMainRole = false,
			},
			{
				soundEventName = "combo1_2/begin",
				soundDelay = 0.0,
				soundAudioAsset = {
					BundleName = "audios/sfxs/roleskill/jijia1_1",
					AssetName = "MingJiangcombo2",
				},
				soundAudioGoName = "MingJiangcombo2",
				soundBtnName = "combo2",
				soundIsMainRole = false,
			},
			{
				soundEventName = "combo1_3/begin",
				soundDelay = 0.0,
				soundAudioAsset = {
					BundleName = "audios/sfxs/roleskill/jijia1_1",
					AssetName = "MingJiangcombo3",
				},
				soundAudioGoName = "MingJiangcombo3",
				soundBtnName = "combo3",
				soundIsMainRole = false,
			},
		},
		cameraShakes = {
			{
				CameraShakeBtnName = "attack2",
				eventName = "attack1_2/begin",
				numberOfShakes = 8,
				distance = 0.5,
				speed = 500.0,
				delay = 1.0,
				decay = 0.0,
			},
			{
				CameraShakeBtnName = "attack3",
				eventName = "attack1_3/begin",
				numberOfShakes = 20,
				distance = 0.5,
				speed = 500.0,
				delay = 2.8,
				decay = 0.0,
			},
			{
				CameraShakeBtnName = "combo3",
				eventName = "combo1_3/begin",
				numberOfShakes = 4,
				distance = 0.5,
				speed = 500.0,
				delay = 0.8,
				decay = 0.0,
			},
		},
		cameraFOVs = {},

		sceneFades = {},

		footsteps = {},

	},
	actorBlinker = {
		blinkFadeIn = 0.0,
		blinkFadeHold = 0.0,
		blinkFadeOut = 0.0,
	},
	TimeLineList = {},

}