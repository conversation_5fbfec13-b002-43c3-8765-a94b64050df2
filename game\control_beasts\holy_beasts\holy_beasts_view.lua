HolyBeastsView = HolyBeastsView or BaseClass(SafeBaseView)

function HolyBeastsView:__init()
	self.view_style = ViewStyle.Full
	self:SetMaskBg(false, true)
	self.is_safe_area_adapter = true
	self.default_index = TabIndex.holy_beasts_contract
	self:SetMaskBgAlpha(MASK_BG_ALPHA_TYPE.Zero)

	self.remind_tab = {{RemindName.HolyBeastsContract}, {RemindName.HolyBeastsSpirit}}
	
	local bundle_name = "uis/view/control_beasts_ui_prefab"
	self:AddViewResource(TabIndex.holy_beasts_spirit, ResPath.CommonBundleName, "layout_a3_common_panel")
	self:AddViewResource(0, bundle_name, "layout_holy_beasts_list")
	self:AddViewResource(TabIndex.holy_beasts_contract, bundle_name, "layout_holy_beasts_contract")
	self:AddViewResource(TabIndex.holy_beasts_spirit, bundle_name, "layout_holy_beasts_spirit")
	self:AddViewResource(0, ResPath.CommonBundleName, "VerticalTabbar")
	self:AddViewResource(0, ResPath.CommonBundleName, "layout_a3_common_top_panel")

	self:SetTabShowUIScene(TabIndex.holy_beasts_contract, {type = UI_SCENE_TYPE.DEFAULT, config_index = UI_SCENE_CONFIG_INDEX.HOLY_BEAST})
end

function HolyBeastsView:__delete()

end

function HolyBeastsView:SetDataAndOpen(beast_type)
	if ControlBeastsWGData.Instance:IsHolyBeastType(beast_type) then
		self.jump_beast_type = beast_type
	else
		self.jump_beast_type = nil
	end
	if not self:IsOpen() then
		self:Open()
	else
		self:Flush()
	end
end

function HolyBeastsView:ReleaseCallBack()
	if self.tabbar then
		self.tabbar:DeleteMe()
		self.tabbar = nil
	end

	if self.holy_beasts_item_list then
		self.holy_beasts_item_list:DeleteMe()
		self.holy_beasts_item_list = nil
	end

	if self.money_bar then
		self.money_bar:DeleteMe()
		self.money_bar = nil
	end

	self:ContractReleaseCallBack()
	self:SpiritReleaseCallBack()

	self.select_item_index = nil
	self.select_holy_beast_data = nil
end

function HolyBeastsView:CloseCallBack()
	self:ContractCloseCallBack()
	self:SpiritCloseCallBack()
end

function HolyBeastsView:LoadCallBack()
	self.node_list["title_view_name"].text.text = Language.ContralBeasts.TitleName15

	if not self.tabbar then
		self.tabbar = Tabbar.New(self.node_list)
		self.tabbar:Init(Language.ContralBeasts.HolyBeastsTabGrop, nil, nil, nil, self.remind_tab)
		self.tabbar:SetSelectCallback(BindTool.Bind1(self.ChangeToIndex, self))
		FunOpen.Instance:RegsiterTabFunUi(GuideModuleName.HolyBeastsView, self.tabbar)
	end

	-- 创建货币栏
	if not self.money_bar then
		self.money_bar = MoneyBar.New()
		local bundle, asset = ResPath.GetWidgets("MoneyBar")
		local show_params = { show_gold = true, }
		self.money_bar:SetMoneyShowInfo(0, 0, show_params)
		self.money_bar:LoadAsset(bundle, asset, self.node_list["money_tabar_pos"].transform)
	end

	if not self.holy_beasts_item_list then
		self.holy_beasts_item_list = AsyncListView.New(HolyBeastCell, self.node_list["holy_beasts_list"])
		self.holy_beasts_item_list:SetSelectCallBack(BindTool.Bind(self.OnHolyBeastItemHandler, self))
	end
end

function HolyBeastsView:LoadIndexCallBack(index)
	if index == TabIndex.holy_beasts_contract then
		self:ContractLoadIndexCallBack()
	elseif index == TabIndex.holy_beasts_spirit then
		self:SpiritLoadIndexCallBack()

		local bundle, asset = ResPath.GetRawImagesPNG("a3_hs_cs_bj")
		if self.node_list.RawImage_tongyong then
			self.node_list["RawImage_tongyong"].raw_image:LoadSprite(bundle, asset, function()
				self.node_list["RawImage_tongyong"].raw_image:SetNativeSize()
			end)
		end
	end
end

function HolyBeastsView:ShowIndexCallBack(index)
	if index == TabIndex.holy_beasts_contract then
		self.money_bar:SetVisible(false)
		self:ContractShowIndexCallBack()
	elseif index == TabIndex.holy_beasts_spirit then
		self.money_bar:SetVisible(true)
		self:SpiritShowIndexCallBack()
	end
end

function HolyBeastsView:OnFlush(param_t, index)
	for k, v in pairs(param_t) do
		if k == "all" then
			if v.item_id then -- 外部跳转
				self.jump_item_id = v.item_id
			end
			if index == TabIndex.holy_beasts_contract then
				self:FlushLeftList()
				self:ContractOnFlush(param_t)
			elseif index == TabIndex.holy_beasts_spirit then
				self:FlushLeftList()
				self:SpiritOnFlush(param_t)
			end
		end
	end
end

function HolyBeastsView:FlushLeftList()
	local holy_beasts_list = ControlBeastsWGData.Instance:GetHolyBeastDataList()
	self.holy_beasts_item_list:SetDataList(holy_beasts_list)

	local jump_index = 1
	if self.jump_item_id then
		for i, v in ipairs(holy_beasts_list) do
			if v.cfg.call_cost_item_id == self.jump_item_id then
				jump_index = i
				self.jump_item_id = nil
				break
			end
		end
	elseif self.jump_beast_type then
		for i, v in ipairs(holy_beasts_list) do
			if v.beast_type == self.jump_beast_type then
				jump_index = i
				self.jump_beast_type = nil
				break
			end
		end
	elseif self.select_holy_beast_data then
		for i, v in ipairs(holy_beasts_list) do
			if v.beast_type == self.select_holy_beast_data.beast_type then
				jump_index = i
				break
			end
		end
	end
	self.holy_beasts_item_list:JumpToIndex(jump_index)
end

function HolyBeastsView:OnHolyBeastItemHandler(item, index)
	if not item or not item.data then
		return
	end

	self.select_item_index = index
	self.select_holy_beast_data = item.data

	ControlBeastsWGData.Instance:SetSelectedHolyBeastData(self.select_holy_beast_data)

	if not self.select_holy_beast_data.is_unlock then
		self:ChangeToIndex(TabIndex.holy_beasts_contract)
	end
	self.tabbar:SetVerToggleCanvas(TabIndex.holy_beasts_spirit, self.select_holy_beast_data.is_unlock)

	if self.show_index == TabIndex.holy_beasts_contract and self:IsLoadedIndex(TabIndex.holy_beasts_contract) then
		self:ContractOnSelectHolyBeast(item)
	elseif self.show_index == TabIndex.holy_beasts_spirit and self:IsLoadedIndex(TabIndex.holy_beasts_spirit) then
		self:SpiritOnSelectHolyBeast(item)
	end
end

function HolyBeastsView:PlayUseEffect(effect_name)
	TipWGCtrl.Instance:ShowEffect({effect_type = effect_name, is_success = true, pos = Vector2(0, 0),
						parent_node = self.node_list["effect_pos"]})
end

---------------------------------------
-- 左侧列表Item HolyBeastCell
---------------------------------------
HolyBeastCell = HolyBeastCell or BaseClass(BaseRender)
function HolyBeastCell:__init()

end

function HolyBeastCell:LoadCallBack()

end

function HolyBeastCell:ReleaseCallBack()

end

function HolyBeastCell:OnFlush()
	if not self.data then
		return
	end

	local bundle, asset = ResPath.GetNoPackPNG("a3_hs_holy_" .. self.data.cfg.beast_id)
	self.node_list["img_beast_icon"].image:LoadSprite(bundle, asset, function()
		self.node_list["img_beast_icon"].image:SetNativeSize()
	end)
	self.node_list["bg_red"]:SetActive(self.data.is_unlock)
	self.node_list["bg_blue"]:SetActive(not self.data.is_unlock)
	self.node_list["img_locked"]:SetActive(not self.data.is_unlock)
	self.node_list["unlock_part"]:SetActive(self.data.is_unlock)
	if self.data.is_unlock then
		self.node_list["link_img"]:SetActive(self.data.is_contracted)
		self.node_list["target_beast_icon"]:SetActive(self.data.is_contracted)
		self.node_list["txt_no_contract"]:SetActive(not self.data.is_contracted)
		if self.data.is_contracted then
			local target_beast_data = ControlBeastsWGData.Instance:GetBeastDataById(self.data.link_bag_id)
			if target_beast_data then
				local beast_cfg = ControlBeastsWGData.Instance:GetBeastCfgById(target_beast_data.server_data.beast_id)
				local icon_id = string.format("a3_hs_head_%s", beast_cfg and beast_cfg.chang_head or 30470)
				bundle, asset = ResPath.GetControlBeastsImg(icon_id)
				self.node_list["target_beast_icon"].image:LoadSprite(bundle, asset)
			else
				print_error("未在背包中找到链接对象, 背包id:", self.data.link_bag_id)
			end
		end
	end

	local beast_cfg = ControlBeastsWGData.Instance:GetBeastCfgById(self.data.cfg.beast_id)
	if beast_cfg then
		self.node_list["txt_beast_name"].tmp.text = beast_cfg.beast_name
	end

	--local cur_index = ControlBeastsWGCtrl.Instance:GetHolyBeastViewCurIndex()
	local is_show_remind = ControlBeastsWGData.Instance:GetSingleHolyBeastRed(self.data)
	self.node_list["remind"]:SetActive(is_show_remind)
end

function HolyBeastCell:SetCurTabIndex(tab_index)
	self.tab_index = tab_index
end

function HolyBeastCell:OnSelectChange(is_select)
	self.node_list["select_hl"]:SetActive(is_select)
end
