-- Y-幽冥秘宝.xls
local item_table={
[1]={item_id=44095,num=1,is_bind=0},
[2]={item_id=44096,num=1,is_bind=0},
[3]={item_id=44094,num=1,is_bind=0},
[4]={item_id=44093,num=1,is_bind=0},
[5]={item_id=50020,num=1,is_bind=1},
[6]={item_id=36418,num=320,is_bind=0},
[7]={item_id=39152,num=1,is_bind=0},
[8]={item_id=48133,num=5,is_bind=0},
[9]={item_id=50020,num=2,is_bind=1},
[10]={item_id=36418,num=680,is_bind=0},
[11]={item_id=39152,num=2,is_bind=0},
[12]={item_id=48133,num=7,is_bind=0},
[13]={item_id=50020,num=5,is_bind=1},
[14]={item_id=36418,num=1280,is_bind=0},
[15]={item_id=39152,num=3,is_bind=0},
[16]={item_id=48133,num=10,is_bind=0},
[17]={item_id=48164,num=1,is_bind=0},
[18]={item_id=48165,num=1,is_bind=0},
[19]={item_id=48163,num=1,is_bind=0},
[20]={item_id=48162,num=1,is_bind=0},
[21]={item_id=48169,num=1,is_bind=0},
[22]={item_id=48166,num=1,is_bind=0},
[23]={item_id=48168,num=1,is_bind=0},
[24]={item_id=48167,num=1,is_bind=0},
[25]={item_id=50025,num=1,is_bind=1},
[26]={item_id=50025,num=9,is_bind=1},
[27]={item_id=50026,num=1,is_bind=1},
[28]={item_id=50027,num=1,is_bind=1},
[29]={item_id=50027,num=9,is_bind=1},
[30]={item_id=50028,num=1,is_bind=1},
[31]={item_id=50029,num=1,is_bind=1},
[32]={item_id=50029,num=9,is_bind=1},
[33]={item_id=50030,num=1,is_bind=1},
[34]={item_id=51002,num=1,is_bind=1},
[35]={item_id=51003,num=1,is_bind=1},
[36]={item_id=51004,num=1,is_bind=1},
[37]={item_id=51005,num=1,is_bind=1},
[38]={item_id=51006,num=1,is_bind=1},
[39]={item_id=51007,num=1,is_bind=1},
[40]={item_id=51008,num=1,is_bind=1},
[41]={item_id=51101,num=1,is_bind=1},
[42]={item_id=51102,num=1,is_bind=1},
[43]={item_id=51103,num=1,is_bind=1},
[44]={item_id=51104,num=1,is_bind=1},
[45]={item_id=51105,num=1,is_bind=1},
[46]={item_id=51106,num=1,is_bind=1},
[47]={item_id=51107,num=1,is_bind=1},
[48]={item_id=51108,num=1,is_bind=1},
[49]={item_id=51201,num=1,is_bind=1},
[50]={item_id=51202,num=1,is_bind=1},
[51]={item_id=51203,num=1,is_bind=1},
[52]={item_id=51204,num=1,is_bind=1},
[53]={item_id=51205,num=1,is_bind=1},
[54]={item_id=51206,num=1,is_bind=1},
[55]={item_id=51207,num=1,is_bind=1},
[56]={item_id=51208,num=1,is_bind=1},
[57]={item_id=51301,num=1,is_bind=1},
[58]={item_id=51302,num=1,is_bind=1},
[59]={item_id=51303,num=1,is_bind=1},
[60]={item_id=51304,num=1,is_bind=1},
[61]={item_id=51305,num=1,is_bind=1},
[62]={item_id=51306,num=1,is_bind=1},
[63]={item_id=51307,num=1,is_bind=1},
[64]={item_id=51308,num=1,is_bind=1},
[65]={item_id=51401,num=1,is_bind=1},
[66]={item_id=51402,num=1,is_bind=1},
[67]={item_id=51403,num=1,is_bind=1},
[68]={item_id=51404,num=1,is_bind=1},
[69]={item_id=51405,num=1,is_bind=1},
[70]={item_id=51406,num=1,is_bind=1},
[71]={item_id=51407,num=1,is_bind=1},
[72]={item_id=51408,num=1,is_bind=1},
[73]={item_id=51501,num=1,is_bind=1},
[74]={item_id=51502,num=1,is_bind=1},
[75]={item_id=51503,num=1,is_bind=1},
[76]={item_id=51504,num=1,is_bind=1},
[77]={item_id=51505,num=1,is_bind=1},
[78]={item_id=51506,num=1,is_bind=1},
[79]={item_id=51507,num=1,is_bind=1},
[80]={item_id=51508,num=1,is_bind=1},
[81]={item_id=51011,num=1,is_bind=1},
[82]={item_id=51012,num=1,is_bind=1},
[83]={item_id=51013,num=1,is_bind=1},
[84]={item_id=51014,num=1,is_bind=1},
[85]={item_id=51015,num=1,is_bind=1},
[86]={item_id=51016,num=1,is_bind=1},
[87]={item_id=51017,num=1,is_bind=1},
[88]={item_id=51018,num=1,is_bind=1},
[89]={item_id=51111,num=1,is_bind=1},
[90]={item_id=51112,num=1,is_bind=1},
[91]={item_id=51113,num=1,is_bind=1},
[92]={item_id=51114,num=1,is_bind=1},
[93]={item_id=51115,num=1,is_bind=1},
[94]={item_id=51116,num=1,is_bind=1},
[95]={item_id=51117,num=1,is_bind=1},
[96]={item_id=51118,num=1,is_bind=1},
[97]={item_id=51211,num=1,is_bind=1},
[98]={item_id=51212,num=1,is_bind=1},
[99]={item_id=51213,num=1,is_bind=1},
[100]={item_id=51214,num=1,is_bind=1},
[101]={item_id=51215,num=1,is_bind=1},
[102]={item_id=51216,num=1,is_bind=1},
[103]={item_id=51217,num=1,is_bind=1},
[104]={item_id=51218,num=1,is_bind=1},
[105]={item_id=51311,num=1,is_bind=1},
[106]={item_id=51312,num=1,is_bind=1},
[107]={item_id=51313,num=1,is_bind=1},
[108]={item_id=51314,num=1,is_bind=1},
[109]={item_id=51315,num=1,is_bind=1},
[110]={item_id=51316,num=1,is_bind=1},
[111]={item_id=51317,num=1,is_bind=1},
[112]={item_id=51318,num=1,is_bind=1},
[113]={item_id=51411,num=1,is_bind=1},
[114]={item_id=51412,num=1,is_bind=1},
[115]={item_id=51413,num=1,is_bind=1},
[116]={item_id=51414,num=1,is_bind=1},
[117]={item_id=51415,num=1,is_bind=1},
[118]={item_id=51416,num=1,is_bind=1},
[119]={item_id=51417,num=1,is_bind=1},
[120]={item_id=51418,num=1,is_bind=1},
[121]={item_id=51511,num=1,is_bind=1},
[122]={item_id=51512,num=1,is_bind=1},
[123]={item_id=51513,num=1,is_bind=1},
[124]={item_id=51514,num=1,is_bind=1},
[125]={item_id=51515,num=1,is_bind=1},
[126]={item_id=51516,num=1,is_bind=1},
[127]={item_id=51517,num=1,is_bind=1},
[128]={item_id=51518,num=1,is_bind=1},
[129]={item_id=51021,num=1,is_bind=1},
[130]={item_id=51022,num=1,is_bind=1},
[131]={item_id=51023,num=1,is_bind=1},
[132]={item_id=51024,num=1,is_bind=1},
[133]={item_id=51025,num=1,is_bind=1},
[134]={item_id=51026,num=1,is_bind=1},
[135]={item_id=51027,num=1,is_bind=1},
[136]={item_id=51028,num=1,is_bind=1},
[137]={item_id=51121,num=1,is_bind=1},
[138]={item_id=51122,num=1,is_bind=1},
[139]={item_id=51123,num=1,is_bind=1},
[140]={item_id=51124,num=1,is_bind=1},
[141]={item_id=51125,num=1,is_bind=1},
[142]={item_id=51126,num=1,is_bind=1},
[143]={item_id=51127,num=1,is_bind=1},
[144]={item_id=51128,num=1,is_bind=1},
[145]={item_id=51221,num=1,is_bind=1},
[146]={item_id=51222,num=1,is_bind=1},
[147]={item_id=51223,num=1,is_bind=1},
[148]={item_id=51224,num=1,is_bind=1},
[149]={item_id=51225,num=1,is_bind=1},
[150]={item_id=51226,num=1,is_bind=1},
[151]={item_id=51227,num=1,is_bind=1},
[152]={item_id=51228,num=1,is_bind=1},
[153]={item_id=51321,num=1,is_bind=1},
[154]={item_id=51322,num=1,is_bind=1},
[155]={item_id=51323,num=1,is_bind=1},
[156]={item_id=51324,num=1,is_bind=1},
[157]={item_id=51325,num=1,is_bind=1},
[158]={item_id=51326,num=1,is_bind=1},
[159]={item_id=51327,num=1,is_bind=1},
[160]={item_id=51328,num=1,is_bind=1},
[161]={item_id=51421,num=1,is_bind=1},
[162]={item_id=51422,num=1,is_bind=1},
[163]={item_id=51423,num=1,is_bind=1},
[164]={item_id=51424,num=1,is_bind=1},
[165]={item_id=51425,num=1,is_bind=1},
[166]={item_id=51426,num=1,is_bind=1},
[167]={item_id=51427,num=1,is_bind=1},
[168]={item_id=51428,num=1,is_bind=1},
[169]={item_id=51521,num=1,is_bind=1},
[170]={item_id=51522,num=1,is_bind=1},
[171]={item_id=51523,num=1,is_bind=1},
[172]={item_id=51524,num=1,is_bind=1},
[173]={item_id=51525,num=1,is_bind=1},
[174]={item_id=51526,num=1,is_bind=1},
[175]={item_id=51527,num=1,is_bind=1},
[176]={item_id=51528,num=1,is_bind=1},
[177]={item_id=52001,num=1,is_bind=1},
[178]={item_id=52002,num=1,is_bind=1},
[179]={item_id=52003,num=1,is_bind=1},
[180]={item_id=52004,num=1,is_bind=1},
[181]={item_id=52005,num=1,is_bind=1},
[182]={item_id=52006,num=1,is_bind=1},
[183]={item_id=52007,num=1,is_bind=1},
[184]={item_id=52008,num=1,is_bind=1},
[185]={item_id=52101,num=1,is_bind=1},
[186]={item_id=52102,num=1,is_bind=1},
[187]={item_id=52103,num=1,is_bind=1},
[188]={item_id=52104,num=1,is_bind=1},
[189]={item_id=52105,num=1,is_bind=1},
[190]={item_id=52106,num=1,is_bind=1},
[191]={item_id=52107,num=1,is_bind=1},
[192]={item_id=52108,num=1,is_bind=1},
[193]={item_id=52201,num=1,is_bind=1},
[194]={item_id=52202,num=1,is_bind=1},
[195]={item_id=52203,num=1,is_bind=1},
[196]={item_id=52204,num=1,is_bind=1},
[197]={item_id=52205,num=1,is_bind=1},
[198]={item_id=52206,num=1,is_bind=1},
[199]={item_id=52207,num=1,is_bind=1},
[200]={item_id=52208,num=1,is_bind=1},
[201]={item_id=52301,num=1,is_bind=1},
[202]={item_id=52302,num=1,is_bind=1},
[203]={item_id=52303,num=1,is_bind=1},
[204]={item_id=52304,num=1,is_bind=1},
[205]={item_id=52305,num=1,is_bind=1},
[206]={item_id=52306,num=1,is_bind=1},
[207]={item_id=52307,num=1,is_bind=1},
[208]={item_id=52308,num=1,is_bind=1},
[209]={item_id=52401,num=1,is_bind=1},
[210]={item_id=52402,num=1,is_bind=1},
[211]={item_id=52403,num=1,is_bind=1},
[212]={item_id=52404,num=1,is_bind=1},
[213]={item_id=52405,num=1,is_bind=1},
[214]={item_id=52406,num=1,is_bind=1},
[215]={item_id=52407,num=1,is_bind=1},
[216]={item_id=52408,num=1,is_bind=1},
[217]={item_id=52501,num=1,is_bind=1},
[218]={item_id=52502,num=1,is_bind=1},
[219]={item_id=52503,num=1,is_bind=1},
[220]={item_id=52504,num=1,is_bind=1},
[221]={item_id=52505,num=1,is_bind=1},
[222]={item_id=52506,num=1,is_bind=1},
[223]={item_id=52507,num=1,is_bind=1},
[224]={item_id=52508,num=1,is_bind=1},
[225]={item_id=52011,num=1,is_bind=1},
[226]={item_id=52012,num=1,is_bind=1},
[227]={item_id=52013,num=1,is_bind=1},
[228]={item_id=52014,num=1,is_bind=1},
[229]={item_id=52015,num=1,is_bind=1},
[230]={item_id=52016,num=1,is_bind=1},
[231]={item_id=52017,num=1,is_bind=1},
[232]={item_id=52018,num=1,is_bind=1},
[233]={item_id=52111,num=1,is_bind=1},
[234]={item_id=52112,num=1,is_bind=1},
[235]={item_id=52113,num=1,is_bind=1},
[236]={item_id=52114,num=1,is_bind=1},
[237]={item_id=52115,num=1,is_bind=1},
[238]={item_id=52116,num=1,is_bind=1},
[239]={item_id=52117,num=1,is_bind=1},
[240]={item_id=52118,num=1,is_bind=1},
[241]={item_id=52211,num=1,is_bind=1},
[242]={item_id=52212,num=1,is_bind=1},
[243]={item_id=52213,num=1,is_bind=1},
[244]={item_id=52214,num=1,is_bind=1},
[245]={item_id=52215,num=1,is_bind=1},
[246]={item_id=52216,num=1,is_bind=1},
[247]={item_id=52217,num=1,is_bind=1},
[248]={item_id=52218,num=1,is_bind=1},
[249]={item_id=52311,num=1,is_bind=1},
[250]={item_id=52312,num=1,is_bind=1},
[251]={item_id=52313,num=1,is_bind=1},
[252]={item_id=52314,num=1,is_bind=1},
[253]={item_id=52315,num=1,is_bind=1},
[254]={item_id=52316,num=1,is_bind=1},
[255]={item_id=52317,num=1,is_bind=1},
[256]={item_id=52318,num=1,is_bind=1},
[257]={item_id=52411,num=1,is_bind=1},
[258]={item_id=52412,num=1,is_bind=1},
[259]={item_id=52413,num=1,is_bind=1},
[260]={item_id=52414,num=1,is_bind=1},
[261]={item_id=52415,num=1,is_bind=1},
[262]={item_id=52416,num=1,is_bind=1},
[263]={item_id=52417,num=1,is_bind=1},
[264]={item_id=52418,num=1,is_bind=1},
[265]={item_id=52511,num=1,is_bind=1},
[266]={item_id=52512,num=1,is_bind=1},
[267]={item_id=52513,num=1,is_bind=1},
[268]={item_id=52514,num=1,is_bind=1},
[269]={item_id=52515,num=1,is_bind=1},
[270]={item_id=52516,num=1,is_bind=1},
[271]={item_id=52517,num=1,is_bind=1},
[272]={item_id=52518,num=1,is_bind=1},
[273]={item_id=52021,num=1,is_bind=1},
[274]={item_id=52022,num=1,is_bind=1},
[275]={item_id=52023,num=1,is_bind=1},
[276]={item_id=52024,num=1,is_bind=1},
[277]={item_id=52025,num=1,is_bind=1},
[278]={item_id=52026,num=1,is_bind=1},
[279]={item_id=52027,num=1,is_bind=1},
[280]={item_id=52028,num=1,is_bind=1},
[281]={item_id=52121,num=1,is_bind=1},
[282]={item_id=52122,num=1,is_bind=1},
[283]={item_id=52123,num=1,is_bind=1},
[284]={item_id=52124,num=1,is_bind=1},
[285]={item_id=52125,num=1,is_bind=1},
[286]={item_id=52126,num=1,is_bind=1},
[287]={item_id=52127,num=1,is_bind=1},
[288]={item_id=52128,num=1,is_bind=1},
[289]={item_id=52221,num=1,is_bind=1},
[290]={item_id=52222,num=1,is_bind=1},
[291]={item_id=53005,num=1,is_bind=1},
[292]={item_id=53105,num=1,is_bind=1},
[293]={item_id=53205,num=1,is_bind=1},
[294]={item_id=53305,num=1,is_bind=1},
[295]={item_id=53405,num=1,is_bind=1},
[296]={item_id=53505,num=1,is_bind=1},
[297]={item_id=54005,num=1,is_bind=1},
[298]={item_id=54105,num=1,is_bind=1},
[299]={item_id=54205,num=1,is_bind=1},
[300]={item_id=54305,num=1,is_bind=1},
[301]={item_id=54405,num=1,is_bind=1},
[302]={item_id=54505,num=1,is_bind=1},
[303]={item_id=50019,num=1,is_bind=1},
[304]={item_id=50019,num=9,is_bind=1},
[305]={item_id=50020,num=1,is_bind=0},
[306]={item_id=50023,num=1,is_bind=1},
[307]={item_id=50023,num=9,is_bind=1},
[308]={item_id=50024,num=1,is_bind=1},
[309]={item_id=51001,num=1,is_bind=1},
}

return {
other={
{}
},

other_meta_table_map={
},
cycle_subactivity={
{},
{cycle=2,},
{cycle=3,},
{cycle=4,},
{cycle=5,}
},

cycle_subactivity_meta_table_map={
},
subactivity_relation={
{},
{subactivity_id=2,},
{subactivity_id=3,pre_subactivity=2,},
{subactivity_id=4,},
{subactivity_id=5,},
{subactivity_id=6,}
},

subactivity_relation_meta_table_map={
},
subactivity_sale={
{subactivity_id=2,special_sale_price=15,rmb_seq=10102,subactivity_name="1折十连",},
{subactivity_id=3,special_sale_price=20,rmb_seq=10103,subactivity_name="2折十连",},
{special_sale_price=128,rmb_seq=10105,item={[0]=item_table[1]},name="山魈之鬼",},
{product_id=2,rmb_seq=10205,item={[0]=item_table[2]},name="魅惑女魔",},
{product_id=3,rmb_seq=10305,item={[0]=item_table[3]},name="魔魍恶灵",},
{product_id=4,rmb_seq=10405,item={[0]=item_table[4]},name="混沌妖魉",},
{subactivity_id=6,price_type=1,special_sale_price=980,discount=3,item={[0]=item_table[5],[1]=item_table[6],[2]=item_table[7],[3]=item_table[8]},limit_buy_times=4,name="幽冥白银礼包",subactivity_name="特惠召唤",},
{product_id=2,special_sale_price=1680,item={[0]=item_table[9],[1]=item_table[10],[2]=item_table[11],[3]=item_table[12]},name="幽冥黄金礼包",},
{product_id=3,special_sale_price=3880,item={[0]=item_table[13],[1]=item_table[14],[2]=item_table[15],[3]=item_table[16]},name="幽冥星耀礼包",},
{cycle=2,rmb_seq=20102,},
{cycle=2,rmb_seq=20103,},
{cycle=2,rmb_seq=20105,item={[0]=item_table[17]},},
{cycle=2,rmb_seq=20205,item={[0]=item_table[18]},},
{cycle=2,rmb_seq=20305,item={[0]=item_table[19]},},
{cycle=2,rmb_seq=20405,item={[0]=item_table[20]},},
{cycle=2,},
{cycle=2,},
{cycle=2,},
{cycle=3,rmb_seq=30102,},
{cycle=3,rmb_seq=30103,},
{cycle=3,rmb_seq=30105,},
{product_id=2,rmb_seq=30205,item={[0]=item_table[21]},name="魅惑女魔",},
{cycle=3,rmb_seq=30305,},
{product_id=4,rmb_seq=30405,item={[0]=item_table[22]},name="混沌妖魉",},
{cycle=3,},
{cycle=3,},
{cycle=3,},
{cycle=4,rmb_seq=40102,},
{cycle=4,rmb_seq=40103,},
{cycle=4,rmb_seq=40105,},
{cycle=4,rmb_seq=40205,},
{cycle=4,rmb_seq=40305,},
{cycle=4,rmb_seq=40405,},
{cycle=4,},
{cycle=4,},
{cycle=4,},
{cycle=5,rmb_seq=50102,},
{cycle=5,rmb_seq=50103,},
{cycle=5,rmb_seq=50105,item={[0]=item_table[23]},name="山魈之鬼",},
{cycle=5,rmb_seq=50205,},
{product_id=3,rmb_seq=50305,item={[0]=item_table[24]},name="魔魍恶灵",},
{cycle=5,rmb_seq=50405,},
{cycle=5,},
{cycle=5,},
{cycle=5,}
},

subactivity_sale_meta_table_map={
[30]=39,	-- depth:1
[21]=39,	-- depth:1
[22]=21,	-- depth:2
[24]=21,	-- depth:2
[28]=1,	-- depth:1
[29]=2,	-- depth:1
[31]=22,	-- depth:3
[33]=24,	-- depth:3
[37]=1,	-- depth:1
[38]=2,	-- depth:1
[40]=22,	-- depth:3
[41]=39,	-- depth:1
[42]=24,	-- depth:3
[32]=41,	-- depth:2
[20]=2,	-- depth:1
[23]=41,	-- depth:2
[10]=1,	-- depth:1
[19]=1,	-- depth:1
[4]=3,	-- depth:1
[11]=2,	-- depth:1
[12]=3,	-- depth:1
[5]=3,	-- depth:1
[6]=3,	-- depth:1
[13]=4,	-- depth:2
[14]=5,	-- depth:2
[15]=6,	-- depth:2
[25]=7,	-- depth:1
[16]=25,	-- depth:2
[43]=16,	-- depth:3
[34]=43,	-- depth:4
[8]=7,	-- depth:1
[9]=7,	-- depth:1
[18]=9,	-- depth:2
[35]=8,	-- depth:2
[27]=18,	-- depth:3
[26]=35,	-- depth:3
[44]=26,	-- depth:4
[17]=44,	-- depth:5
[36]=27,	-- depth:4
[45]=36,	-- depth:5
},
new_reward_pond={
{},
{min_draw_count=26,max_draw_count=75,reward_drop_id=361002,},
{min_draw_count=76,max_draw_count=150,reward_drop_id=361003,},
{min_draw_count=151,max_draw_count=300,reward_drop_id=361004,},
{min_draw_count=301,max_draw_count=500,reward_drop_id=361005,},
{min_draw_count=501,max_draw_count=700,reward_drop_id=361006,},
{min_draw_count=701,max_draw_count=1000,reward_drop_id=361007,},
{min_draw_count=1001,max_draw_count=1300,reward_drop_id=361008,},
{min_draw_count=1301,max_draw_count=1800,reward_drop_id=361009,},
{min_draw_count=1801,max_draw_count=2300,reward_drop_id=361010,},
{sixiang_type=2,reward_drop_id=361011,},
{sixiang_type=2,reward_drop_id=361012,},
{sixiang_type=2,reward_drop_id=361013,},
{sixiang_type=2,reward_drop_id=361014,},
{sixiang_type=2,reward_drop_id=361015,},
{sixiang_type=2,reward_drop_id=361016,},
{sixiang_type=2,reward_drop_id=361017,},
{sixiang_type=2,reward_drop_id=361018,},
{sixiang_type=2,reward_drop_id=361019,},
{sixiang_type=2,reward_drop_id=361020,},
{sixiang_type=3,reward_drop_id=361021,},
{sixiang_type=3,reward_drop_id=361022,},
{sixiang_type=3,reward_drop_id=361023,},
{sixiang_type=3,reward_drop_id=361024,},
{sixiang_type=3,reward_drop_id=361025,},
{sixiang_type=3,reward_drop_id=361026,},
{sixiang_type=3,reward_drop_id=361027,},
{sixiang_type=3,reward_drop_id=361028,},
{sixiang_type=3,reward_drop_id=361029,},
{sixiang_type=3,reward_drop_id=361030,},
{sixiang_type=4,reward_drop_id=361031,},
{sixiang_type=4,reward_drop_id=361032,},
{sixiang_type=4,reward_drop_id=361033,},
{sixiang_type=4,reward_drop_id=361034,},
{sixiang_type=4,reward_drop_id=361035,},
{sixiang_type=4,reward_drop_id=361036,},
{sixiang_type=4,reward_drop_id=361037,},
{sixiang_type=4,reward_drop_id=361038,},
{sixiang_type=4,reward_drop_id=361039,},
{sixiang_type=4,reward_drop_id=361040,}
},

new_reward_pond_meta_table_map={
[28]=8,	-- depth:1
[29]=9,	-- depth:1
[30]=10,	-- depth:1
[32]=2,	-- depth:1
[37]=7,	-- depth:1
[34]=4,	-- depth:1
[35]=5,	-- depth:1
[36]=6,	-- depth:1
[27]=7,	-- depth:1
[38]=8,	-- depth:1
[33]=3,	-- depth:1
[26]=6,	-- depth:1
[20]=10,	-- depth:1
[24]=4,	-- depth:1
[23]=3,	-- depth:1
[22]=2,	-- depth:1
[39]=9,	-- depth:1
[19]=9,	-- depth:1
[18]=8,	-- depth:1
[17]=7,	-- depth:1
[16]=6,	-- depth:1
[15]=5,	-- depth:1
[14]=4,	-- depth:1
[13]=3,	-- depth:1
[12]=2,	-- depth:1
[25]=5,	-- depth:1
[40]=10,	-- depth:1
},
new_consume={
{},
{sixiang_type=2,single_lotto_consume=item_table[25],multi_lotto_consume=item_table[26],multi_lotto_spconsume=item_table[27],},
{sixiang_type=3,single_lotto_consume=item_table[28],multi_lotto_consume=item_table[29],multi_lotto_spconsume=item_table[30],},
{sixiang_type=4,single_lotto_consume=item_table[31],multi_lotto_consume=item_table[32],multi_lotto_spconsume=item_table[33],}
},

new_consume_meta_table_map={
},
first_single_lotto={
{},
{first_single_lotto_reward=2,reward_item=item_table[34],},
{first_single_lotto_reward=3,reward_item=item_table[35],},
{first_single_lotto_reward=4,reward_item=item_table[36],},
{first_single_lotto_reward=5,reward_item=item_table[37],},
{first_single_lotto_reward=6,reward_item=item_table[38],},
{first_single_lotto_reward=7,reward_item=item_table[39],},
{first_single_lotto_reward=8,reward_item=item_table[40],},
{first_single_lotto_reward=9,reward_item=item_table[41],},
{first_single_lotto_reward=10,reward_item=item_table[42],},
{first_single_lotto_reward=11,reward_item=item_table[43],},
{first_single_lotto_reward=12,reward_item=item_table[44],},
{cycle=1,reward_item=item_table[45],},
{cycle=1,reward_item=item_table[46],},
{cycle=1,reward_item=item_table[47],},
{cycle=1,reward_item=item_table[48],},
{cycle=1,reward_item=item_table[49],},
{cycle=1,reward_item=item_table[50],},
{cycle=1,reward_item=item_table[51],},
{cycle=1,reward_item=item_table[52],},
{cycle=1,reward_item=item_table[53],},
{cycle=1,reward_item=item_table[54],},
{cycle=1,reward_item=item_table[55],},
{cycle=1,reward_item=item_table[56],},
{cycle=2,reward_item=item_table[57],},
{cycle=2,reward_item=item_table[58],},
{cycle=2,reward_item=item_table[59],},
{cycle=2,reward_item=item_table[60],},
{cycle=2,reward_item=item_table[61],},
{cycle=2,reward_item=item_table[62],},
{cycle=2,reward_item=item_table[63],},
{cycle=2,reward_item=item_table[64],},
{cycle=2,reward_item=item_table[65],},
{cycle=2,reward_item=item_table[66],},
{cycle=2,reward_item=item_table[67],},
{cycle=2,reward_item=item_table[68],},
{cycle=3,reward_item=item_table[69],},
{cycle=3,reward_item=item_table[70],},
{cycle=3,reward_item=item_table[71],},
{cycle=3,reward_item=item_table[72],},
{cycle=3,reward_item=item_table[73],},
{cycle=3,reward_item=item_table[74],},
{cycle=3,reward_item=item_table[75],},
{cycle=3,reward_item=item_table[76],},
{cycle=3,reward_item=item_table[77],},
{cycle=3,reward_item=item_table[78],},
{cycle=3,reward_item=item_table[79],},
{cycle=3,reward_item=item_table[80],},
{cycle=4,reward_item=item_table[81],},
{cycle=4,reward_item=item_table[82],},
{cycle=4,reward_item=item_table[83],},
{cycle=4,reward_item=item_table[84],},
{cycle=4,reward_item=item_table[85],},
{cycle=4,reward_item=item_table[86],},
{cycle=4,reward_item=item_table[87],},
{cycle=4,reward_item=item_table[88],},
{cycle=4,reward_item=item_table[89],},
{cycle=4,reward_item=item_table[90],},
{cycle=4,reward_item=item_table[91],},
{cycle=4,reward_item=item_table[92],},
{cycle=5,reward_item=item_table[93],},
{cycle=5,reward_item=item_table[94],},
{cycle=5,reward_item=item_table[95],},
{cycle=5,reward_item=item_table[96],},
{cycle=5,reward_item=item_table[97],},
{cycle=5,reward_item=item_table[98],},
{cycle=5,reward_item=item_table[99],},
{cycle=5,reward_item=item_table[100],},
{cycle=5,reward_item=item_table[101],},
{cycle=5,reward_item=item_table[102],},
{cycle=5,reward_item=item_table[103],},
{cycle=5,reward_item=item_table[104],}
},

first_single_lotto_meta_table_map={
[53]=5,	-- depth:1
[54]=6,	-- depth:1
[52]=4,	-- depth:1
[46]=10,	-- depth:1
[50]=2,	-- depth:1
[48]=12,	-- depth:1
[47]=11,	-- depth:1
[55]=7,	-- depth:1
[51]=3,	-- depth:1
[56]=8,	-- depth:1
[63]=3,	-- depth:1
[58]=10,	-- depth:1
[59]=11,	-- depth:1
[60]=12,	-- depth:1
[62]=2,	-- depth:1
[45]=9,	-- depth:1
[64]=4,	-- depth:1
[65]=5,	-- depth:1
[66]=6,	-- depth:1
[67]=7,	-- depth:1
[68]=8,	-- depth:1
[69]=9,	-- depth:1
[70]=10,	-- depth:1
[57]=9,	-- depth:1
[44]=8,	-- depth:1
[36]=12,	-- depth:1
[42]=6,	-- depth:1
[14]=2,	-- depth:1
[15]=3,	-- depth:1
[16]=4,	-- depth:1
[17]=5,	-- depth:1
[18]=6,	-- depth:1
[19]=7,	-- depth:1
[20]=8,	-- depth:1
[21]=9,	-- depth:1
[22]=10,	-- depth:1
[23]=11,	-- depth:1
[24]=12,	-- depth:1
[26]=2,	-- depth:1
[43]=7,	-- depth:1
[27]=3,	-- depth:1
[29]=5,	-- depth:1
[30]=6,	-- depth:1
[31]=7,	-- depth:1
[32]=8,	-- depth:1
[33]=9,	-- depth:1
[34]=10,	-- depth:1
[35]=11,	-- depth:1
[71]=11,	-- depth:1
[38]=2,	-- depth:1
[39]=3,	-- depth:1
[40]=4,	-- depth:1
[41]=5,	-- depth:1
[28]=4,	-- depth:1
[72]=12,	-- depth:1
},
first_multi_lotto_group={
{},
{first_multi_lotto_group=2,group_count=9,},
{cycle=1,},
{cycle=1,},
{cycle=2,},
{cycle=2,},
{cycle=3,},
{cycle=3,},
{cycle=4,},
{cycle=4,},
{cycle=5,},
{cycle=5,}
},

first_multi_lotto_group_meta_table_map={
[4]=2,	-- depth:1
[6]=4,	-- depth:2
[8]=6,	-- depth:3
[10]=8,	-- depth:4
[12]=10,	-- depth:5
},
first_multi_lotto={
{},
{first_single_lotto_id=2,reward_item=item_table[34],},
{first_single_lotto_id=3,reward_item=item_table[35],},
{first_single_lotto_id=4,reward_item=item_table[36],},
{first_single_lotto_id=5,reward_item=item_table[37],},
{first_multi_lotto_group=2,reward_item=item_table[38],reward_type=2,broadcast=2,},
{first_single_lotto_id=2,reward_item=item_table[39],},
{first_single_lotto_id=3,reward_item=item_table[40],},
{first_single_lotto_id=4,reward_item=item_table[41],},
{first_single_lotto_id=5,reward_item=item_table[42],},
{cycle=1,reward_item=item_table[43],},
{cycle=1,reward_item=item_table[44],},
{cycle=1,reward_item=item_table[45],},
{cycle=1,reward_item=item_table[46],},
{cycle=1,reward_item=item_table[47],},
{cycle=1,reward_item=item_table[48],},
{first_single_lotto_id=2,reward_item=item_table[49],},
{first_single_lotto_id=3,reward_item=item_table[50],},
{first_single_lotto_id=4,reward_item=item_table[51],},
{first_single_lotto_id=5,reward_item=item_table[52],},
{cycle=2,reward_item=item_table[53],},
{cycle=2,reward_item=item_table[54],},
{cycle=2,reward_item=item_table[55],},
{cycle=2,reward_item=item_table[56],},
{cycle=2,reward_item=item_table[57],},
{cycle=2,reward_item=item_table[58],},
{first_single_lotto_id=2,reward_item=item_table[59],},
{first_single_lotto_id=3,reward_item=item_table[60],},
{first_single_lotto_id=4,reward_item=item_table[61],},
{first_single_lotto_id=5,reward_item=item_table[62],},
{cycle=3,reward_item=item_table[64],},
{cycle=3,reward_item=item_table[65],},
{cycle=3,reward_item=item_table[66],},
{cycle=3,reward_item=item_table[67],},
{cycle=3,reward_item=item_table[68],},
{cycle=3,reward_item=item_table[69],},
{first_single_lotto_id=2,reward_item=item_table[70],},
{first_single_lotto_id=3,reward_item=item_table[71],},
{first_single_lotto_id=4,reward_item=item_table[72],},
{cycle=3,reward_item=item_table[73],},
{cycle=4,reward_item=item_table[74],},
{cycle=4,reward_item=item_table[75],},
{cycle=4,reward_item=item_table[76],},
{cycle=4,reward_item=item_table[77],},
{cycle=4,reward_item=item_table[78],},
{cycle=4,reward_item=item_table[79],},
{first_single_lotto_id=2,reward_item=item_table[80],},
{first_single_lotto_id=3,reward_item=item_table[81],},
{first_single_lotto_id=4,reward_item=item_table[82],},
{first_single_lotto_id=5,reward_item=item_table[83],},
{cycle=5,reward_item=item_table[84],},
{cycle=5,reward_item=item_table[85],},
{cycle=5,reward_item=item_table[86],},
{cycle=5,reward_item=item_table[87],},
{cycle=5,reward_item=item_table[88],},
{cycle=5,reward_item=item_table[89],},
{first_single_lotto_id=2,reward_item=item_table[90],},
{first_single_lotto_id=3,reward_item=item_table[91],},
{first_single_lotto_id=4,reward_item=item_table[92],},
{first_single_lotto_id=5,reward_item=item_table[93],}
},

first_multi_lotto_meta_table_map={
[43]=3,	-- depth:1
[42]=2,	-- depth:1
[35]=5,	-- depth:1
[34]=4,	-- depth:1
[33]=3,	-- depth:1
[32]=2,	-- depth:1
[52]=2,	-- depth:1
[53]=3,	-- depth:1
[54]=4,	-- depth:1
[25]=5,	-- depth:1
[44]=4,	-- depth:1
[24]=4,	-- depth:1
[23]=3,	-- depth:1
[22]=2,	-- depth:1
[55]=5,	-- depth:1
[15]=5,	-- depth:1
[14]=4,	-- depth:1
[13]=3,	-- depth:1
[12]=2,	-- depth:1
[45]=5,	-- depth:1
[7]=6,	-- depth:1
[8]=6,	-- depth:1
[9]=6,	-- depth:1
[36]=6,	-- depth:1
[10]=6,	-- depth:1
[56]=6,	-- depth:1
[26]=6,	-- depth:1
[16]=6,	-- depth:1
[46]=6,	-- depth:1
[50]=46,	-- depth:2
[49]=46,	-- depth:2
[48]=46,	-- depth:2
[47]=46,	-- depth:2
[58]=56,	-- depth:2
[57]=56,	-- depth:2
[30]=26,	-- depth:2
[39]=36,	-- depth:2
[38]=36,	-- depth:2
[37]=36,	-- depth:2
[59]=56,	-- depth:2
[29]=26,	-- depth:2
[28]=26,	-- depth:2
[27]=26,	-- depth:2
[20]=16,	-- depth:2
[19]=16,	-- depth:2
[18]=16,	-- depth:2
[17]=16,	-- depth:2
[40]=10,	-- depth:2
[60]=56,	-- depth:2
},
special_multi_lotto_group={
{},
{special_multi_lotto_group=2,special_group_count=9,},
{cycle=1,},
{cycle=1,},
{cycle=2,},
{cycle=2,},
{cycle=3,},
{cycle=3,},
{cycle=4,},
{cycle=4,},
{cycle=5,},
{cycle=5,}
},

special_multi_lotto_group_meta_table_map={
[4]=2,	-- depth:1
[6]=4,	-- depth:2
[8]=6,	-- depth:3
[10]=8,	-- depth:4
[12]=10,	-- depth:5
},
special_multi_lotto={
{special_multi_lotto_group=1,reward_type=1,broadcast=1,},
{special_single_lotto_id=2,reward_item=item_table[34],},
{special_single_lotto_id=3,reward_item=item_table[35],},
{special_single_lotto_id=4,reward_item=item_table[36],},
{special_single_lotto_id=5,reward_item=item_table[37],},
{special_single_lotto_id=6,reward_item=item_table[38],},
{special_single_lotto_id=7,reward_item=item_table[39],},
{special_single_lotto_id=8,reward_item=item_table[40],},
{special_single_lotto_id=9,reward_item=item_table[41],},
{special_single_lotto_id=10,reward_item=item_table[42],},
{special_single_lotto_id=11,reward_item=item_table[43],},
{special_single_lotto_id=12,reward_item=item_table[44],reward_type=1,broadcast=1,},
{special_single_lotto_id=13,reward_item=item_table[45],},
{special_single_lotto_id=14,reward_item=item_table[46],},
{special_single_lotto_id=15,reward_item=item_table[47],},
{special_single_lotto_id=16,reward_item=item_table[48],},
{special_single_lotto_id=17,reward_item=item_table[49],},
{special_single_lotto_id=18,reward_item=item_table[50],},
{special_single_lotto_id=19,reward_item=item_table[51],},
{special_single_lotto_id=20,reward_item=item_table[52],},
{special_single_lotto_id=21,reward_item=item_table[53],},
{special_single_lotto_id=22,reward_item=item_table[54],},
{special_single_lotto_id=23,reward_item=item_table[55],},
{special_single_lotto_id=24,reward_item=item_table[56],},
{special_single_lotto_id=25,reward_item=item_table[57],},
{special_single_lotto_id=26,reward_item=item_table[58],},
{special_single_lotto_id=27,reward_item=item_table[59],},
{special_single_lotto_id=28,reward_item=item_table[60],},
{special_single_lotto_id=29,reward_item=item_table[61],},
{special_single_lotto_id=30,reward_item=item_table[62],},
{special_single_lotto_id=31,reward_item=item_table[63],},
{special_single_lotto_id=32,reward_item=item_table[64],},
{special_single_lotto_id=33,reward_item=item_table[65],},
{special_single_lotto_id=34,reward_item=item_table[66],},
{special_single_lotto_id=35,reward_item=item_table[67],},
{special_single_lotto_id=36,reward_item=item_table[68],},
{special_single_lotto_id=37,reward_item=item_table[69],},
{special_single_lotto_id=38,reward_item=item_table[70],},
{special_single_lotto_id=39,reward_item=item_table[71],},
{special_single_lotto_id=40,reward_item=item_table[72],},
{special_single_lotto_id=41,reward_item=item_table[73],},
{special_single_lotto_id=42,reward_item=item_table[74],},
{special_single_lotto_id=43,reward_item=item_table[75],},
{cycle=1,reward_item=item_table[76],},
{special_single_lotto_id=2,reward_item=item_table[77],},
{special_single_lotto_id=3,reward_item=item_table[78],},
{cycle=1,reward_item=item_table[79],},
{special_single_lotto_id=5,reward_item=item_table[80],},
{special_single_lotto_id=6,reward_item=item_table[81],},
{special_single_lotto_id=7,reward_item=item_table[82],},
{special_single_lotto_id=8,reward_item=item_table[83],},
{special_single_lotto_id=9,reward_item=item_table[84],},
{special_single_lotto_id=10,reward_item=item_table[85],},
{special_single_lotto_id=11,reward_item=item_table[86],},
{cycle=1,reward_item=item_table[87],},
{cycle=1,reward_item=item_table[88],},
{cycle=1,reward_item=item_table[89],},
{cycle=1,reward_item=item_table[90],},
{cycle=1,reward_item=item_table[91],},
{cycle=1,reward_item=item_table[92],},
{cycle=1,reward_item=item_table[93],},
{cycle=1,reward_item=item_table[94],},
{cycle=1,reward_item=item_table[95],},
{cycle=1,reward_item=item_table[96],},
{cycle=1,reward_item=item_table[97],},
{cycle=1,reward_item=item_table[98],},
{cycle=1,reward_item=item_table[99],},
{cycle=1,reward_item=item_table[100],},
{cycle=1,reward_item=item_table[101],},
{cycle=1,reward_item=item_table[102],},
{cycle=1,reward_item=item_table[103],},
{cycle=1,reward_item=item_table[104],},
{cycle=1,reward_item=item_table[105],},
{cycle=1,reward_item=item_table[106],},
{cycle=1,reward_item=item_table[107],},
{cycle=1,reward_item=item_table[108],},
{cycle=1,reward_item=item_table[109],},
{cycle=1,reward_item=item_table[110],},
{cycle=1,reward_item=item_table[111],},
{cycle=1,reward_item=item_table[112],},
{cycle=1,reward_item=item_table[113],},
{cycle=1,reward_item=item_table[114],},
{cycle=1,reward_item=item_table[115],},
{cycle=1,reward_item=item_table[116],},
{cycle=1,reward_item=item_table[117],},
{cycle=1,reward_item=item_table[118],},
{cycle=2,reward_item=item_table[119],},
{cycle=2,reward_item=item_table[120],},
{special_single_lotto_id=3,reward_item=item_table[121],},
{cycle=2,reward_item=item_table[122],},
{special_single_lotto_id=5,reward_item=item_table[123],},
{special_single_lotto_id=6,reward_item=item_table[124],},
{cycle=2,reward_item=item_table[125],},
{cycle=2,reward_item=item_table[126],},
{cycle=2,reward_item=item_table[127],},
{cycle=2,reward_item=item_table[128],},
{cycle=2,reward_item=item_table[129],},
{cycle=2,reward_item=item_table[130],},
{special_single_lotto_id=13,reward_item=item_table[131],},
{special_single_lotto_id=14,reward_item=item_table[132],},
{special_single_lotto_id=15,reward_item=item_table[133],},
{special_single_lotto_id=16,reward_item=item_table[134],},
{cycle=2,reward_item=item_table[135],},
{cycle=2,reward_item=item_table[136],},
{cycle=2,reward_item=item_table[137],},
{cycle=2,reward_item=item_table[138],},
{cycle=2,reward_item=item_table[139],},
{cycle=2,reward_item=item_table[140],},
{cycle=2,reward_item=item_table[141],},
{cycle=2,reward_item=item_table[142],},
{cycle=2,reward_item=item_table[143],},
{cycle=2,reward_item=item_table[144],},
{cycle=2,reward_item=item_table[145],},
{cycle=2,reward_item=item_table[146],},
{cycle=2,reward_item=item_table[147],},
{cycle=2,reward_item=item_table[148],},
{cycle=2,reward_item=item_table[149],},
{cycle=2,reward_item=item_table[150],},
{cycle=2,reward_item=item_table[151],},
{cycle=2,reward_item=item_table[152],},
{cycle=2,reward_item=item_table[153],},
{cycle=2,reward_item=item_table[154],},
{cycle=2,reward_item=item_table[155],},
{cycle=2,reward_item=item_table[156],},
{cycle=2,reward_item=item_table[157],},
{cycle=2,reward_item=item_table[158],},
{cycle=2,reward_item=item_table[159],},
{cycle=2,reward_item=item_table[160],},
{cycle=2,reward_item=item_table[161],},
{cycle=3,reward_item=item_table[162],},
{cycle=3,reward_item=item_table[163],},
{special_single_lotto_id=3,reward_item=item_table[164],},
{cycle=3,reward_item=item_table[165],},
{special_single_lotto_id=5,reward_item=item_table[166],},
{special_single_lotto_id=6,reward_item=item_table[167],},
{special_single_lotto_id=7,reward_item=item_table[168],},
{special_single_lotto_id=8,reward_item=item_table[169],},
{cycle=3,reward_item=item_table[170],},
{cycle=3,reward_item=item_table[171],},
{cycle=3,reward_item=item_table[172],},
{cycle=3,reward_item=item_table[173],},
{special_single_lotto_id=13,reward_item=item_table[174],},
{special_single_lotto_id=14,reward_item=item_table[175],},
{special_single_lotto_id=15,reward_item=item_table[176],},
{special_single_lotto_id=16,reward_item=item_table[177],},
{cycle=3,reward_item=item_table[178],},
{cycle=3,reward_item=item_table[179],},
{cycle=3,reward_item=item_table[180],},
{cycle=3,reward_item=item_table[181],},
{cycle=3,reward_item=item_table[182],},
{cycle=3,reward_item=item_table[183],},
{cycle=3,reward_item=item_table[184],},
{cycle=3,reward_item=item_table[185],},
{cycle=3,reward_item=item_table[186],},
{cycle=3,reward_item=item_table[187],},
{cycle=3,reward_item=item_table[188],},
{cycle=3,reward_item=item_table[189],},
{cycle=3,reward_item=item_table[190],},
{cycle=3,reward_item=item_table[191],},
{cycle=3,reward_item=item_table[192],},
{cycle=3,reward_item=item_table[193],},
{cycle=3,reward_item=item_table[194],},
{cycle=3,reward_item=item_table[195],},
{cycle=3,reward_item=item_table[196],},
{cycle=3,reward_item=item_table[197],},
{cycle=3,reward_item=item_table[198],},
{cycle=3,reward_item=item_table[199],},
{cycle=3,reward_item=item_table[200],},
{cycle=3,reward_item=item_table[201],},
{cycle=3,reward_item=item_table[202],},
{cycle=3,reward_item=item_table[203],},
{cycle=3,reward_item=item_table[204],},
{cycle=4,reward_item=item_table[205],},
{cycle=4,reward_item=item_table[206],},
{cycle=4,reward_item=item_table[207],},
{cycle=4,reward_item=item_table[208],},
{special_single_lotto_id=5,reward_item=item_table[209],},
{special_single_lotto_id=6,reward_item=item_table[210],},
{special_single_lotto_id=7,reward_item=item_table[211],},
{special_single_lotto_id=8,reward_item=item_table[212],},
{special_single_lotto_id=9,reward_item=item_table[213],},
{cycle=4,reward_item=item_table[214],},
{special_single_lotto_id=11,reward_item=item_table[215],},
{cycle=4,reward_item=item_table[216],},
{special_single_lotto_id=13,reward_item=item_table[217],},
{special_single_lotto_id=14,reward_item=item_table[218],},
{special_single_lotto_id=15,reward_item=item_table[219],},
{cycle=4,reward_item=item_table[220],},
{special_single_lotto_id=17,reward_item=item_table[221],},
{cycle=4,reward_item=item_table[222],},
{cycle=4,reward_item=item_table[223],},
{cycle=4,reward_item=item_table[224],},
{cycle=4,reward_item=item_table[225],},
{cycle=4,reward_item=item_table[226],},
{cycle=4,reward_item=item_table[227],},
{cycle=4,reward_item=item_table[228],},
{cycle=4,reward_item=item_table[229],},
{cycle=4,reward_item=item_table[230],},
{cycle=4,reward_item=item_table[231],},
{cycle=4,reward_item=item_table[232],},
{cycle=4,reward_item=item_table[233],},
{cycle=4,reward_item=item_table[234],},
{cycle=4,reward_item=item_table[235],},
{cycle=4,reward_item=item_table[236],},
{cycle=4,reward_item=item_table[237],},
{cycle=4,reward_item=item_table[238],},
{cycle=4,reward_item=item_table[239],},
{cycle=4,reward_item=item_table[240],},
{cycle=4,reward_item=item_table[241],},
{cycle=4,reward_item=item_table[242],},
{cycle=4,reward_item=item_table[243],},
{cycle=4,reward_item=item_table[244],},
{cycle=4,reward_item=item_table[245],},
{cycle=4,reward_item=item_table[246],},
{cycle=4,reward_item=item_table[247],},
{cycle=5,reward_item=item_table[248],},
{special_single_lotto_id=2,reward_item=item_table[249],},
{special_single_lotto_id=3,reward_item=item_table[250],},
{special_single_lotto_id=4,reward_item=item_table[251],},
{special_single_lotto_id=5,reward_item=item_table[252],},
{special_single_lotto_id=6,reward_item=item_table[253],},
{cycle=5,reward_item=item_table[254],},
{cycle=5,reward_item=item_table[255],},
{cycle=5,reward_item=item_table[256],},
{cycle=5,reward_item=item_table[257],},
{cycle=5,reward_item=item_table[258],},
{cycle=5,reward_item=item_table[259],},
{cycle=5,reward_item=item_table[260],},
{cycle=5,reward_item=item_table[261],},
{cycle=5,reward_item=item_table[262],},
{cycle=5,reward_item=item_table[263],},
{cycle=5,reward_item=item_table[264],},
{cycle=5,reward_item=item_table[265],},
{cycle=5,reward_item=item_table[266],},
{cycle=5,reward_item=item_table[267],},
{cycle=5,reward_item=item_table[268],},
{cycle=5,reward_item=item_table[269],},
{cycle=5,reward_item=item_table[270],},
{cycle=5,reward_item=item_table[271],},
{cycle=5,reward_item=item_table[272],},
{cycle=5,reward_item=item_table[273],},
{cycle=5,reward_item=item_table[274],},
{cycle=5,reward_item=item_table[275],},
{cycle=5,reward_item=item_table[276],},
{cycle=5,reward_item=item_table[277],},
{cycle=5,reward_item=item_table[278],},
{cycle=5,reward_item=item_table[279],},
{cycle=5,reward_item=item_table[280],},
{cycle=5,reward_item=item_table[281],},
{cycle=5,reward_item=item_table[282],},
{cycle=5,reward_item=item_table[283],},
{cycle=5,reward_item=item_table[284],},
{cycle=5,reward_item=item_table[285],},
{cycle=5,reward_item=item_table[286],},
{cycle=5,reward_item=item_table[287],},
{cycle=5,reward_item=item_table[288],},
{cycle=5,reward_item=item_table[289],},
{cycle=5,reward_item=item_table[290],}
},

special_multi_lotto_meta_table_map={
[158]=29,	-- depth:1
[159]=30,	-- depth:1
[193]=21,	-- depth:1
[192]=20,	-- depth:1
[191]=19,	-- depth:1
[190]=18,	-- depth:1
[172]=43,	-- depth:1
[171]=42,	-- depth:1
[170]=41,	-- depth:1
[160]=31,	-- depth:1
[168]=39,	-- depth:1
[167]=38,	-- depth:1
[166]=37,	-- depth:1
[165]=36,	-- depth:1
[164]=35,	-- depth:1
[163]=34,	-- depth:1
[162]=33,	-- depth:1
[161]=32,	-- depth:1
[157]=28,	-- depth:1
[169]=40,	-- depth:1
[156]=27,	-- depth:1
[154]=25,	-- depth:1
[119]=33,	-- depth:1
[120]=34,	-- depth:1
[121]=35,	-- depth:1
[122]=36,	-- depth:1
[123]=37,	-- depth:1
[124]=38,	-- depth:1
[125]=39,	-- depth:1
[126]=40,	-- depth:1
[127]=41,	-- depth:1
[128]=42,	-- depth:1
[257]=42,	-- depth:1
[147]=18,	-- depth:1
[148]=19,	-- depth:1
[149]=20,	-- depth:1
[150]=21,	-- depth:1
[151]=22,	-- depth:1
[194]=22,	-- depth:1
[152]=23,	-- depth:1
[153]=24,	-- depth:1
[155]=26,	-- depth:1
[195]=23,	-- depth:1
[209]=37,	-- depth:1
[197]=25,	-- depth:1
[238]=23,	-- depth:1
[239]=24,	-- depth:1
[240]=25,	-- depth:1
[241]=26,	-- depth:1
[242]=27,	-- depth:1
[243]=28,	-- depth:1
[244]=29,	-- depth:1
[245]=30,	-- depth:1
[246]=31,	-- depth:1
[247]=32,	-- depth:1
[248]=33,	-- depth:1
[249]=34,	-- depth:1
[250]=35,	-- depth:1
[251]=36,	-- depth:1
[252]=37,	-- depth:1
[253]=38,	-- depth:1
[254]=39,	-- depth:1
[255]=40,	-- depth:1
[256]=41,	-- depth:1
[237]=22,	-- depth:1
[196]=24,	-- depth:1
[236]=21,	-- depth:1
[234]=19,	-- depth:1
[198]=26,	-- depth:1
[199]=27,	-- depth:1
[200]=28,	-- depth:1
[201]=29,	-- depth:1
[202]=30,	-- depth:1
[203]=31,	-- depth:1
[204]=32,	-- depth:1
[205]=33,	-- depth:1
[206]=34,	-- depth:1
[207]=35,	-- depth:1
[208]=36,	-- depth:1
[118]=32,	-- depth:1
[210]=38,	-- depth:1
[211]=39,	-- depth:1
[212]=40,	-- depth:1
[213]=41,	-- depth:1
[214]=42,	-- depth:1
[215]=43,	-- depth:1
[233]=18,	-- depth:1
[235]=20,	-- depth:1
[117]=31,	-- depth:1
[129]=43,	-- depth:1
[115]=29,	-- depth:1
[84]=41,	-- depth:1
[83]=40,	-- depth:1
[82]=39,	-- depth:1
[81]=38,	-- depth:1
[80]=37,	-- depth:1
[79]=36,	-- depth:1
[78]=35,	-- depth:1
[116]=30,	-- depth:1
[77]=34,	-- depth:1
[76]=33,	-- depth:1
[75]=32,	-- depth:1
[74]=31,	-- depth:1
[73]=30,	-- depth:1
[72]=29,	-- depth:1
[71]=28,	-- depth:1
[70]=27,	-- depth:1
[69]=26,	-- depth:1
[68]=25,	-- depth:1
[67]=24,	-- depth:1
[66]=23,	-- depth:1
[65]=22,	-- depth:1
[64]=21,	-- depth:1
[63]=20,	-- depth:1
[62]=19,	-- depth:1
[61]=18,	-- depth:1
[85]=42,	-- depth:1
[86]=43,	-- depth:1
[258]=43,	-- depth:1
[110]=24,	-- depth:1
[104]=18,	-- depth:1
[107]=21,	-- depth:1
[108]=22,	-- depth:1
[109]=23,	-- depth:1
[111]=25,	-- depth:1
[112]=26,	-- depth:1
[113]=27,	-- depth:1
[114]=28,	-- depth:1
[105]=19,	-- depth:1
[106]=20,	-- depth:1
[11]=12,	-- depth:1
[9]=12,	-- depth:1
[10]=12,	-- depth:1
[8]=12,	-- depth:1
[7]=12,	-- depth:1
[5]=12,	-- depth:1
[6]=12,	-- depth:1
[17]=12,	-- depth:1
[16]=12,	-- depth:1
[15]=12,	-- depth:1
[14]=12,	-- depth:1
[13]=12,	-- depth:1
[4]=1,	-- depth:1
[2]=4,	-- depth:2
[146]=17,	-- depth:2
[56]=13,	-- depth:2
[60]=17,	-- depth:2
[59]=16,	-- depth:2
[58]=15,	-- depth:2
[57]=14,	-- depth:2
[3]=4,	-- depth:2
[55]=12,	-- depth:1
[216]=1,	-- depth:1
[53]=55,	-- depth:2
[44]=216,	-- depth:2
[50]=55,	-- depth:2
[51]=55,	-- depth:2
[232]=17,	-- depth:2
[231]=16,	-- depth:2
[230]=15,	-- depth:2
[54]=55,	-- depth:2
[229]=14,	-- depth:2
[226]=11,	-- depth:2
[225]=10,	-- depth:2
[224]=9,	-- depth:2
[223]=8,	-- depth:2
[222]=7,	-- depth:2
[52]=55,	-- depth:2
[228]=13,	-- depth:2
[227]=12,	-- depth:1
[103]=17,	-- depth:2
[139]=10,	-- depth:2
[138]=9,	-- depth:2
[130]=216,	-- depth:2
[140]=11,	-- depth:2
[141]=12,	-- depth:1
[142]=141,	-- depth:2
[143]=141,	-- depth:2
[144]=141,	-- depth:2
[145]=141,	-- depth:2
[87]=216,	-- depth:2
[93]=7,	-- depth:2
[94]=8,	-- depth:2
[95]=9,	-- depth:2
[96]=10,	-- depth:2
[97]=11,	-- depth:2
[98]=12,	-- depth:1
[99]=98,	-- depth:2
[100]=98,	-- depth:2
[101]=98,	-- depth:2
[102]=98,	-- depth:2
[137]=141,	-- depth:2
[173]=216,	-- depth:2
[188]=16,	-- depth:2
[182]=10,	-- depth:2
[184]=12,	-- depth:1
[185]=184,	-- depth:2
[181]=184,	-- depth:2
[180]=184,	-- depth:2
[186]=184,	-- depth:2
[187]=184,	-- depth:2
[179]=184,	-- depth:2
[189]=184,	-- depth:2
[136]=141,	-- depth:2
[183]=184,	-- depth:2
[89]=87,	-- depth:3
[135]=130,	-- depth:3
[134]=130,	-- depth:3
[133]=4,	-- depth:2
[132]=133,	-- depth:3
[92]=87,	-- depth:3
[91]=87,	-- depth:3
[90]=4,	-- depth:2
[176]=4,	-- depth:2
[177]=176,	-- depth:3
[45]=44,	-- depth:3
[46]=44,	-- depth:3
[47]=4,	-- depth:2
[48]=47,	-- depth:3
[49]=47,	-- depth:3
[178]=176,	-- depth:3
[217]=216,	-- depth:2
[218]=216,	-- depth:2
[219]=216,	-- depth:2
[220]=216,	-- depth:2
[221]=216,	-- depth:2
[174]=217,	-- depth:3
[175]=218,	-- depth:3
[88]=217,	-- depth:3
[131]=217,	-- depth:3
},
reward={
{reward_type=1,broadcast=1,},
{reward_id=2,reward_item=item_table[34],},
{reward_id=3,reward_item=item_table[35],},
{reward_id=4,reward_item=item_table[36],},
{reward_id=5,reward_item=item_table[37],},
{reward_id=6,reward_item=item_table[38],},
{reward_id=7,reward_item=item_table[39],},
{reward_id=8,reward_item=item_table[40],},
{reward_id=9,reward_item=item_table[41],},
{reward_id=10,reward_item=item_table[42],},
{reward_id=11,reward_item=item_table[43],},
{reward_id=12,reward_item=item_table[44],},
{reward_id=13,reward_item=item_table[45],},
{reward_id=14,reward_item=item_table[46],},
{reward_id=15,reward_item=item_table[47],},
{reward_id=16,reward_item=item_table[48],},
{reward_id=17,reward_item=item_table[49],},
{reward_id=18,reward_item=item_table[50],},
{reward_id=19,reward_item=item_table[51],},
{reward_id=20,reward_item=item_table[52],},
{reward_id=21,reward_item=item_table[53],},
{reward_id=22,reward_item=item_table[54],},
{reward_id=23,reward_item=item_table[55],},
{reward_id=24,reward_item=item_table[56],},
{reward_id=25,reward_item=item_table[57],},
{reward_id=26,reward_item=item_table[58],},
{reward_id=27,reward_item=item_table[59],},
{reward_id=28,reward_item=item_table[60],},
{reward_id=29,reward_item=item_table[61],},
{reward_id=30,reward_item=item_table[62],},
{reward_id=31,reward_item=item_table[63],},
{reward_id=32,reward_item=item_table[64],},
{reward_id=33,reward_item=item_table[65],},
{reward_id=34,reward_item=item_table[66],},
{reward_id=35,reward_item=item_table[67],},
{reward_id=36,reward_item=item_table[68],},
{reward_id=37,reward_item=item_table[69],},
{reward_id=38,reward_item=item_table[70],},
{reward_id=39,reward_item=item_table[71],},
{reward_id=40,reward_item=item_table[72],},
{reward_id=41,reward_item=item_table[73],},
{reward_id=42,reward_item=item_table[74],},
{reward_id=43,reward_item=item_table[75],},
{reward_id=44,reward_item=item_table[76],},
{reward_id=45,reward_item=item_table[77],},
{reward_id=46,reward_item=item_table[78],},
{reward_id=47,reward_item=item_table[79],},
{sixiang_type=2,},
{sixiang_type=2,},
{sixiang_type=2,},
{sixiang_type=2,},
{sixiang_type=2,},
{sixiang_type=2,},
{sixiang_type=2,},
{sixiang_type=2,},
{sixiang_type=2,},
{sixiang_type=2,},
{sixiang_type=2,},
{sixiang_type=2,},
{sixiang_type=2,},
{sixiang_type=2,},
{sixiang_type=2,},
{sixiang_type=2,},
{sixiang_type=2,},
{sixiang_type=2,},
{sixiang_type=2,},
{sixiang_type=2,},
{sixiang_type=2,},
{sixiang_type=2,},
{sixiang_type=2,},
{sixiang_type=2,},
{sixiang_type=2,},
{sixiang_type=2,},
{sixiang_type=2,},
{sixiang_type=2,},
{sixiang_type=2,},
{sixiang_type=2,},
{sixiang_type=2,},
{sixiang_type=2,},
{sixiang_type=2,},
{sixiang_type=2,},
{sixiang_type=2,},
{sixiang_type=2,},
{sixiang_type=2,},
{sixiang_type=2,},
{sixiang_type=2,},
{sixiang_type=2,},
{sixiang_type=2,},
{sixiang_type=2,},
{sixiang_type=2,},
{sixiang_type=2,},
{sixiang_type=2,},
{sixiang_type=2,},
{sixiang_type=2,},
{sixiang_type=3,},
{sixiang_type=3,},
{sixiang_type=3,},
{sixiang_type=3,},
{sixiang_type=3,},
{sixiang_type=3,},
{sixiang_type=3,},
{sixiang_type=3,},
{sixiang_type=3,},
{sixiang_type=3,},
{sixiang_type=3,},
{sixiang_type=3,},
{sixiang_type=3,},
{sixiang_type=3,},
{sixiang_type=3,},
{sixiang_type=3,},
{sixiang_type=3,},
{sixiang_type=3,},
{sixiang_type=3,},
{sixiang_type=3,},
{sixiang_type=3,},
{sixiang_type=3,},
{sixiang_type=3,},
{sixiang_type=3,},
{sixiang_type=3,},
{sixiang_type=3,},
{sixiang_type=3,},
{sixiang_type=3,},
{sixiang_type=3,},
{sixiang_type=3,},
{sixiang_type=3,},
{sixiang_type=3,},
{sixiang_type=3,},
{sixiang_type=3,},
{sixiang_type=3,},
{sixiang_type=3,},
{sixiang_type=3,},
{sixiang_type=3,},
{sixiang_type=3,},
{sixiang_type=3,},
{sixiang_type=3,},
{sixiang_type=3,},
{sixiang_type=3,},
{sixiang_type=3,},
{sixiang_type=3,},
{sixiang_type=3,},
{sixiang_type=3,},
{sixiang_type=4,},
{sixiang_type=4,},
{sixiang_type=4,},
{sixiang_type=4,},
{sixiang_type=4,},
{sixiang_type=4,},
{sixiang_type=4,},
{sixiang_type=4,},
{sixiang_type=4,},
{sixiang_type=4,},
{sixiang_type=4,},
{sixiang_type=4,},
{sixiang_type=4,},
{sixiang_type=4,},
{sixiang_type=4,},
{sixiang_type=4,},
{sixiang_type=4,},
{sixiang_type=4,},
{sixiang_type=4,},
{sixiang_type=4,},
{sixiang_type=4,},
{sixiang_type=4,},
{sixiang_type=4,},
{sixiang_type=4,},
{sixiang_type=4,},
{sixiang_type=4,},
{sixiang_type=4,},
{sixiang_type=4,},
{sixiang_type=4,},
{sixiang_type=4,},
{sixiang_type=4,},
{sixiang_type=4,},
{sixiang_type=4,},
{sixiang_type=4,},
{sixiang_type=4,},
{sixiang_type=4,},
{sixiang_type=4,},
{sixiang_type=4,},
{sixiang_type=4,},
{sixiang_type=4,},
{sixiang_type=4,},
{sixiang_type=4,},
{sixiang_type=4,},
{sixiang_type=4,},
{sixiang_type=4,},
{sixiang_type=4,},
{sixiang_type=4,}
},

reward_meta_table_map={
[112]=18,	-- depth:1
[159]=112,	-- depth:2
[160]=19,	-- depth:1
[161]=20,	-- depth:1
[162]=21,	-- depth:1
[163]=22,	-- depth:1
[164]=23,	-- depth:1
[165]=24,	-- depth:1
[166]=25,	-- depth:1
[95]=1,	-- depth:1
[167]=26,	-- depth:1
[169]=28,	-- depth:1
[170]=29,	-- depth:1
[90]=43,	-- depth:1
[89]=42,	-- depth:1
[88]=41,	-- depth:1
[87]=40,	-- depth:1
[86]=39,	-- depth:1
[85]=38,	-- depth:1
[84]=37,	-- depth:1
[83]=36,	-- depth:1
[82]=35,	-- depth:1
[168]=27,	-- depth:1
[113]=160,	-- depth:2
[116]=163,	-- depth:2
[115]=162,	-- depth:2
[123]=170,	-- depth:2
[124]=30,	-- depth:1
[125]=31,	-- depth:1
[126]=32,	-- depth:1
[127]=33,	-- depth:1
[128]=34,	-- depth:1
[129]=82,	-- depth:2
[130]=83,	-- depth:2
[131]=84,	-- depth:2
[132]=85,	-- depth:2
[133]=86,	-- depth:2
[134]=87,	-- depth:2
[81]=128,	-- depth:2
[135]=88,	-- depth:2
[136]=89,	-- depth:2
[137]=90,	-- depth:2
[142]=95,	-- depth:2
[122]=169,	-- depth:2
[121]=168,	-- depth:2
[120]=167,	-- depth:2
[119]=166,	-- depth:2
[118]=165,	-- depth:2
[117]=164,	-- depth:2
[114]=161,	-- depth:2
[80]=127,	-- depth:2
[72]=119,	-- depth:3
[78]=125,	-- depth:2
[48]=142,	-- depth:3
[184]=137,	-- depth:3
[183]=136,	-- depth:3
[182]=135,	-- depth:3
[79]=126,	-- depth:2
[180]=133,	-- depth:3
[179]=132,	-- depth:3
[178]=131,	-- depth:3
[177]=130,	-- depth:3
[176]=129,	-- depth:3
[175]=81,	-- depth:3
[174]=80,	-- depth:3
[173]=79,	-- depth:3
[172]=78,	-- depth:3
[181]=134,	-- depth:3
[65]=159,	-- depth:3
[75]=122,	-- depth:3
[76]=123,	-- depth:3
[77]=124,	-- depth:2
[74]=121,	-- depth:3
[71]=118,	-- depth:3
[171]=77,	-- depth:3
[69]=116,	-- depth:3
[68]=115,	-- depth:3
[67]=114,	-- depth:3
[66]=113,	-- depth:3
[70]=117,	-- depth:3
[73]=120,	-- depth:3
[14]=1,	-- depth:1
[13]=14,	-- depth:2
[12]=14,	-- depth:2
[11]=14,	-- depth:2
[8]=14,	-- depth:2
[9]=14,	-- depth:2
[15]=14,	-- depth:2
[7]=14,	-- depth:2
[6]=14,	-- depth:2
[5]=14,	-- depth:2
[4]=14,	-- depth:2
[3]=14,	-- depth:2
[2]=14,	-- depth:2
[10]=14,	-- depth:2
[16]=14,	-- depth:2
[46]=14,	-- depth:2
[44]=14,	-- depth:2
[45]=14,	-- depth:2
[47]=14,	-- depth:2
[17]=14,	-- depth:2
[146]=5,	-- depth:3
[147]=6,	-- depth:3
[148]=7,	-- depth:3
[149]=8,	-- depth:3
[150]=9,	-- depth:3
[158]=17,	-- depth:3
[157]=16,	-- depth:3
[155]=14,	-- depth:2
[154]=13,	-- depth:3
[153]=12,	-- depth:3
[152]=11,	-- depth:3
[151]=10,	-- depth:3
[185]=44,	-- depth:3
[186]=45,	-- depth:3
[156]=15,	-- depth:3
[145]=4,	-- depth:3
[94]=47,	-- depth:3
[143]=2,	-- depth:3
[92]=186,	-- depth:4
[91]=185,	-- depth:4
[64]=158,	-- depth:4
[63]=157,	-- depth:4
[62]=156,	-- depth:4
[61]=155,	-- depth:3
[60]=154,	-- depth:4
[59]=153,	-- depth:4
[93]=46,	-- depth:3
[58]=152,	-- depth:4
[56]=150,	-- depth:4
[55]=149,	-- depth:4
[54]=148,	-- depth:4
[53]=147,	-- depth:4
[52]=146,	-- depth:4
[51]=145,	-- depth:4
[50]=3,	-- depth:3
[49]=143,	-- depth:4
[57]=151,	-- depth:4
[144]=50,	-- depth:4
[187]=93,	-- depth:4
[97]=144,	-- depth:5
[141]=94,	-- depth:4
[140]=187,	-- depth:5
[139]=92,	-- depth:5
[138]=91,	-- depth:5
[111]=64,	-- depth:5
[110]=63,	-- depth:5
[109]=62,	-- depth:5
[108]=61,	-- depth:4
[96]=49,	-- depth:5
[107]=60,	-- depth:5
[105]=58,	-- depth:5
[104]=57,	-- depth:5
[103]=56,	-- depth:5
[102]=55,	-- depth:5
[101]=54,	-- depth:5
[100]=53,	-- depth:5
[99]=52,	-- depth:5
[98]=51,	-- depth:5
[106]=59,	-- depth:5
[188]=141,	-- depth:5
},
group_multi_limit={
{},
{cycle=1,},
{cycle=2,},
{cycle=3,},
{cycle=4,},
{cycle=5,}
},

group_multi_limit_meta_table_map={
},
group_cycle_limit={
{},
{cycle=1,},
{cycle=2,},
{cycle=3,},
{cycle=4,},
{cycle=5,}
},

group_cycle_limit_meta_table_map={
},
pool_cycle={
{},
{cycle=1,},
{cycle=2,},
{cycle=3,},
{cycle=4,},
{cycle=5,}
},

pool_cycle_meta_table_map={
},
reward_cycle={
{},
{reward_id=6,},
{reward_id=7,},
{reward_id=8,},
{reward_id=9,},
{reward_id=10,},
{reward_id=11,},
{reward_id=12,},
{reward_id=13,},
{reward_id=14,},
{reward_id=15,},
{reward_id=16,},
{reward_id=17,},
{cycle=1,},
{cycle=1,},
{reward_id=7,},
{reward_id=8,},
{reward_id=9,},
{reward_id=10,},
{reward_id=11,},
{reward_id=12,},
{reward_id=13,},
{reward_id=14,},
{reward_id=15,},
{reward_id=16,},
{reward_id=17,},
{cycle=2,},
{cycle=2,},
{reward_id=7,},
{cycle=2,},
{reward_id=9,},
{reward_id=10,},
{reward_id=11,},
{reward_id=12,},
{reward_id=13,},
{reward_id=14,},
{reward_id=15,},
{reward_id=16,},
{cycle=2,},
{cycle=3,},
{reward_id=6,},
{reward_id=7,},
{reward_id=8,},
{reward_id=9,},
{reward_id=10,},
{cycle=3,},
{cycle=3,},
{cycle=3,},
{cycle=3,},
{reward_id=15,},
{reward_id=16,},
{reward_id=17,},
{cycle=4,},
{reward_id=6,},
{reward_id=7,},
{reward_id=8,},
{cycle=4,},
{reward_id=10,},
{reward_id=11,},
{reward_id=12,},
{reward_id=13,},
{cycle=4,},
{reward_id=15,},
{reward_id=16,},
{reward_id=17,},
{cycle=5,},
{reward_id=6,},
{cycle=5,},
{reward_id=8,},
{reward_id=9,},
{reward_id=10,},
{reward_id=11,},
{reward_id=12,},
{reward_id=13,},
{reward_id=14,},
{reward_id=15,},
{cycle=5,},
{reward_id=17,}
},

reward_cycle_meta_table_map={
[57]=5,	-- depth:1
[59]=57,	-- depth:2
[58]=59,	-- depth:3
[56]=58,	-- depth:4
[49]=10,	-- depth:1
[54]=56,	-- depth:5
[52]=49,	-- depth:2
[51]=52,	-- depth:3
[50]=51,	-- depth:4
[60]=54,	-- depth:6
[55]=60,	-- depth:7
[61]=55,	-- depth:8
[68]=55,	-- depth:8
[63]=61,	-- depth:9
[64]=63,	-- depth:10
[65]=64,	-- depth:11
[67]=68,	-- depth:9
[48]=61,	-- depth:9
[69]=67,	-- depth:10
[70]=69,	-- depth:11
[71]=70,	-- depth:12
[72]=71,	-- depth:13
[73]=72,	-- depth:14
[74]=73,	-- depth:15
[75]=74,	-- depth:16
[76]=75,	-- depth:17
[62]=75,	-- depth:17
[47]=73,	-- depth:15
[39]=65,	-- depth:12
[45]=47,	-- depth:16
[15]=67,	-- depth:10
[16]=15,	-- depth:11
[17]=16,	-- depth:12
[18]=17,	-- depth:13
[19]=18,	-- depth:14
[20]=19,	-- depth:15
[21]=20,	-- depth:16
[22]=21,	-- depth:17
[23]=22,	-- depth:18
[24]=23,	-- depth:19
[25]=24,	-- depth:20
[26]=25,	-- depth:21
[28]=15,	-- depth:11
[46]=20,	-- depth:16
[29]=28,	-- depth:12
[31]=29,	-- depth:13
[32]=31,	-- depth:14
[33]=32,	-- depth:15
[34]=33,	-- depth:16
[35]=34,	-- depth:17
[36]=35,	-- depth:18
[37]=36,	-- depth:19
[38]=37,	-- depth:20
[77]=38,	-- depth:21
[41]=46,	-- depth:17
[42]=41,	-- depth:18
[43]=42,	-- depth:19
[44]=43,	-- depth:20
[30]=43,	-- depth:20
[78]=77,	-- depth:22
},
exchange={
{exchange_num=400,},
{seq=2,goods=item_table[45],},
{seq=3,goods=item_table[53],},
{seq=4,goods=item_table[61],},
{seq=5,goods=item_table[69],},
{seq=6,goods=item_table[77],},
{seq=7,goods=item_table[181],},
{seq=8,goods=item_table[189],},
{seq=9,goods=item_table[197],},
{seq=10,goods=item_table[205],},
{seq=11,goods=item_table[213],},
{seq=12,goods=item_table[221],},
{seq=13,goods=item_table[291],},
{seq=14,goods=item_table[292],},
{seq=15,goods=item_table[293],},
{seq=16,goods=item_table[294],},
{seq=17,goods=item_table[295],},
{seq=18,goods=item_table[296],exchange_num=100,},
{seq=19,goods=item_table[297],},
{seq=20,goods=item_table[298],},
{seq=21,goods=item_table[299],},
{seq=22,goods=item_table[300],exchange_num=200,},
{seq=23,goods=item_table[301],},
{seq=24,goods=item_table[302],}
},

exchange_meta_table_map={
[19]=1,	-- depth:1
[17]=18,	-- depth:1
[16]=22,	-- depth:1
[12]=18,	-- depth:1
[23]=18,	-- depth:1
[11]=18,	-- depth:1
[10]=22,	-- depth:1
[7]=19,	-- depth:2
[6]=18,	-- depth:1
[5]=18,	-- depth:1
[4]=22,	-- depth:1
[13]=19,	-- depth:2
[24]=18,	-- depth:1
},
client_sub_act={
{name="act_name_1",open_param="SiXiang_ProbabilityUpView",},
{act_id=2,},
{act_id=3,},
{act_id=4,name="act_name_3",item_bg="a2_hlck_tedian_2",open_param="SiXiang_ZhenXuanView",},
{act_id=5,},
{act_id=6,name="act_name_4",item_bg="a2_hlck_tedian_3",open_param="SiXiang_TeHuiZhaoHuanView",},
{cycle=2,},
{cycle=2,},
{act_id=3,},
{cycle=2,},
{cycle=2,},
{cycle=2,},
{cycle=3,},
{cycle=3,},
{act_id=3,},
{act_id=4,},
{cycle=3,},
{cycle=3,},
{cycle=4,},
{act_id=2,},
{cycle=4,},
{cycle=4,},
{cycle=4,},
{cycle=4,},
{cycle=5,},
{act_id=2,},
{cycle=5,},
{cycle=5,},
{cycle=5,},
{cycle=5,}
},

client_sub_act_meta_table_map={
[27]=3,	-- depth:1
[26]=27,	-- depth:2
[21]=27,	-- depth:2
[20]=21,	-- depth:3
[14]=20,	-- depth:4
[15]=14,	-- depth:5
[8]=14,	-- depth:5
[9]=8,	-- depth:6
[7]=1,	-- depth:1
[13]=7,	-- depth:2
[25]=13,	-- depth:3
[19]=25,	-- depth:4
[5]=4,	-- depth:1
[28]=4,	-- depth:1
[24]=6,	-- depth:1
[23]=5,	-- depth:2
[10]=28,	-- depth:2
[18]=24,	-- depth:2
[17]=23,	-- depth:3
[16]=17,	-- depth:4
[29]=17,	-- depth:4
[12]=18,	-- depth:3
[11]=29,	-- depth:5
[22]=16,	-- depth:5
[30]=12,	-- depth:4
},
probability_up_show={
{cycle=1,},
{item_id=51105,},
{item_id=51205,},
{item_id=51305,},
{item_id=51405,},
{item_id=51505,},
{item_id=52005,},
{item_id=52105,},
{item_id=52205,},
{cycle=2,item_id=52305,},
{item_id=52405,},
{item_id=52505,},
{item_id=53005,},
{item_id=53105,},
{item_id=53205,},
{item_id=53305,},
{item_id=53405,},
{cycle=3,item_id=53505,},
{item_id=54005,},
{item_id=54105,},
{item_id=54205,},
{item_id=54305,},
{item_id=54405,},
{cycle=4,item_id=54505,},
{item_id=55005,},
{item_id=55105,},
{item_id=55205,},
{item_id=55305,},
{item_id=55405,},
{item_id=55505,},
{item_id=56005,},
{item_id=56105,}
},

probability_up_show_meta_table_map={
[2]=1,	-- depth:1
[3]=2,	-- depth:2
[4]=3,	-- depth:3
[5]=4,	-- depth:4
[6]=5,	-- depth:5
[7]=10,	-- depth:1
[23]=24,	-- depth:1
[22]=23,	-- depth:2
[9]=7,	-- depth:2
[21]=22,	-- depth:3
[19]=21,	-- depth:4
[17]=18,	-- depth:1
[8]=9,	-- depth:3
[15]=17,	-- depth:2
[14]=15,	-- depth:3
[13]=14,	-- depth:4
[12]=8,	-- depth:4
[11]=12,	-- depth:5
[20]=19,	-- depth:5
[16]=13,	-- depth:5
},
summon_interface={
{},
{sixiang_type=2,summon_panel_bg="sihun_beijing02",item_list="52036,52136,52236,52336,52436,52536",},
{sixiang_type=3,summon_panel_bg="sihun_beijing03",item_list="53036,53136,53236,53336,53436,53536",},
{sixiang_type=4,summon_panel_bg="sihun_beijing04",item_list="54036,54136,54236,54336,54436,54536",}
},

summon_interface_meta_table_map={
},
reward_pond_show={
{item_list={[0]={item_id=51005,num=1,is_bind=1,param0=5},[1]=item_table[38]},},
{title_name="青龙合鸣-飞龙",},
{title_name="青龙合鸣-木神",item_list={[0]=item_table[37],[1]=item_table[38]},},
{sixiang_type=2,title_name="白虎合鸣-迅虎",},
{title_name="白虎合鸣-袭虎",item_list={[0]=item_table[37],[1]=item_table[38]},},
{title_name="白虎合鸣-金神",},
{title_name="朱雀合鸣-火凤",item_list={[0]=item_table[37],[1]=item_table[38]},},
{sixiang_type=3,title_name="朱雀合鸣-离火",},
{title_name="朱雀合鸣-火神",},
{sixiang_type=4,title_name="玄武合鸣-不死",},
{title_name="玄武合鸣-玄冥",item_list={[0]=item_table[37],[1]=item_table[38]},},
{title_name="玄武合鸣-水神",}
},

reward_pond_show_meta_table_map={
[6]=4,	-- depth:1
[12]=10,	-- depth:1
[5]=4,	-- depth:1
[7]=8,	-- depth:1
[9]=7,	-- depth:2
[11]=10,	-- depth:1
},
item_random_desc={
{random_count=0.36,},
{number=2,item_id=51002,},
{number=3,item_id=51003,},
{number=4,item_id=51004,},
{number=5,item_id=51005,},
{number=6,item_id=51006,},
{number=7,item_id=51007,},
{number=8,item_id=51008,},
{number=9,item_id=51101,},
{number=10,item_id=51102,},
{number=11,item_id=51103,},
{number=12,item_id=51104,},
{number=13,item_id=51105,},
{number=14,item_id=51106,},
{number=15,item_id=51107,},
{number=16,item_id=51108,},
{number=17,item_id=51201,},
{number=18,item_id=51202,},
{number=19,item_id=51203,},
{number=20,item_id=51204,},
{number=21,item_id=51205,random_count=3.23,},
{number=22,item_id=51206,},
{number=23,item_id=51207,},
{number=24,item_id=51208,},
{number=25,item_id=51301,},
{number=26,item_id=51302,},
{number=27,item_id=51303,},
{number=28,item_id=51304,},
{number=29,item_id=51305,random_count=4.01,},
{number=30,item_id=51306,},
{number=31,item_id=51307,},
{number=32,item_id=51308,},
{number=33,item_id=51401,random_count=3.68,},
{number=34,item_id=51402,random_count=3.6,},
{number=35,item_id=51403,},
{number=36,item_id=51404,},
{number=37,item_id=51405,},
{number=38,item_id=51406,},
{number=39,item_id=51407,},
{number=40,item_id=51408,},
{number=41,item_id=51501,},
{number=42,item_id=51502,},
{number=43,item_id=51503,random_count=2.5,},
{number=44,item_id=51504,},
{number=45,item_id=51505,},
{number=46,item_id=51506,},
{number=47,item_id=51507,random_count=0.13,},
{sixiang_type=2,},
{sixiang_type=2,},
{sixiang_type=2,},
{sixiang_type=2,},
{sixiang_type=2,},
{sixiang_type=2,},
{sixiang_type=2,},
{sixiang_type=2,},
{sixiang_type=2,},
{sixiang_type=2,},
{sixiang_type=2,},
{sixiang_type=2,},
{sixiang_type=2,},
{sixiang_type=2,},
{sixiang_type=2,},
{sixiang_type=2,},
{sixiang_type=2,},
{sixiang_type=2,},
{sixiang_type=2,},
{sixiang_type=2,},
{sixiang_type=2,},
{sixiang_type=2,},
{sixiang_type=2,},
{sixiang_type=2,},
{sixiang_type=2,},
{sixiang_type=2,},
{sixiang_type=2,},
{sixiang_type=2,},
{sixiang_type=2,},
{sixiang_type=2,},
{sixiang_type=2,},
{sixiang_type=2,},
{sixiang_type=2,},
{sixiang_type=2,},
{sixiang_type=2,},
{sixiang_type=2,},
{sixiang_type=2,},
{sixiang_type=2,},
{sixiang_type=2,},
{sixiang_type=2,},
{sixiang_type=2,},
{sixiang_type=2,},
{sixiang_type=2,},
{sixiang_type=2,},
{sixiang_type=2,},
{sixiang_type=2,},
{sixiang_type=2,},
{sixiang_type=3,},
{sixiang_type=3,},
{sixiang_type=3,},
{sixiang_type=3,},
{sixiang_type=3,},
{sixiang_type=3,},
{sixiang_type=3,},
{sixiang_type=3,},
{sixiang_type=3,},
{sixiang_type=3,},
{sixiang_type=3,},
{sixiang_type=3,},
{sixiang_type=3,},
{sixiang_type=3,},
{sixiang_type=3,},
{sixiang_type=3,},
{sixiang_type=3,},
{sixiang_type=3,},
{sixiang_type=3,},
{sixiang_type=3,},
{sixiang_type=3,},
{sixiang_type=3,},
{sixiang_type=3,},
{sixiang_type=3,},
{sixiang_type=3,},
{sixiang_type=3,},
{sixiang_type=3,},
{sixiang_type=3,},
{sixiang_type=3,},
{sixiang_type=3,},
{sixiang_type=3,},
{sixiang_type=3,},
{sixiang_type=3,},
{sixiang_type=3,},
{sixiang_type=3,},
{sixiang_type=3,},
{sixiang_type=3,},
{sixiang_type=3,},
{sixiang_type=3,},
{sixiang_type=3,},
{sixiang_type=3,},
{sixiang_type=3,},
{sixiang_type=3,},
{sixiang_type=3,},
{sixiang_type=3,},
{sixiang_type=3,},
{sixiang_type=3,},
{sixiang_type=4,},
{sixiang_type=4,},
{sixiang_type=4,},
{sixiang_type=4,},
{sixiang_type=4,},
{sixiang_type=4,},
{sixiang_type=4,},
{sixiang_type=4,},
{sixiang_type=4,},
{sixiang_type=4,},
{sixiang_type=4,},
{sixiang_type=4,},
{sixiang_type=4,},
{sixiang_type=4,},
{sixiang_type=4,},
{sixiang_type=4,},
{sixiang_type=4,},
{sixiang_type=4,},
{sixiang_type=4,},
{sixiang_type=4,},
{sixiang_type=4,},
{sixiang_type=4,},
{sixiang_type=4,},
{sixiang_type=4,},
{sixiang_type=4,},
{sixiang_type=4,},
{sixiang_type=4,},
{sixiang_type=4,},
{sixiang_type=4,},
{sixiang_type=4,},
{sixiang_type=4,},
{sixiang_type=4,},
{sixiang_type=4,},
{sixiang_type=4,},
{sixiang_type=4,},
{sixiang_type=4,},
{sixiang_type=4,},
{sixiang_type=4,},
{sixiang_type=4,},
{sixiang_type=4,},
{sixiang_type=4,},
{sixiang_type=4,},
{sixiang_type=4,},
{sixiang_type=4,},
{sixiang_type=4,},
{sixiang_type=4,},
{sixiang_type=4,}
},

item_random_desc_meta_table_map={
[142]=1,	-- depth:1
[95]=142,	-- depth:2
[48]=95,	-- depth:3
[149]=8,	-- depth:1
[148]=7,	-- depth:1
[147]=6,	-- depth:1
[146]=5,	-- depth:1
[56]=9,	-- depth:1
[64]=17,	-- depth:1
[60]=13,	-- depth:1
[62]=15,	-- depth:1
[61]=14,	-- depth:1
[59]=12,	-- depth:1
[58]=11,	-- depth:1
[57]=10,	-- depth:1
[150]=56,	-- depth:2
[63]=16,	-- depth:1
[111]=64,	-- depth:2
[103]=150,	-- depth:3
[109]=62,	-- depth:2
[151]=57,	-- depth:2
[152]=58,	-- depth:2
[153]=59,	-- depth:2
[154]=60,	-- depth:2
[155]=61,	-- depth:2
[156]=109,	-- depth:3
[157]=63,	-- depth:2
[110]=157,	-- depth:3
[158]=111,	-- depth:3
[102]=149,	-- depth:2
[55]=102,	-- depth:3
[104]=151,	-- depth:3
[105]=152,	-- depth:3
[106]=153,	-- depth:3
[107]=154,	-- depth:3
[108]=155,	-- depth:3
[101]=148,	-- depth:2
[54]=101,	-- depth:3
[100]=147,	-- depth:2
[52]=146,	-- depth:2
[32]=33,	-- depth:1
[31]=33,	-- depth:1
[30]=33,	-- depth:1
[28]=29,	-- depth:1
[27]=29,	-- depth:1
[26]=29,	-- depth:1
[35]=34,	-- depth:1
[25]=29,	-- depth:1
[23]=29,	-- depth:1
[22]=29,	-- depth:1
[20]=21,	-- depth:1
[19]=21,	-- depth:1
[18]=21,	-- depth:1
[4]=1,	-- depth:1
[3]=4,	-- depth:2
[2]=4,	-- depth:2
[24]=29,	-- depth:1
[53]=100,	-- depth:3
[36]=34,	-- depth:1
[38]=34,	-- depth:1
[99]=52,	-- depth:3
[46]=47,	-- depth:1
[45]=47,	-- depth:1
[44]=47,	-- depth:1
[42]=43,	-- depth:1
[37]=34,	-- depth:1
[41]=34,	-- depth:1
[40]=34,	-- depth:1
[39]=34,	-- depth:1
[137]=43,	-- depth:1
[139]=45,	-- depth:2
[140]=46,	-- depth:2
[141]=47,	-- depth:1
[143]=2,	-- depth:3
[144]=3,	-- depth:3
[145]=4,	-- depth:2
[134]=40,	-- depth:2
[136]=42,	-- depth:2
[138]=44,	-- depth:2
[135]=41,	-- depth:2
[183]=136,	-- depth:3
[160]=19,	-- depth:2
[186]=139,	-- depth:3
[185]=138,	-- depth:3
[184]=137,	-- depth:2
[133]=39,	-- depth:2
[182]=135,	-- depth:3
[181]=134,	-- depth:3
[180]=133,	-- depth:3
[179]=38,	-- depth:2
[178]=37,	-- depth:2
[177]=36,	-- depth:2
[176]=35,	-- depth:2
[175]=34,	-- depth:1
[159]=18,	-- depth:2
[174]=33,	-- depth:1
[172]=31,	-- depth:2
[171]=30,	-- depth:2
[170]=29,	-- depth:1
[169]=28,	-- depth:2
[168]=27,	-- depth:2
[167]=26,	-- depth:2
[166]=25,	-- depth:2
[165]=24,	-- depth:2
[164]=23,	-- depth:2
[163]=22,	-- depth:2
[162]=21,	-- depth:1
[161]=20,	-- depth:2
[173]=32,	-- depth:2
[132]=179,	-- depth:3
[94]=141,	-- depth:2
[130]=177,	-- depth:3
[86]=180,	-- depth:4
[85]=132,	-- depth:4
[84]=178,	-- depth:3
[83]=130,	-- depth:4
[82]=176,	-- depth:3
[81]=175,	-- depth:2
[80]=174,	-- depth:2
[79]=173,	-- depth:3
[78]=172,	-- depth:3
[77]=171,	-- depth:3
[76]=170,	-- depth:2
[75]=169,	-- depth:3
[74]=168,	-- depth:3
[73]=167,	-- depth:3
[72]=166,	-- depth:3
[71]=165,	-- depth:3
[70]=164,	-- depth:3
[69]=163,	-- depth:3
[68]=162,	-- depth:2
[67]=161,	-- depth:3
[66]=160,	-- depth:3
[65]=159,	-- depth:3
[51]=145,	-- depth:3
[50]=144,	-- depth:4
[49]=143,	-- depth:4
[87]=181,	-- depth:4
[131]=84,	-- depth:4
[88]=182,	-- depth:4
[90]=184,	-- depth:3
[129]=82,	-- depth:4
[128]=81,	-- depth:3
[127]=80,	-- depth:3
[126]=79,	-- depth:4
[125]=78,	-- depth:4
[124]=77,	-- depth:4
[123]=76,	-- depth:3
[122]=75,	-- depth:4
[121]=74,	-- depth:4
[120]=73,	-- depth:4
[119]=72,	-- depth:4
[118]=71,	-- depth:4
[117]=70,	-- depth:4
[116]=69,	-- depth:4
[115]=68,	-- depth:3
[114]=67,	-- depth:4
[113]=66,	-- depth:4
[112]=65,	-- depth:4
[98]=51,	-- depth:4
[97]=50,	-- depth:5
[96]=49,	-- depth:5
[187]=140,	-- depth:3
[93]=187,	-- depth:4
[92]=186,	-- depth:4
[91]=185,	-- depth:4
[89]=183,	-- depth:4
[188]=94,	-- depth:3
},
other_default_table={single_lotto_consume=item_table[303],complement_num=70,multi_lotto=10,multi_lotto_consume_special=50020,multi_lotto_consume=item_table[304],discount_text="9折",lotto_exchange_num=1,lotto_exchange=91188,danmu_row=4,danmu_time=15,is_jumpanim=0,tedian_cycle_step=4,first_tedian_days=3,cycle_tedian_start_weekday=4,cycle_tedian_days=3,draw_sys_color=6,},

cycle_subactivity_default_table={cycle=1,start_subactivitys="2|3|5|6",},

subactivity_relation_default_table={subactivity_id=1,pre_subactivity="",},

subactivity_sale_default_table={cycle=1,subactivity_id=5,product_id=1,price_type=0,special_sale_price=188,rmb_seq=0,origin_price=0,discount=0,item={[0]=item_table[305]},limit_buy_type=0,limit_buy_times=1,name="",subactivity_name="魂装臻选",},

new_reward_pond_default_table={sixiang_type=1,min_draw_count=1,max_draw_count=25,reward_drop_id=361001,},

new_consume_default_table={sixiang_type=1,single_lotto_consume=item_table[306],multi_lotto_consume=item_table[307],multi_lotto_spconsume=item_table[308],single_public_consume=item_table[303],multi_public_consume=item_table[304],multi_public_spconsume=item_table[5],},

first_single_lotto_default_table={cycle=0,first_single_lotto_reward=1,reward_item=item_table[309],reward_type=2,broadcast=0,},

first_multi_lotto_group_default_table={cycle=0,first_multi_lotto_group=1,group_count=1,},

first_multi_lotto_default_table={cycle=0,first_multi_lotto_group=1,first_single_lotto_id=1,reward_item=item_table[309],reward_type=1,broadcast=1,},

special_multi_lotto_group_default_table={cycle=0,special_multi_lotto_group=1,special_group_count=1,},

special_multi_lotto_default_table={cycle=0,special_multi_lotto_group=2,special_single_lotto_id=1,reward_item=item_table[309],reward_type=2,broadcast=0,},

reward_default_table={sixiang_type=1,reward_id=1,reward_item=item_table[309],reward_type=2,broadcast=0,},

group_multi_limit_default_table={cycle=0,reward_multi_group=1,reward_id="1|2|3|4|5|6|7|8|9|10|11|12|13|14|15|16|17|44|45|46|47",},

group_cycle_limit_default_table={cycle=0,reward_group=1,reward_id="1|2|3|4|5|6|7|8|9|10|11|12|13|14|15|16|17|44|45|46|47",},

pool_cycle_default_table={cycle=0,reward_pool=1,},

reward_cycle_default_table={cycle=0,reward_pool=1,reward_id=5,},

exchange_default_table={seq=1,goods=item_table[37],exchange_num=350,},

client_sub_act_default_table={cycle=1,act_id=1,name="act_name_2",item_bg="a2_hlck_tedian_1",open_param="SiXiang_ShiLianView",},

probability_up_show_default_table={cycle=5,item_id=51005,base_color=5,base_star=5,},

summon_interface_default_table={sixiang_type=1,summon_dish_bg="sihun_039",summon_panel_bg="sihun_beijing01",item_list="51036,51136,51236,51336,51436,51536",},

reward_pond_show_default_table={sixiang_type=1,title_name="青龙合鸣-潜龙",item_list={[0]=item_table[39],[1]=item_table[40]},},

item_random_desc_default_table={sixiang_type=1,number=1,item_id=51001,random_count=0.35,is_rare=0,}

}

