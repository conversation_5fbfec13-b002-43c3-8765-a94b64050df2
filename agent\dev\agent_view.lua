AgentView = AgentView or BaseClass(SafeBaseView)

function AgentView:__init()
	self:AddViewResource(0, "uis/view/login_ui_prefab", "layout_agent")
	-- self.view_layer = UiLayer.SceneLoading
	self.view_layer = UiLayer.PopWhite
	self.active_close = false
	self.click_login_callback = nil
	self.open_tween = nil
	self.close_tween = nil
end

function AgentView:LoadCallBack()
    self.node_list["login_btn"].button:AddClickListener(BindTool.Bind(self.OnLoginClick, self))

	self.node_list["user_name"].input_field.text = PlayerPrefsUtil.GetString("account_name")
end

function AgentView:ReleaseCallBack()
end

function AgentView:ShowIndexCallBack()
	self.node_list["login_btn"]:SetActive(true)
end

function AgentView:SetClickLoginCallback(callback)
	self.click_login_callback = callback
end

function AgentView:OnLoginClick()
	local account_name = self.node_list["user_name"].input_field.text
	if account_name == "" then
		return
	end

	local is_can_login = false
	if RuntimeGUIMgr.Instance:IsAndroidGM() == true then
		--GM包不检测白名单
		is_can_login = true
	elseif not UNITY_EDITOR and nil ~= GLOBAL_CONFIG.param_list.username_white_list then
		for i,v in ipairs(GLOBAL_CONFIG.param_list.username_white_list) do
			if v == account_name then
				is_can_login = true
				break
			end
		end
	else
		is_can_login = true
	end

	if is_can_login then
		self.node_list["login_btn"]:SetActive(false)
		local plat_id = CHANNEL_AGENT_ID
		local partner_login_data
		local login_url
		if USE_INNER_LOGIN then
			partner_login_data = cjson.encode({jyUid = account_name})
			login_url = USE_INNER_LOGIN_URL
		else
			partner_login_data = cjson.encode({uid = account_name})
			login_url = GLOBAL_CONFIG.api_urls.client.login
		end

		local loginData = {
			plat_id = plat_id,
			partner_login_data = partner_login_data,
		}

		PhpHandle.HandlePhpJsonPostRequest(login_url, loginData, loginData,
		function (cb_data)
				local data = cb_data.data
				local uservo = GameVoManager.Instance:GetUserVo()
				uservo.account_user_id = data.uid
				uservo.login_time = data.login_time
				uservo.access_token = data.access_token
				uservo.is_white = data.is_white
				uservo.plat_session_key = data.sign

				print_error("-----OnLoginClick data.role_list-----", data.role_list)
				if LoginWGData.Instance then
					LoginWGData.Instance:SetPHPAccountRoleList(data.role_list)
				end
				
				PlayerPrefsUtil.SetString("cahce_account_user_id", data.uid)
				PlayerPrefsUtil.SetString("account_name", account_name)
				PlayerPrefsUtil.SetString("account_token_" .. (data.uid or ""), data.access_token)
				PlayerPrefsUtil.SetString("plat_session_key_" .. (data.uid or ""), data.sign)
				PlayerPrefsUtil.SetString("is_white_" .. (data.uid or ""), data.is_white)

				GameRoot.Instance:SetBuglyUserID(account_name)
				self.click_login_callback()
				self:Close()
		end,
		function (cb_data)
				self.node_list["login_btn"]:SetActive(true)
				TipsSystemManager.Instance:ShowSystemTips(Language.Login.LoginError)
		end)
	else
		TipsSystemManager.Instance:ShowSystemTips(Language.Login.WhiteAccountFailTip)
	end
end
