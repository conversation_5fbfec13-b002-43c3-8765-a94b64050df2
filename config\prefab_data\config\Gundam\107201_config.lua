return {
	actorController = {
		projectiles = {},

		hurts = {},

		beHurtEffecct = {},

		hurtEffectName = "",
		beHurtNodeName = "",
		beHurtAttach = false,
		hurtEffectFreeDelay = 0.0,
		QualityCtrlList = {},

	},
	actorTriggers = {
		effects = {},

		halts = {},

		sounds = {
			{
				soundEventName = "attack2_1/begin",
				soundDelay = 0.0,
				soundAudioAsset = {
					BundleName = "audios/sfxs/roleskill/jijia1_2",
					AssetName = "MingJiangattack1",
				},
				soundAudioGoName = "MingJiangattack1",
				soundBtnName = "attack1",
				soundIsMainRole = false,
			},
			{
				soundEventName = "attack2_2/begin",
				soundDelay = 0.0,
				soundAudioAsset = {
					BundleName = "audios/sfxs/roleskill/jijia1_2",
					AssetName = "MingJiangattack2",
				},
				soundAudioGoName = "MingJiangattack2",
				soundBtnName = "attack2",
				soundIsMainRole = false,
			},
			{
				soundEventName = "attack2_3/begin",
				soundDelay = 0.0,
				soundAudioAsset = {
					BundleName = "audios/sfxs/roleskill/jijia1_2",
					AssetName = "MingJiangattack3",
				},
				soundAudioGoName = "MingJiangattack3",
				soundBtnName = "attack3",
				soundIsMainRole = false,
			},
			{
				soundEventName = "combo2_1/begin",
				soundDelay = 0.0,
				soundAudioAsset = {
					BundleName = "audios/sfxs/roleskill/jijia1_2",
					AssetName = "MingJiangcombo1",
				},
				soundAudioGoName = "MingJiangcombo1",
				soundBtnName = "combo1",
				soundIsMainRole = false,
			},
			{
				soundEventName = "combo2_2/begin",
				soundDelay = 0.0,
				soundAudioAsset = {
					BundleName = "audios/sfxs/roleskill/jijia1_2",
					AssetName = "MingJiangcombo2",
				},
				soundAudioGoName = "MingJiangcombo2",
				soundBtnName = "combo2",
				soundIsMainRole = false,
			},
			{
				soundEventName = "combo2_3/begin",
				soundDelay = 0.0,
				soundAudioAsset = {
					BundleName = "audios/sfxs/roleskill/jijia1_2",
					AssetName = "MingJiangcombo3",
				},
				soundAudioGoName = "MingJiangcombo3",
				soundBtnName = "combo3",
				soundIsMainRole = false,
			},
		},
		cameraShakes = {
			{
				CameraShakeBtnName = "attack1",
				eventName = "attack2_1/begin",
				numberOfShakes = 8,
				distance = 0.5,
				speed = 500.0,
				delay = 1.0,
				decay = 0.0,
			},
			{
				CameraShakeBtnName = "attack2",
				eventName = "attack2_2/begin",
				numberOfShakes = 20,
				distance = 0.5,
				speed = 2000.0,
				delay = 1.0,
				decay = 0.0,
			},
			{
				CameraShakeBtnName = "attack3",
				eventName = "attack2_3/begin",
				numberOfShakes = 20,
				distance = 0.5,
				speed = 500.0,
				delay = 1.3,
				decay = 0.0,
			},
		},
		cameraFOVs = {},

		sceneFades = {},

		footsteps = {},

	},
	actorBlinker = {
		blinkFadeIn = 0.0,
		blinkFadeHold = 0.0,
		blinkFadeOut = 0.0,
	},
	TimeLineList = {},

}