return {
	actorController = {
		projectiles = {},

		hurts = {},

	},
	actorTriggers = {
		effects = {
			{
				triggerEventName = "attack1/begin",
				triggerDelay = 0.0,
				triggerFreeDelay = 0.0,
				effectGoName = "skill1",
				effectAsset = {
					BundleName = "effects/prefab/model/nuqi/ren/skill1_prefab",
					AssetName = "skill1",
				},
				playerAtTarget = false,
				referenceNodeHierarchyPath = "",
				isAttach = false,
				isRotation = true,
				isUseCustomTransform = false,
				offsetPosX = 0.0,
				offsetPosY = 0.0,
				offsetPosZ = 0.0,
				triggerStopEvent = "",
				effectBtnName = "attack1",
				playerAtPos = false,
				ignoreParentScale = false,
			},
			{
				triggerEventName = "attack2/begin",
				triggerDelay = 0.0,
				triggerFreeDelay = 0.0,
				effectGoName = "skill2",
				effectAsset = {
					BundleName = "effects/prefab/model/nuqi/ren/skill2_prefab",
					AssetName = "skill2",
				},
				playerAtTarget = false,
				referenceNodeHierarchyPath = "",
				isAttach = false,
				isRotation = true,
				isUseCustomTransform = false,
				offsetPosX = 0.0,
				offsetPosY = 0.0,
				offsetPosZ = 0.0,
				triggerStopEvent = "",
				effectBtnName = "attack2",
				playerAtPos = false,
				ignoreParentScale = false,
			},
			{
				triggerEventName = "attack3/begin",
				triggerDelay = 0.0,
				triggerFreeDelay = 0.0,
				effectGoName = "skill3",
				effectAsset = {
					BundleName = "effects/prefab/model/nuqi/ren/skill3_prefab",
					AssetName = "skill3",
				},
				playerAtTarget = false,
				referenceNodeHierarchyPath = "",
				isAttach = false,
				isRotation = true,
				isUseCustomTransform = false,
				offsetPosX = 0.0,
				offsetPosY = 0.0,
				offsetPosZ = 0.0,
				triggerStopEvent = "",
				effectBtnName = "attack3",
				playerAtPos = false,
				ignoreParentScale = false,
			},
			{
				triggerEventName = "attack4/begin",
				triggerDelay = 0.0,
				triggerFreeDelay = 0.0,
				effectGoName = "skill4",
				effectAsset = {
					BundleName = "effects/prefab/model/nuqi/ren/skill4_prefab",
					AssetName = "skill4",
				},
				playerAtTarget = true,
				referenceNodeHierarchyPath = "",
				isAttach = false,
				isRotation = true,
				isUseCustomTransform = true,
				offsetPosX = 0.0,
				offsetPosY = -2.0,
				offsetPosZ = 2.5,
				triggerStopEvent = "",
				effectBtnName = "attack4",
				playerAtPos = false,
				ignoreParentScale = false,
			},
			{
				triggerEventName = "combo1_1/begin",
				triggerDelay = 0.0,
				triggerFreeDelay = 0.0,
				effectGoName = "combo1",
				effectAsset = {
					BundleName = "effects/prefab/model/nuqi/ren/combo1_prefab",
					AssetName = "combo1",
				},
				playerAtTarget = false,
				referenceNodeHierarchyPath = "",
				isAttach = false,
				isRotation = true,
				isUseCustomTransform = true,
				offsetPosX = 0.0,
				offsetPosY = 2.0,
				offsetPosZ = 0.0,
				triggerStopEvent = "",
				effectBtnName = "combo1_1",
				playerAtPos = false,
				ignoreParentScale = false,
			},
			{
				triggerEventName = "combo1_2/begin",
				triggerDelay = 0.0,
				triggerFreeDelay = 0.0,
				effectGoName = "combo1",
				effectAsset = {
					BundleName = "effects/prefab/model/nuqi/ren/combo1_prefab",
					AssetName = "combo1",
				},
				playerAtTarget = false,
				referenceNodeHierarchyPath = "",
				isAttach = false,
				isRotation = true,
				isUseCustomTransform = true,
				offsetPosX = 0.0,
				offsetPosY = 2.0,
				offsetPosZ = 0.0,
				triggerStopEvent = "",
				effectBtnName = "combo1_2",
				playerAtPos = false,
				ignoreParentScale = false,
			},
			{
				triggerEventName = "combo1_3/begin",
				triggerDelay = 0.0,
				triggerFreeDelay = 0.0,
				effectGoName = "combo1",
				effectAsset = {
					BundleName = "effects/prefab/model/nuqi/ren/combo1_prefab",
					AssetName = "combo1",
				},
				playerAtTarget = false,
				referenceNodeHierarchyPath = "",
				isAttach = false,
				isRotation = true,
				isUseCustomTransform = true,
				offsetPosX = 0.0,
				offsetPosY = 2.0,
				offsetPosZ = 0.0,
				triggerStopEvent = "",
				effectBtnName = "combo1_3",
				playerAtPos = false,
				ignoreParentScale = false,
			},
			{
				triggerEventName = "combo1_4/begin",
				triggerDelay = 0.2,
				triggerFreeDelay = 0.0,
				effectGoName = "combo1",
				effectAsset = {
					BundleName = "effects/prefab/model/nuqi/ren/combo1_prefab",
					AssetName = "combo1",
				},
				playerAtTarget = false,
				referenceNodeHierarchyPath = "",
				isAttach = false,
				isRotation = true,
				isUseCustomTransform = true,
				offsetPosX = 2.0,
				offsetPosY = 2.0,
				offsetPosZ = 0.0,
				triggerStopEvent = "",
				effectBtnName = "combo1_4_1",
				playerAtPos = false,
				ignoreParentScale = false,
			},
			{
				triggerEventName = "transformation/begin",
				triggerDelay = 0.0,
				triggerFreeDelay = 0.0,
				effectGoName = "bianshen",
				effectAsset = {
					BundleName = "effects/prefab/model/nuqi/ren/bianshen_prefab",
					AssetName = "bianshen",
				},
				playerAtTarget = false,
				referenceNodeHierarchyPath = "",
				isAttach = false,
				isRotation = true,
				isUseCustomTransform = false,
				offsetPosX = 0.0,
				offsetPosY = 0.0,
				offsetPosZ = 0.0,
				triggerStopEvent = "",
				effectBtnName = "transformation",
				playerAtPos = false,
				ignoreParentScale = false,
			},
			{
				triggerEventName = "combo1_4/begin",
				triggerDelay = 0.3,
				triggerFreeDelay = 0.0,
				effectGoName = "combo1",
				effectAsset = {
					BundleName = "effects/prefab/model/nuqi/ren/combo1_prefab",
					AssetName = "combo1",
				},
				playerAtTarget = false,
				referenceNodeHierarchyPath = "",
				isAttach = false,
				isRotation = true,
				isUseCustomTransform = true,
				offsetPosX = -1.0,
				offsetPosY = 2.0,
				offsetPosZ = 0.0,
				triggerStopEvent = "",
				effectBtnName = "combo1_4_2",
				playerAtPos = false,
				ignoreParentScale = false,
			},
		},
		sounds = {
			{
				soundEventName = "attack1/begin",
				soundDelay = 0.0,
				soundAudioAsset = {
					BundleName = "audios/sfxs/roleskill/110100101",
					AssetName = "skill1",
				},
				soundAudioGoName = "skill1",
				soundBtnName = "attack1",
				soundIsMainRole = false,
			},
			{
				soundEventName = "attack2/begin",
				soundDelay = 0.0,
				soundAudioAsset = {
					BundleName = "audios/sfxs/roleskill/110100101",
					AssetName = "skill2",
				},
				soundAudioGoName = "skill2",
				soundBtnName = "attack2",
				soundIsMainRole = false,
			},
			{
				soundEventName = "attack3/begin",
				soundDelay = 0.0,
				soundAudioAsset = {
					BundleName = "audios/sfxs/roleskill/110100101",
					AssetName = "skill3",
				},
				soundAudioGoName = "skill3",
				soundBtnName = "attack3",
				soundIsMainRole = false,
			},
			{
				soundEventName = "attack4/begin",
				soundDelay = 0.0,
				soundAudioAsset = {
					BundleName = "audios/sfxs/roleskill/110100101",
					AssetName = "skill4",
				},
				soundAudioGoName = "skill4",
				soundBtnName = "attack4",
				soundIsMainRole = false,
			},
			{
				soundEventName = "transformation/begin",
				soundDelay = 0.0,
				soundAudioAsset = {
					BundleName = "audios/sfxs/roleskill/110100101",
					AssetName = "tra",
				},
				soundAudioGoName = "tra",
				soundBtnName = "tra",
				soundIsMainRole = false,
			},
			{
				soundEventName = "combo1_1/begin",
				soundDelay = 0.0,
				soundAudioAsset = {
					BundleName = "audios/sfxs/roleskill/110100101",
					AssetName = "attack1",
				},
				soundAudioGoName = "attack1",
				soundBtnName = "combo1",
				soundIsMainRole = false,
			},
			{
				soundEventName = "combo1_2/begin",
				soundDelay = 0.0,
				soundAudioAsset = {
					BundleName = "audios/sfxs/roleskill/110100101",
					AssetName = "attack2",
				},
				soundAudioGoName = "attack2",
				soundBtnName = "combo2",
				soundIsMainRole = false,
			},
			{
				soundEventName = "combo1_3/begin",
				soundDelay = 0.0,
				soundAudioAsset = {
					BundleName = "audios/sfxs/roleskill/110100101",
					AssetName = "attack3",
				},
				soundAudioGoName = "attack3",
				soundBtnName = "combo3",
				soundIsMainRole = false,
			},
			{
				soundEventName = "combo1_4/begin",
				soundDelay = 0.0,
				soundAudioAsset = {
					BundleName = "audios/sfxs/roleskill/110100101",
					AssetName = "attack4",
				},
				soundAudioGoName = "attack4",
				soundBtnName = "combo4",
				soundIsMainRole = false,
			},
			{
				soundEventName = "transformation/begin",
				soundDelay = 0.0,
				soundAudioAsset = {
					BundleName = "audios/sfxs/skill/110100101",
					AssetName = "transformation",
				},
				soundAudioGoName = "transformation",
				soundBtnName = "tra1",
				soundIsMainRole = false,
			},
		},
		cameraShakes = {
			{
				CameraShakeBtnName = "combo1_4",
				eventName = "combo1_4/begin",
				numberOfShakes = 1,
				distance = 0.2,
				speed = 100.0,
				delay = 0.6,
				decay = 0.0,
			},
			{
				CameraShakeBtnName = "attack1_1",
				eventName = "attack1/begin",
				numberOfShakes = 1,
				distance = 0.1,
				speed = 200.0,
				delay = 0.5,
				decay = 0.0,
			},
			{
				CameraShakeBtnName = "attack1_2",
				eventName = "attack1/begin",
				numberOfShakes = 1,
				distance = 0.1,
				speed = 200.0,
				delay = 0.8,
				decay = 0.0,
			},
			{
				CameraShakeBtnName = "attack1_3",
				eventName = "attack1/begin",
				numberOfShakes = 3,
				distance = 0.2,
				speed = 150.0,
				delay = 1.3,
				decay = 0.0,
			},
			{
				CameraShakeBtnName = "attack3_2",
				eventName = "attack3/begin",
				numberOfShakes = 7,
				distance = 0.2,
				speed = 60.0,
				delay = 1.3,
				decay = 0.1,
			},
			{
				CameraShakeBtnName = "attack3_1",
				eventName = "attack3/begin",
				numberOfShakes = 0,
				distance = 0.3,
				speed = 90.0,
				delay = 0.15,
				decay = 0.0,
			},
			{
				CameraShakeBtnName = "attack4",
				eventName = "attack4/begin",
				numberOfShakes = 12,
				distance = 0.7,
				speed = 500.0,
				delay = 1.6,
				decay = 0.0,
			},
			{
				CameraShakeBtnName = "transformation",
				eventName = "transformation/begin",
				numberOfShakes = 2,
				distance = 0.8,
				speed = 400.0,
				delay = 0.6,
				decay = 0.0,
			},
		},
		radialBlurs = {
			{
				btnName = "attack4",
				eventName = "attack4/begin",
				isRole = true,
				referenceNodeHierarchyPath = "",
				delay = 0.8,
				strength = 0.08,
				riseTime = 0.5,
				holdTime = 0.3,
				fallTime = 0.0,
			},
			{
				btnName = "transformation",
				eventName = "transformation/begin",
				isRole = true,
				referenceNodeHierarchyPath = "root",
				delay = 0.1,
				strength = 0.1,
				riseTime = 0.1,
				holdTime = 0.2,
				fallTime = 0.0,
			},
			{
				btnName = "attack3",
				eventName = "attack3/begin",
				isRole = false,
				referenceNodeHierarchyPath = "",
				delay = 1.2,
				strength = 0.03,
				riseTime = 0.5,
				holdTime = 0.3,
				fallTime = 0.0,
			},
			{
				btnName = "attack2",
				eventName = "attack2/begin",
				isRole = false,
				referenceNodeHierarchyPath = "",
				delay = 0.1,
				strength = 0.01,
				riseTime = 0.1,
				holdTime = 0.2,
				fallTime = 0.0,
			},
		},
	},
}