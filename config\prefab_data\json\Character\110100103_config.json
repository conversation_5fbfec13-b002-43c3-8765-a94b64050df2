{"actorController": {"projectiles": [], "hurts": []}, "actorTriggers": {"effects": [{"triggerEventName": "attack1/begin", "triggerDelay": 0.0, "triggerFreeDelay": 0.0, "effectGoName": "skill1", "effectAsset": {"BundleName": "effects/prefab/model/nuqi/xian/skill1_prefab", "AssetName": "skill1", "AssetGUID": "a05543d0086f00440b5cca864330dcc1", "IsEmpty": false}, "playerAtTarget": false, "referenceNodeHierarchyPath": "", "isAttach": false, "isRotation": true, "isUseCustomTransform": false, "offsetPosX": 0.0, "offsetPosY": 0.0, "offsetPosZ": 0.0, "triggerStopEvent": "[none]", "effectBtnName": "attack1", "playerAtPos": false, "ignoreParentScale": false}, {"triggerEventName": "attack2/begin", "triggerDelay": 0.0, "triggerFreeDelay": 0.0, "effectGoName": "skill2", "effectAsset": {"BundleName": "effects/prefab/model/nuqi/xian/skill2_prefab", "AssetName": "skill2", "AssetGUID": "1368a22d0673cfa4980dead507388b08", "IsEmpty": false}, "playerAtTarget": false, "referenceNodeHierarchyPath": "", "isAttach": false, "isRotation": true, "isUseCustomTransform": false, "offsetPosX": 0.0, "offsetPosY": 0.0, "offsetPosZ": 0.0, "triggerStopEvent": "[none]", "effectBtnName": "attack2", "playerAtPos": false, "ignoreParentScale": false}, {"triggerEventName": "attack3/begin", "triggerDelay": 0.0, "triggerFreeDelay": 0.0, "effectGoName": "skill3", "effectAsset": {"BundleName": "effects/prefab/model/nuqi/xian/skill3_prefab", "AssetName": "skill3", "AssetGUID": "abc34faa1530f404f80233a179b648aa", "IsEmpty": false}, "playerAtTarget": false, "referenceNodeHierarchyPath": "", "isAttach": false, "isRotation": true, "isUseCustomTransform": false, "offsetPosX": 0.0, "offsetPosY": 0.0, "offsetPosZ": 0.0, "triggerStopEvent": "[none]", "effectBtnName": "attack3", "playerAtPos": false, "ignoreParentScale": false}, {"triggerEventName": "attack4/begin", "triggerDelay": 0.0, "triggerFreeDelay": 0.0, "effectGoName": "skill4", "effectAsset": {"BundleName": "effects/prefab/model/nuqi/xian/skill4_prefab", "AssetName": "skill4", "AssetGUID": "74c829ee0f8db5d4abeb5f1b160afe07", "IsEmpty": false}, "playerAtTarget": false, "referenceNodeHierarchyPath": "", "isAttach": false, "isRotation": true, "isUseCustomTransform": true, "offsetPosX": 0.0, "offsetPosY": 1.5, "offsetPosZ": 0.0, "triggerStopEvent": "[none]", "effectBtnName": "attack4", "playerAtPos": false, "ignoreParentScale": false}, {"triggerEventName": "combo1_1/begin", "triggerDelay": 0.0, "triggerFreeDelay": 0.0, "effectGoName": "combo1", "effectAsset": {"BundleName": "effects/prefab/model/nuqi/xian/combo1_prefab", "AssetName": "combo1", "AssetGUID": "e293e286d87f06d429c741df08e70686", "IsEmpty": false}, "playerAtTarget": false, "referenceNodeHierarchyPath": "", "isAttach": false, "isRotation": true, "isUseCustomTransform": true, "offsetPosX": 1.0, "offsetPosY": 2.2, "offsetPosZ": 0.0, "triggerStopEvent": "[none]", "effectBtnName": "combo1_1", "playerAtPos": false, "ignoreParentScale": false}, {"triggerEventName": "combo1_2/begin", "triggerDelay": 0.1, "triggerFreeDelay": 0.0, "effectGoName": "combo2", "effectAsset": {"BundleName": "effects/prefab/model/nuqi/xian/combo2_prefab", "AssetName": "combo2", "AssetGUID": "bb7736e26275c6a4496c50d9ba7211bb", "IsEmpty": false}, "playerAtTarget": false, "referenceNodeHierarchyPath": "", "isAttach": false, "isRotation": true, "isUseCustomTransform": true, "offsetPosX": 0.2, "offsetPosY": 2.2, "offsetPosZ": 0.0, "triggerStopEvent": "[none]", "effectBtnName": "combo1_2", "playerAtPos": false, "ignoreParentScale": false}, {"triggerEventName": "combo1_3/begin", "triggerDelay": 0.1, "triggerFreeDelay": 0.0, "effectGoName": "combo3", "effectAsset": {"BundleName": "effects/prefab/model/nuqi/xian/combo3_prefab", "AssetName": "combo3", "AssetGUID": "b00c01663a4475d48b6bbe81adf3014e", "IsEmpty": false}, "playerAtTarget": false, "referenceNodeHierarchyPath": "", "isAttach": false, "isRotation": true, "isUseCustomTransform": true, "offsetPosX": 0.0, "offsetPosY": 2.2, "offsetPosZ": 0.0, "triggerStopEvent": "[none]", "effectBtnName": "combo1_3", "playerAtPos": false, "ignoreParentScale": false}, {"triggerEventName": "combo1_4/begin", "triggerDelay": 0.0, "triggerFreeDelay": 0.0, "effectGoName": "combo4", "effectAsset": {"BundleName": "effects/prefab/model/nuqi/xian/combo4_prefab", "AssetName": "combo4", "AssetGUID": "3c5147d176b70fe4496c2fcc1316b61e", "IsEmpty": false}, "playerAtTarget": false, "referenceNodeHierarchyPath": "", "isAttach": false, "isRotation": true, "isUseCustomTransform": true, "offsetPosX": 0.0, "offsetPosY": 1.8, "offsetPosZ": 0.0, "triggerStopEvent": "[none]", "effectBtnName": "combo1_4", "playerAtPos": false, "ignoreParentScale": false}, {"triggerEventName": "transformation/begin", "triggerDelay": 0.0, "triggerFreeDelay": 0.0, "effectGoName": "bianshen", "effectAsset": {"BundleName": "effects/prefab/model/nuqi/xian/bianshen_prefab", "AssetName": "bianshen", "AssetGUID": "45afc65f3445fc74a85ff9c35c8afc25", "IsEmpty": false}, "playerAtTarget": false, "referenceNodeHierarchyPath": "", "isAttach": false, "isRotation": true, "isUseCustomTransform": false, "offsetPosX": 0.0, "offsetPosY": 0.0, "offsetPosZ": 0.0, "triggerStopEvent": "[none]", "effectBtnName": "transformation", "playerAtPos": false, "ignoreParentScale": false}], "sounds": [{"soundEventName": "attack1/begin", "soundDelay": 0.0, "soundAudioAsset": {"BundleName": "audios/sfxs/roleskill/110100103", "AssetName": "skill1", "AssetGUID": "0d1fa9d2ba61d0c489aa02a9ac1250ba", "IsEmpty": false}, "soundAudioGoName": "skill1", "soundBtnName": "skill1", "soundIsMainRole": false}, {"soundEventName": "attack2/begin", "soundDelay": 0.0, "soundAudioAsset": {"BundleName": "audios/sfxs/roleskill/110100103", "AssetName": "skill2", "AssetGUID": "970aa4dd52a8a71499fa56a756f20016", "IsEmpty": false}, "soundAudioGoName": "skill2", "soundBtnName": "skill2", "soundIsMainRole": false}, {"soundEventName": "attack3/begin", "soundDelay": 0.0, "soundAudioAsset": {"BundleName": "audios/sfxs/roleskill/110100103", "AssetName": "skill3", "AssetGUID": "c1ecffd8f09332240a769bd68b24184a", "IsEmpty": false}, "soundAudioGoName": "skill3", "soundBtnName": "skill3", "soundIsMainRole": false}, {"soundEventName": "attack4/begin", "soundDelay": 0.0, "soundAudioAsset": {"BundleName": "audios/sfxs/roleskill/110100103", "AssetName": "skill4", "AssetGUID": "265e09e0cd9ff454289d00b4b2b522fd", "IsEmpty": false}, "soundAudioGoName": "skill4", "soundBtnName": "skill4", "soundIsMainRole": false}, {"soundEventName": "combo1_1/begin", "soundDelay": 0.0, "soundAudioAsset": {"BundleName": "audios/sfxs/roleskill/110100103", "AssetName": "attack1", "AssetGUID": "df971439a9ee414458326e7ad878fc71", "IsEmpty": false}, "soundAudioGoName": "attack1", "soundBtnName": "attack1", "soundIsMainRole": false}, {"soundEventName": "combo1_2/begin", "soundDelay": 0.0, "soundAudioAsset": {"BundleName": "audios/sfxs/roleskill/110100103", "AssetName": "attack2", "AssetGUID": "40b44d7eda7c03a4081c4d69fbacd41b", "IsEmpty": false}, "soundAudioGoName": "attack2", "soundBtnName": "attack2", "soundIsMainRole": false}, {"soundEventName": "combo1_3/begin", "soundDelay": 0.0, "soundAudioAsset": {"BundleName": "audios/sfxs/roleskill/110100103", "AssetName": "attack3", "AssetGUID": "cbca1efca6304a14eb9346d5ccd861dd", "IsEmpty": false}, "soundAudioGoName": "attack3", "soundBtnName": "attack3", "soundIsMainRole": false}, {"soundEventName": "combo1_4/begin", "soundDelay": 0.0, "soundAudioAsset": {"BundleName": "audios/sfxs/roleskill/110100103", "AssetName": "attack4", "AssetGUID": "fab285259a356704798aa80c8be739fe", "IsEmpty": false}, "soundAudioGoName": "attack4", "soundBtnName": "attack4", "soundIsMainRole": false}, {"soundEventName": "transformation/begin", "soundDelay": 0.0, "soundAudioAsset": {"BundleName": "audios/sfxs/roleskill/110100101", "AssetName": "tra", "AssetGUID": "87f117226ddb65d4990a5b856d365e4d", "IsEmpty": false}, "soundAudioGoName": "tra", "soundBtnName": "tra", "soundIsMainRole": false}], "cameraShakes": [{"CameraShakeBtnName": "attack1", "eventName": "attack1/begin", "numberOfShakes": 1, "distance": 0.2, "speed": 120.0, "delay": 1.5, "decay": 0.0}, {"CameraShakeBtnName": "attack3", "eventName": "attack3/begin", "numberOfShakes": 10, "distance": 0.2, "speed": 500.0, "delay": 1.2, "decay": 0.0}, {"CameraShakeBtnName": "attack4_1", "eventName": "attack4/begin", "numberOfShakes": 1, "distance": 0.1, "speed": 80.0, "delay": 0.5, "decay": 0.0}, {"CameraShakeBtnName": "attack4_2", "eventName": "attack4/begin", "numberOfShakes": 2, "distance": 0.2, "speed": 80.0, "delay": 1.1, "decay": 0.0}, {"CameraShakeBtnName": "attack4_3", "eventName": "attack4/begin", "numberOfShakes": 3, "distance": 0.2, "speed": 80.0, "delay": 1.4, "decay": 0.0}, {"CameraShakeBtnName": "attack4_4", "eventName": "attack4/begin", "numberOfShakes": 4, "distance": 0.4, "speed": 80.0, "delay": 1.8, "decay": 0.0}, {"CameraShakeBtnName": "transformation", "eventName": "transformation/begin", "numberOfShakes": 2, "distance": 0.8, "speed": 400.0, "delay": 0.9, "decay": 0.0}], "radialBlurs": [{"btnName": "attack2", "eventName": "attack2/begin", "isRole": true, "referenceNodeHierarchyPath": "root", "delay": 0.1, "strength": 0.1, "riseTime": 0.1, "holdTime": 0.0, "fallTime": 0.2}, {"btnName": "transformation", "eventName": "transformation/begin", "isRole": true, "referenceNodeHierarchyPath": "root", "delay": 0.1, "strength": 2.0, "riseTime": 0.1, "holdTime": 0.2, "fallTime": 0.0}]}}