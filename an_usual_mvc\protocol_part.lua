-- 协议请求
-- （客户端 向 服务端请求 一般协议名以CS开头）
CSXXXReq = CSXXXReq or BaseClass(BaseProtocolStruct)
function CSXXXReq:__init()
	self.msg_type = 0		-- 协议号

    self.operate_type = 0
    self.param_1 = 0
    self.param_2 = 0
end

-- 编码
function CSXXXReq:Encode()
	MsgAdapter.WriteBegin(self.msg_type)
	MsgAdapter.WriteInt(self.operate_type)
	MsgAdapter.WriteChar(self.param_1)
	MsgAdapter.WriteShort(self.param_2)
end


-- 协议接收
-- （服务端 向 客户端下发 一般协议名以SC开头）
SCXXXInfo = SCXXXInfo or BaseClass(BaseProtocolStruct)
function SCXXXInfo:__init()
	self.msg_type = 0		-- 协议号
end

-- 解码
function SCXXXInfo:Decode()
    self.param_1 = MsgAdapter.ReadInt()
    self.param_2 = MsgAdapter.ReadChar()
    self.count = MsgAdapter.ReadInt()
	self.param_list = {}
	for i = 1, self.count do
		self.param_list[i] = MsgAdapter.ReadShort()
	end
end