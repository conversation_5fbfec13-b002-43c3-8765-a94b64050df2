-- C-充值奖励_苹果.xls
local item_table={
[1]={item_id=22000,num=1,is_bind=1},
}

return {
reward={
[0]={seq=0,extra_bind_gold=60,},
[1]={seq=1,chongzhi=300,extra_bind_gold=108,discretion="首次购买额外赠送\n<color=#ffff00>108绑定元宝</color>",show_discretion="首次购买额外赠送\n<color=#ffff00>108元宝</color>",shenhe_discretion="首次购买额外赠送\n<color=#ffff00>108元宝</color>",},
[2]={seq=2,chongzhi=680,extra_bind_gold=128,discretion="首次购买额外赠送\n<color=#ffff00>128绑定元宝</color>",show_discretion="首次购买额外赠送\n<color=#ffff00>128元宝</color>",shenhe_discretion="首次购买额外赠送\n<color=#ffff00>128元宝</color>",},
[3]={seq=3,chongzhi=980,extra_bind_gold=198,discretion="首次购买额外赠送\n<color=#ffff00>198绑定元宝</color>",show_discretion="首次购买额外赠送\n<color=#ffff00>198元宝</color>",shenhe_discretion="首次购买额外赠送\n<color=#ffff00>198元宝</color>",},
[4]={seq=4,chongzhi=1280,reward_type=4,extra_gold=218,discretion="首次购买额外赠送\n<color=#ffff00>218元宝</color>",show_discretion="首次购买额外赠送\n<color=#ffff00>218灵玉</color>",shenhe_discretion="首次购买额外赠送\n<color=#ffff00>218灵玉</color>",},
[5]={seq=5,chongzhi=1980,reward_type=4,extra_gold=328,discretion="首次购买额外赠送\n<color=#ffff00>328元宝</color>",show_discretion="首次购买额外赠送\n<color=#ffff00>328灵玉</color>",shenhe_discretion="首次购买额外赠送\n<color=#ffff00>328灵玉</color>",},
[6]={seq=6,chongzhi=3280,reward_type=4,extra_gold=548,discretion="首次购买额外赠送\n<color=#ffff00>548元宝</color>",show_discretion="首次购买额外赠送\n<color=#ffff00>548灵玉</color>",shenhe_discretion="首次购买额外赠送\n<color=#ffff00>548灵玉</color>",},
[7]={seq=7,chongzhi=6480,reward_type=4,extra_gold=1088,discretion="首次购买额外赠送\n<color=#ffff00>1088元宝</color>",show_discretion="首次购买额外赠送\n<color=#ffff00>1088灵玉</color>",shenhe_discretion="首次购买额外赠送\n<color=#ffff00>1088灵玉</color>",}
},

reward_meta_table_map={
},
special_chongzhi_reward={
[0]={seq=0,},
[1]={seq=1,},
[2]={seq=2,}
},

special_chongzhi_reward_meta_table_map={
},
daily_fisrt_chongzhi_reward={
[0]={day=0,},
[1]={day=1,},
[2]={day=2,},
[3]={day=3,},
[4]={day=4,},
[5]={day=5,},
[6]={day=6,}
},

daily_fisrt_chongzhi_reward_meta_table_map={
},
other={
{}
},

other_meta_table_map={
},
daily_total_chongzhi_reward={
{},
{seq=1,need_total_chongzhi=2000,},
{seq=2,need_total_chongzhi=5000,},
{seq=3,need_total_chongzhi=10000,},
{seq=4,need_total_chongzhi=20000,},
{stage=2,},
{stage=2,},
{stage=2,},
{stage=2,},
{stage=2,},
{stage=3,},
{stage=3,},
{stage=3,},
{stage=3,},
{stage=3,},
{stage=4,},
{stage=4,},
{stage=4,},
{stage=4,},
{stage=4,}
},

daily_total_chongzhi_reward_meta_table_map={
[18]=3,	-- depth:1
[17]=2,	-- depth:1
[15]=5,	-- depth:1
[14]=4,	-- depth:1
[10]=15,	-- depth:2
[12]=17,	-- depth:2
[19]=14,	-- depth:2
[9]=19,	-- depth:3
[8]=18,	-- depth:2
[7]=12,	-- depth:3
[13]=8,	-- depth:3
[20]=10,	-- depth:3
},
daily_total_chongzhi_stage={
{},
{stage=2,start_day=3656,end_day=3661,},
{stage=3,start_day=3662,end_day=3667,},
{stage=4,start_day=3668,end_day=3673,}
},

daily_total_chongzhi_stage_meta_table_map={
},
total_chongzhi_want_money={
{},
{state=1,charged=300,random_min=200,random_max=300,},
{state=2,charged=500,random_min=300,random_max=450,},
{state=3,charged=1000,random_min=500,random_max=750,},
{state=4,charged=2000,random_min=1000,random_max=1500,}
},

total_chongzhi_want_money_meta_table_map={
},
weekday_total_chongzhi={
{},
{seq=1,need_total_chongzhi=1000,},
{seq=2,need_total_chongzhi=5000,},
{seq=3,need_total_chongzhi=10000,},
{day_of_week=1,},
{day_of_week=1,},
{day_of_week=1,},
{day_of_week=1,},
{day_of_week=2,},
{day_of_week=2,},
{day_of_week=2,},
{day_of_week=2,},
{day_of_week=3,},
{day_of_week=3,},
{day_of_week=3,},
{day_of_week=3,},
{day_of_week=4,},
{day_of_week=4,},
{day_of_week=4,},
{day_of_week=4,},
{day_of_week=5,},
{day_of_week=5,},
{day_of_week=5,},
{day_of_week=5,},
{day_of_week=6,},
{day_of_week=6,},
{day_of_week=6,},
{day_of_week=6,}
},

weekday_total_chongzhi_meta_table_map={
[26]=2,	-- depth:1
[24]=4,	-- depth:1
[18]=26,	-- depth:2
[23]=3,	-- depth:1
[22]=18,	-- depth:3
[20]=24,	-- depth:2
[19]=23,	-- depth:2
[14]=22,	-- depth:4
[15]=19,	-- depth:3
[27]=15,	-- depth:4
[12]=20,	-- depth:3
[11]=27,	-- depth:5
[10]=14,	-- depth:5
[8]=12,	-- depth:4
[7]=11,	-- depth:6
[6]=10,	-- depth:6
[16]=8,	-- depth:5
[28]=16,	-- depth:6
},
reward_default_table={seq=0,chongzhi=60,reward_type=1,extra_bind_gold=0,extra_coin=0,extra_gold=0,show_rule=0,show_gold=1,discretion="首次购买额外赠送\n<color=#ffff00>60绑定元宝</color>",show_discretion="首次购买额外赠送\n<color=#ffff00>60元宝</color>",shenhe_discretion="首次购买额外赠送\n<color=#ffff00>60元宝</color>",},

special_chongzhi_reward_default_table={seq=0,reward_item=item_table[1],},

daily_fisrt_chongzhi_reward_default_table={day=0,first_recharge=item_table[1],},

other_default_table={first_recharge_extra_bind_gold_times=0,daily_first_recharge_extra_reward_need_times=7,daily_first_recharge_extra_reward_item=item_table[1],zai_chongzhi_need_chongzhi=300,zai_chongzhi_reward_item=item_table[1],third_chongzhi_need_chongzhi=1000,third_chongzhi_reward_item=item_table[1],},

daily_total_chongzhi_reward_default_table={stage=1,seq=0,need_total_chongzhi=1000,total_recharge=item_table[1],},

daily_total_chongzhi_stage_default_table={stage=1,start_day=3650,end_day=3655,},

total_chongzhi_want_money_default_table={state=0,charged=100,random_min=100,random_max=200,},

weekday_total_chongzhi_default_table={day_of_week=0,seq=0,need_total_chongzhi=500,reward_item=item_table[1],}

}

