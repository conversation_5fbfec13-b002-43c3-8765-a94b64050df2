return {
	actorController = {
		projectiles = {},

		hurts = {},

	},
	actorTriggers = {
		effects = {
			{
				triggerEventName = "attack1/begin",
				triggerDelay = 0.0,
				triggerFreeDelay = 0.0,
				effectGoName = "eff_4046yushou_attcak1",
				effectAsset = {
					BundleName = "effects/prefab/model/yushou/4046/eff_4046yushou_attcak1_prefab",
					AssetName = "eff_4046yushou_attcak1",
				},
				playerAtTarget = false,
				referenceNodeHierarchyPath = "root",
				isAttach = true,
				isRotation = false,
				isUseCustomTransform = true,
				offsetPosX = 0.0,
				offsetPosY = -1.2,
				offsetPosZ = -1.0,
				triggerStopEvent = "",
				effectBtnName = "attack1",
				playerAtPos = false,
				ignoreParentScale = false,
			},
			{
				triggerEventName = "attack2/begin",
				triggerDelay = 0.55,
				triggerFreeDelay = 0.0,
				effectGoName = "yushou_skill_8",
				effectAsset = {
					BundleName = "effects/prefab/model/yushou/common/yushou_skill_8_prefab",
					AssetName = "yushou_skill_8",
				},
				playerAtTarget = false,
				referenceNodeHierarchyPath = "root",
				isAttach = true,
				isRotation = false,
				isUseCustomTransform = true,
				offsetPosX = 0.0,
				offsetPosY = 0.0,
				offsetPosZ = 5.0,
				triggerStopEvent = "",
				effectBtnName = "skill",
				playerAtPos = false,
				ignoreParentScale = false,
			},
			{
				triggerEventName = "rest/begin",
				triggerDelay = 0.5,
				triggerFreeDelay = 0.0,
				effectGoName = "eff_4046yushou_rest",
				effectAsset = {
					BundleName = "effects/prefab/model/yushou/4046/eff_4046yushou_rest_prefab",
					AssetName = "eff_4046yushou_rest",
				},
				playerAtTarget = false,
				referenceNodeHierarchyPath = "root",
				isAttach = true,
				isRotation = false,
				isUseCustomTransform = true,
				offsetPosX = 0.0,
				offsetPosY = 0.0,
				offsetPosZ = 0.0,
				triggerStopEvent = "",
				effectBtnName = "rest",
				playerAtPos = false,
				ignoreParentScale = false,
			},
		},
		sounds = {
			{
				soundEventName = "attack2/begin",
				soundDelay = 0.0,
				soundAudioAsset = {
					BundleName = "audios/sfxs/skill/4046",
					AssetName = "4046_skill",
				},
				soundAudioGoName = "4046_skill",
				soundBtnName = "skill",
				soundIsMainRole = false,
			},
		},
		cameraShakes = {},

		radialBlurs = {},

	},
}