return {
	actorController = {
		projectiles = {},

		hurts = {},

	},
	actorTriggers = {
		effects = {
			{
				triggerEventName = "attack1/begin",
				triggerDelay = 0.0,
				triggerFreeDelay = 0.0,
				effectGoName = "10107_skill1",
				effectAsset = {
					BundleName = "effects/prefab/model/tianshen/10107/10107_skill1_prefab",
					AssetName = "10107_skill1",
				},
				playerAtTarget = false,
				referenceNodeHierarchyPath = "",
				isAttach = false,
				isRotation = true,
				isUseCustomTransform = true,
				offsetPosX = -2.0,
				offsetPosY = 0.0,
				offsetPosZ = 2.0,
				triggerStopEvent = "",
				effectBtnName = "skill1",
				playerAtPos = false,
				ignoreParentScale = false,
			},
			{
				triggerEventName = "attack2/begin",
				triggerDelay = 0.0,
				triggerFreeDelay = 0.0,
				effectGoName = "10107_skill2",
				effectAsset = {
					BundleName = "effects/prefab/model/tianshen/10107/10107_skill2_prefab",
					AssetName = "10107_skill2",
				},
				playerAtTarget = false,
				referenceNodeHierarchyPath = "root",
				isAttach = true,
				isRotation = true,
				isUseCustomTransform = true,
				offsetPosX = 100.0,
				offsetPosY = 0.0,
				offsetPosZ = -500.0,
				triggerStopEvent = "",
				effectBtnName = "skill2",
				playerAtPos = false,
				ignoreParentScale = true,
			},
			{
				triggerEventName = "attack3/begin",
				triggerDelay = 0.0,
				triggerFreeDelay = 0.0,
				effectGoName = "10107_skill3",
				effectAsset = {
					BundleName = "effects/prefab/model/tianshen/10107/10107_skill3_prefab",
					AssetName = "10107_skill3",
				},
				playerAtTarget = false,
				referenceNodeHierarchyPath = "root",
				isAttach = true,
				isRotation = true,
				isUseCustomTransform = true,
				offsetPosX = 200.0,
				offsetPosY = 0.0,
				offsetPosZ = -500.0,
				triggerStopEvent = "",
				effectBtnName = "skill3",
				playerAtPos = false,
				ignoreParentScale = true,
			},
			{
				triggerEventName = "combo1_1/begin",
				triggerDelay = 0.0,
				triggerFreeDelay = 0.0,
				effectGoName = "10107_attack",
				effectAsset = {
					BundleName = "effects/prefab/model/tianshen/10107/10107_attack_prefab",
					AssetName = "10107_attack",
				},
				playerAtTarget = false,
				referenceNodeHierarchyPath = "",
				isAttach = false,
				isRotation = true,
				isUseCustomTransform = true,
				offsetPosX = -1.0,
				offsetPosY = 0.0,
				offsetPosZ = 0.0,
				triggerStopEvent = "",
				effectBtnName = "attack",
				playerAtPos = false,
				ignoreParentScale = false,
			},
			{
				triggerEventName = "rest/begin",
				triggerDelay = 0.8,
				triggerFreeDelay = 0.0,
				effectGoName = "10107_rest",
				effectAsset = {
					BundleName = "effects/prefab/model/tianshen/10107/10107_rest_prefab",
					AssetName = "10107_rest",
				},
				playerAtTarget = false,
				referenceNodeHierarchyPath = "root",
				isAttach = true,
				isRotation = false,
				isUseCustomTransform = true,
				offsetPosX = 0.0,
				offsetPosY = 0.0,
				offsetPosZ = 0.0,
				triggerStopEvent = "",
				effectBtnName = "rest",
				playerAtPos = false,
				ignoreParentScale = false,
			},
		},
		sounds = {
			{
				soundEventName = "combo1_1/begin",
				soundDelay = 0.0,
				soundAudioAsset = {
					BundleName = "audios/sfxs/roleskill/tianshen_10107",
					AssetName = "MingJiangcombo_1_1",
				},
				soundAudioGoName = "MingJiangcombo_1_1",
				soundBtnName = "combo1",
				soundIsMainRole = false,
			},
			{
				soundEventName = "combo1_2/begin",
				soundDelay = 0.0,
				soundAudioAsset = {
					BundleName = "audios/sfxs/roleskill/tianshen_10107",
					AssetName = "MingJiangcombo_1_2",
				},
				soundAudioGoName = "MingJiangcombo_1_2",
				soundBtnName = "combo2",
				soundIsMainRole = false,
			},
			{
				soundEventName = "combo1_3/begin",
				soundDelay = 0.0,
				soundAudioAsset = {
					BundleName = "audios/sfxs/roleskill/tianshen_10107",
					AssetName = "MingJiangcombo_1_3",
				},
				soundAudioGoName = "MingJiangcombo_1_3",
				soundBtnName = "combo3",
				soundIsMainRole = false,
			},
			{
				soundEventName = "attack1/begin",
				soundDelay = 0.0,
				soundAudioAsset = {
					BundleName = "audios/sfxs/roleskill/tianshen_10107",
					AssetName = "MingJiangattack1",
				},
				soundAudioGoName = "MingJiangattack1",
				soundBtnName = "attack1",
				soundIsMainRole = false,
			},
			{
				soundEventName = "attack2/begin",
				soundDelay = 0.0,
				soundAudioAsset = {
					BundleName = "audios/sfxs/roleskill/tianshen_10107",
					AssetName = "MingJiangattack2",
				},
				soundAudioGoName = "MingJiangattack2",
				soundBtnName = "attack2",
				soundIsMainRole = false,
			},
			{
				soundEventName = "attack3/begin",
				soundDelay = 0.4,
				soundAudioAsset = {
					BundleName = "audios/sfxs/roleskill/tianshen_10107",
					AssetName = "MingJiangattack3",
				},
				soundAudioGoName = "MingJiangattack3",
				soundBtnName = "attack3",
				soundIsMainRole = false,
			},
			{
				soundEventName = "combo1_3/begin",
				soundDelay = 0.5,
				soundAudioAsset = {
					BundleName = "audios/sfxs/roleskill/tianshen_10107",
					AssetName = "MingJiangcombo_1_31",
				},
				soundAudioGoName = "MingJiangcombo_1_31",
				soundBtnName = "combo3_1",
				soundIsMainRole = false,
			},
		},
		cameraShakes = {
			{
				CameraShakeBtnName = "attack2",
				eventName = "attack2/begin",
				numberOfShakes = 4,
				distance = 1.0,
				speed = 500.0,
				delay = 0.6,
				decay = 0.0,
			},
			{
				CameraShakeBtnName = "attack3",
				eventName = "attack3/begin",
				numberOfShakes = 2,
				distance = 1.0,
				speed = 1000.0,
				delay = 1.0,
				decay = 0.0,
			},
		},
		radialBlurs = {},

	},
}