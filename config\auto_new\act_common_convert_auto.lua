-- H-活动通用兑换.xls
local item_table={
[1]={item_id=27814,num=1,is_bind=1},
[2]={item_id=46513,num=1,is_bind=1},
[3]={item_id=37707,num=1,is_bind=1},
[4]={item_id=37608,num=1,is_bind=1},
[5]={item_id=38104,num=1,is_bind=1},
[6]={item_id=26345,num=1,is_bind=1},
[7]={item_id=26377,num=1,is_bind=1},
[8]={item_id=26376,num=1,is_bind=1},
[9]={item_id=26344,num=1,is_bind=1},
[10]={item_id=27909,num=1,is_bind=1},
[11]={item_id=27908,num=1,is_bind=1},
[12]={item_id=26500,num=1,is_bind=1},
[13]={item_id=26515,num=1,is_bind=1},
[14]={item_id=27611,num=1,is_bind=1},
[15]={item_id=30423,num=1,is_bind=1},
[16]={item_id=37057,num=1,is_bind=1},
[17]={item_id=37237,num=1,is_bind=1},
[18]={item_id=37508,num=1,is_bind=1},
[19]={item_id=37735,num=1,is_bind=1},
[20]={item_id=37635,num=1,is_bind=1},
[21]={item_id=50044,num=1,is_bind=1},
[22]={item_id=28671,num=1,is_bind=1},
[23]={item_id=46048,num=1,is_bind=1},
[24]={item_id=47106,num=1,is_bind=1},
[25]={item_id=50045,num=1,is_bind=1},
[26]={item_id=50051,num=1,is_bind=1},
[27]={item_id=50052,num=1,is_bind=1},
[28]={item_id=56317,num=1,is_bind=1},
[29]={item_id=50054,num=1,is_bind=1},
[30]={item_id=47098,num=1,is_bind=1},
[31]={item_id=29373,num=1,is_bind=1},
[32]={item_id=29374,num=1,is_bind=1},
[33]={item_id=26149,num=1,is_bind=1},
[34]={item_id=37072,num=1,is_bind=1},
[35]={item_id=37254,num=1,is_bind=1},
[36]={item_id=37545,num=1,is_bind=1},
[37]={item_id=37757,num=1,is_bind=1},
[38]={item_id=29375,num=1,is_bind=1},
[39]={item_id=37758,num=1,is_bind=1},
[40]={item_id=37655,num=1,is_bind=1},
[41]={item_id=46505,num=1,is_bind=1},
[42]={item_id=50043,num=1,is_bind=1},
[43]={item_id=29376,num=1,is_bind=1},
[44]={item_id=29377,num=1,is_bind=1},
[45]={item_id=50041,num=1,is_bind=1},
[46]={item_id=50042,num=1,is_bind=1},
[47]={item_id=26680,num=1,is_bind=1},
[48]={item_id=37654,num=1,is_bind=1},
[49]={item_id=29378,num=1,is_bind=1},
[50]={item_id=26665,num=1,is_bind=1},
[51]={item_id=37073,num=1,is_bind=1},
[52]={item_id=37255,num=1,is_bind=1},
[53]={item_id=37546,num=1,is_bind=1},
[54]={item_id=26415,num=1,is_bind=1},
}

return {
activity_time={
{}
},

activity_time_meta_table_map={
},
period={
{},
{week_index=5,period=8,},
{begin_server_day=18,server_day=24,},
{week_index=5,period=9,},
{begin_server_day=25,server_day=31,},
{week_index=5,period=10,},
{begin_server_day=32,server_day=38,},
{week_index=5,period=11,},
{begin_server_day=39,server_day=45,},
{week_index=5,period=12,},
{begin_server_day=46,server_day=52,},
{week_index=5,period=13,},
{begin_server_day=53,server_day=59,},
{begin_server_day=53,server_day=59,},
{begin_server_day=60,server_day=66,},
{week_index=5,period=9,},
{begin_server_day=67,server_day=73,},
{week_index=5,period=10,},
{begin_server_day=74,server_day=80,},
{week_index=5,period=11,},
{begin_server_day=81,server_day=87,},
{begin_server_day=81,server_day=87,},
{begin_server_day=88,server_day=94,},
{week_index=5,period=12,},
{begin_server_day=95,server_day=101,},
{week_index=5,period=13,},
{begin_server_day=102,server_day=108,},
{begin_server_day=102,server_day=108,},
{begin_server_day=109,server_day=115,},
{week_index=5,period=9,},
{begin_server_day=116,server_day=122,},
{week_index=5,period=10,},
{begin_server_day=123,server_day=129,},
{begin_server_day=123,server_day=129,},
{begin_server_day=130,server_day=136,},
{week_index=5,period=12,},
{begin_server_day=137,server_day=143,},
{begin_server_day=137,server_day=143,},
{begin_server_day=144,server_day=150,},
{begin_server_day=144,server_day=150,},
{begin_server_day=151,server_day=157,},
{begin_server_day=151,server_day=157,},
{begin_server_day=158,server_day=164,},
{week_index=5,period=12,},
{begin_server_day=165,server_day=171,},
{begin_server_day=165,server_day=171,},
{begin_server_day=172,server_day=178,},
{begin_server_day=172,server_day=178,},
{begin_server_day=179,server_day=185,},
{begin_server_day=179,server_day=185,},
{begin_server_day=186,server_day=192,},
{begin_server_day=186,server_day=192,},
{begin_server_day=193,server_day=199,},
{week_index=5,period=11,},
{begin_server_day=200,server_day=206,},
{week_index=5,period=12,},
{begin_server_day=207,server_day=213,},
{week_index=5,period=13,},
{begin_server_day=214,server_day=220,},
{begin_server_day=214,server_day=220,},
{begin_server_day=221,server_day=227,},
{begin_server_day=221,server_day=227,},
{begin_server_day=228,server_day=234,},
{week_index=5,period=12,},
{begin_server_day=235,server_day=241,},
{week_index=5,period=13,},
{begin_server_day=242,server_day=248,},
{begin_server_day=242,server_day=248,},
{begin_server_day=249,server_day=255,},
{week_index=5,period=9,},
{begin_server_day=256,server_day=262,},
{begin_server_day=256,server_day=262,},
{begin_server_day=263,server_day=269,},
{week_index=5,period=11,},
{begin_server_day=270,server_day=276,},
{week_index=5,period=12,},
{begin_server_day=277,server_day=283,},
{week_index=5,period=13,},
{begin_server_day=284,server_day=290,},
{begin_server_day=284,server_day=290,},
{begin_server_day=291,server_day=297,},
{begin_server_day=291,server_day=297,},
{begin_server_day=298,server_day=304,period=2,},
{week_index=5,period=9,},
{begin_server_day=305,server_day=311,},
{begin_server_day=305,server_day=311,},
{begin_server_day=312,server_day=318,},
{week_index=5,period=11,},
{begin_server_day=319,server_day=325,},
{week_index=5,period=12,},
{begin_server_day=326,server_day=332,period=6,},
{week_index=5,period=13,},
{begin_server_day=333,server_day=339,},
{begin_server_day=333,server_day=339,},
{begin_server_day=340,server_day=346,},
{week_index=5,period=9,},
{begin_server_day=347,server_day=353,period=3,},
{week_index=5,period=10,},
{begin_server_day=354,server_day=360,},
{week_index=5,period=11,},
{begin_server_day=361,server_day=367,},
{begin_server_day=361,server_day=367,},
{begin_server_day=368,server_day=374,},
{week_index=5,period=13,},
{begin_server_day=375,server_day=381,},
{begin_server_day=375,server_day=381,},
{begin_server_day=382,server_day=388,},
{week_index=5,period=9,},
{begin_server_day=389,server_day=395,},
{begin_server_day=389,server_day=395,},
{begin_server_day=396,server_day=402,},
{week_index=5,period=11,},
{begin_server_day=403,server_day=409,},
{week_index=5,period=12,},
{begin_server_day=410,server_day=416,},
{begin_server_day=410,server_day=416,},
{begin_server_day=417,server_day=423,},
{begin_server_day=417,server_day=423,},
{begin_server_day=424,server_day=430,},
{begin_server_day=424,server_day=430,},
{begin_server_day=431,server_day=437,},
{begin_server_day=431,server_day=437,},
{begin_server_day=438,server_day=444,period=4,},
{week_index=5,period=11,},
{begin_server_day=445,server_day=451,period=5,},
{week_index=5,period=12,},
{begin_server_day=452,server_day=458,},
{begin_server_day=452,server_day=458,},
{begin_server_day=459,server_day=465,},
{begin_server_day=459,server_day=465,},
{begin_server_day=466,server_day=472,},
{begin_server_day=466,server_day=472,},
{begin_server_day=473,server_day=479,},
{begin_server_day=473,server_day=479,},
{begin_server_day=480,server_day=486,},
{week_index=5,period=11,},
{begin_server_day=487,server_day=493,},
{week_index=5,period=12,},
{begin_server_day=494,server_day=500,},
{begin_server_day=494,server_day=500,},
{begin_server_day=501,server_day=507,},
{begin_server_day=501,server_day=507,},
{begin_server_day=508,server_day=514,},
{begin_server_day=508,server_day=514,},
{begin_server_day=515,server_day=521,},
{week_index=5,period=10,},
{begin_server_day=522,server_day=528,},
{week_index=5,period=11,},
{begin_server_day=529,server_day=535,},
{week_index=5,period=12,},
{begin_server_day=536,server_day=542,},
{week_index=5,period=13,},
{begin_server_day=543,server_day=549,},
{begin_server_day=543,server_day=549,},
{begin_server_day=550,server_day=556,},
{week_index=5,period=9,},
{begin_server_day=557,server_day=563,},
{week_index=5,period=10,},
{begin_server_day=564,server_day=570,},
{week_index=5,period=11,},
{begin_server_day=571,server_day=577,},
{week_index=5,period=12,},
{begin_server_day=578,server_day=584,},
{week_index=5,period=13,},
{begin_server_day=585,server_day=591,},
{begin_server_day=585,server_day=591,},
{begin_server_day=592,server_day=598,},
{week_index=5,period=9,},
{begin_server_day=599,server_day=605,},
{week_index=5,period=10,},
{begin_server_day=606,server_day=612,},
{week_index=5,period=11,},
{begin_server_day=613,server_day=619,},
{week_index=5,period=12,},
{begin_server_day=620,server_day=626,},
{week_index=5,period=13,},
{begin_server_day=627,server_day=633,},
{begin_server_day=627,server_day=633,},
{begin_server_day=634,server_day=640,},
{week_index=5,period=9,},
{begin_server_day=641,server_day=647,},
{week_index=5,period=10,},
{begin_server_day=648,server_day=654,},
{week_index=5,period=11,},
{begin_server_day=655,server_day=661,},
{week_index=5,period=12,},
{begin_server_day=662,server_day=668,},
{week_index=5,period=13,},
{begin_server_day=669,server_day=675,},
{begin_server_day=669,server_day=675,},
{begin_server_day=676,server_day=682,},
{week_index=5,period=9,},
{begin_server_day=683,server_day=689,},
{week_index=5,period=10,},
{begin_server_day=690,server_day=696,},
{week_index=5,period=11,},
{begin_server_day=697,server_day=703,},
{week_index=5,period=12,},
{begin_server_day=704,server_day=710,},
{week_index=5,period=13,},
{begin_server_day=711,server_day=717,},
{period=8,},
{begin_server_day=718,server_day=9999,},
{period=9,}
},

period_meta_table_map={
[85]=97,	-- depth:1
[121]=97,	-- depth:1
[119]=83,	-- depth:1
[87]=123,	-- depth:1
[113]=125,	-- depth:1
[89]=125,	-- depth:1
[99]=123,	-- depth:1
[111]=123,	-- depth:1
[109]=97,	-- depth:1
[107]=83,	-- depth:1
[127]=91,	-- depth:1
[95]=83,	-- depth:1
[203]=83,	-- depth:1
[101]=125,	-- depth:1
[115]=91,	-- depth:1
[103]=91,	-- depth:1
[139]=91,	-- depth:1
[133]=97,	-- depth:1
[202]=201,	-- depth:1
[199]=91,	-- depth:1
[197]=125,	-- depth:1
[195]=123,	-- depth:1
[193]=97,	-- depth:1
[191]=83,	-- depth:1
[187]=91,	-- depth:1
[185]=125,	-- depth:1
[183]=123,	-- depth:1
[181]=97,	-- depth:1
[179]=83,	-- depth:1
[175]=91,	-- depth:1
[173]=125,	-- depth:1
[131]=83,	-- depth:1
[171]=123,	-- depth:1
[167]=83,	-- depth:1
[163]=91,	-- depth:1
[161]=125,	-- depth:1
[159]=123,	-- depth:1
[157]=97,	-- depth:1
[155]=83,	-- depth:1
[151]=91,	-- depth:1
[149]=125,	-- depth:1
[147]=123,	-- depth:1
[145]=97,	-- depth:1
[143]=83,	-- depth:1
[137]=125,	-- depth:1
[135]=123,	-- depth:1
[169]=97,	-- depth:1
[77]=91,	-- depth:1
[204]=203,	-- depth:2
[37]=91,	-- depth:1
[63]=125,	-- depth:1
[15]=83,	-- depth:1
[57]=91,	-- depth:1
[55]=125,	-- depth:1
[33]=123,	-- depth:1
[53]=123,	-- depth:1
[19]=123,	-- depth:1
[11]=91,	-- depth:1
[51]=97,	-- depth:1
[23]=125,	-- depth:1
[45]=91,	-- depth:1
[25]=91,	-- depth:1
[43]=125,	-- depth:1
[29]=83,	-- depth:1
[31]=97,	-- depth:1
[35]=125,	-- depth:1
[49]=83,	-- depth:1
[65]=91,	-- depth:1
[17]=97,	-- depth:1
[75]=125,	-- depth:1
[9]=125,	-- depth:1
[71]=97,	-- depth:1
[3]=83,	-- depth:1
[5]=97,	-- depth:1
[73]=123,	-- depth:1
[7]=123,	-- depth:1
[69]=83,	-- depth:1
[188]=187,	-- depth:2
[24]=23,	-- depth:2
[164]=163,	-- depth:2
[162]=161,	-- depth:2
[26]=25,	-- depth:2
[200]=199,	-- depth:2
[28]=2,	-- depth:1
[166]=28,	-- depth:2
[158]=157,	-- depth:2
[4]=3,	-- depth:2
[156]=155,	-- depth:2
[30]=29,	-- depth:2
[154]=28,	-- depth:2
[152]=151,	-- depth:2
[32]=31,	-- depth:2
[150]=149,	-- depth:2
[160]=159,	-- depth:2
[198]=197,	-- depth:2
[22]=28,	-- depth:2
[6]=5,	-- depth:2
[190]=28,	-- depth:2
[186]=185,	-- depth:2
[12]=11,	-- depth:2
[184]=183,	-- depth:2
[14]=28,	-- depth:2
[182]=181,	-- depth:2
[192]=191,	-- depth:2
[8]=7,	-- depth:2
[180]=179,	-- depth:2
[168]=167,	-- depth:2
[16]=15,	-- depth:2
[194]=193,	-- depth:2
[176]=175,	-- depth:2
[18]=17,	-- depth:2
[174]=173,	-- depth:2
[196]=195,	-- depth:2
[172]=171,	-- depth:2
[20]=19,	-- depth:2
[170]=169,	-- depth:2
[10]=9,	-- depth:2
[178]=28,	-- depth:2
[148]=147,	-- depth:2
[78]=77,	-- depth:2
[146]=145,	-- depth:2
[56]=55,	-- depth:2
[108]=107,	-- depth:2
[106]=28,	-- depth:2
[58]=57,	-- depth:2
[104]=103,	-- depth:2
[60]=28,	-- depth:2
[62]=28,	-- depth:2
[100]=99,	-- depth:2
[64]=63,	-- depth:2
[98]=97,	-- depth:1
[96]=95,	-- depth:2
[110]=98,	-- depth:2
[66]=65,	-- depth:2
[68]=28,	-- depth:2
[92]=91,	-- depth:1
[90]=89,	-- depth:2
[70]=69,	-- depth:2
[88]=87,	-- depth:2
[86]=98,	-- depth:2
[72]=98,	-- depth:2
[84]=83,	-- depth:1
[82]=28,	-- depth:2
[74]=73,	-- depth:2
[80]=28,	-- depth:2
[94]=28,	-- depth:2
[112]=111,	-- depth:2
[54]=53,	-- depth:2
[114]=113,	-- depth:2
[144]=84,	-- depth:2
[36]=35,	-- depth:2
[142]=28,	-- depth:2
[76]=75,	-- depth:2
[140]=92,	-- depth:2
[38]=92,	-- depth:2
[138]=137,	-- depth:2
[40]=28,	-- depth:2
[136]=135,	-- depth:2
[42]=28,	-- depth:2
[134]=98,	-- depth:2
[132]=84,	-- depth:2
[44]=43,	-- depth:2
[130]=28,	-- depth:2
[128]=92,	-- depth:2
[46]=92,	-- depth:2
[126]=125,	-- depth:1
[48]=28,	-- depth:2
[124]=123,	-- depth:1
[122]=98,	-- depth:2
[50]=84,	-- depth:2
[120]=84,	-- depth:2
[118]=28,	-- depth:2
[52]=98,	-- depth:2
[116]=92,	-- depth:2
[34]=124,	-- depth:2
[102]=126,	-- depth:2
},
item={
{reward_item=item_table[1],stuff_id=46558,limit_num=5,},
{seq=1,reward_item=item_table[2],stuff_count=2,sort=7,},
{seq=2,reward_item=item_table[3],stuff_count=13,sort=8,},
{seq=3,reward_item=item_table[4],stuff_id=46558,stuff_count=20,sort=9,},
{seq=4,reward_item=item_table[5],stuff_count=10,sort=10,},
{seq=5,stuff_count=2,limit_num=10,sort=11,},
{seq=6,reward_item=item_table[6],sort=12,},
{seq=7,reward_item=item_table[7],stuff_count=10,limit_num=2,sort=13,},
{seq=8,reward_item=item_table[8],limit_num=10,sort=14,},
{seq=9,reward_item=item_table[9],limit_num=10,sort=15,},
{seq=10,reward_item=item_table[10],sort=16,},
{seq=11,reward_item=item_table[11],stuff_count=5,limit_num=5,sort=17,},
{seq=12,reward_item=item_table[12],sort=18,},
{seq=13,reward_item=item_table[13],sort=19,},
{seq=14,reward_item=item_table[14],limit_num=10,sort=20,},
{seq=15,reward_item=item_table[15],limit_num=10,sort=21,},
{seq=16,reward_item=item_table[16],stuff_count=6000,sort=1,},
{seq=17,reward_item=item_table[17],sort=2,},
{seq=18,reward_item=item_table[18],stuff_count=3000,sort=3,},
{seq=19,reward_item=item_table[19],sort=4,},
{seq=20,reward_item=item_table[20],stuff_id=46584,stuff_count=1000,sort=5,},
{period=2,},
{period=2,},
{period=2,},
{period=2,},
{period=2,},
{period=2,},
{period=2,},
{period=2,},
{period=2,},
{period=2,},
{period=2,},
{period=2,},
{period=2,},
{period=2,},
{period=2,},
{period=2,},
{period=2,},
{period=2,},
{period=2,},
{period=2,},
{period=2,},
{period=3,},
{period=3,reward_item=item_table[21],},
{period=3,},
{period=3,},
{period=3,},
{period=3,},
{period=3,},
{period=3,},
{period=3,},
{period=3,},
{period=3,},
{period=3,},
{period=3,},
{period=3,},
{period=3,},
{period=3,},
{period=3,},
{period=3,},
{period=3,},
{period=3,},
{period=3,},
{period=4,stuff_id=46558,},
{period=4,},
{period=4,},
{period=4,reward_item=item_table[22],stuff_id=46558,},
{seq=4,reward_item=item_table[23],sort=10,},
{period=4,},
{period=4,},
{period=4,},
{period=4,reward_item=item_table[24],stuff_count=2,limit_num=5,},
{period=4,},
{period=4,},
{period=4,},
{period=4,},
{period=4,},
{period=4,},
{period=4,},
{period=4,},
{period=4,},
{period=4,},
{period=4,},
{period=4,},
{period=5,},
{period=5,},
{period=5,seq=2,reward_item=item_table[25],sort=8,},
{period=5,},
{period=5,},
{period=5,},
{period=5,},
{period=5,},
{period=5,},
{period=5,},
{period=5,},
{period=5,},
{period=5,},
{period=5,},
{period=5,},
{period=5,},
{period=5,},
{period=5,},
{period=5,},
{period=5,},
{period=5,},
{period=6,},
{period=6,},
{period=6,},
{period=6,},
{period=6,},
{period=6,},
{period=6,},
{period=6,},
{period=6,},
{period=6,},
{period=6,},
{period=6,},
{period=6,},
{period=6,},
{period=6,},
{period=6,},
{period=6,},
{period=6,},
{period=6,},
{period=6,},
{period=6,},
{period=7,},
{period=7,reward_item=item_table[26],stuff_count=6,},
{period=7,reward_item=item_table[27],stuff_count=10,},
{period=7,reward_item=item_table[28],},
{period=7,reward_item=item_table[29],},
{period=7,},
{period=7,},
{period=7,},
{period=7,},
{period=7,reward_item=item_table[30],stuff_count=2,limit_num=5,},
{period=7,},
{period=7,},
{period=7,},
{period=7,},
{period=7,},
{period=7,},
{period=7,stuff_id=46589,},
{period=7,stuff_id=46589,},
{period=7,stuff_id=46589,},
{period=7,stuff_id=46589,},
{period=7,stuff_id=46589,},
{period=8,},
{period=8,stuff_id=46560,},
{period=8,reward_item=item_table[31],},
{period=8,},
{period=8,},
{period=8,},
{period=8,},
{period=8,},
{period=8,},
{period=8,},
{period=8,},
{period=8,},
{period=8,},
{period=8,},
{period=8,},
{period=8,},
{period=8,},
{period=8,},
{period=8,},
{period=8,},
{period=8,},
{period=9,reward_item=item_table[1],},
{period=9,},
{period=9,reward_item=item_table[32],stuff_id=46560,},
{period=9,stuff_id=46560,},
{period=9,stuff_id=46560,},
{period=9,stuff_id=46559,},
{period=9,},
{period=9,},
{period=9,stuff_id=46559,},
{period=9,stuff_id=46559,},
{period=9,stuff_id=46559,},
{period=9,stuff_id=46559,},
{period=9,stuff_id=46559,},
{period=9,stuff_id=46559,},
{period=9,stuff_id=46559,},
{period=9,reward_item=item_table[33],stuff_id=46559,},
{period=9,reward_item=item_table[34],stuff_id=46589,},
{period=9,reward_item=item_table[35],stuff_id=46589,},
{period=9,reward_item=item_table[36],stuff_id=46589,},
{period=9,reward_item=item_table[37],stuff_id=46589,},
{period=9,},
{period=10,},
{period=10,},
{period=10,reward_item=item_table[38],},
{period=10,},
{period=10,},
{period=10,},
{period=10,stuff_id=46559,},
{period=10,stuff_id=46559,},
{period=10,stuff_id=46559,},
{period=10,},
{period=10,},
{period=10,},
{period=10,},
{period=10,},
{period=10,},
{period=10,},
{period=10,},
{period=10,},
{period=10,},
{period=10,reward_item=item_table[39],stuff_id=46590,},
{period=10,reward_item=item_table[40],stuff_id=46590,},
{period=11,reward_item=item_table[41],stuff_id=46560,limit_num=5,},
{seq=1,reward_item=item_table[42],sort=7,},
{period=11,reward_item=item_table[43],},
{period=11,},
{period=11,},
{period=11,},
{period=11,},
{period=11,},
{period=11,},
{period=11,},
{period=11,},
{period=11,},
{period=11,},
{period=11,},
{period=11,},
{period=11,},
{period=11,},
{period=11,},
{period=11,},
{period=11,},
{period=11,},
{period=12,},
{period=12,},
{period=12,reward_item=item_table[44],},
{seq=3,reward_item=item_table[45],limit_num=3,sort=9,},
{seq=4,reward_item=item_table[46],sort=10,},
{period=12,},
{period=12,},
{period=12,},
{period=12,},
{period=12,},
{period=12,},
{period=12,},
{period=12,},
{period=12,reward_item=item_table[47],},
{period=12,},
{period=12,},
{period=12,},
{period=12,},
{period=12,},
{period=12,},
{period=12,reward_item=item_table[48],},
{period=13,},
{period=13,},
{period=13,reward_item=item_table[49],},
{period=13,},
{period=13,},
{period=13,},
{period=13,},
{period=13,},
{period=13,},
{period=13,stuff_id=46559,},
{period=13,},
{period=13,},
{period=13,reward_item=item_table[50],},
{period=13,},
{period=13,},
{period=13,},
{period=13,reward_item=item_table[51],stuff_id=46590,},
{period=13,reward_item=item_table[52],stuff_id=46590,},
{period=13,reward_item=item_table[53],stuff_id=46590,},
{period=13,},
{period=13,}
},

item_meta_table_map={
[64]=211,	-- depth:1
[169]=211,	-- depth:1
[106]=64,	-- depth:2
[232]=211,	-- depth:1
[190]=232,	-- depth:2
[148]=169,	-- depth:2
[253]=190,	-- depth:3
[127]=106,	-- depth:3
[22]=1,	-- depth:1
[43]=127,	-- depth:4
[85]=43,	-- depth:5
[7]=8,	-- depth:1
[48]=6,	-- depth:1
[14]=6,	-- depth:1
[111]=48,	-- depth:2
[57]=15,	-- depth:1
[120]=57,	-- depth:2
[58]=16,	-- depth:1
[3]=4,	-- depth:1
[100]=58,	-- depth:2
[99]=120,	-- depth:3
[132]=111,	-- depth:3
[69]=132,	-- depth:4
[78]=99,	-- depth:4
[79]=100,	-- depth:3
[90]=69,	-- depth:5
[5]=4,	-- depth:1
[121]=79,	-- depth:4
[13]=6,	-- depth:1
[37]=121,	-- depth:5
[20]=21,	-- depth:1
[19]=21,	-- depth:1
[141]=78,	-- depth:5
[27]=90,	-- depth:6
[142]=37,	-- depth:6
[11]=8,	-- depth:1
[31]=10,	-- depth:1
[30]=9,	-- depth:1
[18]=19,	-- depth:2
[17]=21,	-- depth:1
[36]=141,	-- depth:6
[183]=57,	-- depth:2
[185]=17,	-- depth:2
[178]=31,	-- depth:2
[177]=30,	-- depth:2
[174]=48,	-- depth:2
[173]=5,	-- depth:2
[186]=18,	-- depth:3
[172]=4,	-- depth:1
[187]=19,	-- depth:2
[188]=20,	-- depth:2
[184]=16,	-- depth:1
[171]=3,	-- depth:2
[168]=21,	-- depth:1
[139]=13,	-- depth:2
[147]=21,	-- depth:1
[145]=19,	-- depth:2
[144]=18,	-- depth:3
[150]=171,	-- depth:3
[151]=172,	-- depth:2
[152]=173,	-- depth:3
[153]=174,	-- depth:3
[143]=17,	-- depth:2
[156]=177,	-- depth:3
[162]=183,	-- depth:3
[163]=184,	-- depth:2
[164]=17,	-- depth:2
[165]=18,	-- depth:3
[140]=14,	-- depth:2
[166]=19,	-- depth:2
[167]=20,	-- depth:2
[146]=20,	-- depth:2
[138]=12,	-- depth:1
[157]=178,	-- depth:3
[212]=211,	-- depth:1
[191]=212,	-- depth:2
[235]=232,	-- depth:2
[236]=235,	-- depth:3
[237]=153,	-- depth:4
[246]=162,	-- depth:4
[247]=163,	-- depth:3
[248]=185,	-- depth:3
[249]=186,	-- depth:4
[250]=187,	-- depth:3
[251]=188,	-- depth:3
[234]=171,	-- depth:3
[252]=147,	-- depth:2
[255]=171,	-- depth:3
[256]=235,	-- depth:3
[257]=236,	-- depth:4
[258]=237,	-- depth:5
[267]=246,	-- depth:5
[268]=247,	-- depth:4
[269]=17,	-- depth:2
[270]=18,	-- depth:3
[271]=19,	-- depth:2
[254]=191,	-- depth:3
[233]=254,	-- depth:4
[231]=168,	-- depth:2
[230]=167,	-- depth:3
[192]=171,	-- depth:3
[193]=256,	-- depth:4
[194]=257,	-- depth:5
[195]=258,	-- depth:6
[204]=267,	-- depth:6
[205]=268,	-- depth:5
[206]=269,	-- depth:3
[207]=270,	-- depth:4
[208]=271,	-- depth:3
[209]=20,	-- depth:2
[210]=21,	-- depth:1
[272]=209,	-- depth:3
[213]=171,	-- depth:3
[214]=193,	-- depth:5
[215]=194,	-- depth:6
[216]=195,	-- depth:7
[225]=204,	-- depth:7
[226]=205,	-- depth:6
[227]=164,	-- depth:3
[228]=165,	-- depth:4
[229]=166,	-- depth:3
[189]=252,	-- depth:3
[136]=10,	-- depth:1
[137]=11,	-- depth:2
[134]=8,	-- depth:1
[59]=206,	-- depth:4
[60]=207,	-- depth:5
[61]=208,	-- depth:4
[62]=272,	-- depth:4
[63]=210,	-- depth:2
[67]=235,	-- depth:3
[68]=67,	-- depth:4
[70]=7,	-- depth:2
[71]=134,	-- depth:2
[72]=9,	-- depth:1
[73]=136,	-- depth:2
[74]=137,	-- depth:3
[75]=138,	-- depth:2
[135]=72,	-- depth:2
[77]=140,	-- depth:3
[80]=227,	-- depth:4
[81]=228,	-- depth:5
[82]=229,	-- depth:4
[83]=230,	-- depth:4
[84]=231,	-- depth:3
[88]=67,	-- depth:4
[56]=77,	-- depth:4
[89]=68,	-- depth:5
[55]=139,	-- depth:3
[53]=74,	-- depth:4
[2]=1,	-- depth:1
[24]=3,	-- depth:2
[25]=4,	-- depth:1
[26]=5,	-- depth:2
[28]=70,	-- depth:3
[29]=71,	-- depth:3
[32]=53,	-- depth:5
[33]=75,	-- depth:3
[34]=55,	-- depth:4
[35]=56,	-- depth:5
[38]=248,	-- depth:4
[39]=249,	-- depth:5
[40]=250,	-- depth:4
[41]=251,	-- depth:4
[42]=189,	-- depth:4
[46]=88,	-- depth:5
[47]=89,	-- depth:6
[49]=28,	-- depth:4
[50]=29,	-- depth:4
[51]=135,	-- depth:3
[52]=73,	-- depth:3
[54]=33,	-- depth:4
[91]=49,	-- depth:5
[76]=34,	-- depth:5
[93]=51,	-- depth:4
[104]=41,	-- depth:5
[105]=42,	-- depth:5
[109]=46,	-- depth:6
[110]=47,	-- depth:7
[92]=50,	-- depth:5
[112]=91,	-- depth:6
[113]=92,	-- depth:6
[114]=93,	-- depth:5
[115]=52,	-- depth:4
[116]=32,	-- depth:6
[117]=54,	-- depth:5
[118]=76,	-- depth:6
[119]=35,	-- depth:6
[122]=59,	-- depth:5
[123]=60,	-- depth:6
[124]=61,	-- depth:5
[125]=62,	-- depth:5
[126]=63,	-- depth:3
[130]=67,	-- depth:4
[131]=68,	-- depth:5
[133]=112,	-- depth:7
[103]=40,	-- depth:5
[102]=39,	-- depth:6
[273]=126,	-- depth:4
[101]=38,	-- depth:5
[95]=116,	-- depth:7
[94]=115,	-- depth:5
[97]=118,	-- depth:7
[96]=117,	-- depth:6
[98]=119,	-- depth:7
[87]=2,	-- depth:2
[45]=87,	-- depth:3
[44]=2,	-- depth:2
[198]=72,	-- depth:2
[197]=134,	-- depth:2
[149]=2,	-- depth:2
[196]=70,	-- depth:3
[176]=197,	-- depth:3
[182]=140,	-- depth:3
[179]=137,	-- depth:3
[180]=138,	-- depth:2
[23]=2,	-- depth:2
[181]=139,	-- depth:3
[259]=196,	-- depth:4
[245]=182,	-- depth:4
[260]=176,	-- depth:4
[261]=198,	-- depth:3
[262]=136,	-- depth:2
[263]=179,	-- depth:4
[264]=180,	-- depth:3
[265]=181,	-- depth:4
[266]=245,	-- depth:5
[129]=87,	-- depth:3
[86]=44,	-- depth:3
[128]=2,	-- depth:2
[244]=265,	-- depth:5
[242]=263,	-- depth:5
[170]=149,	-- depth:3
[66]=45,	-- depth:4
[65]=86,	-- depth:4
[107]=65,	-- depth:5
[217]=259,	-- depth:5
[218]=260,	-- depth:5
[219]=261,	-- depth:4
[220]=262,	-- depth:3
[221]=242,	-- depth:6
[222]=264,	-- depth:4
[223]=181,	-- depth:4
[224]=182,	-- depth:4
[108]=66,	-- depth:5
[175]=217,	-- depth:6
[161]=224,	-- depth:5
[203]=161,	-- depth:6
[159]=222,	-- depth:5
[158]=221,	-- depth:7
[202]=223,	-- depth:5
[201]=159,	-- depth:6
[155]=218,	-- depth:6
[154]=175,	-- depth:7
[200]=158,	-- depth:8
[199]=220,	-- depth:4
[238]=154,	-- depth:8
[239]=155,	-- depth:7
[240]=219,	-- depth:5
[241]=199,	-- depth:5
[243]=201,	-- depth:7
[160]=202,	-- depth:6
},
show={
{subtab_id="周中活动",},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{},
{}
},

show_meta_table_map={
[2]=1,	-- depth:1
},
interface={
{},
{interface=2,head_picture="banner_exchange_shop_1",list_bg="cslc_04_1",exchange_name_bg="total_left_wenzidi_2",item_bg="total_reward_bg2",}
},

interface_meta_table_map={
},
activity_time_default_table={activity_version=1,activity_icon="icon_1",activity_icon="",},

period_default_table={begin_server_day=10,server_day=17,week_index=2,period=1,},

item_default_table={period=1,seq=0,reward_item=item_table[54],stuff_id=46557,stuff_count=1,limit_num=1,sort=6,stuff_icon="icon_exchange_shop1_2",},

show_default_table={subtab_id="周末活动",show_picture="",show_picture_bundle="",show_item_id=37712,show_model="",show_model_bundle="",position="0|0|0",rotation="-2.49|-4.5|-0.71",scale=1.3,rule_desc="1.活动期间完成相关<color=#95d12b>运营活动</color>可获得<color=#95d12b>兑换道具</color>\n2.可消耗相应数量<color=#95d12b>兑换道具</color>在活动商店兑换奖励",btn_rule_title="活动商店",out_side_rule="消耗材料可兑换丰厚奖励",open_level=100,},

interface_default_table={interface=1,head_picture="banner_exchange_shop",decorate_picture="word_exchange_shop",list_bg="cslc_04",exchange_shop_dizuo="state_exchange_shop",exchange_name_bg="total_left_wenzidi_1",item_bg="total_reward_bg1",word_describe="：活动期间通过完成其他相关<color=#95d12b>活动任务</color>可获得兑换材料",item_name="龙神降临",exchange_des_left="活动描述",}

}

