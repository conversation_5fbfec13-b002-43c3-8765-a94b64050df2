-- Z-战令.xls
local item_table={
[1]={item_id=26097,num=1,is_bind=1},
[2]={item_id=27611,num=6,is_bind=1},
[3]={item_id=26409,num=10,is_bind=1},
[4]={item_id=39147,num=600,is_bind=1},
[5]={item_id=44049,num=1,is_bind=1},
[6]={item_id=37906,num=1,is_bind=1},
[7]={item_id=39105,num=50,is_bind=1},
[8]={item_id=37805,num=1,is_bind=1},
[9]={item_id=27811,num=2,is_bind=1},
[10]={item_id=39144,num=1000,is_bind=1},
[11]={item_id=26503,num=1,is_bind=1},
[12]={item_id=37907,num=1,is_bind=1},
[13]={item_id=37806,num=1,is_bind=1},
[14]={item_id=26518,num=1,is_bind=1},
[15]={item_id=27811,num=4,is_bind=1},
[16]={item_id=44051,num=1,is_bind=1},
[17]={item_id=27611,num=2,is_bind=1},
[18]={item_id=46048,num=2,is_bind=1},
[19]={item_id=39147,num=1,is_bind=1},
[20]={item_id=44052,num=1,is_bind=1},
[21]={item_id=44053,num=1,is_bind=1},
[22]={item_id=39147,num=200,is_bind=1},
[23]={item_id=27611,num=1,is_bind=1},
[24]={item_id=27811,num=1,is_bind=1},
[25]={item_id=46048,num=1,is_bind=1},
[26]={item_id=26409,num=1,is_bind=1},
[27]={item_id=39105,num=1,is_bind=1},
[28]={item_id=39144,num=1,is_bind=1},
}

return {
other={
{}
},

other_meta_table_map={
},
up_level={
{reward_item_1={[0]=item_table[1]},reward_item_2={[0]=item_table[2],[1]=item_table[3]},},
{level=2,reward_item_2={[0]=item_table[1],[1]=item_table[4]},},
{level=3,reward_item_1={[0]=item_table[5]},reward_item_2={[0]=item_table[5],[1]=item_table[6]},},
{level=4,},
{level=5,reward_item_2={[0]=item_table[7],[1]=item_table[8]},},
{level=6,},
{level=7,},
{level=8,},
{level=9,},
{level=10,},
{level=11,},
{level=12,},
{level=13,},
{level=14,},
{level=15,reward_item_2={[0]=item_table[7],[1]=item_table[5]},},
{level=16,},
{level=17,},
{level=18,},
{level=19,},
{level=20,reward_item_1={[0]=item_table[9]},reward_item_2={[0]=item_table[9],[1]=item_table[10]},guding_mark=1,},
{level=21,},
{level=22,},
{level=23,},
{level=24,},
{level=25,reward_item_2={[0]=item_table[11],[1]=item_table[12]},},
{level=26,},
{level=27,},
{level=28,},
{level=29,},
{level=30,reward_item_2={[0]=item_table[9],[1]=item_table[13]},},
{level=31,},
{level=32,},
{level=33,},
{level=34,},
{level=35,reward_item_2={[0]=item_table[14],[1]=item_table[3]},},
{level=36,},
{level=37,},
{level=38,},
{level=39,},
{level=40,},
{level=41,},
{level=42,},
{level=43,},
{level=44,},
{level=45,},
{level=46,},
{level=47,},
{level=48,},
{level=49,},
{level=50,reward_item_2={[0]=item_table[9],[1]=item_table[5]},},
{level=51,},
{level=52,},
{level=53,},
{level=54,},
{level=55,},
{level=56,},
{level=57,},
{level=58,},
{level=59,},
{level=60,},
{level=61,},
{level=62,},
{level=63,},
{level=64,},
{level=65,},
{level=66,},
{level=67,},
{level=68,},
{level=69,},
{level=70,reward_item_2={[0]=item_table[15],[1]=item_table[5]},},
{level=71,},
{level=72,},
{level=73,},
{level=74,},
{level=75,},
{level=76,},
{level=77,},
{level=78,},
{level=79,},
{level=80,reward_item_2={[0]=item_table[15],[1]=item_table[10]},},
{level=81,},
{level=82,},
{level=83,},
{level=84,},
{level=85,reward_item_1={[0]=item_table[16]},reward_item_2={[0]=item_table[11],[1]=item_table[10]},},
{level=86,},
{level=87,reward_item_1={[0]=item_table[17]},reward_item_2={[0]=item_table[2]},},
{level=88,},
{level=89,},
{level=90,reward_item_1={[0]=item_table[18]},model_item_id=27803,},
{cycle_id=2,reward_item_1={[0]=item_table[17]},},
{level=2,},
{cycle_id=2,},
{level=4,},
{cycle_id=2,},
{level=6,},
{level=7,},
{level=8,},
{level=9,},
{level=10,},
{level=11,},
{level=12,},
{level=13,},
{level=14,},
{cycle_id=2,},
{level=16,},
{level=17,},
{level=18,},
{level=19,},
{level=20,},
{level=21,},
{level=22,},
{level=23,},
{level=24,},
{cycle_id=2,},
{level=26,},
{level=27,},
{level=28,},
{level=29,},
{cycle_id=2,},
{level=31,},
{level=32,},
{level=33,},
{level=34,},
{level=35,},
{level=36,},
{level=37,},
{level=38,},
{level=39,},
{level=40,},
{level=41,},
{level=42,},
{level=43,},
{cycle_id=2,},
{level=45,},
{level=46,},
{level=47,},
{level=48,},
{level=49,},
{cycle_id=2,},
{level=51,},
{cycle_id=2,},
{level=53,},
{level=54,},
{cycle_id=2,},
{level=56,},
{level=57,},
{level=58,},
{level=59,},
{cycle_id=2,},
{level=61,},
{level=62,},
{level=63,},
{level=64,},
{cycle_id=2,},
{level=66,},
{level=67,},
{level=68,},
{level=69,},
{cycle_id=2,},
{level=71,},
{level=72,},
{level=73,},
{level=74,},
{level=75,},
{level=76,},
{level=77,},
{level=78,},
{level=79,},
{cycle_id=2,},
{level=81,},
{level=82,},
{level=83,},
{level=84,},
{level=85,},
{level=86,},
{level=87,},
{level=88,},
{level=89,},
{cycle_id=2,}
},

up_level_meta_table_map={
[134]=44,	-- depth:1
[98]=134,	-- depth:2
[94]=98,	-- depth:3
[92]=94,	-- depth:4
[153]=92,	-- depth:5
[158]=153,	-- depth:6
[154]=158,	-- depth:7
[173]=154,	-- depth:8
[159]=173,	-- depth:9
[163]=159,	-- depth:10
[169]=163,	-- depth:11
[164]=169,	-- depth:12
[168]=164,	-- depth:13
[179]=168,	-- depth:14
[138]=179,	-- depth:15
[99]=138,	-- depth:16
[103]=99,	-- depth:17
[139]=103,	-- depth:18
[133]=139,	-- depth:19
[129]=133,	-- depth:20
[128]=129,	-- depth:21
[174]=128,	-- depth:22
[124]=174,	-- depth:23
[123]=124,	-- depth:24
[178]=123,	-- depth:25
[143]=178,	-- depth:26
[119]=143,	-- depth:27
[118]=119,	-- depth:28
[114]=118,	-- depth:29
[113]=114,	-- depth:30
[109]=113,	-- depth:31
[108]=109,	-- depth:32
[104]=108,	-- depth:33
[148]=104,	-- depth:34
[149]=148,	-- depth:35
[144]=149,	-- depth:36
[86]=87,	-- depth:1
[91]=1,	-- depth:1
[82]=86,	-- depth:2
[81]=82,	-- depth:3
[46]=81,	-- depth:4
[25]=85,	-- depth:1
[45]=85,	-- depth:1
[11]=46,	-- depth:5
[42]=11,	-- depth:6
[41]=42,	-- depth:7
[12]=41,	-- depth:8
[15]=85,	-- depth:1
[37]=12,	-- depth:9
[7]=37,	-- depth:10
[16]=7,	-- depth:11
[36]=16,	-- depth:12
[35]=85,	-- depth:1
[77]=36,	-- depth:13
[32]=77,	-- depth:14
[31]=32,	-- depth:15
[21]=31,	-- depth:16
[27]=21,	-- depth:17
[22]=27,	-- depth:18
[17]=22,	-- depth:19
[47]=17,	-- depth:20
[26]=47,	-- depth:21
[66]=26,	-- depth:22
[71]=66,	-- depth:23
[56]=71,	-- depth:24
[65]=45,	-- depth:2
[5]=3,	-- depth:1
[72]=56,	-- depth:25
[61]=72,	-- depth:26
[62]=61,	-- depth:27
[55]=35,	-- depth:2
[67]=62,	-- depth:28
[75]=55,	-- depth:3
[52]=67,	-- depth:29
[51]=52,	-- depth:30
[76]=51,	-- depth:31
[6]=76,	-- depth:32
[57]=6,	-- depth:33
[145]=55,	-- depth:3
[142]=52,	-- depth:30
[141]=142,	-- depth:31
[146]=141,	-- depth:32
[161]=146,	-- depth:33
[167]=161,	-- depth:34
[162]=167,	-- depth:35
[151]=162,	-- depth:36
[152]=151,	-- depth:37
[166]=152,	-- depth:38
[165]=145,	-- depth:4
[155]=65,	-- depth:3
[156]=166,	-- depth:39
[157]=156,	-- depth:40
[10]=20,	-- depth:1
[172]=157,	-- depth:41
[147]=172,	-- depth:42
[80]=20,	-- depth:1
[125]=165,	-- depth:5
[136]=147,	-- depth:43
[70]=20,	-- depth:1
[60]=10,	-- depth:2
[93]=3,	-- depth:1
[95]=5,	-- depth:2
[96]=136,	-- depth:44
[97]=96,	-- depth:45
[101]=97,	-- depth:46
[102]=101,	-- depth:47
[50]=20,	-- depth:1
[105]=15,	-- depth:2
[106]=102,	-- depth:48
[107]=106,	-- depth:49
[177]=107,	-- depth:50
[111]=177,	-- depth:51
[112]=111,	-- depth:52
[115]=25,	-- depth:2
[116]=112,	-- depth:53
[117]=116,	-- depth:54
[40]=60,	-- depth:3
[176]=117,	-- depth:55
[121]=176,	-- depth:56
[122]=121,	-- depth:57
[126]=122,	-- depth:58
[127]=126,	-- depth:59
[175]=155,	-- depth:4
[131]=127,	-- depth:60
[132]=131,	-- depth:61
[30]=20,	-- depth:1
[135]=175,	-- depth:5
[137]=132,	-- depth:62
[171]=137,	-- depth:63
[90]=70,	-- depth:2
[160]=70,	-- depth:2
[150]=60,	-- depth:3
[140]=50,	-- depth:2
[130]=150,	-- depth:4
[120]=30,	-- depth:2
[110]=130,	-- depth:5
[100]=110,	-- depth:6
[170]=80,	-- depth:2
[180]=90,	-- depth:3
},
task_list={
{task_type=2,task_des="世界发言%s次",jump_view="ChatView#70",},
{task_id=2,task_type=1,task_des="仙盟发言%s次",jump_view="ChatView#60",},
{task_id=3,level_limit=178,task_type=31,task_des="赠送%s朵玫瑰",jump_view="Flower",},
{task_id=4,level_limit=200,open_server_limit=4,param1=3,jump_view="boss#boss_world",},
{task_id=5,level_limit=200,open_server_limit=2,task_type=11,task_des="参与击杀%s只谪仙之境BOSS",jump_view="WorldServer#world_new_shenyuan_boss",},
{task_id=6,level_limit=240,task_type=10,task_weight=0,task_des="击杀%s只天道苍茫BOSS",jump_view="boss#boss_dabao",},
{task_id=7,level_limit=200,open_server_limit=5,task_type=12,param1=3,task_des="击杀%s只荒天炎窟BOSS",jump_view="WorldServer#worserv_boss_mh",},
{task_id=8,task_type=9,task_des="击杀%s只心魔牢狱BOSS",jump_view="boss#boss_personal",},
{task_id=9,task_type=8,param1=5,task_des="击杀%s只仙遗洞天BOSS",jump_view="boss#boss_vip",},
{task_id=10,task_type=33,param1=5,task_weight=0,task_des="参与论道天下%s次",jump_view="act_jjc",},
{task_id=11,task_type=19,param1=20,task_des="完成%s次悬赏任务",jump_view="bizuo#bizuo_bizuo",},
{task_id=12,task_type=21,param1=2,task_weight=5000,task_des="铜钱祈福%s次",jump_view="qifu#qifu_qf",},
{task_id=13,task_type=20,task_weight=3125,task_des="经验祈福%s次",jump_view="qifu#qifu_qf",},
{task_id=14,task_type=24,param1=3,task_des="完成%s次双倍护送",},
{task_id=15,task_type=23,task_des="参与仙境夜宴",jump_view="guild#guild_activity",},
{task_id=16,task_type=25,task_des="参与仙盟守护",jump_view="guild#guild_activity",},
{task_id=17,level_limit=200,open_server_limit=6,task_type=15,param1=3,task_des="完成熔火之心副本%s次",jump_view="fubenpanel#fubenpanel_copper",},
{task_id=18,task_type=16,task_des="完成蓬莱仙境副本%s次",jump_view="fubenpanel#fubenpanel_pet",},
{task_id=19,level_limit=360,open_server_limit=6,task_type=18,param1=10,task_weight=0,task_des="完成古仙遗迹任务%s次",jump_view="fubenpanel#fubenpanel_bootybay",},
{task_id=20,level_limit=210,task_type=17,param1=2,task_des="完成神灵仙岛副本%s次",jump_view="fubenpanel#fubenpanel_bagua",},
{task_id=21,task_type=34,param1=2,task_des="完成日月修行副本%s次",jump_view="fubenpanel#fubenpanel_exp",},
{task_id=22,task_type=35,daily_flag=0,week_flag=1,task_exp=300,task_des="完成蛮荒妖谷副本%s次",difficult=3,jump_view="fubenpanel#fubenpanel_equip_high",},
{task_id=23,task_type=26,daily_flag=0,week_flag=1,task_weight=5000,task_exp=300,task_des="参与锁妖宝塔%s次",difficult=3,},
{task_id=24,task_type=43,task_weight=0,task_des="参与群雄争霸%s次",},
{task_id=25,task_type=28,param1=10,task_des="参与沧海夺锋 %s次",jump_view="act_jjc#arena_kf1v1",},
{task_id=26,task_type=41,param1=40,task_weight=694,task_des="抽取天地至宝%s次",jump_view="TreasureHunt#treasurehunt_fuwen",},
{task_id=27,task_type=42,task_weight=521,task_des="抽奖星光魂契%s次",jump_view="TreasureHunt#treasurehunt_equip",},
{task_id=28,task_type=34,param1=10,task_des="完成日月修行副本%s次",jump_view="fubenpanel#fubenpanel_exp",},
{task_id=29,param1=10,},
{task_id=30,task_type=8,param1=30,task_des="击杀%s只仙遗洞天BOSS",jump_view="boss#boss_vip",},
{task_id=31,task_type=9,task_des="击杀%s只灵妖奇脉BOSS",jump_view="boss#boss_personal",},
{task_id=32,param1=20,daily_flag=0,week_flag=1,task_weight=5000,task_exp=300,difficult=3,},
{task_id=33,open_server_limit=2,task_type=11,param1=7,task_des="参与击杀%s只谪仙之境BOSS",jump_view="WorldServer#world_new_shenyuan_boss",}
},

task_list_meta_table_map={
[8]=21,	-- depth:1
[18]=21,	-- depth:1
[22]=14,	-- depth:1
[23]=14,	-- depth:1
[24]=23,	-- depth:2
[28]=22,	-- depth:2
[29]=22,	-- depth:2
[30]=22,	-- depth:2
[31]=29,	-- depth:3
[32]=4,	-- depth:1
[25]=23,	-- depth:2
[26]=23,	-- depth:2
[27]=25,	-- depth:3
[33]=32,	-- depth:2
},
rechange_shop={
{item_id=46382,item_price=100000,model_id=101020101,},
{item_seq=2,limit_type=0,},
{item_seq=3,item_id=26409,limit_count=50,item_price=400,},
{item_seq=4,item_id=44051,limit_type=0,item_price=1000,},
{item_seq=5,item_id=44049,limit_type=0,item_price=3000,},
{item_seq=6,item_id=46048,item_price=3000,},
{item_seq=7,},
{item_seq=8,item_id=39988,limit_count=50,},
{cycle_id=2,limit_type=0,},
{cycle_id=2,item_seq=2,},
{cycle_id=2,item_seq=3,},
{cycle_id=2,item_seq=4,limit_count=0,},
{cycle_id=2,item_seq=5,limit_count=10,},
{cycle_id=2,item_id=50076,limit_count=40,item_price=4500,}
},

rechange_shop_meta_table_map={
[7]=2,	-- depth:1
[10]=3,	-- depth:1
[11]=1,	-- depth:1
[13]=6,	-- depth:1
[14]=6,	-- depth:1
[12]=5,	-- depth:1
},
buy_exp={
{},
{id=2,cost=5000,exp=500,discount_value="2|4|5",level=5,},
{id=3,cost=10000,exp=1000,discount_count="1|2",discount_value="3|5",level=10,},
{id=4,cost=20000,exp=2000,discount_count=1,discount_value=4,level=20,}
},

buy_exp_meta_table_map={
},
item_sort={
{},
{cycle=2,}
},

item_sort_meta_table_map={
},
buy_model_show={
{},
{cycle_id=2,buy_model_item_id=46401,reward_item_1={[0]=item_table[17],[1]=item_table[19],[2]=item_table[5],[3]=item_table[17],[4]=item_table[9],[5]=item_table[16],[6]=item_table[18],},reward_item_2={[0]=item_table[2],[1]=item_table[3],[2]=item_table[4],[3]=item_table[5],[4]=item_table[6],[5]=item_table[7],[6]=item_table[8],[7]=item_table[2],[8]=item_table[9],[9]=item_table[10],[10]=item_table[7],[11]=item_table[5],[12]=item_table[11],[13]=item_table[12],[14]=item_table[9],[15]=item_table[13],[16]=item_table[14],[17]=item_table[3],[18]=item_table[11],[19]=item_table[10],[20]=item_table[9],[21]=item_table[5],[22]=item_table[15],[23]=item_table[5],[24]=item_table[15],[25]=item_table[10],},}
},

buy_model_show_meta_table_map={
},
other_default_table={open_level=1,cycle_day=7,flash_cost=50,unlock_type=1,unlock_cost=98,rmb_type=123,rmb_seq=0,show_value=18000,reward_item_id=39147,zhanling_tips_1="每升一级即可领取战令奖励哦！",zhanling_tips_2="周期内升级战令即可领取奖励，不容错过哦",daily_task_num=10,weekly_task_num=5,reward_item_1={[0]=item_table[20]},reward_item_2={[0]=item_table[21]},fix_up_level_exp=100,exp_item=46047,},

up_level_default_table={cycle_id=1,level=1,up_level_exp=100,reward_item_1={[0]=item_table[22]},reward_item_2={[0]=item_table[4]},guding_mark=0,model_item_id=0,},

task_list_default_table={task_id=1,level_limit=1,open_server_limit=1,task_type=7,param1=1,param2=0,param3=0,daily_flag=1,week_flag=0,task_weight=10000,task_exp=50,task_des="击杀%s只伏魔战场BOSS",difficult=1,jump_view="bizuo#bizuo_act_hall",},

rechange_shop_default_table={cycle_id=1,item_seq=1,item_id=27611,limit_type=2,limit_count=1,item_price=100,is_bind=1,model_id=0,},

buy_exp_default_table={id=1,cost=1000,exp=100,limit_count=5,discount_count="1|2|3",discount_value="1|2|3",level=1,},

item_sort_default_table={cycle=1,item_id=46048,weight=100,},

buy_model_show_default_table={cycle_id=1,buy_model_item_id=27803,guding_mark=1,art_fonts="极品服装",cycle_title_img="a3_zl_ggy",reward_item_1={[0]=item_table[1],[1]=item_table[19],[2]=item_table[5],[3]=item_table[23],[4]=item_table[24],[5]=item_table[16],[6]=item_table[25],[7]=item_table[24],[8]=item_table[16],[9]=item_table[25],},reward_item_2={[0]=item_table[26],[1]=item_table[1],[2]=item_table[19],[3]=item_table[5],[4]=item_table[6],[5]=item_table[27],[6]=item_table[8],[7]=item_table[23],[8]=item_table[24],[9]=item_table[28],[10]=item_table[27],[11]=item_table[11],[12]=item_table[12],[13]=item_table[13],[14]=item_table[14],},}

}

