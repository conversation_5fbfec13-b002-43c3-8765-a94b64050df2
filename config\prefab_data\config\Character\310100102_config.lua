return {
	actorController = {
		projectiles = {},

		hurts = {},

	},
	actorTriggers = {
		effects = {
			{
				triggerEventName = "attack1/begin",
				triggerDelay = 0.0,
				triggerFreeDelay = 0.0,
				effectGoName = "skill1",
				effectAsset = {
					BundleName = "effects/prefab/model/nuqi/mo/skill1_prefab",
					AssetName = "skill1",
				},
				playerAtTarget = false,
				referenceNodeHierarchyPath = "",
				isAttach = false,
				isRotation = true,
				isUseCustomTransform = false,
				offsetPosX = 0.0,
				offsetPosY = 0.0,
				offsetPosZ = 0.0,
				triggerStopEvent = "",
				effectBtnName = "attack1",
				playerAtPos = false,
				ignoreParentScale = false,
			},
			{
				triggerEventName = "attack2/begin",
				triggerDelay = 0.0,
				triggerFreeDelay = 0.0,
				effectGoName = "skill2",
				effectAsset = {
					BundleName = "effects/prefab/model/nuqi/mo/skill2_prefab",
					AssetName = "skill2",
				},
				playerAtTarget = false,
				referenceNodeHierarchyPath = "",
				isAttach = false,
				isRotation = true,
				isUseCustomTransform = false,
				offsetPosX = 0.0,
				offsetPosY = 0.0,
				offsetPosZ = 0.0,
				triggerStopEvent = "",
				effectBtnName = "attack2",
				playerAtPos = false,
				ignoreParentScale = false,
			},
			{
				triggerEventName = "attack3/begin",
				triggerDelay = 0.0,
				triggerFreeDelay = 0.0,
				effectGoName = "skill3",
				effectAsset = {
					BundleName = "effects/prefab/model/nuqi/mo/skill3_prefab",
					AssetName = "skill3",
				},
				playerAtTarget = false,
				referenceNodeHierarchyPath = "",
				isAttach = false,
				isRotation = true,
				isUseCustomTransform = false,
				offsetPosX = 0.0,
				offsetPosY = 0.0,
				offsetPosZ = 0.0,
				triggerStopEvent = "",
				effectBtnName = "attack3",
				playerAtPos = false,
				ignoreParentScale = false,
			},
			{
				triggerEventName = "attack4/begin",
				triggerDelay = 0.0,
				triggerFreeDelay = 0.0,
				effectGoName = "skill4",
				effectAsset = {
					BundleName = "effects/prefab/model/nuqi/mo/skill4_prefab",
					AssetName = "skill4",
				},
				playerAtTarget = false,
				referenceNodeHierarchyPath = "",
				isAttach = false,
				isRotation = true,
				isUseCustomTransform = true,
				offsetPosX = 0.0,
				offsetPosY = 1.5,
				offsetPosZ = 0.0,
				triggerStopEvent = "",
				effectBtnName = "attack4",
				playerAtPos = false,
				ignoreParentScale = false,
			},
			{
				triggerEventName = "combo1_1/begin",
				triggerDelay = 0.0,
				triggerFreeDelay = 0.0,
				effectGoName = "combo1",
				effectAsset = {
					BundleName = "effects/prefab/model/nuqi/mo/combo1_prefab",
					AssetName = "combo1",
				},
				playerAtTarget = true,
				referenceNodeHierarchyPath = "",
				isAttach = false,
				isRotation = true,
				isUseCustomTransform = false,
				offsetPosX = 0.0,
				offsetPosY = 0.0,
				offsetPosZ = 0.0,
				triggerStopEvent = "",
				effectBtnName = "combo1_1",
				playerAtPos = true,
				ignoreParentScale = false,
			},
			{
				triggerEventName = "combo1_2/begin",
				triggerDelay = 0.0,
				triggerFreeDelay = 0.0,
				effectGoName = "combo2",
				effectAsset = {
					BundleName = "effects/prefab/model/nuqi/mo/combo2_prefab",
					AssetName = "combo2",
				},
				playerAtTarget = false,
				referenceNodeHierarchyPath = "",
				isAttach = false,
				isRotation = true,
				isUseCustomTransform = true,
				offsetPosX = 1.0,
				offsetPosY = 1.0,
				offsetPosZ = 0.0,
				triggerStopEvent = "",
				effectBtnName = "combo1_2",
				playerAtPos = false,
				ignoreParentScale = false,
			},
			{
				triggerEventName = "combo1_3/begin",
				triggerDelay = 0.0,
				triggerFreeDelay = 0.0,
				effectGoName = "combo3",
				effectAsset = {
					BundleName = "effects/prefab/model/nuqi/mo/combo3_prefab",
					AssetName = "combo3",
				},
				playerAtTarget = true,
				referenceNodeHierarchyPath = "",
				isAttach = false,
				isRotation = true,
				isUseCustomTransform = false,
				offsetPosX = 0.0,
				offsetPosY = 0.0,
				offsetPosZ = 0.0,
				triggerStopEvent = "",
				effectBtnName = "combo1_3",
				playerAtPos = true,
				ignoreParentScale = false,
			},
			{
				triggerEventName = "combo1_4/begin",
				triggerDelay = 0.0,
				triggerFreeDelay = 0.0,
				effectGoName = "combo4",
				effectAsset = {
					BundleName = "effects/prefab/model/nuqi/mo/combo4_prefab",
					AssetName = "combo4",
				},
				playerAtTarget = false,
				referenceNodeHierarchyPath = "",
				isAttach = false,
				isRotation = true,
				isUseCustomTransform = false,
				offsetPosX = 0.0,
				offsetPosY = 0.0,
				offsetPosZ = 0.0,
				triggerStopEvent = "",
				effectBtnName = "combo1_4",
				playerAtPos = true,
				ignoreParentScale = false,
			},
			{
				triggerEventName = "transformation/begin",
				triggerDelay = 0.0,
				triggerFreeDelay = 0.0,
				effectGoName = "bianshen",
				effectAsset = {
					BundleName = "effects/prefab/model/nuqi/mo/bianshen_prefab",
					AssetName = "bianshen",
				},
				playerAtTarget = false,
				referenceNodeHierarchyPath = "",
				isAttach = false,
				isRotation = false,
				isUseCustomTransform = false,
				offsetPosX = 0.0,
				offsetPosY = 0.0,
				offsetPosZ = 0.0,
				triggerStopEvent = "",
				effectBtnName = "transformation",
				playerAtPos = false,
				ignoreParentScale = false,
			},
		},
		sounds = {
			{
				soundEventName = "transformation/begin",
				soundDelay = 0.0,
				soundAudioAsset = {
					BundleName = "audios/sfxs/roleskill/110100101",
					AssetName = "tra",
				},
				soundAudioGoName = "tra",
				soundBtnName = "tra",
				soundIsMainRole = false,
			},
			{
				soundEventName = "attack1/begin",
				soundDelay = 0.0,
				soundAudioAsset = {
					BundleName = "audios/sfxs/roleskill/110100102",
					AssetName = "skill1",
				},
				soundAudioGoName = "skill1",
				soundBtnName = "skill1",
				soundIsMainRole = false,
			},
			{
				soundEventName = "attack2/begin",
				soundDelay = 0.0,
				soundAudioAsset = {
					BundleName = "audios/sfxs/roleskill/110100102",
					AssetName = "skill2",
				},
				soundAudioGoName = "skill2",
				soundBtnName = "skill2",
				soundIsMainRole = false,
			},
			{
				soundEventName = "attack3/begin",
				soundDelay = 0.0,
				soundAudioAsset = {
					BundleName = "audios/sfxs/roleskill/110100102",
					AssetName = "skill3",
				},
				soundAudioGoName = "skill3",
				soundBtnName = "skill3",
				soundIsMainRole = false,
			},
			{
				soundEventName = "attack4/begin",
				soundDelay = 0.0,
				soundAudioAsset = {
					BundleName = "audios/sfxs/roleskill/110100102",
					AssetName = "skill4",
				},
				soundAudioGoName = "skill4",
				soundBtnName = "skill4",
				soundIsMainRole = false,
			},
			{
				soundEventName = "combo1_1/begin",
				soundDelay = 0.0,
				soundAudioAsset = {
					BundleName = "audios/sfxs/roleskill/110100102",
					AssetName = "attack1",
				},
				soundAudioGoName = "attack1",
				soundBtnName = "attack1",
				soundIsMainRole = false,
			},
			{
				soundEventName = "combo1_2/begin",
				soundDelay = 0.0,
				soundAudioAsset = {
					BundleName = "audios/sfxs/roleskill/110100102",
					AssetName = "attack2",
				},
				soundAudioGoName = "attack2",
				soundBtnName = "attack2",
				soundIsMainRole = false,
			},
			{
				soundEventName = "combo1_3/begin",
				soundDelay = 0.0,
				soundAudioAsset = {
					BundleName = "audios/sfxs/roleskill/110100102",
					AssetName = "attack3",
				},
				soundAudioGoName = "attack3",
				soundBtnName = "attack3",
				soundIsMainRole = false,
			},
			{
				soundEventName = "combo1_4/begin",
				soundDelay = 0.0,
				soundAudioAsset = {
					BundleName = "audios/sfxs/roleskill/110100102",
					AssetName = "attack4",
				},
				soundAudioGoName = "attack4",
				soundBtnName = "attack4",
				soundIsMainRole = false,
			},
		},
		cameraShakes = {
			{
				CameraShakeBtnName = "combo1_4",
				eventName = "combo1_4/begin",
				numberOfShakes = 2,
				distance = 0.4,
				speed = 100.0,
				delay = 0.8,
				decay = 0.0,
			},
			{
				CameraShakeBtnName = "attack2",
				eventName = "attack2/begin",
				numberOfShakes = 2,
				distance = 0.4,
				speed = 100.0,
				delay = 0.2,
				decay = 0.0,
			},
			{
				CameraShakeBtnName = "attack3",
				eventName = "attack3/begin",
				numberOfShakes = 1,
				distance = 0.4,
				speed = 150.0,
				delay = 0.6,
				decay = 0.0,
			},
			{
				CameraShakeBtnName = "attack4_1",
				eventName = "attack4/begin",
				numberOfShakes = 8,
				distance = 0.4,
				speed = 600.0,
				delay = 1.3,
				decay = 0.0,
			},
			{
				CameraShakeBtnName = "attack4_2",
				eventName = "attack4/begin",
				numberOfShakes = 9,
				distance = 0.6,
				speed = 180.0,
				delay = 1.8,
				decay = 0.0,
			},
			{
				CameraShakeBtnName = "transformation",
				eventName = "transformation/begin",
				numberOfShakes = 2,
				distance = 0.8,
				speed = 400.0,
				delay = 0.8,
				decay = 0.0,
			},
		},
		radialBlurs = {
			{
				btnName = "transformation",
				eventName = "transformation/begin",
				isRole = true,
				referenceNodeHierarchyPath = "root",
				delay = 0.1,
				strength = 2.0,
				riseTime = 0.1,
				holdTime = 0.2,
				fallTime = 0.0,
			},
			{
				btnName = "attack4",
				eventName = "attack4/begin",
				isRole = true,
				referenceNodeHierarchyPath = "RoleBody",
				delay = 0.1,
				strength = 0.2,
				riseTime = 0.1,
				holdTime = 0.3,
				fallTime = 0.0,
			},
		},
	},
}