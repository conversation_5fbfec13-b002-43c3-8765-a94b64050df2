{"actorController": {"projectiles": [], "hurts": []}, "actorTriggers": {"effects": [{"triggerEventName": "attack1/begin", "triggerDelay": 0.0, "triggerFreeDelay": 0.0, "effectGoName": "", "effectAsset": {"BundleName": "", "AssetName": "", "AssetGUID": "", "IsEmpty": true}, "playerAtTarget": false, "referenceNodeHierarchyPath": "", "isAttach": false, "isRotation": true, "isUseCustomTransform": false, "offsetPosX": 0.0, "offsetPosY": 0.0, "offsetPosZ": 0.0, "triggerStopEvent": "[none]", "effectBtnName": "attack1", "playerAtPos": false, "ignoreParentScale": false, "effectArray": [{"effectGoName": "1101_attack1", "effectAsset": {"BundleName": "effects/prefab/role/1101/1101_attack1_prefab", "AssetName": "1101_attack1", "AssetGUID": "5f0fc5f011338354f9d91d0e9b47e11f", "IsEmpty": false}}, {"effectGoName": "1101_attack1_qing", "effectAsset": {"BundleName": "effects/prefab/role/1101/1101_attack1_qing_prefab", "AssetName": "1101_attack1_qing", "AssetGUID": "3c780359175f5024aa20698d11f3dea7", "IsEmpty": false}}]}, {"triggerEventName": "attack2/begin", "triggerDelay": 0.0, "triggerFreeDelay": 0.0, "effectGoName": "", "effectAsset": {"BundleName": "", "AssetName": "", "AssetGUID": "", "IsEmpty": true}, "playerAtTarget": false, "referenceNodeHierarchyPath": "", "isAttach": false, "isRotation": true, "isUseCustomTransform": false, "offsetPosX": 0.0, "offsetPosY": 0.0, "offsetPosZ": 0.0, "triggerStopEvent": "[none]", "effectBtnName": "attack2", "playerAtPos": false, "ignoreParentScale": false, "effectArray": [{"effectGoName": "1101_attack2", "effectAsset": {"BundleName": "effects/prefab/role/1101/1101_attack2_prefab", "AssetName": "1101_attack2", "AssetGUID": "396a86938102a944b8a16e169c71a3ad", "IsEmpty": false}}, {"effectGoName": "1101_attack2_qing", "effectAsset": {"BundleName": "effects/prefab/role/1101/1101_attack2_qing_prefab", "AssetName": "1101_attack2_qing", "AssetGUID": "8ebf48e3ae990aa419662f4540329cfa", "IsEmpty": false}}]}, {"triggerEventName": "attack3/begin", "triggerDelay": 0.0, "triggerFreeDelay": 0.0, "effectGoName": "", "effectAsset": {"BundleName": "", "AssetName": "", "AssetGUID": "", "IsEmpty": true}, "playerAtTarget": false, "referenceNodeHierarchyPath": "", "isAttach": false, "isRotation": true, "isUseCustomTransform": false, "offsetPosX": 0.0, "offsetPosY": 0.0, "offsetPosZ": 0.0, "triggerStopEvent": "[none]", "effectBtnName": "attack3", "playerAtPos": false, "ignoreParentScale": false, "effectArray": [{"effectGoName": "1101_attack3", "effectAsset": {"BundleName": "effects/prefab/role/1101/1101_attack3_prefab", "AssetName": "1101_attack3", "AssetGUID": "5455a3df96297ab4582b704d99a6ec86", "IsEmpty": false}}, {"effectGoName": "1101_attack3_qing", "effectAsset": {"BundleName": "effects/prefab/role/1101/1101_attack3_qing_prefab", "AssetName": "1101_attack3_qing", "AssetGUID": "d66a6de7b8e80cb439a48b486c819556", "IsEmpty": false}}]}, {"triggerEventName": "attack4/begin", "triggerDelay": 0.0, "triggerFreeDelay": 0.0, "effectGoName": "", "effectAsset": {"BundleName": "", "AssetName": "", "AssetGUID": "", "IsEmpty": true}, "playerAtTarget": false, "referenceNodeHierarchyPath": "", "isAttach": false, "isRotation": true, "isUseCustomTransform": false, "offsetPosX": 0.0, "offsetPosY": 0.0, "offsetPosZ": 0.0, "triggerStopEvent": "[none]", "effectBtnName": "attack4", "playerAtPos": false, "ignoreParentScale": false, "effectArray": [{"effectGoName": "1101_attack4", "effectAsset": {"BundleName": "effects/prefab/role/1101/1101_attack4_prefab", "AssetName": "1101_attack4", "AssetGUID": "bfc9cb40c7bd0074eb44bb5011bf52c6", "IsEmpty": false}}, {"effectGoName": "1101_attack4_qing", "effectAsset": {"BundleName": "effects/prefab/role/1101/1101_attack4_qing_prefab", "AssetName": "1101_attack4_qing", "AssetGUID": "d11bef6c8d43c0647b9856381aa4c6c4", "IsEmpty": false}}]}, {"triggerEventName": "combo1_1/begin", "triggerDelay": 0.0, "triggerFreeDelay": 0.0, "effectGoName": "", "effectAsset": {"BundleName": "", "AssetName": "", "AssetGUID": "", "IsEmpty": true}, "playerAtTarget": false, "referenceNodeHierarchyPath": "", "isAttach": false, "isRotation": true, "isUseCustomTransform": false, "offsetPosX": 0.0, "offsetPosY": 0.0, "offsetPosZ": 0.0, "triggerStopEvent": "[none]", "effectBtnName": "combo1_1", "playerAtPos": false, "ignoreParentScale": false, "effectArray": [{"effectGoName": "1101_combo1", "effectAsset": {"BundleName": "effects/prefab/role/1101/1101_combo1_prefab", "AssetName": "1101_combo1", "AssetGUID": "6ae2395e627d7534599b1a5d2af4eab8", "IsEmpty": false}}, {"effectGoName": "1101_combo1_qing", "effectAsset": {"BundleName": "effects/prefab/role/1101/1101_combo1_qing_prefab", "AssetName": "1101_combo1_qing", "AssetGUID": "3d7a2342490395a4d87ed69397cdf382", "IsEmpty": false}}]}, {"triggerEventName": "combo1_2/begin", "triggerDelay": 0.0, "triggerFreeDelay": 0.0, "effectGoName": "", "effectAsset": {"BundleName": "", "AssetName": "", "AssetGUID": "", "IsEmpty": true}, "playerAtTarget": false, "referenceNodeHierarchyPath": "", "isAttach": false, "isRotation": true, "isUseCustomTransform": false, "offsetPosX": 0.0, "offsetPosY": 0.0, "offsetPosZ": 0.0, "triggerStopEvent": "[none]", "effectBtnName": "combo1_2", "playerAtPos": false, "ignoreParentScale": false, "effectArray": [{"effectGoName": "1101_combo2", "effectAsset": {"BundleName": "effects/prefab/role/1101/1101_combo2_prefab", "AssetName": "1101_combo2", "AssetGUID": "564bccdde0eb0994b82f766f61a33536", "IsEmpty": false}}, {"effectGoName": "1101_combo2_qing", "effectAsset": {"BundleName": "effects/prefab/role/1101/1101_combo2_qing_prefab", "AssetName": "1101_combo2_qing", "AssetGUID": "9bb6fb35d9269d84295b9257223aa2d2", "IsEmpty": false}}]}, {"triggerEventName": "combo1_3/begin", "triggerDelay": 0.0, "triggerFreeDelay": 0.0, "effectGoName": "", "effectAsset": {"BundleName": "", "AssetName": "", "AssetGUID": "", "IsEmpty": true}, "playerAtTarget": false, "referenceNodeHierarchyPath": "", "isAttach": false, "isRotation": true, "isUseCustomTransform": false, "offsetPosX": 0.0, "offsetPosY": 0.0, "offsetPosZ": 0.0, "triggerStopEvent": "[none]", "effectBtnName": "combo1_3", "playerAtPos": false, "ignoreParentScale": false, "effectArray": [{"effectGoName": "1101_combo3", "effectAsset": {"BundleName": "effects/prefab/role/1101/1101_combo3_prefab", "AssetName": "1101_combo3", "AssetGUID": "c9ccf193e3ffadd44866e647dfa58e97", "IsEmpty": false}}, {"effectGoName": "1101_combo3_qing", "effectAsset": {"BundleName": "effects/prefab/role/1101/1101_combo3_qing_prefab", "AssetName": "1101_combo3_qing", "AssetGUID": "574b77db89bee594d9da85f52982f8ed", "IsEmpty": false}}]}, {"triggerEventName": "combo1_4/begin", "triggerDelay": 0.0, "triggerFreeDelay": 0.0, "effectGoName": "", "effectAsset": {"BundleName": "", "AssetName": "", "AssetGUID": "", "IsEmpty": true}, "playerAtTarget": false, "referenceNodeHierarchyPath": "", "isAttach": false, "isRotation": true, "isUseCustomTransform": false, "offsetPosX": 0.0, "offsetPosY": 0.0, "offsetPosZ": 0.0, "triggerStopEvent": "[none]", "effectBtnName": "combo1_4", "playerAtPos": false, "ignoreParentScale": false, "effectArray": [{"effectGoName": "1101_combo4", "effectAsset": {"BundleName": "effects/prefab/role/1101/1101_combo4_prefab", "AssetName": "1101_combo4", "AssetGUID": "ae4b975007598194ea8b177cd7772b6e", "IsEmpty": false}}, {"effectGoName": "1101_combo4_qing", "effectAsset": {"BundleName": "effects/prefab/role/1101/1101_combo4_qing_prefab", "AssetName": "1101_combo4_qing", "AssetGUID": "d4a139fdf87bd2a4fa60447f107de205", "IsEmpty": false}}]}, {"triggerEventName": "esoterica_attack_1/begin", "triggerDelay": 0.0, "triggerFreeDelay": 0.0, "effectGoName": "", "effectAsset": {"BundleName": "", "AssetName": "", "AssetGUID": "", "IsEmpty": true}, "playerAtTarget": false, "referenceNodeHierarchyPath": "", "isAttach": false, "isRotation": true, "isUseCustomTransform": false, "offsetPosX": 0.0, "offsetPosY": 0.0, "offsetPosZ": 0.0, "triggerStopEvent": "[none]", "effectBtnName": "esoterica_attack_1", "playerAtPos": false, "ignoreParentScale": false, "effectArray": [{"effectGoName": "miji_attack01", "effectAsset": {"BundleName": "effects/prefab/model/miji/miji_attack01_prefab", "AssetName": "miji_attack01", "AssetGUID": "f994bccd8b0cf5b43a70cead8dbaa535", "IsEmpty": false}}, {"effectGoName": "", "effectAsset": {"BundleName": "", "AssetName": "", "AssetGUID": "", "IsEmpty": true}}]}, {"triggerEventName": "esoterica_attack_2/begin", "triggerDelay": 0.0, "triggerFreeDelay": 0.0, "effectGoName": "", "effectAsset": {"BundleName": "", "AssetName": "", "AssetGUID": "", "IsEmpty": true}, "playerAtTarget": false, "referenceNodeHierarchyPath": "", "isAttach": false, "isRotation": true, "isUseCustomTransform": false, "offsetPosX": 0.0, "offsetPosY": 0.0, "offsetPosZ": 0.0, "triggerStopEvent": "[none]", "effectBtnName": "esoterica_attack_2", "playerAtPos": false, "ignoreParentScale": false, "effectArray": [{"effectGoName": "xianfa_attack9", "effectAsset": {"BundleName": "effects/prefab/model/xianfa/xianfa_attack9_prefab", "AssetName": "xianfa_attack9", "AssetGUID": "ed21f6540190afa42a0dc2557588f18a", "IsEmpty": false}}, {"effectGoName": "", "effectAsset": {"BundleName": "", "AssetName": "", "AssetGUID": "", "IsEmpty": true}}]}, {"triggerEventName": "esoterica_attack_3/begin", "triggerDelay": 0.0, "triggerFreeDelay": 0.0, "effectGoName": "", "effectAsset": {"BundleName": "", "AssetName": "", "AssetGUID": "", "IsEmpty": true}, "playerAtTarget": false, "referenceNodeHierarchyPath": "", "isAttach": false, "isRotation": true, "isUseCustomTransform": false, "offsetPosX": 0.0, "offsetPosY": 0.0, "offsetPosZ": 0.0, "triggerStopEvent": "[none]", "effectBtnName": "esoterica_attack_3", "playerAtPos": false, "ignoreParentScale": false, "effectArray": [{"effectGoName": "xianfa_attack3", "effectAsset": {"BundleName": "effects/prefab/model/xianfa/xianfa_attack3_prefab", "AssetName": "xianfa_attack3", "AssetGUID": "52a62e7527880c540a72b41e5898ce87", "IsEmpty": false}}, {"effectGoName": "", "effectAsset": {"BundleName": "", "AssetName": "", "AssetGUID": "", "IsEmpty": true}}]}, {"triggerEventName": "esoterica_attack_4/begin", "triggerDelay": 0.0, "triggerFreeDelay": 0.0, "effectGoName": "", "effectAsset": {"BundleName": "", "AssetName": "", "AssetGUID": "", "IsEmpty": true}, "playerAtTarget": false, "referenceNodeHierarchyPath": "", "isAttach": false, "isRotation": true, "isUseCustomTransform": false, "offsetPosX": 0.0, "offsetPosY": 0.0, "offsetPosZ": 0.0, "triggerStopEvent": "[none]", "effectBtnName": "esoterica_attack_4", "playerAtPos": false, "ignoreParentScale": false, "effectArray": [{"effectGoName": "xianfa_attack1", "effectAsset": {"BundleName": "effects/prefab/model/xianfa/xianfa_attack1_prefab", "AssetName": "xianfa_attack1", "AssetGUID": "23194b7835085324487da9a6e339a81d", "IsEmpty": false}}, {"effectGoName": "", "effectAsset": {"BundleName": "", "AssetName": "", "AssetGUID": "", "IsEmpty": true}}]}, {"triggerEventName": "esoterica_attack_5/begin", "triggerDelay": 0.0, "triggerFreeDelay": 0.0, "effectGoName": "", "effectAsset": {"BundleName": "", "AssetName": "", "AssetGUID": "", "IsEmpty": true}, "playerAtTarget": false, "referenceNodeHierarchyPath": "", "isAttach": false, "isRotation": true, "isUseCustomTransform": false, "offsetPosX": 0.0, "offsetPosY": 0.0, "offsetPosZ": 0.0, "triggerStopEvent": "[none]", "effectBtnName": "esoterica_attack_5", "playerAtPos": false, "ignoreParentScale": false, "effectArray": [{"effectGoName": "xianfa_attack6", "effectAsset": {"BundleName": "effects/prefab/model/xianfa/xianfa_attack6_prefab", "AssetName": "xianfa_attack6", "AssetGUID": "34314d3151a39c14eae2d8e0f5b285b8", "IsEmpty": false}}, {"effectGoName": "", "effectAsset": {"BundleName": "", "AssetName": "", "AssetGUID": "", "IsEmpty": true}}]}, {"triggerEventName": "esoterica_attack_6/begin", "triggerDelay": 0.0, "triggerFreeDelay": 0.0, "effectGoName": "", "effectAsset": {"BundleName": "", "AssetName": "", "AssetGUID": "", "IsEmpty": true}, "playerAtTarget": false, "referenceNodeHierarchyPath": "", "isAttach": false, "isRotation": true, "isUseCustomTransform": false, "offsetPosX": 0.0, "offsetPosY": 0.0, "offsetPosZ": 0.0, "triggerStopEvent": "[none]", "effectBtnName": "esoterica_attack_6", "playerAtPos": false, "ignoreParentScale": false, "effectArray": [{"effectGoName": "xianfa_attack4", "effectAsset": {"BundleName": "effects/prefab/model/xianfa/xianfa_attack4_prefab", "AssetName": "xianfa_attack4", "AssetGUID": "7da6118c654021d48be6517dcf57c258", "IsEmpty": false}}, {"effectGoName": "", "effectAsset": {"BundleName": "", "AssetName": "", "AssetGUID": "", "IsEmpty": true}}]}, {"triggerEventName": "esoterica_attack_7/begin", "triggerDelay": 0.0, "triggerFreeDelay": 0.0, "effectGoName": "", "effectAsset": {"BundleName": "", "AssetName": "", "AssetGUID": "", "IsEmpty": true}, "playerAtTarget": false, "referenceNodeHierarchyPath": "", "isAttach": false, "isRotation": true, "isUseCustomTransform": false, "offsetPosX": 4.23, "offsetPosY": 4.74, "offsetPosZ": 14.65, "triggerStopEvent": "[none]", "effectBtnName": "esoterica_attack_7", "playerAtPos": false, "ignoreParentScale": false, "effectArray": [{"effectGoName": "xianfa_attack8", "effectAsset": {"BundleName": "effects/prefab/model/xianfa/xianfa_attack8_prefab", "AssetName": "xianfa_attack8", "AssetGUID": "0dbff1b2487193c4c8a84818c05d6752", "IsEmpty": false}}, {"effectGoName": "", "effectAsset": {"BundleName": "", "AssetName": "", "AssetGUID": "", "IsEmpty": true}}]}, {"triggerEventName": "attack5/begin", "triggerDelay": 0.0, "triggerFreeDelay": 0.0, "effectGoName": "", "effectAsset": {"BundleName": "", "AssetName": "", "AssetGUID": "", "IsEmpty": true}, "playerAtTarget": false, "referenceNodeHierarchyPath": "", "isAttach": false, "isRotation": true, "isUseCustomTransform": false, "offsetPosX": 0.0, "offsetPosY": 0.0, "offsetPosZ": 0.0, "triggerStopEvent": "[none]", "effectBtnName": "attack5", "playerAtPos": false, "ignoreParentScale": false, "effectArray": [{"effectGoName": "dingzhi_attack01", "effectAsset": {"BundleName": "effects/prefab/model/dingzhi/dingzhi_attack01_prefab", "AssetName": "dingzhi_attack01", "AssetGUID": "b022eeef2e2e6c646bab3b856284fdc2", "IsEmpty": false}}, {"effectGoName": "", "effectAsset": {"BundleName": "", "AssetName": "", "AssetGUID": "", "IsEmpty": true}}]}, {"triggerEventName": "attack6/begin", "triggerDelay": 0.0, "triggerFreeDelay": 0.0, "effectGoName": "", "effectAsset": {"BundleName": "", "AssetName": "", "AssetGUID": "", "IsEmpty": true}, "playerAtTarget": false, "referenceNodeHierarchyPath": "", "isAttach": false, "isRotation": true, "isUseCustomTransform": false, "offsetPosX": 0.0, "offsetPosY": 0.0, "offsetPosZ": 0.0, "triggerStopEvent": "[none]", "effectBtnName": "attack6", "playerAtPos": false, "ignoreParentScale": false, "effectArray": [{"effectGoName": "dingzhi_attack02", "effectAsset": {"BundleName": "effects/prefab/model/dingzhi/dingzhi_attack02_prefab", "AssetName": "dingzhi_attack02", "AssetGUID": "6f207f36daca9154cb841cffa8f9a613", "IsEmpty": false}}, {"effectGoName": "", "effectAsset": {"BundleName": "", "AssetName": "", "AssetGUID": "", "IsEmpty": true}}]}, {"triggerEventName": "attack7/begin", "triggerDelay": 0.0, "triggerFreeDelay": 0.0, "effectGoName": "", "effectAsset": {"BundleName": "", "AssetName": "", "AssetGUID": "", "IsEmpty": true}, "playerAtTarget": false, "referenceNodeHierarchyPath": "", "isAttach": false, "isRotation": true, "isUseCustomTransform": false, "offsetPosX": 0.0, "offsetPosY": 0.0, "offsetPosZ": 0.0, "triggerStopEvent": "[none]", "effectBtnName": "attack7", "playerAtPos": false, "ignoreParentScale": false, "effectArray": [{"effectGoName": "dingzhi_attack05", "effectAsset": {"BundleName": "effects/prefab/model/dingzhi/dingzhi_attack05_prefab", "AssetName": "dingzhi_attack05", "AssetGUID": "fb83bd0beaf27a341aecad02414b0690", "IsEmpty": false}}, {"effectGoName": "", "effectAsset": {"BundleName": "", "AssetName": "", "AssetGUID": "", "IsEmpty": true}}]}, {"triggerEventName": "fall_rest/begin", "triggerDelay": 0.0, "triggerFreeDelay": 0.0, "effectGoName": "", "effectAsset": {"BundleName": "", "AssetName": "", "AssetGUID": "", "IsEmpty": true}, "playerAtTarget": false, "referenceNodeHierarchyPath": "root", "isAttach": true, "isRotation": false, "isUseCustomTransform": false, "offsetPosX": 0.0, "offsetPosY": 0.0, "offsetPosZ": 0.0, "triggerStopEvent": "[none]", "effectBtnName": "fall_rest", "playerAtPos": false, "ignoreParentScale": false, "effectArray": [{"effectGoName": "eff_chuchang_shandian", "effectAsset": {"BundleName": "effects/prefab/environment/common/eff_chuchang_shandian_prefab", "AssetName": "eff_chuchang_shandian", "AssetGUID": "8ee5fd14ef7f9de44a87bee18aa45a63", "IsEmpty": false}}, {"effectGoName": "", "effectAsset": {"BundleName": "", "AssetName": "", "AssetGUID": "", "IsEmpty": true}}]}, {"triggerEventName": "prizedraw/begin", "triggerDelay": 0.0, "triggerFreeDelay": 0.0, "effectGoName": "", "effectAsset": {"BundleName": "", "AssetName": "", "AssetGUID": "", "IsEmpty": true}, "playerAtTarget": false, "referenceNodeHierarchyPath": "root", "isAttach": true, "isRotation": false, "isUseCustomTransform": false, "offsetPosX": 0.0, "offsetPosY": 0.0, "offsetPosZ": 0.0, "triggerStopEvent": "[none]", "effectBtnName": "choujiang", "playerAtPos": false, "ignoreParentScale": true, "effectArray": [{"effectGoName": "choujiang_eff", "effectAsset": {"BundleName": "effects/prefab/environment/common/choujiang_eff_prefab", "AssetName": "choujiang_eff", "AssetGUID": "07e5ba189c5069e4e9558cb1d0bf3ef3", "IsEmpty": false}}, {"effectGoName": "", "effectAsset": {"BundleName": "", "AssetName": "", "AssetGUID": "", "IsEmpty": true}}]}, {"triggerEventName": "esoterica_attack_8/begin", "triggerDelay": 0.0, "triggerFreeDelay": 0.0, "effectGoName": "", "effectAsset": {"BundleName": "", "AssetName": "", "AssetGUID": "", "IsEmpty": true}, "playerAtTarget": false, "referenceNodeHierarchyPath": "", "isAttach": false, "isRotation": true, "isUseCustomTransform": true, "offsetPosX": 1.0, "offsetPosY": 0.0, "offsetPosZ": -6.0, "triggerStopEvent": "[none]", "effectBtnName": "esoterica_attack_8", "playerAtPos": false, "ignoreParentScale": false, "effectArray": [{"effectGoName": "miji_attack08", "effectAsset": {"BundleName": "effects/prefab/model/miji/miji_attack08_prefab", "AssetName": "miji_attack08", "AssetGUID": "afb7616833ab6f643bb4344338980bd7", "IsEmpty": false}}, {"effectGoName": "", "effectAsset": {"BundleName": "", "AssetName": "", "AssetGUID": "", "IsEmpty": true}}]}, {"triggerEventName": "esoterica_attack_9/begin", "triggerDelay": 0.0, "triggerFreeDelay": 0.0, "effectGoName": "", "effectAsset": {"BundleName": "", "AssetName": "", "AssetGUID": "", "IsEmpty": true}, "playerAtTarget": false, "referenceNodeHierarchyPath": "", "isAttach": false, "isRotation": true, "isUseCustomTransform": false, "offsetPosX": 0.0, "offsetPosY": 0.0, "offsetPosZ": 0.0, "triggerStopEvent": "[none]", "effectBtnName": "esoterica_attack_9", "playerAtPos": false, "ignoreParentScale": false, "effectArray": [{"effectGoName": "xianfa_attack10", "effectAsset": {"BundleName": "effects/prefab/model/xianfa/xianfa_attack10_prefab", "AssetName": "xianfa_attack10", "AssetGUID": "1f207c1e703af0c4cb4667d65040cfbd", "IsEmpty": false}}, {"effectGoName": "", "effectAsset": {"BundleName": "", "AssetName": "", "AssetGUID": "", "IsEmpty": true}}]}, {"triggerEventName": "esoterica_attack_10/begin", "triggerDelay": 0.0, "triggerFreeDelay": 0.0, "effectGoName": "", "effectAsset": {"BundleName": "", "AssetName": "", "AssetGUID": "", "IsEmpty": true}, "playerAtTarget": false, "referenceNodeHierarchyPath": "root", "isAttach": true, "isRotation": true, "isUseCustomTransform": false, "offsetPosX": 0.0, "offsetPosY": 0.0, "offsetPosZ": 0.0, "triggerStopEvent": "[none]", "effectBtnName": "esoterica_attack_10", "playerAtPos": false, "ignoreParentScale": false, "effectArray": [{"effectGoName": "xianfa_attack2", "effectAsset": {"BundleName": "effects/prefab/model/xianfa/xianfa_attack2_prefab", "AssetName": "xianfa_attack2", "AssetGUID": "db15a3bdaf4531040a53689e2aca1d1a", "IsEmpty": false}}, {"effectGoName": "", "effectAsset": {"BundleName": "", "AssetName": "", "AssetGUID": "", "IsEmpty": true}}]}], "sounds": [{"soundEventName": "combo1_1/begin", "soundDelay": 0.0, "soundAudioAsset": {"BundleName": "audios/sfxs/roleskill/role4", "AssetName": "role4_attack1", "AssetGUID": "b7f8ea64a2d800f4783f89d2670b9c42", "IsEmpty": false}, "soundAudioGoName": "role4_attack1", "soundBtnName": "combo1_1", "soundIsMainRole": false}, {"soundEventName": "combo1_2/begin", "soundDelay": 0.0, "soundAudioAsset": {"BundleName": "audios/sfxs/roleskill/role4", "AssetName": "role4_attack2", "AssetGUID": "382b5ea05139c7145b9313bd00429fe6", "IsEmpty": false}, "soundAudioGoName": "role4_attack2", "soundBtnName": "combo1_2", "soundIsMainRole": false}, {"soundEventName": "combo1_3/begin", "soundDelay": 0.0, "soundAudioAsset": {"BundleName": "audios/sfxs/roleskill/role4", "AssetName": "role4_attack3", "AssetGUID": "a1854f5ce8e7bc542b9f2659b3516757", "IsEmpty": false}, "soundAudioGoName": "role4_attack3", "soundBtnName": "combo1_3", "soundIsMainRole": false}, {"soundEventName": "combo1_4/begin", "soundDelay": 0.0, "soundAudioAsset": {"BundleName": "audios/sfxs/roleskill/role4", "AssetName": "role4_attack4", "AssetGUID": "f6459581fa2ec8448876171b7b1127b9", "IsEmpty": false}, "soundAudioGoName": "role4_attack4", "soundBtnName": "combo1_4", "soundIsMainRole": false}, {"soundEventName": "attack1/begin", "soundDelay": 0.0, "soundAudioAsset": {"BundleName": "audios/sfxs/roleskill/role4", "AssetName": "role4_skill1", "AssetGUID": "f565a6b0688c500418759ca386d1c752", "IsEmpty": false}, "soundAudioGoName": "role4_skill1", "soundBtnName": "attack1", "soundIsMainRole": false}, {"soundEventName": "attack2/begin", "soundDelay": 0.0, "soundAudioAsset": {"BundleName": "audios/sfxs/roleskill/role4", "AssetName": "role4_skill2", "AssetGUID": "a160f97af1763aa449e568337768ad83", "IsEmpty": false}, "soundAudioGoName": "role4_skill2", "soundBtnName": "attack2", "soundIsMainRole": false}, {"soundEventName": "attack3/begin", "soundDelay": 0.0, "soundAudioAsset": {"BundleName": "audios/sfxs/roleskill/role4", "AssetName": "role4_skill3", "AssetGUID": "8ea74794bdfba9e448c523d60ad5bf91", "IsEmpty": false}, "soundAudioGoName": "role4_skill3", "soundBtnName": "attack3", "soundIsMainRole": false}, {"soundEventName": "attack4/begin", "soundDelay": 0.0, "soundAudioAsset": {"BundleName": "audios/sfxs/roleskill/role4", "AssetName": "role4_skill4", "AssetGUID": "ff00cc483e1cc304983ef2d99a1e62a3", "IsEmpty": false}, "soundAudioGoName": "role4_skill4", "soundBtnName": "attack4", "soundIsMainRole": false}, {"soundEventName": "attack5/begin", "soundDelay": 0.0, "soundAudioAsset": {"BundleName": "audios/sfxs/roleskill/jineng", "AssetName": "role1_attack5", "AssetGUID": "c6cce806e6242d34cbbf42f08495835d", "IsEmpty": false}, "soundAudioGoName": "role1_attack5", "soundBtnName": "attack5", "soundIsMainRole": false}, {"soundEventName": "attack6/begin", "soundDelay": 0.0, "soundAudioAsset": {"BundleName": "audios/sfxs/roleskill/jineng", "AssetName": "role1_attack6", "AssetGUID": "4a908c8fae4b09f4291e8dff9a7bbe1b", "IsEmpty": false}, "soundAudioGoName": "role1_attack6", "soundBtnName": "attack6", "soundIsMainRole": false}, {"soundEventName": "attack9/begin", "soundDelay": 0.0, "soundAudioAsset": {"BundleName": "audios/sfxs/roleskill/jineng", "AssetName": "role1_attack7", "AssetGUID": "9d0739ea8831f774082170089f3b7739", "IsEmpty": false}, "soundAudioGoName": "role1_attack7", "soundBtnName": "attack9", "soundIsMainRole": false}, {"soundEventName": "attack10/begin", "soundDelay": 0.0, "soundAudioAsset": {"BundleName": "audios/sfxs/roleskill/jineng", "AssetName": "role1_attack8", "AssetGUID": "01b92d13028e4dd4a9b727eb58061f32", "IsEmpty": false}, "soundAudioGoName": "role1_attack8", "soundBtnName": "attack10", "soundIsMainRole": false}, {"soundEventName": "attack11/begin", "soundDelay": 0.0, "soundAudioAsset": {"BundleName": "audios/sfxs/roleskill/jineng", "AssetName": "role1_attack9", "AssetGUID": "fd77df63743c0de4e875fd7156c19ac2", "IsEmpty": false}, "soundAudioGoName": "role1_attack9", "soundBtnName": "attack11", "soundIsMainRole": false}, {"soundEventName": "attack12/begin", "soundDelay": 0.0, "soundAudioAsset": {"BundleName": "audios/sfxs/roleskill/jineng", "AssetName": "role1_attack10", "AssetGUID": "cf61af0a916dd984d82d5c69767aee0d", "IsEmpty": false}, "soundAudioGoName": "role1_attack10", "soundBtnName": "attack12", "soundIsMainRole": false}, {"soundEventName": "esoterica_attack_1/begin", "soundDelay": 0.0, "soundAudioAsset": {"BundleName": "audios/sfxs/roleskill/jineng", "AssetName": "miji1", "AssetGUID": "fbde1ec19b2a7ad40ac8ac805ed138ac", "IsEmpty": false}, "soundAudioGoName": "miji1", "soundBtnName": "miji1", "soundIsMainRole": false}, {"soundEventName": "esoterica_attack_2/begin", "soundDelay": 0.0, "soundAudioAsset": {"BundleName": "audios/sfxs/roleskill/jineng", "AssetName": "miji2", "AssetGUID": "408b41cc6b036d74bb9a6c09326ebe53", "IsEmpty": false}, "soundAudioGoName": "miji2", "soundBtnName": "miji2", "soundIsMainRole": false}, {"soundEventName": "esoterica_attack_3/begin", "soundDelay": 0.0, "soundAudioAsset": {"BundleName": "audios/sfxs/roleskill/jineng", "AssetName": "miji3", "AssetGUID": "5c34d12218e396e4caa18fbe850d6525", "IsEmpty": false}, "soundAudioGoName": "miji3", "soundBtnName": "miji3", "soundIsMainRole": false}, {"soundEventName": "esoterica_attack_4/begin", "soundDelay": 0.0, "soundAudioAsset": {"BundleName": "audios/sfxs/roleskill/jineng", "AssetName": "miji4", "AssetGUID": "21c07d7b36b8ab942b31ba9614aa1ef8", "IsEmpty": false}, "soundAudioGoName": "miji4", "soundBtnName": "miji4", "soundIsMainRole": false}, {"soundEventName": "esoterica_attack_5/begin", "soundDelay": 0.0, "soundAudioAsset": {"BundleName": "audios/sfxs/roleskill/jineng", "AssetName": "miji5", "AssetGUID": "19f9e4f6494cfc14684aea6aa1f53e61", "IsEmpty": false}, "soundAudioGoName": "miji5", "soundBtnName": "miji5", "soundIsMainRole": false}, {"soundEventName": "esoterica_attack_6/begin", "soundDelay": 0.0, "soundAudioAsset": {"BundleName": "audios/sfxs/roleskill/jineng", "AssetName": "miji6", "AssetGUID": "a999d23c1f02d1347be97257cafa9d59", "IsEmpty": false}, "soundAudioGoName": "miji6", "soundBtnName": "miji6", "soundIsMainRole": false}, {"soundEventName": "esoterica_attack_7/begin", "soundDelay": 0.0, "soundAudioAsset": {"BundleName": "audios/sfxs/roleskill/jineng", "AssetName": "miji7", "AssetGUID": "0eda33b3b3077bd4586549847682b1da", "IsEmpty": false}, "soundAudioGoName": "miji7", "soundBtnName": "miji7", "soundIsMainRole": false}], "cameraShakes": [{"CameraShakeBtnName": "attack1", "eventName": "attack1/begin", "numberOfShakes": 1, "distance": 0.2, "speed": 650.0, "delay": 0.65, "decay": 0.2}, {"CameraShakeBtnName": "attack2", "eventName": "attack2/begin", "numberOfShakes": 1, "distance": 0.3, "speed": 300.0, "delay": 0.95, "decay": 0.0}, {"CameraShakeBtnName": "combo1_4", "eventName": "combo1_4/begin", "numberOfShakes": 3, "distance": 0.5, "speed": 450.0, "delay": 1.1, "decay": 0.0}, {"CameraShakeBtnName": "attack4_2", "eventName": "attack4/begin", "numberOfShakes": 1, "distance": 1.7, "speed": 60.0, "delay": 1.7, "decay": 0.0}, {"CameraShakeBtnName": "es_attack1_1", "eventName": "esoterica_attack_1/begin", "numberOfShakes": 1, "distance": 0.2, "speed": 300.0, "delay": 0.2, "decay": 0.0}, {"CameraShakeBtnName": "es_attack1_2", "eventName": "esoterica_attack_1/begin", "numberOfShakes": 2, "distance": 0.2, "speed": 300.0, "delay": 0.6, "decay": 0.0}, {"CameraShakeBtnName": "es_attack1_3", "eventName": "esoterica_attack_1/begin", "numberOfShakes": 3, "distance": 0.5, "speed": 400.0, "delay": 2.1, "decay": 0.0}, {"CameraShakeBtnName": "es_attack2_1", "eventName": "esoterica_attack_2/begin", "numberOfShakes": 2, "distance": 0.4, "speed": 200.0, "delay": 0.6, "decay": 0.0}, {"CameraShakeBtnName": "es_attack3_1", "eventName": "esoterica_attack_3/begin", "numberOfShakes": 1, "distance": 0.2, "speed": 200.0, "delay": 0.6, "decay": 0.0}, {"CameraShakeBtnName": "es_attack4_1", "eventName": "esoterica_attack_4/begin", "numberOfShakes": 8, "distance": 0.3, "speed": 500.0, "delay": 0.6, "decay": 0.0}, {"CameraShakeBtnName": "es_attack5_1", "eventName": "esoterica_attack_5/begin", "numberOfShakes": 1, "distance": 0.2, "speed": 350.0, "delay": 0.8, "decay": 0.0}, {"CameraShakeBtnName": "es_attack6_1", "eventName": "esoterica_attack_6/begin", "numberOfShakes": 1, "distance": 0.2, "speed": 200.0, "delay": 0.6, "decay": 0.0}, {"CameraShakeBtnName": "es_attack7_1", "eventName": "esoterica_attack_7/begin", "numberOfShakes": 1, "distance": 0.2, "speed": 150.0, "delay": 0.8, "decay": 0.0}, {"CameraShakeBtnName": "es_attack7_2", "eventName": "esoterica_attack_7/begin", "numberOfShakes": 1, "distance": 0.3, "speed": 200.0, "delay": 1.3, "decay": 0.2}, {"CameraShakeBtnName": "es_attack9_1", "eventName": "esoterica_attack_9/begin", "numberOfShakes": 1, "distance": 0.08, "speed": 100.0, "delay": 0.2, "decay": 0.0}, {"CameraShakeBtnName": "es_attack9_2", "eventName": "esoterica_attack_9/begin", "numberOfShakes": 1, "distance": 0.1, "speed": 100.0, "delay": 0.8, "decay": 0.0}, {"CameraShakeBtnName": "es_attack10_1", "eventName": "esoterica_attack_10/begin", "numberOfShakes": 3, "distance": 0.1, "speed": 120.0, "delay": 0.8, "decay": 0.0}], "radialBlurs": [{"btnName": "attack4", "eventName": "attack4/begin", "isRole": true, "referenceNodeHierarchyPath": "root/Bip001/Bip001 Pelvis/Bip001 Spine/buff_middle", "delay": 1.7, "strength": 0.6, "riseTime": 0.1, "holdTime": 0.25, "fallTime": 0.1}, {"btnName": "attack3", "eventName": "attack3/begin", "isRole": true, "referenceNodeHierarchyPath": "root/Bip001/Bip001 Pelvis/Bip001 Spine/Bip001 Spine1/hurt_middle", "delay": 0.5, "strength": 0.5, "riseTime": 0.1, "holdTime": 0.25, "fallTime": 0.1}, {"btnName": "es_attack7_1", "eventName": "esoterica_attack_7/begin", "isRole": true, "referenceNodeHierarchyPath": "root/Bip001/Bip001 Pelvis/Bip001 Spine/buff_middle", "delay": 0.3, "strength": 0.1, "riseTime": 0.1, "holdTime": 0.15, "fallTime": 0.1}, {"btnName": "es_attack9", "eventName": "esoterica_attack_9/begin", "isRole": true, "referenceNodeHierarchyPath": "root/Bip001/Bip001 Pelvis/Bip001 Spine/buff_middle", "delay": 0.1, "strength": 0.2, "riseTime": 0.2, "holdTime": 0.2, "fallTime": 0.0}]}}