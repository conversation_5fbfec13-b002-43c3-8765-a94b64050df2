return {
	actorController = {
		projectiles = {},

		hurts = {},

	},
	actorTriggers = {
		effects = {
			{
				triggerEventName = "attack1/begin",
				triggerDelay = 0.0,
				triggerFreeDelay = 0.0,
				effectGoName = "boss8004_attack",
				effectAsset = {
					BundleName = "effects/prefab/model/boss/8004/boss8004_attack_prefab",
					AssetName = "boss8004_attack",
				},
				playerAtTarget = false,
				referenceNodeHierarchyPath = "",
				isAttach = false,
				isRotation = true,
				isUseCustomTransform = true,
				offsetPosX = 0.0,
				offsetPosY = 0.0,
				offsetPosZ = 0.0,
				triggerStopEvent = "",
				effectBtnName = "attack1",
				playerAtPos = false,
				ignoreParentScale = true,
			},
			{
				triggerEventName = "attack2/begin",
				triggerDelay = 0.0,
				triggerFreeDelay = 0.0,
				effectGoName = "boss8004_skill",
				effectAsset = {
					BundleName = "effects/prefab/model/boss/8004/boss8004_skill_prefab",
					AssetName = "boss8004_skill",
				},
				playerAtTarget = false,
				referenceNodeHierarchyPath = "root",
				isAttach = true,
				isRotation = false,
				isUseCustomTransform = true,
				offsetPosX = 0.0,
				offsetPosY = 0.0,
				offsetPosZ = 0.0,
				triggerStopEvent = "",
				effectBtnName = "attack2",
				playerAtPos = false,
				ignoreParentScale = true,
			},
			{
				triggerEventName = "attack1/begin",
				triggerDelay = 0.6,
				triggerFreeDelay = 0.0,
				effectGoName = "eff_hit_cheng",
				effectAsset = {
					BundleName = "effects/prefab/environment/common/eff_hit_cheng_prefab",
					AssetName = "eff_hit_cheng",
				},
				playerAtTarget = true,
				referenceNodeHierarchyPath = "",
				isAttach = false,
				isRotation = false,
				isUseCustomTransform = false,
				offsetPosX = 0.0,
				offsetPosY = 0.0,
				offsetPosZ = 0.0,
				triggerStopEvent = "",
				effectBtnName = "attack1_hit",
				playerAtPos = false,
				ignoreParentScale = false,
			},
			{
				triggerEventName = "attack2/begin",
				triggerDelay = 1.0,
				triggerFreeDelay = 0.0,
				effectGoName = "eff_hit_cheng",
				effectAsset = {
					BundleName = "effects/prefab/environment/common/eff_hit_cheng_prefab",
					AssetName = "eff_hit_cheng",
				},
				playerAtTarget = true,
				referenceNodeHierarchyPath = "",
				isAttach = false,
				isRotation = false,
				isUseCustomTransform = false,
				offsetPosX = 0.0,
				offsetPosY = 0.0,
				offsetPosZ = 0.0,
				triggerStopEvent = "",
				effectBtnName = "attack2_hit",
				playerAtPos = false,
				ignoreParentScale = false,
			},
		},
		sounds = {},

		cameraShakes = {},

		radialBlurs = {},

	},
}