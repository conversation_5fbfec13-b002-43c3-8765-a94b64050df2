return {
	actorController = {
		projectiles = {},

		hurts = {},

	},
	actorTriggers = {
		effects = {
			{
				triggerEventName = "attack1/begin",
				triggerDelay = 0.0,
				triggerFreeDelay = 0.0,
				effectGoName = "",
				effectAsset = {},

				playerAtTarget = false,
				referenceNodeHierarchyPath = "",
				isAttach = false,
				isRotation = true,
				isUseCustomTransform = false,
				offsetPosX = 0.0,
				offsetPosY = 0.0,
				offsetPosZ = 0.0,
				triggerStopEvent = "",
				effectBtnName = "attack1",
				playerAtPos = false,
				ignoreParentScale = false,
				effectArray = {
					{
						effectGoName = "1101_attack1",
						effectAsset = {
							BundleName = "effects/prefab/role/1101/1101_attack1_prefab",
							AssetName = "1101_attack1",
						},
					},
					{
						effectGoName = "1101_attack1_qing",
						effectAsset = {
							BundleName = "effects/prefab/role/1101/1101_attack1_qing_prefab",
							AssetName = "1101_attack1_qing",
						},
					},
				},
			},
			{
				triggerEventName = "attack2/begin",
				triggerDelay = 0.0,
				triggerFreeDelay = 0.0,
				effectGoName = "",
				effectAsset = {},

				playerAtTarget = false,
				referenceNodeHierarchyPath = "",
				isAttach = false,
				isRotation = true,
				isUseCustomTransform = false,
				offsetPosX = 0.0,
				offsetPosY = 0.0,
				offsetPosZ = 0.0,
				triggerStopEvent = "",
				effectBtnName = "attack2",
				playerAtPos = false,
				ignoreParentScale = false,
				effectArray = {
					{
						effectGoName = "1101_attack2",
						effectAsset = {
							BundleName = "effects/prefab/role/1101/1101_attack2_prefab",
							AssetName = "1101_attack2",
						},
					},
					{
						effectGoName = "1101_attack2_qing",
						effectAsset = {
							BundleName = "effects/prefab/role/1101/1101_attack2_qing_prefab",
							AssetName = "1101_attack2_qing",
						},
					},
				},
			},
			{
				triggerEventName = "attack3/begin",
				triggerDelay = 0.0,
				triggerFreeDelay = 0.0,
				effectGoName = "",
				effectAsset = {},

				playerAtTarget = false,
				referenceNodeHierarchyPath = "",
				isAttach = false,
				isRotation = true,
				isUseCustomTransform = false,
				offsetPosX = 0.0,
				offsetPosY = 0.0,
				offsetPosZ = 0.0,
				triggerStopEvent = "",
				effectBtnName = "attack3",
				playerAtPos = false,
				ignoreParentScale = false,
				effectArray = {
					{
						effectGoName = "1101_attack3",
						effectAsset = {
							BundleName = "effects/prefab/role/1101/1101_attack3_prefab",
							AssetName = "1101_attack3",
						},
					},
					{
						effectGoName = "1101_attack3_qing",
						effectAsset = {
							BundleName = "effects/prefab/role/1101/1101_attack3_qing_prefab",
							AssetName = "1101_attack3_qing",
						},
					},
				},
			},
			{
				triggerEventName = "attack4/begin",
				triggerDelay = 0.0,
				triggerFreeDelay = 0.0,
				effectGoName = "",
				effectAsset = {},

				playerAtTarget = false,
				referenceNodeHierarchyPath = "",
				isAttach = false,
				isRotation = true,
				isUseCustomTransform = false,
				offsetPosX = 0.0,
				offsetPosY = 0.0,
				offsetPosZ = 0.0,
				triggerStopEvent = "",
				effectBtnName = "attack4",
				playerAtPos = false,
				ignoreParentScale = false,
				effectArray = {
					{
						effectGoName = "1101_attack4",
						effectAsset = {
							BundleName = "effects/prefab/role/1101/1101_attack4_prefab",
							AssetName = "1101_attack4",
						},
					},
					{
						effectGoName = "1101_attack4_qing",
						effectAsset = {
							BundleName = "effects/prefab/role/1101/1101_attack4_qing_prefab",
							AssetName = "1101_attack4_qing",
						},
					},
				},
			},
			{
				triggerEventName = "combo1_1/begin",
				triggerDelay = 0.0,
				triggerFreeDelay = 0.0,
				effectGoName = "",
				effectAsset = {},

				playerAtTarget = false,
				referenceNodeHierarchyPath = "",
				isAttach = false,
				isRotation = true,
				isUseCustomTransform = false,
				offsetPosX = 0.0,
				offsetPosY = 0.0,
				offsetPosZ = 0.0,
				triggerStopEvent = "",
				effectBtnName = "combo1_1",
				playerAtPos = false,
				ignoreParentScale = false,
				effectArray = {
					{
						effectGoName = "1101_combo1",
						effectAsset = {
							BundleName = "effects/prefab/role/1101/1101_combo1_prefab",
							AssetName = "1101_combo1",
						},
					},
					{
						effectGoName = "1101_combo1_qing",
						effectAsset = {
							BundleName = "effects/prefab/role/1101/1101_combo1_qing_prefab",
							AssetName = "1101_combo1_qing",
						},
					},
				},
			},
			{
				triggerEventName = "combo1_2/begin",
				triggerDelay = 0.0,
				triggerFreeDelay = 0.0,
				effectGoName = "",
				effectAsset = {},

				playerAtTarget = false,
				referenceNodeHierarchyPath = "",
				isAttach = false,
				isRotation = true,
				isUseCustomTransform = false,
				offsetPosX = 0.0,
				offsetPosY = 0.0,
				offsetPosZ = 0.0,
				triggerStopEvent = "",
				effectBtnName = "combo1_2",
				playerAtPos = false,
				ignoreParentScale = false,
				effectArray = {
					{
						effectGoName = "1101_combo2",
						effectAsset = {
							BundleName = "effects/prefab/role/1101/1101_combo2_prefab",
							AssetName = "1101_combo2",
						},
					},
					{
						effectGoName = "1101_combo2_qing",
						effectAsset = {
							BundleName = "effects/prefab/role/1101/1101_combo2_qing_prefab",
							AssetName = "1101_combo2_qing",
						},
					},
				},
			},
			{
				triggerEventName = "combo1_3/begin",
				triggerDelay = 0.0,
				triggerFreeDelay = 0.0,
				effectGoName = "",
				effectAsset = {},

				playerAtTarget = false,
				referenceNodeHierarchyPath = "",
				isAttach = false,
				isRotation = true,
				isUseCustomTransform = false,
				offsetPosX = 0.0,
				offsetPosY = 0.0,
				offsetPosZ = 0.0,
				triggerStopEvent = "",
				effectBtnName = "combo1_3",
				playerAtPos = false,
				ignoreParentScale = false,
				effectArray = {
					{
						effectGoName = "1101_combo3",
						effectAsset = {
							BundleName = "effects/prefab/role/1101/1101_combo3_prefab",
							AssetName = "1101_combo3",
						},
					},
					{
						effectGoName = "1101_combo3_qing",
						effectAsset = {
							BundleName = "effects/prefab/role/1101/1101_combo3_qing_prefab",
							AssetName = "1101_combo3_qing",
						},
					},
				},
			},
			{
				triggerEventName = "combo1_4/begin",
				triggerDelay = 0.0,
				triggerFreeDelay = 0.0,
				effectGoName = "",
				effectAsset = {},

				playerAtTarget = false,
				referenceNodeHierarchyPath = "",
				isAttach = false,
				isRotation = true,
				isUseCustomTransform = false,
				offsetPosX = 0.0,
				offsetPosY = 0.0,
				offsetPosZ = 0.0,
				triggerStopEvent = "",
				effectBtnName = "combo1_4",
				playerAtPos = false,
				ignoreParentScale = false,
				effectArray = {
					{
						effectGoName = "1101_combo4",
						effectAsset = {
							BundleName = "effects/prefab/role/1101/1101_combo4_prefab",
							AssetName = "1101_combo4",
						},
					},
					{
						effectGoName = "1101_combo4_qing",
						effectAsset = {
							BundleName = "effects/prefab/role/1101/1101_combo4_qing_prefab",
							AssetName = "1101_combo4_qing",
						},
					},
				},
			},
			{
				triggerEventName = "esoterica_attack_1/begin",
				triggerDelay = 0.0,
				triggerFreeDelay = 0.0,
				effectGoName = "",
				effectAsset = {},

				playerAtTarget = false,
				referenceNodeHierarchyPath = "",
				isAttach = false,
				isRotation = true,
				isUseCustomTransform = false,
				offsetPosX = 0.0,
				offsetPosY = 0.0,
				offsetPosZ = 0.0,
				triggerStopEvent = "",
				effectBtnName = "esoterica_attack_1",
				playerAtPos = false,
				ignoreParentScale = false,
				effectArray = {
					{
						effectGoName = "miji_attack01",
						effectAsset = {
							BundleName = "effects/prefab/model/miji/miji_attack01_prefab",
							AssetName = "miji_attack01",
						},
					},
					{
						effectGoName = "",
						effectAsset = {},

					},
				},
			},
			{
				triggerEventName = "esoterica_attack_2/begin",
				triggerDelay = 0.0,
				triggerFreeDelay = 0.0,
				effectGoName = "",
				effectAsset = {},

				playerAtTarget = false,
				referenceNodeHierarchyPath = "",
				isAttach = false,
				isRotation = true,
				isUseCustomTransform = false,
				offsetPosX = 0.0,
				offsetPosY = 0.0,
				offsetPosZ = 0.0,
				triggerStopEvent = "",
				effectBtnName = "esoterica_attack_2",
				playerAtPos = false,
				ignoreParentScale = false,
				effectArray = {
					{
						effectGoName = "xianfa_attack9",
						effectAsset = {
							BundleName = "effects/prefab/model/xianfa/xianfa_attack9_prefab",
							AssetName = "xianfa_attack9",
						},
					},
					{
						effectGoName = "",
						effectAsset = {},

					},
				},
			},
			{
				triggerEventName = "esoterica_attack_3/begin",
				triggerDelay = 0.0,
				triggerFreeDelay = 0.0,
				effectGoName = "",
				effectAsset = {},

				playerAtTarget = false,
				referenceNodeHierarchyPath = "",
				isAttach = false,
				isRotation = true,
				isUseCustomTransform = false,
				offsetPosX = 0.0,
				offsetPosY = 0.0,
				offsetPosZ = 0.0,
				triggerStopEvent = "",
				effectBtnName = "esoterica_attack_3",
				playerAtPos = false,
				ignoreParentScale = false,
				effectArray = {
					{
						effectGoName = "xianfa_attack3",
						effectAsset = {
							BundleName = "effects/prefab/model/xianfa/xianfa_attack3_prefab",
							AssetName = "xianfa_attack3",
						},
					},
					{
						effectGoName = "",
						effectAsset = {},

					},
				},
			},
			{
				triggerEventName = "esoterica_attack_4/begin",
				triggerDelay = 0.0,
				triggerFreeDelay = 0.0,
				effectGoName = "",
				effectAsset = {},

				playerAtTarget = false,
				referenceNodeHierarchyPath = "",
				isAttach = false,
				isRotation = true,
				isUseCustomTransform = false,
				offsetPosX = 0.0,
				offsetPosY = 0.0,
				offsetPosZ = 0.0,
				triggerStopEvent = "",
				effectBtnName = "esoterica_attack_4",
				playerAtPos = false,
				ignoreParentScale = false,
				effectArray = {
					{
						effectGoName = "xianfa_attack1",
						effectAsset = {
							BundleName = "effects/prefab/model/xianfa/xianfa_attack1_prefab",
							AssetName = "xianfa_attack1",
						},
					},
					{
						effectGoName = "",
						effectAsset = {},

					},
				},
			},
			{
				triggerEventName = "esoterica_attack_5/begin",
				triggerDelay = 0.0,
				triggerFreeDelay = 0.0,
				effectGoName = "",
				effectAsset = {},

				playerAtTarget = false,
				referenceNodeHierarchyPath = "",
				isAttach = false,
				isRotation = true,
				isUseCustomTransform = false,
				offsetPosX = 0.0,
				offsetPosY = 0.0,
				offsetPosZ = 0.0,
				triggerStopEvent = "",
				effectBtnName = "esoterica_attack_5",
				playerAtPos = false,
				ignoreParentScale = false,
				effectArray = {
					{
						effectGoName = "xianfa_attack6",
						effectAsset = {
							BundleName = "effects/prefab/model/xianfa/xianfa_attack6_prefab",
							AssetName = "xianfa_attack6",
						},
					},
					{
						effectGoName = "",
						effectAsset = {},

					},
				},
			},
			{
				triggerEventName = "esoterica_attack_6/begin",
				triggerDelay = 0.0,
				triggerFreeDelay = 0.0,
				effectGoName = "",
				effectAsset = {},

				playerAtTarget = false,
				referenceNodeHierarchyPath = "",
				isAttach = false,
				isRotation = true,
				isUseCustomTransform = false,
				offsetPosX = 0.0,
				offsetPosY = 0.0,
				offsetPosZ = 0.0,
				triggerStopEvent = "",
				effectBtnName = "esoterica_attack_6",
				playerAtPos = false,
				ignoreParentScale = false,
				effectArray = {
					{
						effectGoName = "xianfa_attack4",
						effectAsset = {
							BundleName = "effects/prefab/model/xianfa/xianfa_attack4_prefab",
							AssetName = "xianfa_attack4",
						},
					},
					{
						effectGoName = "",
						effectAsset = {},

					},
				},
			},
			{
				triggerEventName = "esoterica_attack_7/begin",
				triggerDelay = 0.0,
				triggerFreeDelay = 0.0,
				effectGoName = "",
				effectAsset = {},

				playerAtTarget = false,
				referenceNodeHierarchyPath = "",
				isAttach = false,
				isRotation = true,
				isUseCustomTransform = false,
				offsetPosX = 4.23,
				offsetPosY = 4.74,
				offsetPosZ = 14.65,
				triggerStopEvent = "",
				effectBtnName = "esoterica_attack_7",
				playerAtPos = false,
				ignoreParentScale = false,
				effectArray = {
					{
						effectGoName = "xianfa_attack8",
						effectAsset = {
							BundleName = "effects/prefab/model/xianfa/xianfa_attack8_prefab",
							AssetName = "xianfa_attack8",
						},
					},
					{
						effectGoName = "",
						effectAsset = {},

					},
				},
			},
			{
				triggerEventName = "attack5/begin",
				triggerDelay = 0.0,
				triggerFreeDelay = 0.0,
				effectGoName = "",
				effectAsset = {},

				playerAtTarget = false,
				referenceNodeHierarchyPath = "",
				isAttach = false,
				isRotation = true,
				isUseCustomTransform = false,
				offsetPosX = 0.0,
				offsetPosY = 0.0,
				offsetPosZ = 0.0,
				triggerStopEvent = "",
				effectBtnName = "attack5",
				playerAtPos = false,
				ignoreParentScale = false,
				effectArray = {
					{
						effectGoName = "dingzhi_attack01",
						effectAsset = {
							BundleName = "effects/prefab/model/dingzhi/dingzhi_attack01_prefab",
							AssetName = "dingzhi_attack01",
						},
					},
					{
						effectGoName = "",
						effectAsset = {},

					},
				},
			},
			{
				triggerEventName = "attack6/begin",
				triggerDelay = 0.0,
				triggerFreeDelay = 0.0,
				effectGoName = "",
				effectAsset = {},

				playerAtTarget = false,
				referenceNodeHierarchyPath = "",
				isAttach = false,
				isRotation = true,
				isUseCustomTransform = false,
				offsetPosX = 0.0,
				offsetPosY = 0.0,
				offsetPosZ = 0.0,
				triggerStopEvent = "",
				effectBtnName = "attack6",
				playerAtPos = false,
				ignoreParentScale = false,
				effectArray = {
					{
						effectGoName = "dingzhi_attack02",
						effectAsset = {
							BundleName = "effects/prefab/model/dingzhi/dingzhi_attack02_prefab",
							AssetName = "dingzhi_attack02",
						},
					},
					{
						effectGoName = "",
						effectAsset = {},

					},
				},
			},
			{
				triggerEventName = "attack7/begin",
				triggerDelay = 0.0,
				triggerFreeDelay = 0.0,
				effectGoName = "",
				effectAsset = {},

				playerAtTarget = false,
				referenceNodeHierarchyPath = "",
				isAttach = false,
				isRotation = true,
				isUseCustomTransform = false,
				offsetPosX = 0.0,
				offsetPosY = 0.0,
				offsetPosZ = 0.0,
				triggerStopEvent = "",
				effectBtnName = "attack7",
				playerAtPos = false,
				ignoreParentScale = false,
				effectArray = {
					{
						effectGoName = "dingzhi_attack05",
						effectAsset = {
							BundleName = "effects/prefab/model/dingzhi/dingzhi_attack05_prefab",
							AssetName = "dingzhi_attack05",
						},
					},
					{
						effectGoName = "",
						effectAsset = {},

					},
				},
			},
			{
				triggerEventName = "fall_rest/begin",
				triggerDelay = 0.0,
				triggerFreeDelay = 0.0,
				effectGoName = "",
				effectAsset = {},

				playerAtTarget = false,
				referenceNodeHierarchyPath = "root",
				isAttach = true,
				isRotation = false,
				isUseCustomTransform = false,
				offsetPosX = 0.0,
				offsetPosY = 0.0,
				offsetPosZ = 0.0,
				triggerStopEvent = "",
				effectBtnName = "fall_rest",
				playerAtPos = false,
				ignoreParentScale = false,
				effectArray = {
					{
						effectGoName = "eff_chuchang_shandian",
						effectAsset = {
							BundleName = "effects/prefab/environment/common/eff_chuchang_shandian_prefab",
							AssetName = "eff_chuchang_shandian",
						},
					},
					{
						effectGoName = "",
						effectAsset = {},

					},
				},
			},
			{
				triggerEventName = "prizedraw/begin",
				triggerDelay = 0.0,
				triggerFreeDelay = 0.0,
				effectGoName = "",
				effectAsset = {},

				playerAtTarget = false,
				referenceNodeHierarchyPath = "root",
				isAttach = true,
				isRotation = false,
				isUseCustomTransform = false,
				offsetPosX = 0.0,
				offsetPosY = 0.0,
				offsetPosZ = 0.0,
				triggerStopEvent = "",
				effectBtnName = "choujiang",
				playerAtPos = false,
				ignoreParentScale = true,
				effectArray = {
					{
						effectGoName = "choujiang_eff",
						effectAsset = {
							BundleName = "effects/prefab/environment/common/choujiang_eff_prefab",
							AssetName = "choujiang_eff",
						},
					},
					{
						effectGoName = "",
						effectAsset = {},

					},
				},
			},
			{
				triggerEventName = "esoterica_attack_8/begin",
				triggerDelay = 0.0,
				triggerFreeDelay = 0.0,
				effectGoName = "",
				effectAsset = {},

				playerAtTarget = false,
				referenceNodeHierarchyPath = "",
				isAttach = false,
				isRotation = true,
				isUseCustomTransform = true,
				offsetPosX = 1.0,
				offsetPosY = 0.0,
				offsetPosZ = -6.0,
				triggerStopEvent = "",
				effectBtnName = "esoterica_attack_8",
				playerAtPos = false,
				ignoreParentScale = false,
				effectArray = {
					{
						effectGoName = "miji_attack08",
						effectAsset = {
							BundleName = "effects/prefab/model/miji/miji_attack08_prefab",
							AssetName = "miji_attack08",
						},
					},
					{
						effectGoName = "",
						effectAsset = {},

					},
				},
			},
			{
				triggerEventName = "esoterica_attack_9/begin",
				triggerDelay = 0.0,
				triggerFreeDelay = 0.0,
				effectGoName = "",
				effectAsset = {},

				playerAtTarget = false,
				referenceNodeHierarchyPath = "",
				isAttach = false,
				isRotation = true,
				isUseCustomTransform = false,
				offsetPosX = 0.0,
				offsetPosY = 0.0,
				offsetPosZ = 0.0,
				triggerStopEvent = "",
				effectBtnName = "esoterica_attack_9",
				playerAtPos = false,
				ignoreParentScale = false,
				effectArray = {
					{
						effectGoName = "xianfa_attack10",
						effectAsset = {
							BundleName = "effects/prefab/model/xianfa/xianfa_attack10_prefab",
							AssetName = "xianfa_attack10",
						},
					},
					{
						effectGoName = "",
						effectAsset = {},

					},
				},
			},
			{
				triggerEventName = "esoterica_attack_10/begin",
				triggerDelay = 0.0,
				triggerFreeDelay = 0.0,
				effectGoName = "",
				effectAsset = {},

				playerAtTarget = false,
				referenceNodeHierarchyPath = "root",
				isAttach = true,
				isRotation = true,
				isUseCustomTransform = false,
				offsetPosX = 0.0,
				offsetPosY = 0.0,
				offsetPosZ = 0.0,
				triggerStopEvent = "",
				effectBtnName = "esoterica_attack_10",
				playerAtPos = false,
				ignoreParentScale = false,
				effectArray = {
					{
						effectGoName = "xianfa_attack2",
						effectAsset = {
							BundleName = "effects/prefab/model/xianfa/xianfa_attack2_prefab",
							AssetName = "xianfa_attack2",
						},
					},
					{
						effectGoName = "",
						effectAsset = {},

					},
				},
			},
		},
		sounds = {
			{
				soundEventName = "combo1_1/begin",
				soundDelay = 0.0,
				soundAudioAsset = {
					BundleName = "audios/sfxs/roleskill/role4",
					AssetName = "role4_attack1",
				},
				soundAudioGoName = "role4_attack1",
				soundBtnName = "combo1_1",
				soundIsMainRole = false,
			},
			{
				soundEventName = "combo1_2/begin",
				soundDelay = 0.0,
				soundAudioAsset = {
					BundleName = "audios/sfxs/roleskill/role4",
					AssetName = "role4_attack2",
				},
				soundAudioGoName = "role4_attack2",
				soundBtnName = "combo1_2",
				soundIsMainRole = false,
			},
			{
				soundEventName = "combo1_3/begin",
				soundDelay = 0.0,
				soundAudioAsset = {
					BundleName = "audios/sfxs/roleskill/role4",
					AssetName = "role4_attack3",
				},
				soundAudioGoName = "role4_attack3",
				soundBtnName = "combo1_3",
				soundIsMainRole = false,
			},
			{
				soundEventName = "combo1_4/begin",
				soundDelay = 0.0,
				soundAudioAsset = {
					BundleName = "audios/sfxs/roleskill/role4",
					AssetName = "role4_attack4",
				},
				soundAudioGoName = "role4_attack4",
				soundBtnName = "combo1_4",
				soundIsMainRole = false,
			},
			{
				soundEventName = "attack1/begin",
				soundDelay = 0.0,
				soundAudioAsset = {
					BundleName = "audios/sfxs/roleskill/role4",
					AssetName = "role4_skill1",
				},
				soundAudioGoName = "role4_skill1",
				soundBtnName = "attack1",
				soundIsMainRole = false,
			},
			{
				soundEventName = "attack2/begin",
				soundDelay = 0.0,
				soundAudioAsset = {
					BundleName = "audios/sfxs/roleskill/role4",
					AssetName = "role4_skill2",
				},
				soundAudioGoName = "role4_skill2",
				soundBtnName = "attack2",
				soundIsMainRole = false,
			},
			{
				soundEventName = "attack3/begin",
				soundDelay = 0.0,
				soundAudioAsset = {
					BundleName = "audios/sfxs/roleskill/role4",
					AssetName = "role4_skill3",
				},
				soundAudioGoName = "role4_skill3",
				soundBtnName = "attack3",
				soundIsMainRole = false,
			},
			{
				soundEventName = "attack4/begin",
				soundDelay = 0.0,
				soundAudioAsset = {
					BundleName = "audios/sfxs/roleskill/role4",
					AssetName = "role4_skill4",
				},
				soundAudioGoName = "role4_skill4",
				soundBtnName = "attack4",
				soundIsMainRole = false,
			},
			{
				soundEventName = "attack5/begin",
				soundDelay = 0.0,
				soundAudioAsset = {
					BundleName = "audios/sfxs/roleskill/jineng",
					AssetName = "role1_attack5",
				},
				soundAudioGoName = "role1_attack5",
				soundBtnName = "attack5",
				soundIsMainRole = false,
			},
			{
				soundEventName = "attack6/begin",
				soundDelay = 0.0,
				soundAudioAsset = {
					BundleName = "audios/sfxs/roleskill/jineng",
					AssetName = "role1_attack6",
				},
				soundAudioGoName = "role1_attack6",
				soundBtnName = "attack6",
				soundIsMainRole = false,
			},
			{
				soundEventName = "attack9/begin",
				soundDelay = 0.0,
				soundAudioAsset = {
					BundleName = "audios/sfxs/roleskill/jineng",
					AssetName = "role1_attack7",
				},
				soundAudioGoName = "role1_attack7",
				soundBtnName = "attack9",
				soundIsMainRole = false,
			},
			{
				soundEventName = "attack10/begin",
				soundDelay = 0.0,
				soundAudioAsset = {
					BundleName = "audios/sfxs/roleskill/jineng",
					AssetName = "role1_attack8",
				},
				soundAudioGoName = "role1_attack8",
				soundBtnName = "attack10",
				soundIsMainRole = false,
			},
			{
				soundEventName = "attack11/begin",
				soundDelay = 0.0,
				soundAudioAsset = {
					BundleName = "audios/sfxs/roleskill/jineng",
					AssetName = "role1_attack9",
				},
				soundAudioGoName = "role1_attack9",
				soundBtnName = "attack11",
				soundIsMainRole = false,
			},
			{
				soundEventName = "attack12/begin",
				soundDelay = 0.0,
				soundAudioAsset = {
					BundleName = "audios/sfxs/roleskill/jineng",
					AssetName = "role1_attack10",
				},
				soundAudioGoName = "role1_attack10",
				soundBtnName = "attack12",
				soundIsMainRole = false,
			},
			{
				soundEventName = "esoterica_attack_1/begin",
				soundDelay = 0.0,
				soundAudioAsset = {
					BundleName = "audios/sfxs/roleskill/jineng",
					AssetName = "miji1",
				},
				soundAudioGoName = "miji1",
				soundBtnName = "miji1",
				soundIsMainRole = false,
			},
			{
				soundEventName = "esoterica_attack_2/begin",
				soundDelay = 0.0,
				soundAudioAsset = {
					BundleName = "audios/sfxs/roleskill/jineng",
					AssetName = "miji2",
				},
				soundAudioGoName = "miji2",
				soundBtnName = "miji2",
				soundIsMainRole = false,
			},
			{
				soundEventName = "esoterica_attack_3/begin",
				soundDelay = 0.0,
				soundAudioAsset = {
					BundleName = "audios/sfxs/roleskill/jineng",
					AssetName = "miji3",
				},
				soundAudioGoName = "miji3",
				soundBtnName = "miji3",
				soundIsMainRole = false,
			},
			{
				soundEventName = "esoterica_attack_4/begin",
				soundDelay = 0.0,
				soundAudioAsset = {
					BundleName = "audios/sfxs/roleskill/jineng",
					AssetName = "miji4",
				},
				soundAudioGoName = "miji4",
				soundBtnName = "miji4",
				soundIsMainRole = false,
			},
			{
				soundEventName = "esoterica_attack_5/begin",
				soundDelay = 0.0,
				soundAudioAsset = {
					BundleName = "audios/sfxs/roleskill/jineng",
					AssetName = "miji5",
				},
				soundAudioGoName = "miji5",
				soundBtnName = "miji5",
				soundIsMainRole = false,
			},
			{
				soundEventName = "esoterica_attack_6/begin",
				soundDelay = 0.0,
				soundAudioAsset = {
					BundleName = "audios/sfxs/roleskill/jineng",
					AssetName = "miji6",
				},
				soundAudioGoName = "miji6",
				soundBtnName = "miji6",
				soundIsMainRole = false,
			},
			{
				soundEventName = "esoterica_attack_7/begin",
				soundDelay = 0.0,
				soundAudioAsset = {
					BundleName = "audios/sfxs/roleskill/jineng",
					AssetName = "miji7",
				},
				soundAudioGoName = "miji7",
				soundBtnName = "miji7",
				soundIsMainRole = false,
			},
		},
		cameraShakes = {
			{
				CameraShakeBtnName = "attack1",
				eventName = "attack1/begin",
				numberOfShakes = 1,
				distance = 0.2,
				speed = 650.0,
				delay = 0.65,
				decay = 0.2,
			},
			{
				CameraShakeBtnName = "attack2",
				eventName = "attack2/begin",
				numberOfShakes = 1,
				distance = 0.3,
				speed = 300.0,
				delay = 0.95,
				decay = 0.0,
			},
			{
				CameraShakeBtnName = "combo1_4",
				eventName = "combo1_4/begin",
				numberOfShakes = 3,
				distance = 0.5,
				speed = 450.0,
				delay = 1.1,
				decay = 0.0,
			},
			{
				CameraShakeBtnName = "attack4_2",
				eventName = "attack4/begin",
				numberOfShakes = 1,
				distance = 1.7,
				speed = 60.0,
				delay = 1.7,
				decay = 0.0,
			},
			{
				CameraShakeBtnName = "es_attack1_1",
				eventName = "esoterica_attack_1/begin",
				numberOfShakes = 1,
				distance = 0.2,
				speed = 300.0,
				delay = 0.2,
				decay = 0.0,
			},
			{
				CameraShakeBtnName = "es_attack1_2",
				eventName = "esoterica_attack_1/begin",
				numberOfShakes = 2,
				distance = 0.2,
				speed = 300.0,
				delay = 0.6,
				decay = 0.0,
			},
			{
				CameraShakeBtnName = "es_attack1_3",
				eventName = "esoterica_attack_1/begin",
				numberOfShakes = 3,
				distance = 0.5,
				speed = 400.0,
				delay = 2.1,
				decay = 0.0,
			},
			{
				CameraShakeBtnName = "es_attack2_1",
				eventName = "esoterica_attack_2/begin",
				numberOfShakes = 2,
				distance = 0.4,
				speed = 200.0,
				delay = 0.6,
				decay = 0.0,
			},
			{
				CameraShakeBtnName = "es_attack3_1",
				eventName = "esoterica_attack_3/begin",
				numberOfShakes = 1,
				distance = 0.2,
				speed = 200.0,
				delay = 0.6,
				decay = 0.0,
			},
			{
				CameraShakeBtnName = "es_attack4_1",
				eventName = "esoterica_attack_4/begin",
				numberOfShakes = 8,
				distance = 0.3,
				speed = 500.0,
				delay = 0.6,
				decay = 0.0,
			},
			{
				CameraShakeBtnName = "es_attack5_1",
				eventName = "esoterica_attack_5/begin",
				numberOfShakes = 1,
				distance = 0.2,
				speed = 350.0,
				delay = 0.8,
				decay = 0.0,
			},
			{
				CameraShakeBtnName = "es_attack6_1",
				eventName = "esoterica_attack_6/begin",
				numberOfShakes = 1,
				distance = 0.2,
				speed = 200.0,
				delay = 0.6,
				decay = 0.0,
			},
			{
				CameraShakeBtnName = "es_attack7_1",
				eventName = "esoterica_attack_7/begin",
				numberOfShakes = 1,
				distance = 0.2,
				speed = 150.0,
				delay = 0.8,
				decay = 0.0,
			},
			{
				CameraShakeBtnName = "es_attack7_2",
				eventName = "esoterica_attack_7/begin",
				numberOfShakes = 1,
				distance = 0.3,
				speed = 200.0,
				delay = 1.3,
				decay = 0.2,
			},
			{
				CameraShakeBtnName = "es_attack9_1",
				eventName = "esoterica_attack_9/begin",
				numberOfShakes = 1,
				distance = 0.08,
				speed = 100.0,
				delay = 0.2,
				decay = 0.0,
			},
			{
				CameraShakeBtnName = "es_attack9_2",
				eventName = "esoterica_attack_9/begin",
				numberOfShakes = 1,
				distance = 0.1,
				speed = 100.0,
				delay = 0.8,
				decay = 0.0,
			},
			{
				CameraShakeBtnName = "es_attack10_1",
				eventName = "esoterica_attack_10/begin",
				numberOfShakes = 3,
				distance = 0.1,
				speed = 120.0,
				delay = 0.8,
				decay = 0.0,
			},
		},
		radialBlurs = {
			{
				btnName = "attack4",
				eventName = "attack4/begin",
				isRole = true,
				referenceNodeHierarchyPath = "root/Bip001/Bip001 Pelvis/Bip001 Spine/buff_middle",
				delay = 1.7,
				strength = 0.6,
				riseTime = 0.1,
				holdTime = 0.25,
				fallTime = 0.1,
			},
			{
				btnName = "attack3",
				eventName = "attack3/begin",
				isRole = true,
				referenceNodeHierarchyPath = "root/Bip001/Bip001 Pelvis/Bip001 Spine/Bip001 Spine1/hurt_middle",
				delay = 0.5,
				strength = 0.5,
				riseTime = 0.1,
				holdTime = 0.25,
				fallTime = 0.1,
			},
			{
				btnName = "es_attack7_1",
				eventName = "esoterica_attack_7/begin",
				isRole = true,
				referenceNodeHierarchyPath = "root/Bip001/Bip001 Pelvis/Bip001 Spine/buff_middle",
				delay = 0.3,
				strength = 0.1,
				riseTime = 0.1,
				holdTime = 0.15,
				fallTime = 0.1,
			},
			{
				btnName = "es_attack9",
				eventName = "esoterica_attack_9/begin",
				isRole = true,
				referenceNodeHierarchyPath = "root/Bip001/Bip001 Pelvis/Bip001 Spine/buff_middle",
				delay = 0.1,
				strength = 0.2,
				riseTime = 0.2,
				holdTime = 0.2,
				fallTime = 0.0,
			},
		},
	},
}