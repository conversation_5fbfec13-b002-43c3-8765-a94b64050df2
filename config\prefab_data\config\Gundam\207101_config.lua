return {
	actorController = {
		projectiles = {},

		hurts = {},

		beHurtEffecct = {},

		hurtEffectName = "",
		beHurtNodeName = "",
		beHurtAttach = false,
		hurtEffectFreeDelay = 0.0,
		QualityCtrlList = {},

	},
	actorTriggers = {
		effects = {},

		halts = {},

		sounds = {
			{
				soundEventName = "attack1_1/begin",
				soundDelay = 0.0,
				soundAudioAsset = {
					BundleName = "audios/sfxs/roleskill/jijia2_1",
					AssetName = "skill2_1_1",
				},
				soundAudioGoName = "skill2_1_1",
				soundBtnName = "attack1",
				soundIsMainRole = false,
			},
			{
				soundEventName = "attack1_2/begin",
				soundDelay = 0.0,
				soundAudioAsset = {
					BundleName = "audios/sfxs/roleskill/jijia2_1",
					AssetName = "skill2_1_2",
				},
				soundAudioGoName = "skill2_1_2",
				soundBtnName = "attack2",
				soundIsMainRole = false,
			},
			{
				soundEventName = "attack1_3/begin",
				soundDelay = 0.0,
				soundAudioAsset = {
					BundleName = "audios/sfxs/roleskill/jijia2_1",
					AssetName = "skill2_1_3",
				},
				soundAudioGoName = "skill2_1_3",
				soundBtnName = "attack3",
				soundIsMainRole = false,
			},
			{
				soundEventName = "combo1_1/begin",
				soundDelay = 0.0,
				soundAudioAsset = {
					BundleName = "audios/sfxs/roleskill/jijia2_1",
					AssetName = "attack2_1_1",
				},
				soundAudioGoName = "attack2_1_1",
				soundBtnName = "combo1",
				soundIsMainRole = false,
			},
			{
				soundEventName = "combo1_2/begin",
				soundDelay = 0.0,
				soundAudioAsset = {
					BundleName = "audios/sfxs/roleskill/jijia2_1",
					AssetName = "attack2_1_2",
				},
				soundAudioGoName = "attack2_1_2",
				soundBtnName = "combo2",
				soundIsMainRole = false,
			},
			{
				soundEventName = "combo1_3/begin",
				soundDelay = 0.0,
				soundAudioAsset = {
					BundleName = "audios/sfxs/roleskill/jijia2_1",
					AssetName = "attack2_1_3",
				},
				soundAudioGoName = "attack2_1_3",
				soundBtnName = "combo3",
				soundIsMainRole = false,
			},
		},
		cameraShakes = {
			{
				CameraShakeBtnName = "attack1",
				eventName = "attack1_1/begin",
				numberOfShakes = 5,
				distance = 0.1,
				speed = 100.0,
				delay = 0.5,
				decay = 0.0,
			},
			{
				CameraShakeBtnName = "attack2",
				eventName = "attack1_2/begin",
				numberOfShakes = 8,
				distance = 0.15,
				speed = 100.0,
				delay = 0.5,
				decay = 0.01,
			},
			{
				CameraShakeBtnName = "attack3_1",
				eventName = "attack1_3/begin",
				numberOfShakes = 5,
				distance = 0.2,
				speed = 100.0,
				delay = 0.36,
				decay = 0.0,
			},
			{
				CameraShakeBtnName = "attack3_2",
				eventName = "attack1_3/begin",
				numberOfShakes = 5,
				distance = 0.2,
				speed = 100.0,
				delay = 3.3,
				decay = 0.0,
			},
			{
				CameraShakeBtnName = "combo3",
				eventName = "combo1_3/begin",
				numberOfShakes = 3,
				distance = 0.07,
				speed = 200.0,
				delay = 0.4,
				decay = 0.1,
			},
		},
		cameraFOVs = {},

		sceneFades = {},

		footsteps = {},

	},
	actorBlinker = {
		blinkFadeIn = 0.0,
		blinkFadeHold = 0.0,
		blinkFadeOut = 0.0,
	},
	TimeLineList = {},

}