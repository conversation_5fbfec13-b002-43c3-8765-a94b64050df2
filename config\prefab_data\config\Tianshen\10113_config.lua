return {
	actorController = {
		projectiles = {},

		hurts = {},

	},
	actorTriggers = {
		effects = {
			{
				triggerEventName = "attack1/begin",
				triggerDelay = 0.0,
				triggerFreeDelay = 0.0,
				effectGoName = "10113_skill1",
				effectAsset = {
					BundleName = "effects/prefab/model/tianshen/10113/10113_skill1_prefab",
					AssetName = "10113_skill1",
				},
				playerAtTarget = false,
				referenceNodeHierarchyPath = "buff_down",
				isAttach = true,
				isRotation = true,
				isUseCustomTransform = true,
				offsetPosX = 0.5,
				offsetPosY = 0.0,
				offsetPosZ = -2.0,
				triggerStopEvent = "",
				effectBtnName = "attack1",
				playerAtPos = false,
				ignoreParentScale = false,
			},
			{
				triggerEventName = "attack2/begin",
				triggerDelay = 0.0,
				triggerFreeDelay = 0.0,
				effectGoName = "10113_skill2",
				effectAsset = {
					BundleName = "effects/prefab/model/tianshen/10113/10113_skill2_prefab",
					AssetName = "10113_skill2",
				},
				playerAtTarget = false,
				referenceNodeHierarchyPath = "",
				isAttach = false,
				isRotation = true,
				isUseCustomTransform = false,
				offsetPosX = 0.0,
				offsetPosY = 0.0,
				offsetPosZ = 0.0,
				triggerStopEvent = "",
				effectBtnName = "attack2",
				playerAtPos = false,
				ignoreParentScale = false,
			},
			{
				triggerEventName = "attack3/begin",
				triggerDelay = 0.0,
				triggerFreeDelay = 0.0,
				effectGoName = "10113_skill3",
				effectAsset = {
					BundleName = "effects/prefab/model/tianshen/10113/10113_skill3_prefab",
					AssetName = "10113_skill3",
				},
				playerAtTarget = false,
				referenceNodeHierarchyPath = "buff_down",
				isAttach = true,
				isRotation = true,
				isUseCustomTransform = true,
				offsetPosX = 0.5,
				offsetPosY = 0.0,
				offsetPosZ = -8.0,
				triggerStopEvent = "",
				effectBtnName = "attack3",
				playerAtPos = false,
				ignoreParentScale = false,
			},
			{
				triggerEventName = "combo1_1/begin",
				triggerDelay = 0.0,
				triggerFreeDelay = 0.0,
				effectGoName = "10113_attack",
				effectAsset = {
					BundleName = "effects/prefab/model/tianshen/10113/10113_attack_prefab",
					AssetName = "10113_attack",
				},
				playerAtTarget = false,
				referenceNodeHierarchyPath = "",
				isAttach = false,
				isRotation = true,
				isUseCustomTransform = false,
				offsetPosX = 0.0,
				offsetPosY = 0.0,
				offsetPosZ = 0.0,
				triggerStopEvent = "",
				effectBtnName = "combo",
				playerAtPos = false,
				ignoreParentScale = false,
			},
			{
				triggerEventName = "rest/begin",
				triggerDelay = 0.0,
				triggerFreeDelay = 0.0,
				effectGoName = "10113_skill3",
				effectAsset = {
					BundleName = "effects/prefab/model/tianshen/10113/10113_skill3_prefab",
					AssetName = "10113_skill3",
				},
				playerAtTarget = false,
				referenceNodeHierarchyPath = "hurt_root",
				isAttach = true,
				isRotation = false,
				isUseCustomTransform = true,
				offsetPosX = 0.0,
				offsetPosY = 0.0,
				offsetPosZ = -10.0,
				triggerStopEvent = "",
				effectBtnName = "rest",
				playerAtPos = false,
				ignoreParentScale = false,
			},
		},
		sounds = {
			{
				soundEventName = "combo1_1/begin",
				soundDelay = 0.0,
				soundAudioAsset = {
					BundleName = "audios/sfxs/roleskill/tianshen_10113",
					AssetName = "MingJiangcombo_1_1",
				},
				soundAudioGoName = "MingJiangcombo_1_1",
				soundBtnName = "combo1",
				soundIsMainRole = false,
			},
			{
				soundEventName = "combo1_2/begin",
				soundDelay = 0.0,
				soundAudioAsset = {
					BundleName = "audios/sfxs/roleskill/tianshen_10113",
					AssetName = "MingJiangcombo_1_2",
				},
				soundAudioGoName = "MingJiangcombo_1_2",
				soundBtnName = "combo2",
				soundIsMainRole = false,
			},
			{
				soundEventName = "combo1_3/begin",
				soundDelay = 0.0,
				soundAudioAsset = {
					BundleName = "audios/sfxs/roleskill/tianshen_10113",
					AssetName = "MingJiangcombo_1_3",
				},
				soundAudioGoName = "MingJiangcombo_1_3",
				soundBtnName = "combo3",
				soundIsMainRole = false,
			},
			{
				soundEventName = "attack1/begin",
				soundDelay = 0.0,
				soundAudioAsset = {
					BundleName = "audios/sfxs/roleskill/tianshen_10113",
					AssetName = "MingJiangattack1",
				},
				soundAudioGoName = "MingJiangattack1",
				soundBtnName = "attack1",
				soundIsMainRole = false,
			},
			{
				soundEventName = "attack2/begin",
				soundDelay = 0.0,
				soundAudioAsset = {
					BundleName = "audios/sfxs/roleskill/tianshen_10113",
					AssetName = "MingJiangattack3",
				},
				soundAudioGoName = "MingJiangattack3",
				soundBtnName = "attack2",
				soundIsMainRole = false,
			},
			{
				soundEventName = "attack3/begin",
				soundDelay = 0.0,
				soundAudioAsset = {
					BundleName = "audios/sfxs/roleskill/tianshen_10113",
					AssetName = "MingJiangattack2",
				},
				soundAudioGoName = "MingJiangattack2",
				soundBtnName = "attack3",
				soundIsMainRole = false,
			},
		},
		cameraShakes = {
			{
				CameraShakeBtnName = "attack3-1",
				eventName = "attack3/begin",
				numberOfShakes = 5,
				distance = 0.1,
				speed = 500.0,
				delay = 1.3,
				decay = 0.0,
			},
			{
				CameraShakeBtnName = "attack1",
				eventName = "attack1/begin",
				numberOfShakes = 5,
				distance = 0.1,
				speed = 500.0,
				delay = 0.4,
				decay = 0.0,
			},
			{
				CameraShakeBtnName = "attack3-2",
				eventName = "attack3/begin",
				numberOfShakes = 3,
				distance = 0.1,
				speed = 400.0,
				delay = 0.3,
				decay = 0.0,
			},
			{
				CameraShakeBtnName = "attack2-1",
				eventName = "attack2/begin",
				numberOfShakes = 10,
				distance = 0.05,
				speed = 600.0,
				delay = 1.5,
				decay = 0.0,
			},
		},
		radialBlurs = {},

	},
}