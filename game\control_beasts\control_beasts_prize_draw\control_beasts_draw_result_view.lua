-- 额外消耗类型
local EXTRA_COST_TYPE = {
	LINGYU = 1,
	YANYUGE_SCORE = 2,
}

ControlBeastsDrawResultView = ControlBeastsDrawResultView or BaseClass(SafeBaseView)

function ControlBeastsDrawResultView:__init()
	self.view_style = ViewStyle.Full
    self.view_layer = UiLayer.Normal
	local common_bundle = "uis/view/common_panel_prefab"
	self:AddViewResource(0, common_bundle, "layout_a3_common_panel")
    self:AddViewResource(0, "uis/view/control_beasts_ui_prefab", "layout_control_beasts_draw_result_result")
    self:AddViewResource(0, common_bundle, "layout_a3_common_top_panel")
end

function ControlBeastsDrawResultView:SetData(contract_select_type, id_list, again_func, other_info, no_need_sort, sure_func)
	self.contract_select_type = contract_select_type
	self.id_list = id_list
	self.again_func = again_func
	self.other_info = other_info
	self.no_need_sort = no_need_sort
	self.sure_func = sure_func

	local curr_draw_anim, _ = ControlBeastsPrizeDrawWGData.Instance:GetOperateStatus(self.contract_select_type)
	self.is_skip_anim = curr_draw_anim == 1
end

function ControlBeastsDrawResultView:LoadCallBack()
	self.node_list["title_view_name"].text.text = Language.ContralBeasts.TitleName5

	local bundle, asset = ResPath.GetRawImagesJPG("a3_hs_cj_result_bg")
	if self.node_list.RawImage_tongyong then
		self.node_list["RawImage_tongyong"].raw_image:LoadSprite(bundle, asset, function()
			self.node_list["RawImage_tongyong"].raw_image:SetNativeSize()
		end)
	end

	if not self.reward_data_list then
		self.reward_data_list = AsyncListView.New(OAControlBeastsDrawResuleListCellRender, self.node_list.reward_data_list)
		self.reward_data_list:SetStartZeroIndex(false)
	end

	XUI.AddClickEventListener(self.node_list.btn_sure, BindTool.Bind1(self.OnClickSureBtn, self))
	XUI.AddClickEventListener(self.node_list.btn_one_more, BindTool.Bind1(self.OnClickOneMoreBtn, self))
	XUI.AddClickEventListener(self.node_list.skip_anim_toggle, BindTool.Bind(self.OnClinkSkipAnim, self))
	--XUI.AddClickEventListener(self.node_list.jump_to_battle, BindTool.Bind(self.OnClinkJumpToBattle, self))
end

function ControlBeastsDrawResultView:ReleaseCallBack()
	self.info = nil
	self.one_draw_mode_again_func = nil
	self.ten_draw_mode_again_func = nil

	if self.reward_data_list then
		self.reward_data_list:DeleteMe()
		self.reward_data_list = nil
	end
end

function ControlBeastsDrawResultView:ShowIndexCallBack()
	ControlBeastsOADrawWGData.Instance:SetDrawSkipTweenFlag(false)
end

function ControlBeastsDrawResultView:OnFlush()
	self.node_list.skip_anim_toggle.toggle.isOn = self.is_skip_anim
	self.node_list.btn_one_more:CustomSetActive(self.again_func ~= nil and self.other_info ~= nil)
	self.node_list.skip_anim_toggle:CustomSetActive(self.again_func ~= nil)

	if self.other_info then
		self.node_list.btn_sure_text.tmp.text = self.other_info.sure_text or Language.Common.Confirm
		self.node_list.btn_again_txt.tmp.text = self.other_info.again_text or Language.TreasureHunt.BtnText[0]
		self.node_list.spend_root:CustomSetActive(self.other_info.stuff_id ~= nil and self.other_info.times ~= nil and self.other_info.spend ~= nil)

		if self.other_info.stuff_id then
			local has_num = ItemWGData.Instance:GetItemNumInBagById(self.other_info.stuff_id) --拥有的材料数量
			self.node_list.xianyu_icon:CustomSetActive(has_num < self.other_info.times)
			self.node_list.one_more_cosume:CustomSetActive(has_num < self.other_info.times)
			self.node_list.zbitem_key:CustomSetActive(has_num > 0)

			self.node_list.zbitem_key_num.tmp.text = has_num .. "/" .. self.other_info.times
			local zb_item_bundle, zb_item_asset = ResPath.GetItem(ItemWGData.Instance:GetItemIconByItemId(self.other_info.stuff_id))
			self.node_list.img_zbitem.image:LoadSprite(zb_item_bundle, zb_item_asset, function ()
				self.node_list.img_zbitem.image:SetNativeSize()
			end)
			self.node_list.one_more_cosume.tmp.text = (self.other_info.times - has_num) * self.other_info.spend
			self.node_list.img_zbitem:CustomSetActive(true)

			local extra_cost_type = self.other_info.extra_cost_type or EXTRA_COST_TYPE.LINGYU

			if extra_cost_type == EXTRA_COST_TYPE.LINGYU then
				local xianyu_icon_bundle, xianyu_icon_asset = ResPath.GetCommonIcon("a3_huobi_xianyu")
				self.node_list["xianyu_icon"].image:LoadSprite(xianyu_icon_bundle, xianyu_icon_asset, function()
					self.node_list["xianyu_icon"].image:SetNativeSize()
				end)
			elseif extra_cost_type == EXTRA_COST_TYPE.YANYUGE_SCORE then
				-- 积分加道具足够
				local show_item = YanYuGeWGData.Instance:GetOtherCfgAttrValue("show_item")
				local xianyu_icon_bundle, xianyu_icon_asset = ResPath.GetItem(ItemWGData.Instance:GetItemIconByItemId(show_item))
				self.node_list["xianyu_icon"].image:LoadSprite(xianyu_icon_bundle, xianyu_icon_asset, function()
					self.node_list["xianyu_icon"].image:SetNativeSize()
				end)

				local score = YanYuGeWGData.Instance:GetCurScore()
				if score < ((self.other_info.times - has_num) * self.other_info.spend) then
					self.node_list.xianyu_icon:CustomSetActive(false)
					self.node_list.one_more_cosume:CustomSetActive(false)
					self.node_list.zbitem_key:CustomSetActive(true)
				end
			end
		end
	end

	self:ResetItemData()
end

function ControlBeastsDrawResultView:ResetItemData()
	if not self.id_list then
		return
	end

	local list_info = self.id_list
	list_info = self:SortItem(list_info)

	local cell_time = 0.2
	local delay_per_time = 0.1
	local delay_show_time = self.is_skip_anim and 0.1 or (cell_time + (#list_info - 1) * delay_per_time)

	for k, v in pairs(list_info) do
		v.is_skip_anim = self.is_skip_anim
		v.cell_time = cell_time
		v.delay_per_time = delay_per_time
		v.delay_show_time = delay_show_time
		v.can_auto_decompose = self.other_info.can_auto_decompose or 0
	end

	self.node_list.right_arrow:CustomSetActive(#list_info > 10)
	self.node_list.left_arrow:CustomSetActive(#list_info > 10)
	self.reward_data_list:SetDataList(list_info)
	self.reward_data_list:JumpToIndex(1)
end

function ControlBeastsDrawResultView:SortItem(item_list)
	if self.no_need_sort then
		return item_list
	end

	local item_cfg, item_type
	for i, v in ipairs(item_list) do
		local is_beasts_item = ControlBeastsWGData.Instance:IsBeastsItem(v.item_id)
		local beast_cfg = {}

		if is_beasts_item then
			beast_cfg = ControlBeastsWGData.Instance:GetBeastCfgById(v.item_id)
		else
			local chip_id_to_beasts_id = ControlBeastsWGData.Instance:GetBeastsChipIdToBeastsId(v.item_id)

			if chip_id_to_beasts_id then
				beast_cfg = ControlBeastsWGData.Instance:GetBeastCfgById(chip_id_to_beasts_id)
			end
		end

		v.beast_color = beast_cfg and beast_cfg.beast_color or 0
		v.beast_star = beast_cfg and beast_cfg.beast_star or 0
		v.beasts_flag = is_beasts_item and 1 or 0
	end
	
	SortTools.SortDesc(item_list, "beast_color", "beast_star", "beasts_flag")
	return item_list
end

function ControlBeastsDrawResultView:OnClickSureBtn()
	if self.sure_func then
		self.sure_func()
	end
	
	self:Close()
end

function ControlBeastsDrawResultView:OnClickOneMoreBtn()
	if self.again_func then
		self.again_func()
	end
end

function ControlBeastsDrawResultView:OnClinkSkipAnim(is_on)
	self.is_skip_anim = is_on
	ControlBeastsOADrawWGData.Instance:SetDrawSkipTweenFlag(is_on)
end

-- function ControlBeastsDrawResultView:OnClinkJumpToBattle()
-- 	ControlBeastsWGCtrl.Instance:FlushSingleBeastBagInfo(true)
-- 	ViewManager.Instance:Open(GuideModuleName.ControlBeastsView, TabIndex.beasts_battle)
-- 	self:Close()
-- end

local OA_DRAW_ITEM_EFFECT_CFG = {
	[1] = "UI_HSZH_lv",
	[2] = "UI_HSZH_lan",
	[3] = "UI_HSZH_zi",
	[4] = "UI_HSZH_cheng",
	[5] = "UI_HSZH_hong",
	[6] = "UI_HSZH_fen",
	[7] = "UI_HSZH_jin",
}

-------------------------------------OAControlBeastsDrawResuleListCellRender------------------------------------------
OAControlBeastsDrawResuleListCellRender = OAControlBeastsDrawResuleListCellRender or BaseClass(BaseRender)

function OAControlBeastsDrawResuleListCellRender:LoadCallBack()
	self.node_list.root.rect.anchoredPosition = Vector2(200, 0)
	self.node_list.root.canvas_group.alpha = 0
	-- self.node_list.mask_bg.canvas_group.alpha = 0
	self.node_list.item_pos:CustomSetActive(false)

	if not self.item then
		self.item = ItemCell.New(self.node_list.item_pos)
		self.item:SetUseButton(false)
	end
end

function OAControlBeastsDrawResuleListCellRender:__delete()
	if self.show_sequence then
		self.show_sequence:Kill()
		self.show_sequence = nil
	end

	if self.item then
		self.item:DeleteMe()
		self.item = nil
	end

	if self.delay_set_func then
		GlobalTimerQuest:CancelQuest(self.delay_set_func)
		self.delay_set_func = nil
	end
end

function OAControlBeastsDrawResuleListCellRender:OnFlush()
	if IsEmptyTable(self.data) then
		return
	end

	self.node_list.root_decompose:CustomSetActive(false)
	local beast_id = self.data.item_id
	local is_beasts_item = ControlBeastsWGData.Instance:IsBeastsItem(beast_id)
	local beast_cfg = {}
	local color = nil

	if is_beasts_item then
		beast_cfg = ControlBeastsWGData.Instance:GetBeastCfgById(beast_id)
	else
		local chip_id_to_beasts_id = ControlBeastsWGData.Instance:GetBeastsChipIdToBeastsId(beast_id)

		if chip_id_to_beasts_id then
			beast_cfg = ControlBeastsWGData.Instance:GetBeastCfgById(chip_id_to_beasts_id)
		end
	end
	local is_beast = not IsEmptyTable(beast_cfg)
	self.node_list.root_ship:CustomSetActive((not is_beasts_item) and is_beast)
	if not is_beasts_item then
		self.node_list.desc_ship_num.text.text = "x" .. CommonDataManager.ConverMoneyByThousand(self.data.bag_index)
	end

	-- 如果不是幻兽则是道具
	local item_cfg = nil
	color = beast_cfg.beast_color

	if IsEmptyTable(beast_cfg) then
		item_cfg = ItemWGData.Instance:GetItemConfig(self.data.item_id)
		color = item_cfg.color
	end

	local quality_bg_bundle, quality_bg_asset = ResPath.GetRawImagesPNG("a3_hs_dr_bg" .. color)
	self.node_list.quality_bg.raw_image:LoadSprite(quality_bg_bundle, quality_bg_asset, function ()
		self.node_list.quality_bg.raw_image:SetNativeSize()
	end)

	local effect_name = OA_DRAW_ITEM_EFFECT_CFG[tonumber(color)]
	if nil ~= effect_name then
		local bundle, asset = ResPath.GetEffect(effect_name)
		self.node_list.effect:ChangeAsset(bundle, asset)
		self.node_list.effect:CustomSetActive(true)
	else
		self.node_list.effect:CustomSetActive(false)
	end 

	self.node_list.effect_saoguang:CustomSetActive(true)
	self.node_list.beast_root:CustomSetActive(is_beast)
	local asset_bundle, asset_name
	local str = ""

	asset_bundle, asset_name = ResPath.GetRawImagesPNG(string.format("a3_hs_dr_color_bg%d" , color))
	self.node_list.quality_color_bg.raw_image:LoadSprite(asset_bundle, asset_name, function ()
		self.node_list.quality_color_bg.raw_image:SetNativeSize()
	end)

	if is_beast then
		asset_bundle, asset_name = ResPath.GetRawImagesPNG("a3_hs_bt_icon" .. beast_cfg.beast_type)
		self.node_list.beast_icon.raw_image:LoadSprite(asset_bundle, asset_name, function ()
			self.node_list.beast_icon.raw_image:SetNativeSize()
		end)
	
		asset_bundle, asset_name = ResPath.GetControlBeastsImg("a3_hs_ele_bg_".. beast_cfg.beast_color .."_".. beast_cfg.beast_element)
		self.node_list.beast_color_icon.image:LoadSprite(asset_bundle, asset_name, function ()
			self.node_list.beast_color_icon.image:SetNativeSize()
		end)
	
		asset_bundle, asset_name = ResPath.GetControlBeastsImg("a3_hs_bq_" .. beast_cfg.beast_element)
		self.node_list.beast_ele_icon.image:LoadSprite(asset_bundle, asset_name, function ()
			self.node_list.beast_ele_icon.image:SetNativeSize()
		end)

		asset_bundle, asset_name = ResPath.GetCommonImages(string.format("a3_hs_pz%d", color))
		self.node_list.active_jingshen_img.image:LoadSprite(asset_bundle, asset_name, function()
			self.node_list.active_jingshen_img.image:SetNativeSize()
		end)

		if BEAST_EFFECT_COLOR[beast_cfg.beast_color] then
			asset_bundle, asset_name = ResPath.GetUIEffect(BEAST_EFFECT_COLOR[color])
			self.node_list.active_jingshen_img:ChangeAsset(asset_bundle, asset_name)
		end

		str = beast_cfg.beast_name or ""
	else
		asset_bundle, asset_name = ResPath.GetRawImagesPNG("a3_hs_dr_bg" .. self.data.item_id)
		self.node_list.beast_icon.raw_image:LoadSprite(asset_bundle, asset_name, function ()
			self.node_list.beast_icon.raw_image:SetNativeSize()
		end)

		str = item_cfg.name or ""
	end

	self.node_list.beast_name.text.text = str

	self:DoShowTween()
	local base_cfg = ControlBeastsWGData.Instance:GetBaseCfg()
	local auto_despose_color = base_cfg and base_cfg.auto_despose_color or 1
	self.node_list.item_pos:CustomSetActive(false)

	if is_beast then
		if beast_cfg.beast_color <= auto_despose_color then
			local decompose_cfg = ControlBeastsWGData.Instance:GetBeastDesposeMessageCfgById(beast_id)
			local decompose_data = ((decompose_cfg or {}).break_exp or {})[0]
			if decompose_data then
				local item_cfg = ItemWGData.Instance:GetItemConfig(decompose_data.item_id)
	
				self.item:SetFlushCallBack(function ()
					local right_text = CommonDataManager.ConverMoneyByThousand(item_cfg.param1 * decompose_data.num)
					self.item:SetRightBottomColorText(right_text)
					self.item:SetRightBottomTextVisible(true)
				end)
				self.item:SetData({item_id = decompose_data.item_id})
	
				local bundle, asset = ResPath.GetItem(ItemWGData.Instance:GetItemIconByItemId(decompose_data.item_id))
				self.node_list.exp_icon.image:LoadSprite(bundle, asset, function()
					self.node_list.exp_icon.image:SetNativeSize()
				end)
	
				self.node_list.desc_exp_num.text.text = "x" .. CommonDataManager.ConverMoneyByThousand(item_cfg.param1 * decompose_data.num)
			end
			self.node_list.item_pos:CustomSetActive(nil ~= decompose_data)
	
			if self.delay_set_func then
				GlobalTimerQuest:CancelQuest(self.delay_set_func)
				self.delay_set_func = nil
			end

			self.delay_set_func = GlobalTimerQuest:AddDelayTimer(function ()
				if self.node_list and self.node_list.root_decompose then
					self.node_list.root_decompose:CustomSetActive(self.data.can_auto_decompose == 1 and is_beasts_item)
				end
			end, self.data.delay_show_time)

			-- ReDelayCall(self, function ()
			-- 	self.node_list.root_decompose:CustomSetActive(self.data.can_auto_decompose == 1 and is_beasts_item)
			-- end, self.data.delay_show_time, "OAControlBeastsDrawResuleListCellRender")
		end
	end
end

function OAControlBeastsDrawResuleListCellRender:DoShowTween()
	if self.show_sequence then
		self.show_sequence:Kill()
		self.show_sequence = nil
	end

	if self.data.is_skip_anim then
		self.node_list.root.rect.anchoredPosition = Vector2(0, 0)
		self.node_list.root.canvas_group.alpha = 1
	else
		self.node_list.root.rect.anchoredPosition = Vector2(200, 0)
		self.node_list.root.canvas_group.alpha = 0
		local cell_time = 0.2
		local delay_per_time = 0.1
		local delay_time = (self.index > 0 and self.index < 11) and ((self.index - 1) * delay_per_time) or 0
	
		local sequence = DG.Tweening.DOTween.Sequence()
		sequence:Join(self.node_list.root.rect:DOAnchorPosX(0, cell_time))
		sequence:Join(self.node_list.root.canvas_group:DoAlpha(0, 1, cell_time))
		sequence:SetDelay(delay_time)
		sequence:SetEase(DG.Tweening.Ease.OutQuad)
		sequence:OnComplete(function ()
		end)
		
		self.show_sequence = sequence
	end
end