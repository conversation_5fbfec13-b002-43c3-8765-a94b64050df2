-- Y-运营活动-战神令.xls
local item_table={
[1]={item_id=44053,num=1,is_bind=1},
[2]={item_id=44054,num=1,is_bind=1},
[3]={item_id=44055,num=1,is_bind=1},
[4]={item_id=27611,num=2,is_bind=1},
[5]={item_id=39147,num=200,is_bind=1},
[6]={item_id=0,num=1,is_bind=1},
[7]={item_id=44051,num=1,is_bind=1},
[8]={item_id=27611,num=6,is_bind=1},
[9]={item_id=39147,num=600,is_bind=1},
[10]={item_id=46501,num=2,is_bind=1},
[11]={item_id=46048,num=2,is_bind=1},
[12]={item_id=44049,num=1,is_bind=1},
[13]={item_id=50075,num=2,is_bind=1},
[14]={item_id=44052,num=1,is_bind=1},
}

return {
other={
{}
},

other_meta_table_map={
},
open_day={
{}
},

open_day_meta_table_map={
},
rmb_buy={
{is_normal=1,},
{seq=1,rmb_seq=101,rmb_price=30,reward_item=item_table[1],},
{seq=2,rmb_seq=102,rmb_price=50,level=5,reward_item=item_table[2],},
{seq=3,rmb_seq=103,rmb_price=100,level=10,reward_item=item_table[3],}
},

rmb_buy_meta_table_map={
},
level={
{reward_item_1=item_table[4],reward_item_3=item_table[4],reward_item_4=item_table[5],},
{level=1,reward_item_4=item_table[6],},
{reward_item_2=item_table[6],reward_item_4=item_table[6],},
{level=3,reward_item_2=item_table[6],},
{level=4,},
{level=5,reward_item_4=item_table[6],},
{level=6,},
{level=7,},
{level=8,},
{level=9,reward_item_2=item_table[6],},
{level=10,},
{level=11,reward_item_2=item_table[6],},
{level=12,reward_item_2=item_table[6],},
{level=13,},
{level=14,reward_item_1=item_table[7],reward_item_2=item_table[8],},
{level=15,reward_item_1=item_table[4],},
{reward_item_3=item_table[6],reward_item_4=item_table[8],},
{level=17,reward_item_3=item_table[6],},
{reward_item_3=item_table[6],reward_item_4=item_table[9],},
{level=19,reward_item_1=item_table[6],reward_item_2=item_table[8],},
{level=20,reward_item_1=item_table[6],},
{reward_item_1=item_table[6],reward_item_4=item_table[8],},
{level=22,},
{reward_item_1=item_table[6],},
{level=24,},
{level=25,},
{level=26,},
{level=27,},
{reward_item_4=item_table[8],},
{level=29,},
{level=30,reward_item_4=item_table[9],},
{level=31,},
{level=32,},
{level=33,},
{level=34,},
{level=35,},
{level=36,},
{level=37,},
{level=38,},
{level=39,},
{level=40,},
{level=41,},
{level=42,},
{level=43,},
{reward_item_4=item_table[8],},
{level=45,},
{reward_item_4=item_table[9],},
{level=47,},
{level=48,reward_item_4=item_table[8],},
{level=49,reward_item_4=item_table[9],},
{level=50,},
{level=51,reward_item_4=item_table[8],},
{level=52,},
{level=53,},
{reward_item_2=item_table[8],reward_item_4=item_table[7],},
{level=55,},
{level=56,},
{level=57,},
{level=58,},
{level=59,reward_item_1=item_table[10],reward_item_3=item_table[10],reward_item_4=item_table[10],},
{level=60,},
{level=61,},
{level=62,},
{level=63,},
{reward_item_4=item_table[7],},
{level=65,},
{level=66,},
{level=67,},
{reward_item_2=item_table[8],},
{level=69,},
{level=70,},
{level=71,reward_item_1=item_table[4],reward_item_3=item_table[4],},
{reward_item_4=item_table[9],},
{level=73,},
{level=74,},
{level=75,},
{level=76,},
{level=77,},
{level=78,},
{level=79,reward_item_4=item_table[5],},
{level=80,},
{level=81,},
{level=82,},
{level=83,},
{level=84,},
{level=85,},
{level=86,},
{level=87,},
{level=88,},
{level=89,reward_item_1=item_table[11],reward_item_3=item_table[11],reward_item_4=item_table[5],},
{reward_item_2=item_table[8],reward_item_4=item_table[10],},
{level=1,},
{level=2,reward_item_1=item_table[12],reward_item_3=item_table[12],},
{level=3,},
{level=4,reward_item_4=item_table[5],},
{level=5,},
{level=6,},
{level=7,},
{level=8,},
{level=9,},
{level=10,},
{level=11,},
{level=12,},
{level=13,},
{level=14,},
{level=15,},
{level=16,},
{level=17,},
{level=18,reward_item_4=item_table[5],},
{level=19,},
{level=20,},
{level=21,},
{level=22,},
{level=23,},
{level=24,reward_item_1=item_table[7],reward_item_3=item_table[7],reward_item_4=item_table[5],},
{level=25,},
{level=26,},
{level=27,},
{level=28,},
{level=29,},
{level=30,},
{level=31,},
{level=32,},
{level=33,},
{level=34,},
{level=35,},
{level=36,},
{level=37,},
{level=38,},
{level=39,reward_item_2=item_table[8],},
{level=40,},
{level=41,},
{level=42,reward_item_2=item_table[8],},
{reward_item_2=item_table[8],},
{level=44,},
{level=45,reward_item_4=item_table[7],},
{level=46,},
{level=47,},
{level=48,},
{level=49,reward_item_1=item_table[13],reward_item_3=item_table[13],reward_item_4=item_table[5],},
{level=50,},
{level=51,reward_item_2=item_table[8],},
{level=52,},
{level=53,},
{level=54,},
{level=55,},
{level=56,},
{level=57,},
{level=58,},
{level=59,},
{level=60,reward_item_4=item_table[10],},
{level=61,},
{level=62,},
{level=63,},
{level=64,},
{level=65,},
{level=66,},
{level=67,},
{level=68,},
{level=69,},
{level=70,},
{level=71,reward_item_4=item_table[7],},
{level=72,},
{level=73,},
{level=74,},
{level=75,reward_item_4=item_table[5],},
{level=76,},
{},
{level=78,},
{level=79,},
{level=80,},
{level=81,},
{level=82,},
{level=83,},
{level=84,},
{level=85,},
{level=86,},
{},
{reward_item_4=item_table[9],},
{}
},

level_meta_table_map={
[168]=78,	-- depth:1
[178]=88,	-- depth:1
[73]=163,	-- depth:1
[59]=109,	-- depth:1
[139]=59,	-- depth:2
[64]=139,	-- depth:3
[58]=64,	-- depth:4
[63]=73,	-- depth:2
[54]=58,	-- depth:5
[74]=63,	-- depth:3
[83]=133,	-- depth:1
[79]=54,	-- depth:6
[124]=74,	-- depth:4
[84]=124,	-- depth:5
[119]=79,	-- depth:7
[89]=119,	-- depth:8
[179]=89,	-- depth:9
[92]=83,	-- depth:2
[94]=179,	-- depth:10
[114]=94,	-- depth:11
[99]=89,	-- depth:9
[104]=114,	-- depth:12
[129]=99,	-- depth:10
[144]=104,	-- depth:13
[53]=144,	-- depth:14
[29]=119,	-- depth:8
[43]=53,	-- depth:15
[154]=43,	-- depth:16
[174]=129,	-- depth:11
[44]=154,	-- depth:17
[33]=44,	-- depth:18
[28]=33,	-- depth:19
[148]=92,	-- depth:3
[149]=174,	-- depth:12
[39]=29,	-- depth:9
[38]=28,	-- depth:20
[34]=38,	-- depth:21
[159]=149,	-- depth:13
[24]=114,	-- depth:12
[23]=24,	-- depth:13
[13]=73,	-- depth:2
[18]=29,	-- depth:9
[14]=13,	-- depth:3
[9]=14,	-- depth:4
[8]=9,	-- depth:5
[4]=2,	-- depth:1
[19]=109,	-- depth:1
[71]=72,	-- depth:1
[69]=159,	-- depth:14
[68]=69,	-- depth:15
[132]=71,	-- depth:2
[67]=132,	-- depth:3
[134]=44,	-- depth:18
[48]=134,	-- depth:19
[57]=67,	-- depth:4
[49]=133,	-- depth:1
[137]=57,	-- depth:5
[142]=72,	-- depth:1
[117]=142,	-- depth:2
[175]=115,	-- depth:1
[177]=117,	-- depth:3
[112]=177,	-- depth:4
[110]=140,	-- depth:1
[145]=175,	-- depth:2
[147]=112,	-- depth:5
[172]=147,	-- depth:6
[150]=110,	-- depth:2
[165]=145,	-- depth:3
[151]=1,	-- depth:1
[120]=150,	-- depth:3
[122]=172,	-- depth:7
[160]=120,	-- depth:4
[136]=1,	-- depth:1
[135]=165,	-- depth:4
[125]=135,	-- depth:5
[152]=122,	-- depth:8
[157]=152,	-- depth:9
[127]=157,	-- depth:10
[155]=125,	-- depth:6
[170]=160,	-- depth:5
[146]=136,	-- depth:2
[47]=137,	-- depth:6
[66]=127,	-- depth:11
[65]=155,	-- depth:7
[62]=66,	-- depth:12
[61]=62,	-- depth:13
[56]=61,	-- depth:14
[52]=1,	-- depth:1
[51]=47,	-- depth:7
[50]=60,	-- depth:1
[107]=56,	-- depth:15
[45]=135,	-- depth:5
[40]=50,	-- depth:2
[35]=45,	-- depth:6
[30]=40,	-- depth:3
[25]=35,	-- depth:7
[21]=47,	-- depth:7
[16]=19,	-- depth:2
[70]=60,	-- depth:1
[75]=155,	-- depth:7
[180]=90,	-- depth:1
[77]=107,	-- depth:16
[102]=77,	-- depth:17
[100]=170,	-- depth:6
[97]=102,	-- depth:18
[105]=75,	-- depth:8
[91]=1,	-- depth:1
[87]=97,	-- depth:19
[86]=146,	-- depth:3
[95]=93,	-- depth:1
[85]=105,	-- depth:9
[81]=151,	-- depth:2
[80]=60,	-- depth:1
[82]=87,	-- depth:20
[10]=50,	-- depth:2
[162]=91,	-- depth:2
[3]=93,	-- depth:1
[20]=50,	-- depth:2
[17]=107,	-- depth:16
[5]=3,	-- depth:2
[166]=91,	-- depth:2
[167]=91,	-- depth:2
[176]=167,	-- depth:3
[15]=19,	-- depth:2
[12]=52,	-- depth:2
[171]=162,	-- depth:3
[11]=12,	-- depth:3
[161]=166,	-- depth:3
[7]=11,	-- depth:4
[6]=12,	-- depth:3
[22]=112,	-- depth:5
[31]=91,	-- depth:2
[26]=31,	-- depth:3
[130]=140,	-- depth:1
[131]=176,	-- depth:4
[126]=171,	-- depth:4
[121]=131,	-- depth:5
[55]=145,	-- depth:3
[141]=121,	-- depth:6
[116]=126,	-- depth:5
[46]=26,	-- depth:4
[111]=141,	-- depth:7
[96]=116,	-- depth:6
[41]=46,	-- depth:5
[37]=41,	-- depth:6
[36]=37,	-- depth:7
[32]=36,	-- depth:8
[76]=96,	-- depth:7
[101]=111,	-- depth:8
[156]=76,	-- depth:8
[27]=32,	-- depth:9
[42]=27,	-- depth:10
[106]=156,	-- depth:9
},
task_list={
{task_type=2,param1=1,},
{task_id=1,task_type=1,},
{task_id=2,task_type=4,},
{task_id=3,task_type=5,param1=9,},
{task_id=4,task_type=7,},
{task_id=5,param1=2,},
{task_id=6,task_type=6,is_week_task=1,},
{task_id=7,task_type=3,},
{task_id=8,task_type=9,param1=2,is_week_task=1,},
{task_id=9,param1=5,is_week_task=1,},
{task_id=10,}
},

task_list_meta_table_map={
[2]=1,	-- depth:1
[8]=7,	-- depth:1
[11]=10,	-- depth:1
},
other_default_table={flash_task_cost=50,refresh_daily_task_times=3,fix_up_level_exp=100,exp_item=46047,daily_list_num=5,},

open_day_default_table={start_day=1,end_day=7,grade=1,},

rmb_buy_default_table={grade=1,seq=0,rmb_type=123,rmb_seq=100,rmb_price=0,level=0,is_normal=0,reward_item=item_table[14],},

level_default_table={grade=1,level=0,up_level_exp=100,reward_item_1=item_table[5],reward_item_2=item_table[9],reward_item_3=item_table[5],reward_item_4=item_table[4],},

task_list_default_table={grade=1,task_id=0,task_type=8,param1=3,param2=0,param3=0,task_weight=10000,task_exp=25,is_week_task=0,}

}

