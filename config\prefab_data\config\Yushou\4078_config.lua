return {
	actorController = {
		projectiles = {},

		hurts = {},

	},
	actorTriggers = {
		effects = {
			{
				triggerEventName = "attack1/begin",
				triggerDelay = 0.0,
				triggerFreeDelay = 0.0,
				effectGoName = "boss8031_attcak",
				effectAsset = {
					BundleName = "effects/prefab/model/boss/8031/boss8031_attcak_prefab",
					AssetName = "boss8031_attcak",
				},
				playerAtTarget = false,
				referenceNodeHierarchyPath = "root",
				isAttach = true,
				isRotation = false,
				isUseCustomTransform = true,
				offsetPosX = 0.0,
				offsetPosY = -1.0,
				offsetPosZ = 0.0,
				triggerStopEvent = "",
				effectBtnName = "attack",
				playerAtPos = false,
				ignoreParentScale = false,
			},
			{
				triggerEventName = "attack2/begin",
				triggerDelay = 0.0,
				triggerFreeDelay = 0.0,
				effectGoName = "boss8031_skill11",
				effectAsset = {
					BundleName = "effects/prefab/model/boss/8031/boss8031_skill11_prefab",
					AssetName = "boss8031_skill11",
				},
				playerAtTarget = false,
				referenceNodeHierarchyPath = "root",
				isAttach = true,
				isRotation = false,
				isUseCustomTransform = false,
				offsetPosX = 0.0,
				offsetPosY = 0.0,
				offsetPosZ = 0.0,
				triggerStopEvent = "",
				effectBtnName = "skill",
				playerAtPos = false,
				ignoreParentScale = true,
			},
			{
				triggerEventName = "rest/begin",
				triggerDelay = 0.5,
				triggerFreeDelay = 0.0,
				effectGoName = "boss8031_rest",
				effectAsset = {
					BundleName = "effects/prefab/model/boss/8031/boss8031_rest_prefab",
					AssetName = "boss8031_rest",
				},
				playerAtTarget = false,
				referenceNodeHierarchyPath = "root",
				isAttach = true,
				isRotation = false,
				isUseCustomTransform = true,
				offsetPosX = 0.0,
				offsetPosY = 0.0,
				offsetPosZ = -6.0,
				triggerStopEvent = "",
				effectBtnName = "rest",
				playerAtPos = false,
				ignoreParentScale = true,
			},
		},
		sounds = {
			{
				soundEventName = "attack2/begin",
				soundDelay = 0.5,
				soundAudioAsset = {
					BundleName = "audios/sfxs/skill/4078",
					AssetName = "4078_skill",
				},
				soundAudioGoName = "4078_skill",
				soundBtnName = "skill",
				soundIsMainRole = false,
			},
		},
		cameraShakes = {},

		radialBlurs = {},

	},
}