{"actorController": {"projectiles": [], "hurts": []}, "actorTriggers": {"effects": [{"triggerEventName": "attack1/begin", "triggerDelay": 0.0, "triggerFreeDelay": 0.0, "effectGoName": "", "effectAsset": {"BundleName": "", "AssetName": "", "AssetGUID": "", "IsEmpty": true}, "playerAtTarget": false, "referenceNodeHierarchyPath": "", "isAttach": false, "isRotation": true, "isUseCustomTransform": false, "offsetPosX": 0.0, "offsetPosY": 0.0, "offsetPosZ": 0.0, "triggerStopEvent": "[none]", "effectBtnName": "attack1", "playerAtPos": false, "ignoreParentScale": false, "effectArray": [{"effectGoName": "3102_attack1", "effectAsset": {"BundleName": "effects/prefab/role/3102/3102_attack1_prefab", "AssetName": "3102_attack1", "AssetGUID": null, "IsEmpty": false}}, {"effectGoName": "3102_attack1_hong", "effectAsset": {"BundleName": "effects/prefab/role/3102/3102_attack1_hong_prefab", "AssetName": "3102_attack1_hong", "AssetGUID": "96169b7be95087a42895cd2335b77a85", "IsEmpty": false}}]}, {"triggerEventName": "attack3/begin", "triggerDelay": 0.0, "triggerFreeDelay": 0.0, "effectGoName": "", "effectAsset": {"BundleName": "", "AssetName": "", "AssetGUID": "", "IsEmpty": true}, "playerAtTarget": false, "referenceNodeHierarchyPath": "", "isAttach": false, "isRotation": true, "isUseCustomTransform": false, "offsetPosX": 0.0, "offsetPosY": 0.0, "offsetPosZ": 0.0, "triggerStopEvent": "[none]", "effectBtnName": "attack3", "playerAtPos": false, "ignoreParentScale": false, "effectArray": [{"effectGoName": "3102_attack3", "effectAsset": {"BundleName": "effects/prefab/role/3102/3102_attack3_prefab", "AssetName": "3102_attack3", "AssetGUID": null, "IsEmpty": false}}, {"effectGoName": "3102_attack3_hong", "effectAsset": {"BundleName": "effects/prefab/role/3102/3102_attack3_hong_prefab", "AssetName": "3102_attack3_hong", "AssetGUID": "615dc4a24da1acf4bb512b1b23eebfc9", "IsEmpty": false}}]}, {"triggerEventName": "attack4/begin", "triggerDelay": 0.0, "triggerFreeDelay": 0.0, "effectGoName": "", "effectAsset": {"BundleName": "", "AssetName": "", "AssetGUID": "", "IsEmpty": true}, "playerAtTarget": false, "referenceNodeHierarchyPath": "", "isAttach": false, "isRotation": true, "isUseCustomTransform": false, "offsetPosX": 0.0, "offsetPosY": 0.0, "offsetPosZ": 0.0, "triggerStopEvent": "[none]", "effectBtnName": "attack4", "playerAtPos": false, "ignoreParentScale": false, "effectArray": [{"effectGoName": "3102_attack4", "effectAsset": {"BundleName": "effects/prefab/role/3102/3102_attack4_prefab", "AssetName": "3102_attack4", "AssetGUID": null, "IsEmpty": false}}, {"effectGoName": "3102_attack4_hong", "effectAsset": {"BundleName": "effects/prefab/role/3102/3102_attack4_hong_prefab", "AssetName": "3102_attack4_hong", "AssetGUID": "2f49804e1c87c36438fa9b479a3cd6c5", "IsEmpty": false}}]}, {"triggerEventName": "combo1_1/begin", "triggerDelay": 0.0, "triggerFreeDelay": 0.0, "effectGoName": "", "effectAsset": {"BundleName": "", "AssetName": "", "AssetGUID": "", "IsEmpty": true}, "playerAtTarget": false, "referenceNodeHierarchyPath": "", "isAttach": false, "isRotation": true, "isUseCustomTransform": false, "offsetPosX": 0.0, "offsetPosY": 0.0, "offsetPosZ": 0.0, "triggerStopEvent": "[none]", "effectBtnName": "combo1_1", "playerAtPos": false, "ignoreParentScale": false, "effectArray": [{"effectGoName": "3102_combo1", "effectAsset": {"BundleName": "effects/prefab/role/3102/3102_combo1_prefab", "AssetName": "3102_combo1", "AssetGUID": null, "IsEmpty": false}}, {"effectGoName": "3102_combo1_hong", "effectAsset": {"BundleName": "effects/prefab/role/3102/3102_combo1_hong_prefab", "AssetName": "3102_combo1_hong", "AssetGUID": "49c824ad179f9714995df390de9a3195", "IsEmpty": false}}]}, {"triggerEventName": "combo1_2/begin", "triggerDelay": 0.0, "triggerFreeDelay": 0.0, "effectGoName": "", "effectAsset": {"BundleName": "", "AssetName": "", "AssetGUID": "", "IsEmpty": true}, "playerAtTarget": false, "referenceNodeHierarchyPath": "", "isAttach": false, "isRotation": true, "isUseCustomTransform": false, "offsetPosX": 0.0, "offsetPosY": 0.0, "offsetPosZ": 0.0, "triggerStopEvent": "[none]", "effectBtnName": "combo1_2", "playerAtPos": false, "ignoreParentScale": false, "effectArray": [{"effectGoName": "3102_combo2", "effectAsset": {"BundleName": "effects/prefab/role/3102/3102_combo2_prefab", "AssetName": "3102_combo2", "AssetGUID": null, "IsEmpty": false}}, {"effectGoName": "3102_combo2_hong", "effectAsset": {"BundleName": "effects/prefab/role/3102/3102_combo2_hong_prefab", "AssetName": "3102_combo2_hong", "AssetGUID": "04ebc27db3b7957449f31c1e2d16f30b", "IsEmpty": false}}]}, {"triggerEventName": "combo1_3/begin", "triggerDelay": 0.0, "triggerFreeDelay": 0.0, "effectGoName": "", "effectAsset": {"BundleName": "", "AssetName": "", "AssetGUID": "", "IsEmpty": true}, "playerAtTarget": false, "referenceNodeHierarchyPath": "", "isAttach": false, "isRotation": true, "isUseCustomTransform": false, "offsetPosX": 0.0, "offsetPosY": 0.0, "offsetPosZ": 0.0, "triggerStopEvent": "[none]", "effectBtnName": "combo1_3", "playerAtPos": false, "ignoreParentScale": false, "effectArray": [{"effectGoName": "3102_combo3", "effectAsset": {"BundleName": "effects/prefab/role/3102/3102_combo3_prefab", "AssetName": "3102_combo3", "AssetGUID": null, "IsEmpty": false}}, {"effectGoName": "3102_combo3_hong", "effectAsset": {"BundleName": "effects/prefab/role/3102/3102_combo3_hong_prefab", "AssetName": "3102_combo3_hong", "AssetGUID": "f7cf1aff3461f9345a90bbb95f2d8dc3", "IsEmpty": false}}]}, {"triggerEventName": "combo1_4/begin", "triggerDelay": 0.0, "triggerFreeDelay": 0.0, "effectGoName": "", "effectAsset": {"BundleName": "", "AssetName": "", "AssetGUID": "", "IsEmpty": true}, "playerAtTarget": false, "referenceNodeHierarchyPath": "", "isAttach": false, "isRotation": true, "isUseCustomTransform": false, "offsetPosX": 0.0, "offsetPosY": 0.0, "offsetPosZ": 0.0, "triggerStopEvent": "[none]", "effectBtnName": "combo1_4", "playerAtPos": false, "ignoreParentScale": false, "effectArray": [{"effectGoName": "3102_combo4", "effectAsset": {"BundleName": "effects/prefab/role/3102/3102_combo4_prefab", "AssetName": "3102_combo4", "AssetGUID": null, "IsEmpty": false}}, {"effectGoName": "3102_combo4_hong", "effectAsset": {"BundleName": "effects/prefab/role/3102/3102_combo4_hong_prefab", "AssetName": "3102_combo4_hong", "AssetGUID": "529329a35fdf1954c97514282f1b209c", "IsEmpty": false}}]}, {"triggerEventName": "attack2/begin", "triggerDelay": 0.0, "triggerFreeDelay": 0.0, "effectGoName": "", "effectAsset": {"BundleName": "", "AssetName": "", "AssetGUID": "", "IsEmpty": true}, "playerAtTarget": false, "referenceNodeHierarchyPath": "", "isAttach": false, "isRotation": true, "isUseCustomTransform": false, "offsetPosX": 0.0, "offsetPosY": 0.0, "offsetPosZ": 0.0, "triggerStopEvent": "[none]", "effectBtnName": "attack2", "playerAtPos": false, "ignoreParentScale": false, "effectArray": [{"effectGoName": "3102_attack2", "effectAsset": {"BundleName": "effects/prefab/role/3102/3102_attack2_prefab", "AssetName": "3102_attack2", "AssetGUID": null, "IsEmpty": false}}, {"effectGoName": "3102_attack2_hong", "effectAsset": {"BundleName": "effects/prefab/role/3102/3102_attack2_hong_prefab", "AssetName": "3102_attack2_hong", "AssetGUID": "ba9c368427118c44697de6be4f5fc6fe", "IsEmpty": false}}]}, {"triggerEventName": "esoterica_attack_1/begin", "triggerDelay": 0.0, "triggerFreeDelay": 0.0, "effectGoName": "", "effectAsset": {"BundleName": "", "AssetName": "", "AssetGUID": "", "IsEmpty": true}, "playerAtTarget": false, "referenceNodeHierarchyPath": "", "isAttach": false, "isRotation": true, "isUseCustomTransform": false, "offsetPosX": 0.0, "offsetPosY": 0.0, "offsetPosZ": 0.0, "triggerStopEvent": "", "effectBtnName": "esoterica_attack_1", "playerAtPos": false, "ignoreParentScale": false, "effectArray": [{"effectGoName": "miji_attack01", "effectAsset": {"BundleName": "effects/prefab/model/miji/miji_attack01_prefab", "AssetName": "miji_attack01", "AssetGUID": null, "IsEmpty": false}}, {"effectGoName": "", "effectAsset": {"BundleName": "", "AssetName": "", "AssetGUID": "", "IsEmpty": true}}]}, {"triggerEventName": "esoterica_attack_2/begin", "triggerDelay": 0.0, "triggerFreeDelay": 0.0, "effectGoName": "", "effectAsset": {"BundleName": "", "AssetName": "", "AssetGUID": "", "IsEmpty": true}, "playerAtTarget": false, "referenceNodeHierarchyPath": "", "isAttach": false, "isRotation": true, "isUseCustomTransform": false, "offsetPosX": 0.0, "offsetPosY": 0.0, "offsetPosZ": 0.0, "triggerStopEvent": "[none]", "effectBtnName": "esoterica_attack_2", "playerAtPos": false, "ignoreParentScale": false, "effectArray": [{"effectGoName": "xianfa_attack9", "effectAsset": {"BundleName": "effects/prefab/model/xianfa/xianfa_attack9_prefab", "AssetName": "xianfa_attack9", "AssetGUID": "ed21f6540190afa42a0dc2557588f18a", "IsEmpty": false}}, {"effectGoName": "", "effectAsset": {"BundleName": "", "AssetName": "", "AssetGUID": "", "IsEmpty": true}}]}, {"triggerEventName": "esoterica_attack_3/begin", "triggerDelay": 0.0, "triggerFreeDelay": 0.0, "effectGoName": "", "effectAsset": {"BundleName": "", "AssetName": "", "AssetGUID": "", "IsEmpty": true}, "playerAtTarget": false, "referenceNodeHierarchyPath": "", "isAttach": false, "isRotation": true, "isUseCustomTransform": false, "offsetPosX": 0.0, "offsetPosY": 0.0, "offsetPosZ": 0.0, "triggerStopEvent": "[none]", "effectBtnName": "esoterica_attack_3", "playerAtPos": false, "ignoreParentScale": false, "effectArray": [{"effectGoName": "xianfa_attack3", "effectAsset": {"BundleName": "effects/prefab/model/xianfa/xianfa_attack3_prefab", "AssetName": "xianfa_attack3", "AssetGUID": "52a62e7527880c540a72b41e5898ce87", "IsEmpty": false}}, {"effectGoName": "", "effectAsset": {"BundleName": "", "AssetName": "", "AssetGUID": "", "IsEmpty": true}}]}, {"triggerEventName": "esoterica_attack_4/begin", "triggerDelay": 0.0, "triggerFreeDelay": 0.0, "effectGoName": "", "effectAsset": {"BundleName": "", "AssetName": "", "AssetGUID": "", "IsEmpty": true}, "playerAtTarget": false, "referenceNodeHierarchyPath": "", "isAttach": false, "isRotation": true, "isUseCustomTransform": false, "offsetPosX": 0.0, "offsetPosY": 0.0, "offsetPosZ": 0.0, "triggerStopEvent": "[none]", "effectBtnName": "esoterica_attack_4", "playerAtPos": false, "ignoreParentScale": false, "effectArray": [{"effectGoName": "xianfa_attack1", "effectAsset": {"BundleName": "effects/prefab/model/xianfa/xianfa_attack1_prefab", "AssetName": "xianfa_attack1", "AssetGUID": "23194b7835085324487da9a6e339a81d", "IsEmpty": false}}, {"effectGoName": "", "effectAsset": {"BundleName": "", "AssetName": "", "AssetGUID": "", "IsEmpty": true}}]}, {"triggerEventName": "esoterica_attack_5/begin", "triggerDelay": 0.0, "triggerFreeDelay": 0.0, "effectGoName": "", "effectAsset": {"BundleName": "", "AssetName": "", "AssetGUID": "", "IsEmpty": true}, "playerAtTarget": false, "referenceNodeHierarchyPath": "", "isAttach": false, "isRotation": true, "isUseCustomTransform": false, "offsetPosX": 0.0, "offsetPosY": 0.0, "offsetPosZ": 0.0, "triggerStopEvent": "[none]", "effectBtnName": "esoterica_attack_5", "playerAtPos": false, "ignoreParentScale": false, "effectArray": [{"effectGoName": "xianfa_attack6", "effectAsset": {"BundleName": "effects/prefab/model/xianfa/xianfa_attack6_prefab", "AssetName": "xianfa_attack6", "AssetGUID": "34314d3151a39c14eae2d8e0f5b285b8", "IsEmpty": false}}, {"effectGoName": "", "effectAsset": {"BundleName": "", "AssetName": "", "AssetGUID": "", "IsEmpty": true}}]}, {"triggerEventName": "esoterica_attack_6/begin", "triggerDelay": 0.0, "triggerFreeDelay": 0.0, "effectGoName": "", "effectAsset": {"BundleName": "", "AssetName": "", "AssetGUID": "", "IsEmpty": true}, "playerAtTarget": false, "referenceNodeHierarchyPath": "", "isAttach": false, "isRotation": true, "isUseCustomTransform": false, "offsetPosX": 0.0, "offsetPosY": 0.0, "offsetPosZ": 0.0, "triggerStopEvent": "[none]", "effectBtnName": "esoterica_attack_6", "playerAtPos": false, "ignoreParentScale": false, "effectArray": [{"effectGoName": "xianfa_attack4", "effectAsset": {"BundleName": "effects/prefab/model/xianfa/xianfa_attack4_prefab", "AssetName": "xianfa_attack4", "AssetGUID": "7da6118c654021d48be6517dcf57c258", "IsEmpty": false}}, {"effectGoName": "", "effectAsset": {"BundleName": "", "AssetName": "", "AssetGUID": "", "IsEmpty": true}}]}, {"triggerEventName": "esoterica_attack_7/begin", "triggerDelay": 0.0, "triggerFreeDelay": 0.0, "effectGoName": "", "effectAsset": {"BundleName": "", "AssetName": "", "AssetGUID": "", "IsEmpty": true}, "playerAtTarget": false, "referenceNodeHierarchyPath": "", "isAttach": false, "isRotation": true, "isUseCustomTransform": false, "offsetPosX": 0.0, "offsetPosY": 0.0, "offsetPosZ": 0.0, "triggerStopEvent": "[none]", "effectBtnName": "esoterica_attack_7", "playerAtPos": false, "ignoreParentScale": false, "effectArray": [{"effectGoName": "xianfa_attack8", "effectAsset": {"BundleName": "effects/prefab/model/xianfa/xianfa_attack8_prefab", "AssetName": "xianfa_attack8", "AssetGUID": "0dbff1b2487193c4c8a84818c05d6752", "IsEmpty": false}}, {"effectGoName": "", "effectAsset": {"BundleName": "", "AssetName": "", "AssetGUID": "", "IsEmpty": true}}]}, {"triggerEventName": "attack5/begin", "triggerDelay": 0.0, "triggerFreeDelay": 0.0, "effectGoName": "", "effectAsset": {"BundleName": "", "AssetName": "", "AssetGUID": "", "IsEmpty": true}, "playerAtTarget": false, "referenceNodeHierarchyPath": "", "isAttach": false, "isRotation": true, "isUseCustomTransform": false, "offsetPosX": 0.0, "offsetPosY": 0.0, "offsetPosZ": 0.0, "triggerStopEvent": "", "effectBtnName": "attack5", "playerAtPos": false, "ignoreParentScale": false, "effectArray": [{"effectGoName": "dingzhi_attack01", "effectAsset": {"BundleName": "effects/prefab/model/dingzhi/dingzhi_attack01_prefab", "AssetName": "dingzhi_attack01", "AssetGUID": null, "IsEmpty": false}}, {"effectGoName": "", "effectAsset": {"BundleName": "", "AssetName": "", "AssetGUID": "", "IsEmpty": true}}]}, {"triggerEventName": "attack6/begin", "triggerDelay": 0.0, "triggerFreeDelay": 0.0, "effectGoName": "", "effectAsset": {"BundleName": "", "AssetName": "", "AssetGUID": "", "IsEmpty": true}, "playerAtTarget": false, "referenceNodeHierarchyPath": "", "isAttach": false, "isRotation": true, "isUseCustomTransform": false, "offsetPosX": 0.0, "offsetPosY": 0.0, "offsetPosZ": 0.0, "triggerStopEvent": "", "effectBtnName": "attack6", "playerAtPos": false, "ignoreParentScale": false, "effectArray": [{"effectGoName": "dingzhi_attack02", "effectAsset": {"BundleName": "effects/prefab/model/dingzhi/dingzhi_attack02_prefab", "AssetName": "dingzhi_attack02", "AssetGUID": null, "IsEmpty": false}}, {"effectGoName": "", "effectAsset": {"BundleName": "", "AssetName": "", "AssetGUID": "", "IsEmpty": true}}]}, {"triggerEventName": "attack7/begin", "triggerDelay": 0.0, "triggerFreeDelay": 0.0, "effectGoName": "", "effectAsset": {"BundleName": "", "AssetName": "", "AssetGUID": "", "IsEmpty": true}, "playerAtTarget": false, "referenceNodeHierarchyPath": "", "isAttach": false, "isRotation": true, "isUseCustomTransform": false, "offsetPosX": 0.0, "offsetPosY": 0.0, "offsetPosZ": 0.0, "triggerStopEvent": "", "effectBtnName": "attack7", "playerAtPos": false, "ignoreParentScale": false, "effectArray": [{"effectGoName": "dingzhi_attack05", "effectAsset": {"BundleName": "effects/prefab/model/dingzhi/dingzhi_attack05_prefab", "AssetName": "dingzhi_attack05", "AssetGUID": null, "IsEmpty": false}}, {"effectGoName": "", "effectAsset": {"BundleName": "", "AssetName": "", "AssetGUID": "", "IsEmpty": true}}]}, {"triggerEventName": "fall_rest/begin", "triggerDelay": 0.0, "triggerFreeDelay": 0.0, "effectGoName": "", "effectAsset": {"BundleName": "", "AssetName": "", "AssetGUID": "", "IsEmpty": true}, "playerAtTarget": false, "referenceNodeHierarchyPath": "root", "isAttach": true, "isRotation": false, "isUseCustomTransform": false, "offsetPosX": 0.0, "offsetPosY": 0.0, "offsetPosZ": 0.0, "triggerStopEvent": "", "effectBtnName": "fall_rest", "playerAtPos": false, "ignoreParentScale": false, "effectArray": [{"effectGoName": "eff_chuchang_shandian", "effectAsset": {"BundleName": "effects/prefab/environment/common/eff_chuchang_shandian_prefab", "AssetName": "eff_chuchang_shandian", "AssetGUID": null, "IsEmpty": false}}, {"effectGoName": "", "effectAsset": {"BundleName": "", "AssetName": "", "AssetGUID": "", "IsEmpty": true}}]}, {"triggerEventName": "prizedraw/begin", "triggerDelay": 0.0, "triggerFreeDelay": 0.0, "effectGoName": "", "effectAsset": {"BundleName": "", "AssetName": "", "AssetGUID": "", "IsEmpty": true}, "playerAtTarget": false, "referenceNodeHierarchyPath": "root", "isAttach": true, "isRotation": false, "isUseCustomTransform": false, "offsetPosX": 0.0, "offsetPosY": 0.0, "offsetPosZ": 0.0, "triggerStopEvent": "[none]", "effectBtnName": "choujiang", "playerAtPos": false, "ignoreParentScale": true, "effectArray": [{"effectGoName": "choujiang_eff", "effectAsset": {"BundleName": "effects/prefab/environment/common/choujiang_eff_prefab", "AssetName": "choujiang_eff", "AssetGUID": "07e5ba189c5069e4e9558cb1d0bf3ef3", "IsEmpty": false}}, {"effectGoName": "", "effectAsset": {"BundleName": "", "AssetName": "", "AssetGUID": "", "IsEmpty": true}}]}, {"triggerEventName": "esoterica_attack_8/begin", "triggerDelay": 0.0, "triggerFreeDelay": 0.0, "effectGoName": "", "effectAsset": {"BundleName": "", "AssetName": "", "AssetGUID": "", "IsEmpty": true}, "playerAtTarget": false, "referenceNodeHierarchyPath": "", "isAttach": false, "isRotation": false, "isUseCustomTransform": true, "offsetPosX": 1.0, "offsetPosY": 0.0, "offsetPosZ": -6.0, "triggerStopEvent": "[none]", "effectBtnName": "esoterica_attack_8", "playerAtPos": false, "ignoreParentScale": false, "effectArray": [{"effectGoName": "miji_attack08", "effectAsset": {"BundleName": "effects/prefab/model/miji/miji_attack08_prefab", "AssetName": "miji_attack08", "AssetGUID": "afb7616833ab6f643bb4344338980bd7", "IsEmpty": false}}, {"effectGoName": "", "effectAsset": {"BundleName": "", "AssetName": "", "AssetGUID": "", "IsEmpty": true}}]}, {"triggerEventName": "esoterica_attack_9/begin", "triggerDelay": 0.0, "triggerFreeDelay": 0.0, "effectGoName": "", "effectAsset": {"BundleName": "", "AssetName": "", "AssetGUID": "", "IsEmpty": true}, "playerAtTarget": false, "referenceNodeHierarchyPath": "", "isAttach": false, "isRotation": true, "isUseCustomTransform": false, "offsetPosX": 0.0, "offsetPosY": 0.0, "offsetPosZ": 0.0, "triggerStopEvent": "[none]", "effectBtnName": "esoterica_attack_9", "playerAtPos": false, "ignoreParentScale": false, "effectArray": [{"effectGoName": "xianfa_attack10", "effectAsset": {"BundleName": "effects/prefab/model/xianfa/xianfa_attack10_prefab", "AssetName": "xianfa_attack10", "AssetGUID": "1f207c1e703af0c4cb4667d65040cfbd", "IsEmpty": false}}, {"effectGoName": "", "effectAsset": {"BundleName": "", "AssetName": "", "AssetGUID": "", "IsEmpty": true}}]}, {"triggerEventName": "esoterica_attack_10/begin", "triggerDelay": 0.0, "triggerFreeDelay": 0.0, "effectGoName": "", "effectAsset": {"BundleName": "", "AssetName": "", "AssetGUID": "", "IsEmpty": true}, "playerAtTarget": false, "referenceNodeHierarchyPath": "", "isAttach": false, "isRotation": true, "isUseCustomTransform": false, "offsetPosX": 0.0, "offsetPosY": 0.0, "offsetPosZ": 0.0, "triggerStopEvent": "[none]", "effectBtnName": "esoterica_attack_10", "playerAtPos": false, "ignoreParentScale": false, "effectArray": [{"effectGoName": "xianfa_attack2", "effectAsset": {"BundleName": "effects/prefab/model/xianfa/xianfa_attack2_prefab", "AssetName": "xianfa_attack2", "AssetGUID": "db15a3bdaf4531040a53689e2aca1d1a", "IsEmpty": false}}, {"effectGoName": "", "effectAsset": {"BundleName": "", "AssetName": "", "AssetGUID": "", "IsEmpty": true}}]}], "sounds": [{"soundEventName": "combo1_1/begin", "soundDelay": 0.0, "soundAudioAsset": {"BundleName": "audios/sfxs/roleskill/role1", "AssetName": "role1_attack1", "AssetGUID": "7122a6b1658e86b478eb6319ebe1e84e", "IsEmpty": false}, "soundAudioGoName": "role1_attack1", "soundBtnName": "combo2_1", "soundIsMainRole": false}, {"soundEventName": "combo1_2/begin", "soundDelay": 0.0, "soundAudioAsset": {"BundleName": "audios/sfxs/roleskill/role1", "AssetName": "role1_attack2", "AssetGUID": "7ae7b4632c6701b4da4cb1c392b4b04c", "IsEmpty": false}, "soundAudioGoName": "role1_attack2", "soundBtnName": "combo2_2", "soundIsMainRole": false}, {"soundEventName": "combo1_3/begin", "soundDelay": 0.0, "soundAudioAsset": {"BundleName": "audios/sfxs/roleskill/role1", "AssetName": "role1_attack3", "AssetGUID": "cdcb000d31fe62f4aa7a88fe1a59d691", "IsEmpty": false}, "soundAudioGoName": "role1_attack3", "soundBtnName": "combo2_3", "soundIsMainRole": false}, {"soundEventName": "combo1_4/begin", "soundDelay": 0.0, "soundAudioAsset": {"BundleName": "audios/sfxs/roleskill/role1", "AssetName": "role1_attack4", "AssetGUID": "09e44c9d39a0e1242b60d35b5cfb6c8c", "IsEmpty": false}, "soundAudioGoName": "role1_attack4", "soundBtnName": "combo2_4", "soundIsMainRole": false}, {"soundEventName": "attack1/begin", "soundDelay": 0.0, "soundAudioAsset": {"BundleName": "audios/sfxs/roleskill/role1", "AssetName": "role1_skill1", "AssetGUID": "1f154bb38dac8894fbc15480decd423b", "IsEmpty": false}, "soundAudioGoName": "role1_skill1", "soundBtnName": "attack1", "soundIsMainRole": false}, {"soundEventName": "attack2/begin", "soundDelay": 0.0, "soundAudioAsset": {"BundleName": "audios/sfxs/roleskill/role1", "AssetName": "role1_skill2", "AssetGUID": "515f402adf2c0d24785f00838053180e", "IsEmpty": false}, "soundAudioGoName": "role1_skill2", "soundBtnName": "attack2", "soundIsMainRole": false}, {"soundEventName": "attack3/begin", "soundDelay": 0.0, "soundAudioAsset": {"BundleName": "audios/sfxs/roleskill/role1", "AssetName": "role1_skill3", "AssetGUID": "cdf33c7b0e0fed64c919a0d79b5fea39", "IsEmpty": false}, "soundAudioGoName": "role1_skill3", "soundBtnName": "attack3", "soundIsMainRole": false}, {"soundEventName": "attack4/begin", "soundDelay": 0.0, "soundAudioAsset": {"BundleName": "audios/sfxs/roleskill/role1", "AssetName": "role1_skill4", "AssetGUID": "e3a522f85c3249e4eb39d97d506a31f3", "IsEmpty": false}, "soundAudioGoName": "role1_skill4", "soundBtnName": "attack4", "soundIsMainRole": false}], "cameraShakes": [{"CameraShakeBtnName": "attack2_2", "eventName": "attack2/begin", "numberOfShakes": 1, "distance": 0.2, "speed": 100.0, "delay": 0.75, "decay": 0.0}, {"CameraShakeBtnName": "attack4_3", "eventName": "attack4/begin", "numberOfShakes": 1, "distance": 1.2, "speed": 80.0, "delay": 2.0, "decay": 0.0}, {"CameraShakeBtnName": "combo1_42", "eventName": "combo1_4/begin", "numberOfShakes": 1, "distance": 0.15, "speed": 80.0, "delay": 0.6, "decay": 0.0}, {"CameraShakeBtnName": "attack4_1", "eventName": "attack4/begin", "numberOfShakes": 1, "distance": 0.1, "speed": 100.0, "delay": 0.5, "decay": 0.0}, {"CameraShakeBtnName": "attack4_2", "eventName": "attack4/begin", "numberOfShakes": 1, "distance": 0.1, "speed": 100.0, "delay": 0.7, "decay": 0.0}, {"CameraShakeBtnName": "attack4_4", "eventName": "attack4/begin", "numberOfShakes": 1, "distance": 0.15, "speed": 100.0, "delay": 0.8, "decay": 0.0}, {"CameraShakeBtnName": "attack4_5", "eventName": "attack4/begin", "numberOfShakes": 1, "distance": 0.15, "speed": 100.0, "delay": 1.0, "decay": 0.0}, {"CameraShakeBtnName": "attack4_6", "eventName": "attack4/begin", "numberOfShakes": 1, "distance": 0.15, "speed": 100.0, "delay": 1.6, "decay": 0.0}, {"CameraShakeBtnName": "attack3_1", "eventName": "attack3/begin", "numberOfShakes": 1, "distance": 0.1, "speed": 70.0, "delay": 1.1, "decay": 0.0}, {"CameraShakeBtnName": "esoterica_attack_9_1", "eventName": "esoterica_attack_9/begin", "numberOfShakes": 1, "distance": 0.08, "speed": 100.0, "delay": 0.2, "decay": 0.0}, {"CameraShakeBtnName": "esoterica_attack_9_2", "eventName": "esoterica_attack_9/begin", "numberOfShakes": 1, "distance": 0.1, "speed": 100.0, "delay": 0.8, "decay": 0.0}, {"CameraShakeBtnName": "esoterica_attack_10", "eventName": "esoterica_attack_10/begin", "numberOfShakes": 3, "distance": 0.1, "speed": 120.0, "delay": 0.8, "decay": 0.0}], "radialBlurs": [{"btnName": "attack1", "eventName": "attack1/begin", "isRole": true, "referenceNodeHierarchyPath": "root/Bip001/Bip001 Pelvis/Bip001 Spine/Bip001 Spine1/hurt_middle", "delay": 0.01, "strength": 0.1, "riseTime": 0.1, "holdTime": 0.2, "fallTime": 0.05}, {"btnName": "attack4", "eventName": "attack4/begin", "isRole": true, "referenceNodeHierarchyPath": "root/Bip001/Bip001 Pelvis/Bip001 Spine/Bip001 Spine1/hurt_middle", "delay": 2.0, "strength": 0.6, "riseTime": 0.1, "holdTime": 0.3, "fallTime": 0.1}]}}