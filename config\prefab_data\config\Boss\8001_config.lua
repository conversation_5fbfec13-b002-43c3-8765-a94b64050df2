return {
	actorController = {
		projectiles = {},

		hurts = {},

	},
	actorTriggers = {
		effects = {
			{
				triggerEventName = "attack1/begin",
				triggerDelay = 0.0,
				triggerFreeDelay = 0.0,
				effectGoName = "boss8001_attack",
				effectAsset = {
					BundleName = "effects/prefab/model/boss/8001/boss8001_attack_prefab",
					AssetName = "boss8001_attack",
				},
				playerAtTarget = false,
				referenceNodeHierarchyPath = "",
				isAttach = false,
				isRotation = true,
				isUseCustomTransform = true,
				offsetPosX = 0.0,
				offsetPosY = -2.0,
				offsetPosZ = 0.0,
				triggerStopEvent = "",
				effectBtnName = "attack1",
				playerAtPos = false,
				ignoreParentScale = false,
			},
			{
				triggerEventName = "attack2/begin",
				triggerDelay = 0.0,
				triggerFreeDelay = 0.0,
				effectGoName = "boss8001_skill",
				effectAsset = {
					BundleName = "effects/prefab/model/boss/8001/boss8001_skill_prefab",
					AssetName = "boss8001_skill",
				},
				playerAtTarget = false,
				referenceNodeHierarchyPath = "",
				isAttach = false,
				isRotation = true,
				isUseCustomTransform = true,
				offsetPosX = -5.0,
				offsetPosY = 0.28,
				offsetPosZ = 5.0,
				triggerStopEvent = "",
				effectBtnName = "attack2",
				playerAtPos = false,
				ignoreParentScale = false,
			},
			{
				triggerEventName = "attack1/begin",
				triggerDelay = 0.7,
				triggerFreeDelay = 0.0,
				effectGoName = "eff_hit_zi",
				effectAsset = {
					BundleName = "effects/prefab/environment/common/eff_hit_zi_prefab",
					AssetName = "eff_hit_zi",
				},
				playerAtTarget = true,
				referenceNodeHierarchyPath = "",
				isAttach = false,
				isRotation = false,
				isUseCustomTransform = false,
				offsetPosX = 0.0,
				offsetPosY = 0.0,
				offsetPosZ = 0.0,
				triggerStopEvent = "",
				effectBtnName = "attack1_hit",
				playerAtPos = false,
				ignoreParentScale = false,
			},
			{
				triggerEventName = "attack2/begin",
				triggerDelay = 2.0,
				triggerFreeDelay = 0.0,
				effectGoName = "eff_hit_zi",
				effectAsset = {
					BundleName = "effects/prefab/environment/common/eff_hit_zi_prefab",
					AssetName = "eff_hit_zi",
				},
				playerAtTarget = true,
				referenceNodeHierarchyPath = "",
				isAttach = false,
				isRotation = false,
				isUseCustomTransform = false,
				offsetPosX = 0.0,
				offsetPosY = 0.0,
				offsetPosZ = 0.0,
				triggerStopEvent = "",
				effectBtnName = "attack2_hit",
				playerAtPos = false,
				ignoreParentScale = false,
			},
		},
		sounds = {},

		cameraShakes = {},

		radialBlurs = {},

	},
}