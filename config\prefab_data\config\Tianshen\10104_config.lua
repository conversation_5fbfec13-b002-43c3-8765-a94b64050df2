return {
	actorController = {
		projectiles = {},

		hurts = {},

	},
	actorTriggers = {
		effects = {
			{
				triggerEventName = "attack1/begin",
				triggerDelay = 0.0,
				triggerFreeDelay = 0.0,
				effectGoName = "10104_skill1",
				effectAsset = {
					BundleName = "effects/prefab/model/tianshen/10104/10104_skill1_prefab",
					AssetName = "10104_skill1",
				},
				playerAtTarget = false,
				referenceNodeHierarchyPath = "",
				isAttach = false,
				isRotation = true,
				isUseCustomTransform = false,
				offsetPosX = 0.0,
				offsetPosY = 0.0,
				offsetPosZ = 0.0,
				triggerStopEvent = "",
				effectBtnName = "attack1",
				playerAtPos = false,
				ignoreParentScale = false,
			},
			{
				triggerEventName = "combo1_1/begin",
				triggerDelay = 0.0,
				triggerFreeDelay = 0.0,
				effectGoName = "10104_attack01",
				effectAsset = {
					BundleName = "effects/prefab/model/tianshen/10104/10104_attack01_prefab",
					AssetName = "10104_attack01",
				},
				playerAtTarget = false,
				referenceNodeHierarchyPath = "",
				isAttach = false,
				isRotation = true,
				isUseCustomTransform = false,
				offsetPosX = 0.0,
				offsetPosY = 0.0,
				offsetPosZ = 0.0,
				triggerStopEvent = "",
				effectBtnName = "combo1",
				playerAtPos = false,
				ignoreParentScale = false,
			},
			{
				triggerEventName = "attack2/begin",
				triggerDelay = 0.0,
				triggerFreeDelay = 0.0,
				effectGoName = "10104_skill2",
				effectAsset = {
					BundleName = "effects/prefab/model/tianshen/10104/10104_skill2_prefab",
					AssetName = "10104_skill2",
				},
				playerAtTarget = false,
				referenceNodeHierarchyPath = "",
				isAttach = false,
				isRotation = true,
				isUseCustomTransform = false,
				offsetPosX = 0.0,
				offsetPosY = 0.0,
				offsetPosZ = 0.0,
				triggerStopEvent = "",
				effectBtnName = "attack2",
				playerAtPos = false,
				ignoreParentScale = false,
			},
			{
				triggerEventName = "attack3/begin",
				triggerDelay = 0.0,
				triggerFreeDelay = 0.0,
				effectGoName = "10104_skill3",
				effectAsset = {
					BundleName = "effects/prefab/model/tianshen/10104/10104_skill3_prefab",
					AssetName = "10104_skill3",
				},
				playerAtTarget = false,
				referenceNodeHierarchyPath = "root",
				isAttach = true,
				isRotation = false,
				isUseCustomTransform = false,
				offsetPosX = 0.0,
				offsetPosY = 0.0,
				offsetPosZ = 0.0,
				triggerStopEvent = "",
				effectBtnName = "attack3",
				playerAtPos = false,
				ignoreParentScale = false,
			},
			{
				triggerEventName = "rest/begin",
				triggerDelay = 0.0,
				triggerFreeDelay = 0.0,
				effectGoName = "10104_skill2",
				effectAsset = {
					BundleName = "effects/prefab/model/tianshen/10104/10104_skill2_prefab",
					AssetName = "10104_skill2",
				},
				playerAtTarget = false,
				referenceNodeHierarchyPath = "root",
				isAttach = true,
				isRotation = false,
				isUseCustomTransform = false,
				offsetPosX = 0.0,
				offsetPosY = 0.0,
				offsetPosZ = 0.0,
				triggerStopEvent = "",
				effectBtnName = "rest",
				playerAtPos = false,
				ignoreParentScale = false,
			},
		},
		sounds = {},

		cameraShakes = {
			{
				CameraShakeBtnName = "combo1_1",
				eventName = "combo1_1/begin",
				numberOfShakes = 1,
				distance = 0.06,
				speed = 200.0,
				delay = 0.7,
				decay = 0.3,
			},
			{
				CameraShakeBtnName = "attack1",
				eventName = "attack1/begin",
				numberOfShakes = 6,
				distance = 0.15,
				speed = 800.0,
				delay = 1.4,
				decay = 0.0,
			},
			{
				CameraShakeBtnName = "attack3",
				eventName = "attack3/begin",
				numberOfShakes = 2,
				distance = 0.3,
				speed = 300.0,
				delay = 1.9,
				decay = 0.0,
			},
			{
				CameraShakeBtnName = "attack2",
				eventName = "attack2/begin",
				numberOfShakes = 1,
				distance = 0.05,
				speed = 100.0,
				delay = 1.4,
				decay = 0.0,
			},
		},
		radialBlurs = {},

	},
}