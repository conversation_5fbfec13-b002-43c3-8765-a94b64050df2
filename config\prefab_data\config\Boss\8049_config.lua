return {
	actorController = {
		projectiles = {},

		hurts = {},

		beHurtEffecct = {},

		hurtEffectName = "",
		beHurtNodeName = "",
		beHurtAttach = false,
	},
	actorTriggers = {
		effects = {
			{
				triggerEventName = "magic1_3/begin",
				triggerDelay = 1.0,
				triggerFreeDelay = 0.0,
				effectGoName = "8049_attack",
				effectAsset = {
					BundleName = "effects/prefab/model/boss/8049/8049_attack_prefab",
					AssetName = "8049_attack",
				},
				playerAtTarget = false,
				referenceNodeHierarchyPath = "",
				isAttach = false,
				isRotation = true,
				isUseCustomTransform = false,
				offsetPosX = 0.0,
				offsetPosY = 0.0,
				offsetPosZ = 0.0,
				triggerStopEvent = "",
				effectBtnName = "magic1",
				playerAtPos = false,
				ignoreParentScale = true,
			},
			{
				triggerEventName = "magic2_3/begin",
				triggerDelay = 0.0,
				triggerFreeDelay = 0.0,
				effectGoName = "8049_skill",
				effectAsset = {
					BundleName = "effects/prefab/model/boss/8049/8049_skill_prefab",
					AssetName = "8049_skill",
				},
				playerAtTarget = false,
				referenceNodeHierarchyPath = "",
				isAttach = false,
				isRotation = true,
				isUseCustomTransform = false,
				offsetPosX = 0.0,
				offsetPosY = 0.0,
				offsetPosZ = 0.0,
				triggerStopEvent = "",
				effectBtnName = "magic2",
				playerAtPos = false,
				ignoreParentScale = true,
			},
			{
				triggerEventName = "magic3_3/begin",
				triggerDelay = 1.6,
				triggerFreeDelay = 0.0,
				effectGoName = "8049_skill2",
				effectAsset = {
					BundleName = "effects/prefab/model/boss/8049/8049_skill2_prefab",
					AssetName = "8049_skill2",
				},
				playerAtTarget = false,
				referenceNodeHierarchyPath = "",
				isAttach = false,
				isRotation = true,
				isUseCustomTransform = false,
				offsetPosX = 0.0,
				offsetPosY = 0.0,
				offsetPosZ = 0.0,
				triggerStopEvent = "",
				effectBtnName = "magic3",
				playerAtPos = false,
				ignoreParentScale = true,
			},
			{
				triggerEventName = "magic4_3/begin",
				triggerDelay = 1.0,
				triggerFreeDelay = 0.0,
				effectGoName = "8049_buff_kongzhan_duwu",
				effectAsset = {
					BundleName = "effects/prefab/model/boss/8049/8049_buff_kongzhan_duwu_prefab",
					AssetName = "8049_buff_kongzhan_duwu",
				},
				playerAtTarget = false,
				referenceNodeHierarchyPath = "",
				isAttach = false,
				isRotation = true,
				isUseCustomTransform = false,
				offsetPosX = 0.0,
				offsetPosY = 0.0,
				offsetPosZ = 0.0,
				triggerStopEvent = "",
				effectBtnName = "magic4",
				playerAtPos = false,
				ignoreParentScale = true,
			},
		},
		sounds = {},

		cameraShakes = {},

		radialBlurs = {},

	},
}