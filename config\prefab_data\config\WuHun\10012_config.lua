return {
	actorController = {
		projectiles = {},

		hurts = {},

		beHurtEffecct = {},

		hurtEffectName = "",
		beHurtNodeName = "",
		beHurtAttach = false,
		hurtEffectFreeDelay = 0.0,
		QualityCtrlList = {},

	},
	actorTriggers = {
		effects = {
			{
				triggerEventName = "combo1_1/begin",
				triggerDelay = 0.0,
				triggerFreeDelay = 0.0,
				effectGoName = "10011_skill",
				effectAsset = {
					BundleName = "effects/prefab/model/wuhun/10011/10011_skill_prefab",
					AssetName = "10011_skill",
				},
				playerAtTarget = false,
				referenceNodeHierarchyPath = "",
				isAttach = false,
				isRotation = true,
				isUseCustomTransform = false,
				offsetPosX = 0.0,
				offsetPosY = 0.0,
				offsetPosZ = 0.0,
				triggerStopEvent = "",
				effectBtnName = "combo1_1",
				playerAtPos = false,
				ignoreParentScale = false,
			},
			{
				triggerEventName = "attack1/begin",
				triggerDelay = 0.0,
				triggerFreeDelay = 0.0,
				effectGoName = "10011_attack1",
				effectAsset = {
					BundleName = "effects/prefab/model/wuhun/10011/10011_attack1_prefab",
					AssetName = "10011_attack1",
				},
				playerAtTarget = false,
				referenceNodeHierarchyPath = "",
				isAttach = false,
				isRotation = true,
				isUseCustomTransform = false,
				offsetPosX = 0.0,
				offsetPosY = 0.0,
				offsetPosZ = 0.0,
				triggerStopEvent = "",
				effectBtnName = "attack1",
				playerAtPos = false,
				ignoreParentScale = false,
			},
			{
				triggerEventName = "attack2/begin",
				triggerDelay = 0.0,
				triggerFreeDelay = 0.0,
				effectGoName = "10011_attack2",
				effectAsset = {
					BundleName = "effects/prefab/model/wuhun/10011/10011_attack2_prefab",
					AssetName = "10011_attack2",
				},
				playerAtTarget = false,
				referenceNodeHierarchyPath = "",
				isAttach = false,
				isRotation = true,
				isUseCustomTransform = false,
				offsetPosX = 0.0,
				offsetPosY = 0.0,
				offsetPosZ = 0.0,
				triggerStopEvent = "",
				effectBtnName = "attack2",
				playerAtPos = false,
				ignoreParentScale = false,
			},
			{
				triggerEventName = "rest/begin",
				triggerDelay = 0.0,
				triggerFreeDelay = 0.0,
				effectGoName = "10011_rest",
				effectAsset = {
					BundleName = "effects/prefab/model/wuhun/10011/10011_rest_prefab",
					AssetName = "10011_rest",
				},
				playerAtTarget = false,
				referenceNodeHierarchyPath = "root",
				isAttach = true,
				isRotation = true,
				isUseCustomTransform = false,
				offsetPosX = 0.0,
				offsetPosY = 0.0,
				offsetPosZ = 0.0,
				triggerStopEvent = "",
				effectBtnName = "rest",
				playerAtPos = false,
				ignoreParentScale = true,
			},
		},
		halts = {},

		sounds = {
			{
				soundEventName = "combo1_1/begin",
				soundDelay = 0.0,
				soundAudioAsset = {
					BundleName = "audios/sfxs/roleskill/wuhun_10011",
					AssetName = "MingJiangattack1",
				},
				soundAudioGoName = "MingJiangattack1",
				soundBtnName = "combo1_1",
				soundIsMainRole = false,
			},
			{
				soundEventName = "attack1/begin",
				soundDelay = 0.0,
				soundAudioAsset = {
					BundleName = "audios/sfxs/roleskill/wuhun_10011",
					AssetName = "wuhun_10011attack1",
				},
				soundAudioGoName = "wuhun_10011attack1",
				soundBtnName = "attack1",
				soundIsMainRole = false,
			},
			{
				soundEventName = "attack2/begin",
				soundDelay = 0.0,
				soundAudioAsset = {
					BundleName = "audios/sfxs/roleskill/wuhun_10011",
					AssetName = "MingJiangattack1",
				},
				soundAudioGoName = "MingJiangattack1",
				soundBtnName = "attack2",
				soundIsMainRole = false,
			},
		},
		cameraShakes = {},

		cameraFOVs = {},

		sceneFades = {},

		footsteps = {},

	},
	actorBlinker = {
		blinkFadeIn = 0.0,
		blinkFadeHold = 0.0,
		blinkFadeOut = 0.0,
	},
	TimeLineList = {},

}