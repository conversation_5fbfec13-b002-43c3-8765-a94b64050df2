-- Y-印记转盘.xls
local item_table={
[1]={item_id=50093,num=3,is_bind=1},
[2]={item_id=50093,num=4,is_bind=1},
[3]={item_id=50093,num=5,is_bind=1},
[4]={item_id=50092,num=1,is_bind=1},
[5]={item_id=50087,num=1,is_bind=1},
[6]={item_id=50091,num=1,is_bind=1},
[7]={item_id=50086,num=1,is_bind=1},
[8]={item_id=50090,num=1,is_bind=1},
[9]={item_id=50085,num=1,is_bind=1},
[10]={item_id=50089,num=1,is_bind=1},
[11]={item_id=50085,num=3,is_bind=1},
[12]={item_id=50089,num=3,is_bind=1},
[13]={item_id=50087,num=2,is_bind=1},
[14]={item_id=50091,num=2,is_bind=1},
[15]={item_id=50086,num=2,is_bind=1},
[16]={item_id=50090,num=2,is_bind=1},
[17]={item_id=50085,num=2,is_bind=1},
[18]={item_id=50089,num=2,is_bind=1},
[19]={item_id=50093,num=1,is_bind=1},
[20]={item_id=50093,num=9,is_bind=1},
[21]={item_id=48408,num=1,is_bind=1},
[22]={item_id=50093,num=2,is_bind=1},
[23]={item_id=50088,num=1,is_bind=1},
}

return {
other={
{}
},

other_meta_table_map={
},
dailyrecharge={
{},
{id=2,price=300,},
{id=3,price=680,},
{id=4,price=1280,},
{id=5,price=3280,},
{id=6,price=6480,},
{id=7,price=10000,},
{id=8,price=20000,reward_item=item_table[1],},
{id=9,price=50000,reward_item=item_table[2],},
{id=10,price=100000,reward_item=item_table[3],}
},

dailyrecharge_meta_table_map={
},
rewardpool={
{reward_seq=1,is_special=1,tx_dl="UI_guanguan_fs0",tx_ks="UI_0_6bianx_fs",tx_cj="UI_fs_huo",},
{reward_id=2,reward_item=item_table[4],reward_seq=2,tx_ks="UI_0_5bianx_fs",},
{reward_id=3,reward_item=item_table[5],reward_seq=3,is_special=1,tx_dl="UI_guanguan_hs0",tx_ks="UI_0_6bianx_hs",tx_cj="UI_hs_huo",},
{reward_id=4,reward_item=item_table[6],reward_seq=4,tx_ks="UI_0_5bianx_hs",},
{reward_id=5,reward_item=item_table[7],reward_seq=5,tx_dl="UI_guanguan_cs0",tx_ks="UI_0_6bianx_cs",tx_cj="UI_cs_huo",},
{reward_id=6,reward_item=item_table[8],reward_seq=6,tx_ks="UI_0_5bianx_cs",},
{reward_id=7,reward_item=item_table[9],},
{reward_id=8,reward_item=item_table[10],reward_seq=8,tx_ks="UI_0_5bianx_zs",},
{reward_id=9,reward_item=item_table[11],},
{reward_id=10,reward_item=item_table[12],},
{reward_id=11,reward_item=item_table[13],},
{reward_id=12,reward_item=item_table[14],},
{reward_id=13,reward_item=item_table[15],},
{reward_id=14,reward_item=item_table[16],},
{reward_id=15,reward_item=item_table[17],},
{reward_id=16,reward_item=item_table[18],}
},

rewardpool_meta_table_map={
[10]=8,	-- depth:1
[16]=8,	-- depth:1
[6]=5,	-- depth:1
[13]=5,	-- depth:1
[14]=6,	-- depth:2
[2]=1,	-- depth:1
[4]=3,	-- depth:1
[11]=3,	-- depth:1
[12]=4,	-- depth:2
},
rewardgroup={
{},
{},
{}
},

rewardgroup_meta_table_map={
},
item_random_desc={
[1]={number=1,item_id=50088,},
[2]={number=2,item_id=50092,random_count=0.2,},
[3]={number=3,item_id=50087,},
[4]={number=4,item_id=50091,},
[5]={number=5,item_id=50086,},
[6]={number=6,item_id=50090,},
[7]={number=7,},
[8]={number=8,item_id=50089,},
[9]={number=9,},
[10]={number=10,},
[11]={number=11,item_id=50087,},
[12]={number=12,random_count=0.25,},
[13]={number=13,item_id=50086,},
[14]={number=14,random_count=0.25,},
[15]={number=15,random_count=0.1,},
[16]={number=16,random_count=1.2,}
},

item_random_desc_meta_table_map={
[10]=8,	-- depth:1
[1]=15,	-- depth:1
[3]=2,	-- depth:1
[12]=4,	-- depth:1
[13]=15,	-- depth:1
[14]=6,	-- depth:1
[16]=8,	-- depth:1
},
other_default_table={single_consume=item_table[19],multi_consume=item_table[20],count_item=item_table[21],count=100,xianyu_count=100,open_level=1999,reward_list={[0]=item_table[19]},},

dailyrecharge_default_table={id=1,price=60,reward_item=item_table[22],},

rewardpool_default_table={reward_id=1,reward_item=item_table[23],reward_seq=7,is_special=0,tx_dl="UI_guanguan_zs0",tx_ks="UI_0_6bianx_zs",tx_cj="UI_zs_huo",},

rewardgroup_default_table={},

item_random_desc_default_table={number=1,item_id=50085,random_count=0.3,is_rare=1,}

}

