-- X-系统预告.xls
local item_table={
[1]={item_id=23310,num=1,is_bind=1},
[2]={item_id=28666,num=1,is_bind=1},
[3]={item_id=28665,num=5,is_bind=1},
[4]={item_id=37219,num=1,is_bind=1},
[5]={item_id=37746,num=1,is_bind=1},
[6]={item_id=37645,num=1,is_bind=1},
[7]={item_id=48187,num=1,is_bind=1},
[8]={item_id=22575,num=1,is_bind=1},
[9]={item_id=22576,num=1,is_bind=1},
[10]={item_id=22577,num=1,is_bind=1},
[11]={item_id=22578,num=1,is_bind=1},
[12]={item_id=22579,num=1,is_bind=1},
[13]={item_id=22580,num=1,is_bind=1},
[14]={item_id=22581,num=1,is_bind=1},
[15]={item_id=22582,num=1,is_bind=1},
[16]={item_id=22583,num=1,is_bind=1},
[17]={item_id=22584,num=1,is_bind=1},
[18]={item_id=22585,num=1,is_bind=1},
[19]={item_id=22586,num=1,is_bind=1},
[20]={item_id=22587,num=1,is_bind=1},
[21]={item_id=22588,num=1,is_bind=1},
[22]={item_id=22589,num=1,is_bind=1},
[23]={item_id=50459,num=5,is_bind=1},
[24]={item_id=23311,num=1,is_bind=1},
[25]={item_id=27613,num=1,is_bind=1},
[26]={item_id=27612,num=2,is_bind=1},
[27]={item_id=27611,num=5,is_bind=1},
[28]={item_id=23311,num=5,is_bind=1},
[29]={item_id=48504,num=1,is_bind=1},
}

return {
other={
{}
},

other_meta_table_map={
},
forceshow={
{}
},

forceshow_meta_table_map={
},
chapter={
{},
{seq=1,reward_item={[0]=item_table[1],[1]=item_table[2],[2]=item_table[3],[3]=item_table[3]},task_reward_item={},model_name="武魂·混沌魔猿",model_show_itemid1=23300,display_pos="0|1.5",display_scale=1.5,}
},

chapter_meta_table_map={
},
task={
{task_type=14,param1=100,task_title="仙府等级",task_decs="获得%s/100经验",open_panel="YanYuGeNobleView",},
{task_id=1,task_type=15,param1=2,param2=69,task_title="神女无梦",task_decs="通关%s/2次",open_panel="fubenpanel#fubenpanel_beauty",},
{task_id=2,},
{task_id=3,task_type=17,param1=1,param2=8,task_title="再次相见",task_decs="第八天登录",open_panel="",},
{seq=1,task_type=18,param1=430,task_title="百倍爆装",task_decs="获得%s/430经验",open_panel="HundredEquipView",},
{seq=1,task_id=1,task_type=19,param1=8,task_title="异域魂界",task_decs="击败%s/8只",open_panel="WorldServer#xianjie_boss",},
{seq=1,},
{seq=1,param2=11,task_decs="第十一天登录",}
},

task_meta_table_map={
[7]=3,	-- depth:1
[8]=4,	-- depth:1
},
icon_show={
{}
},

icon_show_meta_table_map={
},
other_default_table={open_level=1,},

forceshow_default_table={seq=0,open_day=7,open_level=100,close_day=999,label_name="诸神预告",desc="完成所有任务激活诸神系统获得天翎剑尊",open_view="TianShenView",rmb_type=141,price=6,finish_reward_item={},all_reward_show={[0]=item_table[4],[1]=item_table[5],[2]=item_table[6],[3]=item_table[7],[4]=item_table[8],[5]=item_table[8],[6]=item_table[9],[7]=item_table[10],[8]=item_table[11],[9]=item_table[12],[10]=item_table[13],[11]=item_table[14],[12]=item_table[15],[13]=item_table[16],[14]=item_table[17],[15]=item_table[18],[16]=item_table[19],[17]=item_table[20],[18]=item_table[21],[19]=item_table[22]},open_reward_item={[0]=item_table[4],[1]=item_table[5],[2]=item_table[6],[3]=item_table[7],[4]=item_table[8]},fun_name="SystemForceLongShenView",fun_ui_type=1,reward_show=item_table[23],},

chapter_default_table={seq=0,chapter=0,reward_item={[0]=item_table[24],[1]=item_table[25],[2]=item_table[26],[3]=item_table[27]},task_reward_item={[0]=item_table[28]},model_name="诸神·天翎剑尊",day=1,model_show_type1=1,model_bundle_name1="",model_asset_name1="",model_show_itemid1=27802,special_show_name1="",display_pos="0|0",rotation="0|0|0",display_scale=1.3,show_special_effect1="",special_img1="",if_showHalo=0,type=0,},

task_default_table={seq=0,task_id=0,chapter=0,task_type=21,param1=20000,param2=0,param3=0,param4=0,reward_item={[0]=item_table[29]},task_title="消耗灵玉",task_decs="%s/20000",open_panel="market",buy_open_panel="",activity_id="",},

icon_show_default_table={server_day=9999,icon="a2_zjm_icon_hdlx",icon_effect="UI_wuhun2",}

}

