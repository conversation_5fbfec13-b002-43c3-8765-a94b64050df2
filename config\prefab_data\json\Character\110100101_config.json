{"actorController": {"projectiles": [], "hurts": []}, "actorTriggers": {"effects": [{"triggerEventName": "attack1/begin", "triggerDelay": 0.0, "triggerFreeDelay": 0.0, "effectGoName": "skill1", "effectAsset": {"BundleName": "effects/prefab/model/nuqi/ren/skill1_prefab", "AssetName": "skill1", "AssetGUID": "f6162d29190e4b3409b6b1671d9dee48", "IsEmpty": false}, "playerAtTarget": false, "referenceNodeHierarchyPath": "", "isAttach": false, "isRotation": true, "isUseCustomTransform": false, "offsetPosX": 0.0, "offsetPosY": 0.0, "offsetPosZ": 0.0, "triggerStopEvent": "[none]", "effectBtnName": "attack1", "playerAtPos": false, "ignoreParentScale": false}, {"triggerEventName": "attack2/begin", "triggerDelay": 0.0, "triggerFreeDelay": 0.0, "effectGoName": "skill2", "effectAsset": {"BundleName": "effects/prefab/model/nuqi/ren/skill2_prefab", "AssetName": "skill2", "AssetGUID": "b4b41bfc1e433a14ea73fbee0555900d", "IsEmpty": false}, "playerAtTarget": false, "referenceNodeHierarchyPath": "", "isAttach": false, "isRotation": true, "isUseCustomTransform": false, "offsetPosX": 0.0, "offsetPosY": 0.0, "offsetPosZ": 0.0, "triggerStopEvent": "[none]", "effectBtnName": "attack2", "playerAtPos": false, "ignoreParentScale": false}, {"triggerEventName": "attack3/begin", "triggerDelay": 0.0, "triggerFreeDelay": 0.0, "effectGoName": "skill3", "effectAsset": {"BundleName": "effects/prefab/model/nuqi/ren/skill3_prefab", "AssetName": "skill3", "AssetGUID": "e01d926d355002c49b533c577cc94f5d", "IsEmpty": false}, "playerAtTarget": false, "referenceNodeHierarchyPath": "", "isAttach": false, "isRotation": true, "isUseCustomTransform": false, "offsetPosX": 0.0, "offsetPosY": 0.0, "offsetPosZ": 0.0, "triggerStopEvent": "[none]", "effectBtnName": "attack3", "playerAtPos": false, "ignoreParentScale": false}, {"triggerEventName": "attack4/begin", "triggerDelay": 0.0, "triggerFreeDelay": 0.0, "effectGoName": "skill4", "effectAsset": {"BundleName": "effects/prefab/model/nuqi/ren/skill4_prefab", "AssetName": "skill4", "AssetGUID": "c33ae1cf2fc9ba047a233d5021c1b701", "IsEmpty": false}, "playerAtTarget": true, "referenceNodeHierarchyPath": "", "isAttach": false, "isRotation": true, "isUseCustomTransform": true, "offsetPosX": 0.0, "offsetPosY": -2.0, "offsetPosZ": 2.5, "triggerStopEvent": "[none]", "effectBtnName": "attack4", "playerAtPos": false, "ignoreParentScale": false}, {"triggerEventName": "combo1_1/begin", "triggerDelay": 0.0, "triggerFreeDelay": 0.0, "effectGoName": "combo1", "effectAsset": {"BundleName": "effects/prefab/model/nuqi/ren/combo1_prefab", "AssetName": "combo1", "AssetGUID": "732013c294ccd6e40b088488b094f4c4", "IsEmpty": false}, "playerAtTarget": false, "referenceNodeHierarchyPath": "", "isAttach": false, "isRotation": true, "isUseCustomTransform": true, "offsetPosX": 0.0, "offsetPosY": 2.0, "offsetPosZ": 0.0, "triggerStopEvent": "[none]", "effectBtnName": "combo1_1", "playerAtPos": false, "ignoreParentScale": false}, {"triggerEventName": "combo1_2/begin", "triggerDelay": 0.0, "triggerFreeDelay": 0.0, "effectGoName": "combo1", "effectAsset": {"BundleName": "effects/prefab/model/nuqi/ren/combo1_prefab", "AssetName": "combo1", "AssetGUID": "732013c294ccd6e40b088488b094f4c4", "IsEmpty": false}, "playerAtTarget": false, "referenceNodeHierarchyPath": "", "isAttach": false, "isRotation": true, "isUseCustomTransform": true, "offsetPosX": 0.0, "offsetPosY": 2.0, "offsetPosZ": 0.0, "triggerStopEvent": "[none]", "effectBtnName": "combo1_2", "playerAtPos": false, "ignoreParentScale": false}, {"triggerEventName": "combo1_3/begin", "triggerDelay": 0.0, "triggerFreeDelay": 0.0, "effectGoName": "combo1", "effectAsset": {"BundleName": "effects/prefab/model/nuqi/ren/combo1_prefab", "AssetName": "combo1", "AssetGUID": "732013c294ccd6e40b088488b094f4c4", "IsEmpty": false}, "playerAtTarget": false, "referenceNodeHierarchyPath": "", "isAttach": false, "isRotation": true, "isUseCustomTransform": true, "offsetPosX": 0.0, "offsetPosY": 2.0, "offsetPosZ": 0.0, "triggerStopEvent": "[none]", "effectBtnName": "combo1_3", "playerAtPos": false, "ignoreParentScale": false}, {"triggerEventName": "combo1_4/begin", "triggerDelay": 0.2, "triggerFreeDelay": 0.0, "effectGoName": "combo1", "effectAsset": {"BundleName": "effects/prefab/model/nuqi/ren/combo1_prefab", "AssetName": "combo1", "AssetGUID": "732013c294ccd6e40b088488b094f4c4", "IsEmpty": false}, "playerAtTarget": false, "referenceNodeHierarchyPath": "", "isAttach": false, "isRotation": true, "isUseCustomTransform": true, "offsetPosX": 2.0, "offsetPosY": 2.0, "offsetPosZ": 0.0, "triggerStopEvent": "[none]", "effectBtnName": "combo1_4_1", "playerAtPos": false, "ignoreParentScale": false}, {"triggerEventName": "transformation/begin", "triggerDelay": 0.0, "triggerFreeDelay": 0.0, "effectGoName": "bianshen", "effectAsset": {"BundleName": "effects/prefab/model/nuqi/ren/bianshen_prefab", "AssetName": "bianshen", "AssetGUID": "51291cff2b9ed914da548aa6c62c28f4", "IsEmpty": false}, "playerAtTarget": false, "referenceNodeHierarchyPath": "", "isAttach": false, "isRotation": true, "isUseCustomTransform": false, "offsetPosX": 0.0, "offsetPosY": 0.0, "offsetPosZ": 0.0, "triggerStopEvent": "[none]", "effectBtnName": "transformation", "playerAtPos": false, "ignoreParentScale": false}, {"triggerEventName": "combo1_4/begin", "triggerDelay": 0.3, "triggerFreeDelay": 0.0, "effectGoName": "combo1", "effectAsset": {"BundleName": "effects/prefab/model/nuqi/ren/combo1_prefab", "AssetName": "combo1", "AssetGUID": "732013c294ccd6e40b088488b094f4c4", "IsEmpty": false}, "playerAtTarget": false, "referenceNodeHierarchyPath": "", "isAttach": false, "isRotation": true, "isUseCustomTransform": true, "offsetPosX": -1.0, "offsetPosY": 2.0, "offsetPosZ": 0.0, "triggerStopEvent": "[none]", "effectBtnName": "combo1_4_2", "playerAtPos": false, "ignoreParentScale": false}], "sounds": [{"soundEventName": "attack1/begin", "soundDelay": 0.0, "soundAudioAsset": {"BundleName": "audios/sfxs/roleskill/110100101", "AssetName": "skill1", "AssetGUID": "1b7cd755c24e0ef4697b63ae91743d40", "IsEmpty": false}, "soundAudioGoName": "skill1", "soundBtnName": "attack1", "soundIsMainRole": false}, {"soundEventName": "attack2/begin", "soundDelay": 0.0, "soundAudioAsset": {"BundleName": "audios/sfxs/roleskill/110100101", "AssetName": "skill2", "AssetGUID": "036fc67d533b63e44a1df8046deb4c2d", "IsEmpty": false}, "soundAudioGoName": "skill2", "soundBtnName": "attack2", "soundIsMainRole": false}, {"soundEventName": "attack3/begin", "soundDelay": 0.0, "soundAudioAsset": {"BundleName": "audios/sfxs/roleskill/110100101", "AssetName": "skill3", "AssetGUID": "1be0790144c50b347af8e06079c277b1", "IsEmpty": false}, "soundAudioGoName": "skill3", "soundBtnName": "attack3", "soundIsMainRole": false}, {"soundEventName": "attack4/begin", "soundDelay": 0.0, "soundAudioAsset": {"BundleName": "audios/sfxs/roleskill/110100101", "AssetName": "skill4", "AssetGUID": "6482bee8ac9b86a42abfbe76965f0b11", "IsEmpty": false}, "soundAudioGoName": "skill4", "soundBtnName": "attack4", "soundIsMainRole": false}, {"soundEventName": "transformation/begin", "soundDelay": 0.0, "soundAudioAsset": {"BundleName": "audios/sfxs/roleskill/110100101", "AssetName": "tra", "AssetGUID": "87f117226ddb65d4990a5b856d365e4d", "IsEmpty": false}, "soundAudioGoName": "tra", "soundBtnName": "tra", "soundIsMainRole": false}, {"soundEventName": "combo1_1/begin", "soundDelay": 0.0, "soundAudioAsset": {"BundleName": "audios/sfxs/roleskill/110100101", "AssetName": "attack1", "AssetGUID": "03936a4c12efc6b4a82797885fbf2b0d", "IsEmpty": false}, "soundAudioGoName": "attack1", "soundBtnName": "combo1", "soundIsMainRole": false}, {"soundEventName": "combo1_2/begin", "soundDelay": 0.0, "soundAudioAsset": {"BundleName": "audios/sfxs/roleskill/110100101", "AssetName": "attack2", "AssetGUID": "253f0cde27674694cae81a26c66245bb", "IsEmpty": false}, "soundAudioGoName": "attack2", "soundBtnName": "combo2", "soundIsMainRole": false}, {"soundEventName": "combo1_3/begin", "soundDelay": 0.0, "soundAudioAsset": {"BundleName": "audios/sfxs/roleskill/110100101", "AssetName": "attack3", "AssetGUID": "78993daeff220c045a4febab85db3d08", "IsEmpty": false}, "soundAudioGoName": "attack3", "soundBtnName": "combo3", "soundIsMainRole": false}, {"soundEventName": "combo1_4/begin", "soundDelay": 0.0, "soundAudioAsset": {"BundleName": "audios/sfxs/roleskill/110100101", "AssetName": "attack4", "AssetGUID": "48097f55d6dab7142a3c3c232e5f26b2", "IsEmpty": false}, "soundAudioGoName": "attack4", "soundBtnName": "combo4", "soundIsMainRole": false}, {"soundEventName": "transformation/begin", "soundDelay": 0.0, "soundAudioAsset": {"BundleName": "audios/sfxs/skill/110100101", "AssetName": "transformation", "AssetGUID": "f7fa12188ec5921429729e9e27633991", "IsEmpty": false}, "soundAudioGoName": "transformation", "soundBtnName": "tra1", "soundIsMainRole": false}], "cameraShakes": [{"CameraShakeBtnName": "combo1_4", "eventName": "combo1_4/begin", "numberOfShakes": 1, "distance": 0.2, "speed": 100.0, "delay": 0.6, "decay": 0.0}, {"CameraShakeBtnName": "attack1_1", "eventName": "attack1/begin", "numberOfShakes": 1, "distance": 0.1, "speed": 200.0, "delay": 0.5, "decay": 0.0}, {"CameraShakeBtnName": "attack1_2", "eventName": "attack1/begin", "numberOfShakes": 1, "distance": 0.1, "speed": 200.0, "delay": 0.8, "decay": 0.0}, {"CameraShakeBtnName": "attack1_3", "eventName": "attack1/begin", "numberOfShakes": 3, "distance": 0.2, "speed": 150.0, "delay": 1.3, "decay": 0.0}, {"CameraShakeBtnName": "attack3_2", "eventName": "attack3/begin", "numberOfShakes": 7, "distance": 0.2, "speed": 60.0, "delay": 1.3, "decay": 0.1}, {"CameraShakeBtnName": "attack3_1", "eventName": "attack3/begin", "numberOfShakes": 0, "distance": 0.3, "speed": 90.0, "delay": 0.15, "decay": 0.0}, {"CameraShakeBtnName": "attack4", "eventName": "attack4/begin", "numberOfShakes": 12, "distance": 0.7, "speed": 500.0, "delay": 1.6, "decay": 0.0}, {"CameraShakeBtnName": "transformation", "eventName": "transformation/begin", "numberOfShakes": 2, "distance": 0.8, "speed": 400.0, "delay": 0.6, "decay": 0.0}], "radialBlurs": [{"btnName": "attack4", "eventName": "attack4/begin", "isRole": true, "referenceNodeHierarchyPath": "", "delay": 0.8, "strength": 0.08, "riseTime": 0.5, "holdTime": 0.3, "fallTime": 0.0}, {"btnName": "transformation", "eventName": "transformation/begin", "isRole": true, "referenceNodeHierarchyPath": "root", "delay": 0.1, "strength": 0.1, "riseTime": 0.1, "holdTime": 0.2, "fallTime": 0.0}, {"btnName": "attack3", "eventName": "attack3/begin", "isRole": false, "referenceNodeHierarchyPath": "", "delay": 1.2, "strength": 0.03, "riseTime": 0.5, "holdTime": 0.3, "fallTime": 0.0}, {"btnName": "attack2", "eventName": "attack2/begin", "isRole": false, "referenceNodeHierarchyPath": "", "delay": 0.1, "strength": 0.01, "riseTime": 0.1, "holdTime": 0.2, "fallTime": 0.0}]}}