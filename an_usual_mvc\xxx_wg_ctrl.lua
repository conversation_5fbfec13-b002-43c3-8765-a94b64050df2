-- 在 require_list.lua 添加需要require的 xxx_wg_ctrl文件
-- 在 modules_wg_ctrl.lua 添加需要创建的XXXWGCtrl

-- 加载xxx_wg_data.lua、xxx_wg_view.lua脚本
require("an_usual_mvc/xxx_wg_data")
require("an_usual_mvc/xxx_wg_view")

XXXWGCtrl = XXXWGCtrl or BaseClass(BaseWGCtrl)
function XXXWGCtrl:__init()
	if XXXWGCtrl.Instance then
		error("[XXXWGCtrl]:Attempt to create singleton twice!")
	end
    -- 单例
	XXXWGCtrl.Instance = self

    -- 创建data
    self.data = XXXWGData.New()
    -- 创建view
    -- 一般上需要配置支持跳转的，才需添加对应的模块名GuideModuleName.XXXView
    -- 见 guide_config.lua 关注 GuideModuleName表 FunName表 TabIndex表
    self.view = XXXView.New(GuideModuleName.XXXView)

    -- 注册协议
    self:RegisterAllProtocols()
    -- 注册事件监听
    self:RegisterAllEvents()
end

function XXXWGCtrl:__delete()
    self:UnRegisterAllEvents()

    -- 销毁data
    self.data:DeleteMe()
	self.data = nil
    -- 销毁view
	self.view:DeleteMe()
	self.view = nil

    XXXWGCtrl.Instance = nil
end

-- 注册协议
function XXXWGCtrl:RegisterAllProtocols()
    self:RegisterProtocol(CSXXXReq)
    -- 接收协议 与对应方法名绑定
	self:RegisterProtocol(SCXXXInfo, "OnSCXXXInfo")
end

-- 注册事件监听
function XXXWGCtrl:RegisterAllEvents()
    -- 【常见的事件】

    -- 物品改变
    self.item_data_change = BindTool.Bind(self.OnItemDataChange, self)
	ItemWGData.Instance:NotifyDataChangeCallBack(self.item_data_change, false)

    -- 角色属性改变
    -- RoleWGData.Instance:NotifyAttrChange(回调方法, 属性表)
    -- 属性字段 在 game_vo.lua 的 RoleVo表 
    self.role_data_change = BindTool.Bind(self.OnRoleAttrChange, self)
	RoleWGData.Instance:NotifyAttrChange(self.role_data_change, {"level"})

    -- 活动改变
	self.act_change = BindTool.Bind(self.OnActivityChange, self)
	ActivityWGData.Instance:NotifyActChangeCallback(self.act_change)

    -- 天数改变
	self:BindGlobalEvent(OtherEventType.PASS_DAY, BindTool.Bind1(self.OnDayChange, self))

    -- 其余的看 global_events.lua
end

-- 注销事件监听
function XXXWGCtrl:UnRegisterAllEvents()
    -- 注销物品改变监听
    if self.item_data_change then
		ItemWGData.Instance:UnNotifyDataChangeCallBack(self.item_data_change)
		self.item_data_change = nil
	end

    -- 注销角色属性改变监听
    if self.role_data_change then
		RoleWGData.Instance:UnNotifyAttrChange(self.role_data_change)
		self.role_data_change = nil
	end

    -- 注销活动改变监听
    if self.act_change then
        ActivityWGData.Instance:UnNotifyActChangeCallback(self.act_change)
        self.act_change = nil
    end
end

-- 物品变化
function XXXWGCtrl:OnItemDataChange(change_item_id, change_item_index, change_reason, put_reason, old_num, new_num)
    -- 物品数量增加
	if change_reason == GameEnum.DATALIST_CHANGE_REASON_ADD or
	(change_reason == GameEnum.DATALIST_CHANGE_REASON_UPDATE and old_num ~= nil and new_num ~= nil and new_num > old_num) then
        -- do
	end

    -- 物品数量减少
    if change_reason == GameEnum.DATALIST_CHANGE_REASON_REMOVE or
	(change_reason == GameEnum.DATALIST_CHANGE_REASON_UPDATE and old_num ~= nil and new_num ~= nil and new_num < old_num) then
        -- 一般不对物品减少进行操作，因为物品的消耗，通常伴随着相关功能的协议下发
    end
end

-- 角色属性改变
function XXXWGCtrl:OnRoleAttrChange(attr_name, value, old_value)
	if attr_name == "level" then
        -- do
	end
end

-- 活动改变
function XXXWGCtrl:OnActivityChange(activity_type, status, next_time, open_type)
    if activity_type == ACTIVITY_TYPE.XXX and status == ACTIVITY_STATUS.OPEN then
        -- do
    end
end

-- 天数改变
function XXXWGCtrl:OnDayChange()
    -- 游戏时间 TimeWGCtrl
end

-- 操作请求
function XXXWGCtrl:SendOperateReq(operate_type, param_1, param_2)
	local protocol = ProtocolPool.Instance:GetProtocol(CSXXXReq)
	protocol.operate_type = operate_type or 0
	protocol.param_1 = param_1 or 0
	protocol.param_2 = param_2 or 0
	protocol:EncodeAndSend()
end

-- 接收协议
function XXXWGCtrl:OnSCXXXInfo(protocol)
    -- 数据存储
    self.data:SetXXXInfo(protocol)
    -- 界面刷新
    local index, key, param_t
    self:FlushView(index, key, param_t)
    -- 刷新红点
    RemindManager.Instance:Fire(RemindName.XXX)
end

-- 刷新界面
function XXXWGCtrl:FlushView(index, key, param_t)
	if self.view:IsOpen() then
		self.view:Flush(index, key, param_t)
	end
end

-- 打开界面
function XXXWGCtrl:OpenView()
    -- 直接调打开
    self.view:Open()
    -- 通过模块名打开
    ViewManager.Instance:Open(GuideModuleName.XXXView, TabIndex.xxx, key, param_t)
    -- 通过配置的界面链接打开，如role#role_bag#op=1,uin=cell
    local cfg_path = ""
    FunOpen.Instance:OpenViewNameByCfg(cfg_path)
end