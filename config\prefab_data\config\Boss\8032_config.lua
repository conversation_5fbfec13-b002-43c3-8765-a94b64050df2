return {
	actorController = {
		projectiles = {},

		hurts = {},

		beHurtEffecct = {},

		hurtEffectName = "",
		beHurtNodeName = "",
		beHurtAttach = false,
	},
	actorTriggers = {
		effects = {
			{
				triggerEventName = "attack1/begin",
				triggerDelay = 0.3,
				triggerFreeDelay = 0.0,
				effectGoName = "boss8032_skill",
				effectAsset = {
					BundleName = "effects/prefab/model/boss/8032/boss8032_skill_prefab",
					AssetName = "boss8032_skill",
				},
				playerAtTarget = false,
				referenceNodeHierarchyPath = "",
				isAttach = false,
				isRotation = false,
				isUseCustomTransform = false,
				offsetPosX = 0.0,
				offsetPosY = 0.0,
				offsetPosZ = 0.0,
				triggerStopEvent = "",
				effectBtnName = "attack1",
				playerAtPos = false,
				ignoreParentScale = false,
			},
			{
				triggerEventName = "attack2/begin",
				triggerDelay = 0.0,
				triggerFreeDelay = 0.0,
				effectGoName = "boss8032_skill",
				effectAsset = {
					BundleName = "effects/prefab/model/boss/8032/boss8032_skill_prefab",
					AssetName = "boss8032_skill",
				},
				playerAtTarget = false,
				referenceNodeHierarchyPath = "",
				isAttach = false,
				isRotation = false,
				isUseCustomTransform = false,
				offsetPosX = 0.0,
				offsetPosY = 0.0,
				offsetPosZ = 0.0,
				triggerStopEvent = "",
				effectBtnName = "attack2",
				playerAtPos = false,
				ignoreParentScale = true,
			},
		},
		sounds = {},

		cameraShakes = {},

		radialBlurs = {},

	},
}