return {
	actorController = {
		projectiles = {},

		hurts = {},

	},
	actorTriggers = {
		effects = {
			{
				triggerEventName = "attack1/begin",
				triggerDelay = 0.0,
				triggerFreeDelay = 0.0,
				effectGoName = "yushou4076_attack",
				effectAsset = {
					BundleName = "effects/prefab/model/yushou/4076/yushou4076_attack_prefab",
					AssetName = "yushou4076_attack",
				},
				playerAtTarget = false,
				referenceNodeHierarchyPath = "buff_down",
				isAttach = true,
				isRotation = false,
				isUseCustomTransform = false,
				offsetPosX = 0.0,
				offsetPosY = 0.0,
				offsetPosZ = 0.0,
				triggerStopEvent = "",
				effectBtnName = "attack1",
				playerAtPos = false,
				ignoreParentScale = true,
			},
			{
				triggerEventName = "attack2/begin",
				triggerDelay = 0.0,
				triggerFreeDelay = 0.0,
				effectGoName = "yushou4076_skill",
				effectAsset = {
					BundleName = "effects/prefab/model/yushou/4076/yushou4076_skill_prefab",
					AssetName = "yushou4076_skill",
				},
				playerAtTarget = false,
				referenceNodeHierarchyPath = "buff_down",
				isAttach = true,
				isRotation = false,
				isUseCustomTransform = true,
				offsetPosX = 0.0,
				offsetPosY = 1.0,
				offsetPosZ = -1.5,
				triggerStopEvent = "",
				effectBtnName = "attack2",
				playerAtPos = false,
				ignoreParentScale = true,
			},
			{
				triggerEventName = "rest/begin",
				triggerDelay = 2.8,
				triggerFreeDelay = 0.0,
				effectGoName = "yushou4076_rest",
				effectAsset = {
					BundleName = "effects/prefab/model/yushou/4076/yushou4076_rest_prefab",
					AssetName = "yushou4076_rest",
				},
				playerAtTarget = false,
				referenceNodeHierarchyPath = "buff_down",
				isAttach = true,
				isRotation = false,
				isUseCustomTransform = false,
				offsetPosX = 0.0,
				offsetPosY = 0.0,
				offsetPosZ = 0.0,
				triggerStopEvent = "",
				effectBtnName = "rest",
				playerAtPos = false,
				ignoreParentScale = false,
			},
		},
		sounds = {
			{
				soundEventName = "attack2/begin",
				soundDelay = 0.0,
				soundAudioAsset = {
					BundleName = "audios/sfxs/skill/4076",
					AssetName = "4076_skill",
				},
				soundAudioGoName = "4076_skill",
				soundBtnName = "skill",
				soundIsMainRole = false,
			},
		},
		cameraShakes = {},

		radialBlurs = {},

	},
}