return {
	actorController = {
		projectiles = {},

		hurts = {},

	},
	actorTriggers = {
		effects = {
			{
				triggerEventName = "attack2/begin",
				triggerDelay = 0.9,
				triggerFreeDelay = 0.0,
				effectGoName = "yushou_skill_2",
				effectAsset = {
					BundleName = "effects/prefab/model/yushou/common/yushou_skill_2_prefab",
					AssetName = "yushou_skill_2",
				},
				playerAtTarget = false,
				referenceNodeHierarchyPath = "root",
				isAttach = true,
				isRotation = false,
				isUseCustomTransform = true,
				offsetPosX = 0.0,
				offsetPosY = 0.0,
				offsetPosZ = 0.0,
				triggerStopEvent = "",
				effectBtnName = "attack2",
				playerAtPos = false,
				ignoreParentScale = false,
			},
			{
				triggerEventName = "attack1/begin",
				triggerDelay = 0.0,
				triggerFreeDelay = 0.0,
				effectGoName = "eff_4036yushou_attcak1",
				effectAsset = {
					BundleName = "effects/prefab/model/yushou/4036/eff_4036yushou_attcak1_prefab",
					AssetName = "eff_4036yushou_attcak1",
				},
				playerAtTarget = false,
				referenceNodeHierarchyPath = "root",
				isAttach = true,
				isRotation = false,
				isUseCustomTransform = true,
				offsetPosX = 0.0,
				offsetPosY = -0.5,
				offsetPosZ = -3.0,
				triggerStopEvent = "",
				effectBtnName = "attack1",
				playerAtPos = false,
				ignoreParentScale = false,
			},
			{
				triggerEventName = "rest/begin",
				triggerDelay = 0.55,
				triggerFreeDelay = 0.0,
				effectGoName = "eff_4036yushou_rest",
				effectAsset = {
					BundleName = "effects/prefab/model/yushou/4036/eff_4036yushou_rest_prefab",
					AssetName = "eff_4036yushou_rest",
				},
				playerAtTarget = false,
				referenceNodeHierarchyPath = "root",
				isAttach = true,
				isRotation = true,
				isUseCustomTransform = true,
				offsetPosX = 0.0,
				offsetPosY = -0.4,
				offsetPosZ = 0.0,
				triggerStopEvent = "",
				effectBtnName = "rest",
				playerAtPos = false,
				ignoreParentScale = false,
			},
		},
		sounds = {
			{
				soundEventName = "attack2/begin",
				soundDelay = 0.0,
				soundAudioAsset = {
					BundleName = "audios/sfxs/skill/4036",
					AssetName = "4036_skill",
				},
				soundAudioGoName = "4036_skill",
				soundBtnName = "skill",
				soundIsMainRole = false,
			},
		},
		cameraShakes = {},

		radialBlurs = {},

	},
}