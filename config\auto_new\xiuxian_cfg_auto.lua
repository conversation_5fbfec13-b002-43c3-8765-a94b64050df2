-- X-修仙试炼.xls
local item_table={
[1]={item_id=39153,num=1,is_bind=1},
[2]={item_id=22575,num=1,is_bind=1},
[3]={item_id=26214,num=1,is_bind=1},
[4]={item_id=22117,num=1,is_bind=1},
[5]={item_id=26350,num=1,is_bind=1},
[6]={item_id=22531,num=1,is_bind=1},
}

return {
other={
{}
},

other_meta_table_map={
},
chapter_cfg={
[1]={chapter_id=1,},
[2]={chapter_id=2,task_list="5|6|7|8",open_level=105,attr_type1="yuansu_hujia",chapter_name="炼丹成丹",chapter_png="a2_xdxl_ditu2",item_list={[0]=item_table[1],[1]=item_table[2]},task_id=17730,}
},

chapter_cfg_meta_table_map={
},
task_cfg={
{},
{task_type=2,condition_type=35,param1=6,task_des="通关九重劫塔6层",go_param="fubenpanel#fubenpanel_welkin",},
{task_type=3,condition_type=103,param1=3,param2=-1,task_des="幻兽提升到3级",go_param="ControlBeastsView#beasts_culture",},
{task_type=4,condition_type=15,reward_type=1,money_param=500000,reward_item={[0]=item_table[3]},task_des="进行1次许愿",go_param="qifu#qifu_qf",},
{task_type=5,chapter_id=2,condition_type=4,param1=2,reward_item={[0]=item_table[4]},task_des="完成2次悬赏任务",go_param="bizuo#bizuo_bizuo",},
{task_type=6,chapter_id=2,condition_type=96,param1=3,param2=1,reward_item={[0]=item_table[4]},task_des="击杀3个仙遗洞天BOSS",go_param="boss#boss_vip",},
{task_type=7,chapter_id=2,condition_type=17,param1=3,reward_item={[0]=item_table[3]},task_des="挑战天梯争霸3次",go_param="act_jjc",},
{task_type=8,chapter_id=2,condition_type=75,reward_item={[0]=item_table[3]},task_des="激活1阶装备集成",go_param="CultivationView#equip_target#uip=1",}
},

task_cfg_meta_table_map={
},
world_chat_cfg={
{},
{number=2,chat_content="盆友，来一杯吧",},
{number=3,chat_content="有没有大佬带队打仙遗洞天",},
{number=4,chat_content="180级结婚预约啦！",},
{number=5,chat_content="人好看，连空气都呼唤",}
},

world_chat_cfg_meta_table_map={
},
model_show={
{model_show_type=3,model_bundle_name="uis/rawimages/a3_xunxian_01",model_asset_name="a3_xunxian_01.png",model_show_itemid="",mainui_display_scale=0.8,unlock_item=27800,},
{chapter_id=2,model_show_itemid=27799,unlock_item=27801,},
{chapter_id=3,model_bundle_name="uis/rawimages/a3_xunxian_02",model_asset_name="a3_xunxian_02.png",unlock_item=27802,},
{chapter_id=4,},
{chapter_id=5,model_bundle_name="uis/rawimages/a3_xunxian_03",model_asset_name="a3_xunxian_03.png",unlock_item=27804,},
{chapter_id=6,model_show_itemid=27800,unlock_item=27805,},
{chapter_id=7,model_bundle_name="uis/rawimages/a3_xunxian_04",model_asset_name="a3_xunxian_04.png",unlock_item=27806,},
{chapter_id=8,unlock_item=27807,},
{chapter_id=9,unlock_item=27808,},
{chapter_id=10,unlock_item=27809,},
{chapter_id=11,},
{chapter_id=12,},
{chapter_id=13,},
{chapter_id=14,},
{chapter_id=15,},
{chapter_id=16,},
{chapter_id=17,}
},

model_show_meta_table_map={
[7]=1,	-- depth:1
[5]=1,	-- depth:1
[3]=1,	-- depth:1
},
other_default_table={suit_stone_id_list="26093|26094|26095",},

chapter_cfg_default_table={chapter_id=1,task_list="1|2|3|4",open_level=40,skill_id=0,param1=0,param2=0,param3=0,param4=0,capability_inc=0,attr_type1="yuansu_shanghai",attr_type2="",skill_index="",chapter_name="初入仙门",zhanli="",chapter_png="a2_xdxl_ditu1",item_list={[0]=item_table[5],[1]=item_table[2]},task_id=0,chapter_des="解锁法器系统",},

task_cfg_default_table={task_type=1,chapter_id=1,condition_type=101,param1=1,param2=0,param3=0,param4=0,reward_type=0,money_param=0,reward_item={[0]=item_table[6]},task_des="角色仙修达到练气前期4层",go_type=1,go_param="CultivationView",go_btn_effect=0,},

world_chat_cfg_default_table={number=1,chat_content="初来咋到，请多关照！",},

model_show_default_table={chapter_id=1,model_show_type=1,model_bundle_name="",model_asset_name="",model_show_itemid=27803,special_show_name="",display_pos="0|0|0",display_scale=1.3,rotation="0|0|0",mainui_display_pos="0|0|0",mainui_display_scale=0.6,mainui_rotation="0|0|0",unlock_item=27803,}

}

