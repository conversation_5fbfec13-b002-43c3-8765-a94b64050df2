{"Lua.workspace.maxPreload": 10000, "Lua.workspace.preloadFileSize": 2000, "Lua.hover.viewStringMax": 5000, "Lua.diagnostics.workspaceDelay": 30000, "Lua.diagnostics.globals": ["Language", "print_error", "print_log", "DG", "Vector2", "Vector3", "ConfigManager", "UnityEngine", "typeof", "System", "AudioGVoice", "AudioPlayer", "AudioRecorder", "DeviceTool", "UNITY_ANDROID", "MinimapCamera", "ChannelAgent", "Color", "LuaDebugTool", "CS", "print_warning", "Quaternion", "QualityConfig", "MaterialMgr", "<PERSON><PERSON><PERSON>", "ChangeSkin", "Vector4", "ActorQingGongObject", "YYPostProcessing", "GameRoot", "XCommon", "EmojiText", "IsNIl", "UINameTable", "config_chatfilter_list", "config_usernamefilter_list", "MD52", "BackgroundDome", "<PERSON><PERSON>", "UNITY_EDITOR", "EventTriggerListener", "UtilU3d", "Nirvana", "ActorAttach<PERSON>", "DynamicBone", "Scheduler", "UIGradient", "EasyTouch", "UIVariableTable", "UIEventTable", "UIOverrideOrder", "Game", "ChannelUserInfo", "WebView", "EnhancedUI", "AccordionElement", "UIGrayscale", "ListViewCell", "ListViewDelegate", "ListViewSimpleDelegate", "ListViewPageScroll", "ListViewPageScroll2", "<PERSON><PERSON><PERSON><PERSON>", "PageViewSimpleDelegate", "UIJoystick", "UI3DDisplay", "UIPrefabLoader", "GuideMask", "MoveableObject", "ActorController", "AttachObject", "CameraFollow", "ClickManager", "ClickableObject", "UIDrag", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "AttachSkinObject", "UIFollowTarget", "UIFollowDistance", "InstantiateQueue", "AnimatorCullingMode", "RoleType", "Config_scenelist", "AdapterToLua", "StringUtil", "UI3DModel", "OverrideOrderGroupMgr", "SafeAreaAdpater", "TypeUnityPrefab", "CheckLuaConfig", "HandleRenderUnit", "ApplicationBrightness", "ShowFPS", "DownloadBufferMgr", "TableCopyCfg", "IS_IOS_OR_ANDROID", "UNITY_IOS", "u3dpool", "u3d", "LuaUInt64", "bit", "is_develop", "RichHAlignment", "CheckProtocolUseBandWidth", "MeshSprite", "RichTextGroup", "MD5", "UtilEx", "struct", "RichHAlignment", "ToLuaProfile", "LOW_GRAPHICS_MEMORY", "LUA_CALLBACK", "HttpUploader", "HttpRequester", "CalendarUnit", "LocalNotification", "HttpDownloader", "NetStateChanged", "EffectControl", "SceneOptimizeMgr", "CameraSnapshot", "CGController", "QingGongState", "use_prof_normal_skill_list", "use_prof_skill_list", "z<PERSON><PERSON>_skill_list", "chuangjue_bundle", "chuangjue_asset", "Error", "QualityBudget", "UIGrey", "EncryptMgr", "UI3DShadow", "NodePrefabList", "ImagePicker", "CameraShake", "QualityControlActive", "Projectile", "UIAttachName", "HightFogEffect", "YYWaveEffect", "CameraCullingDistance", "YYCelestialObject", "<PERSON>az<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "ShadowProjector", "PostEffects", "AssetID", "ImageTool", "ListenTrigger", "NetWorkState", "GAME_ASSETBUNDLE", "ErrorInfo", "NetClient", "develop_mode", "HeadCustomizationType", "UIGlassBlurCtrl", "CameraController", "TMPro", "FancyScrollView", "UI3DDisplay10086", "CharacterShadows"]}