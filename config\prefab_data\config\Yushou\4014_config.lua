return {
	actorController = {
		projectiles = {},

		hurts = {},

	},
	actorTriggers = {
		effects = {
			{
				triggerEventName = "attack1/begin",
				triggerDelay = 0.4,
				triggerFreeDelay = 0.0,
				effectGoName = "eff_4014yushou_attack1",
				effectAsset = {
					BundleName = "effects/prefab/model/yushou/4014/eff_4014yushou_attack1_prefab",
					AssetName = "eff_4014yushou_attack1",
				},
				playerAtTarget = false,
				referenceNodeHierarchyPath = "root",
				isAttach = true,
				isRotation = false,
				isUseCustomTransform = false,
				offsetPosX = 0.0,
				offsetPosY = 0.0,
				offsetPosZ = 0.0,
				triggerStopEvent = "",
				effectBtnName = "attack1",
				playerAtPos = false,
				ignoreParentScale = false,
			},
			{
				triggerEventName = "rest/begin",
				triggerDelay = 3.55,
				triggerFreeDelay = 0.0,
				effectGoName = "eff_4015yushou_rest",
				effectAsset = {
					BundleName = "effects/prefab/model/yushou/4015/eff_4015yushou_rest_prefab",
					AssetName = "eff_4015yushou_rest",
				},
				playerAtTarget = false,
				referenceNodeHierarchyPath = "root",
				isAttach = true,
				isRotation = false,
				isUseCustomTransform = false,
				offsetPosX = 0.0,
				offsetPosY = 0.0,
				offsetPosZ = 0.0,
				triggerStopEvent = "",
				effectBtnName = "rest",
				playerAtPos = false,
				ignoreParentScale = false,
			},
			{
				triggerEventName = "attack2/begin",
				triggerDelay = 0.7,
				triggerFreeDelay = 0.0,
				effectGoName = "yushou_skill_5",
				effectAsset = {
					BundleName = "effects/prefab/model/yushou/common/yushou_skill_5_prefab",
					AssetName = "yushou_skill_5",
				},
				playerAtTarget = false,
				referenceNodeHierarchyPath = "root",
				isAttach = true,
				isRotation = false,
				isUseCustomTransform = false,
				offsetPosX = 0.0,
				offsetPosY = 0.0,
				offsetPosZ = 0.0,
				triggerStopEvent = "",
				effectBtnName = "attack2",
				playerAtPos = false,
				ignoreParentScale = false,
			},
		},
		sounds = {},

		cameraShakes = {},

		radialBlurs = {},

	},
}