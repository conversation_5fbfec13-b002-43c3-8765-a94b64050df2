-- Y-运营活动-一生所爱.xls
local item_table={
[1]={item_id=26455,num=1,is_bind=1},
[2]={item_id=26369,num=5,is_bind=1},
[3]={item_id=26380,num=5,is_bind=1},
[4]={item_id=26345,num=5,is_bind=1},
[5]={item_id=26377,num=5,is_bind=1},
[6]={item_id=38453,num=1,is_bind=1},
[7]={item_id=37081,num=1,is_bind=1},
[8]={item_id=37074,num=1,is_bind=1},
[9]={item_id=37080,num=1,is_bind=1},
[10]={item_id=38445,num=1,is_bind=1},
[11]={item_id=48525,num=1,is_bind=1},
[12]={item_id=26450,num=1,is_bind=1},
[13]={item_id=26380,num=3,is_bind=1},
[14]={item_id=26377,num=3,is_bind=1},
[15]={item_id=56317,num=3,is_bind=1},
[16]={item_id=48544,num=1,is_bind=1},
[17]={item_id=26437,num=1,is_bind=1},
[18]={item_id=26379,num=3,is_bind=1},
[19]={item_id=26376,num=3,is_bind=1},
[20]={item_id=56316,num=3,is_bind=1},
[21]={item_id=48543,num=1,is_bind=1},
[22]={item_id=48542,num=1,is_bind=1},
[23]={item_id=48541,num=1,is_bind=1},
[24]={item_id=28874,num=1,is_bind=1},
[25]={item_id=26444,num=1,is_bind=1},
[26]={item_id=38452,num=1,is_bind=1},
[27]={item_id=48524,num=1,is_bind=1},
}

return {
other={
{}
},

other_meta_table_map={
},
task={
{},
{seq=1,param1=69,show_id=38792,},
{seq=2,param1=70,show_id=38793,},
{seq=3,param1=71,show_id=38794,},
{seq=4,param1=72,show_id=38795,},
{seq=5,param1=73,show_id=38796,},
{seq=6,param1=74,show_id=38797,},
{seq=7,reward_type=1,param1=68,reward_item={[0]=item_table[1],[1]=item_table[2],[2]=item_table[3],[3]=item_table[4],[4]=item_table[5]},show_id=38791,},
{seq=8,param1=75,show_id=38798,},
{seq=9,param1=76,show_id=38799,},
{seq=10,param1=77,show_id=38800,},
{seq=11,param1=78,show_id=38030,},
{seq=12,param1=79,show_id=38031,},
{seq=13,param1=80,show_id=38032,},
{grade=2,param1=86,show_id=38038,},
{grade=2,seq=1,param1=87,show_id=38039,},
{grade=2,seq=2,param1=88,show_id=38040,},
{grade=2,seq=3,param1=89,show_id=38041,},
{grade=2,seq=4,param1=90,show_id=38042,},
{grade=2,seq=5,param1=91,show_id=38043,},
{grade=2,seq=6,param1=92,show_id=38044,},
{grade=2,param1=93,show_id=38045,},
{grade=2,param1=94,show_id=38046,},
{grade=2,param1=95,show_id=38047,},
{grade=2,param1=96,show_id=38048,},
{grade=2,param1=97,show_id=38049,},
{grade=2,param1=98,show_id=38050,},
{seq=13,param1=99,show_id=38051,},
{grade=3,param1=110,show_id=38062,},
{grade=3,seq=1,param1=111,show_id=38063,},
{grade=3,seq=2,param1=112,show_id=38064,},
{grade=3,seq=3,param1=113,show_id=38065,},
{grade=3,seq=4,param1=114,show_id=38066,},
{grade=3,seq=5,param1=115,show_id=38067,},
{grade=3,seq=6,param1=116,show_id=38068,},
{grade=3,param1=103,show_id=38055,},
{seq=8,param1=104,show_id=38056,},
{seq=9,param1=105,show_id=38057,},
{seq=10,param1=106,show_id=38058,},
{seq=11,param1=107,show_id=38059,},
{grade=3,param1=108,show_id=38060,},
{seq=13,param1=109,show_id=38061,}
},

task_meta_table_map={
[9]=8,	-- depth:1
[10]=8,	-- depth:1
[11]=8,	-- depth:1
[12]=8,	-- depth:1
[14]=8,	-- depth:1
[13]=8,	-- depth:1
[27]=13,	-- depth:2
[26]=12,	-- depth:2
[25]=11,	-- depth:2
[24]=10,	-- depth:2
[23]=9,	-- depth:2
[22]=8,	-- depth:1
[41]=13,	-- depth:2
[36]=8,	-- depth:1
[37]=36,	-- depth:2
[38]=36,	-- depth:2
[39]=36,	-- depth:2
[40]=36,	-- depth:2
[28]=22,	-- depth:2
[42]=36,	-- depth:2
},
reward={
{},
{reward_type=1,reward_item={[0]=item_table[6]},},
{grade=2,reward_item={[0]=item_table[7]},},
{reward_type=1,reward_item={[0]=item_table[8]},},
{grade=3,reward_item={[0]=item_table[9]},},
{reward_type=1,reward_item={[0]=item_table[10]},}
},

reward_meta_table_map={
[4]=3,	-- depth:1
[6]=5,	-- depth:1
},
control={
{},
{min_day=15,max_day=22,grade=2,},
{min_day=23,max_day=30,grade=3,},
{min_day=31,max_day=38,},
{min_day=39,max_day=46,},
{min_day=47,max_day=54,},
{min_day=55,max_day=62,},
{min_day=63,max_day=70,},
{min_day=71,max_day=99999,}
},

control_meta_table_map={
[5]=2,	-- depth:1
[6]=3,	-- depth:1
[8]=2,	-- depth:1
[9]=3,	-- depth:1
},
rmb_grade={
{},
{reward_type=1,rmb_number=30,reward_item={[0]=item_table[11],[1]=item_table[12],[2]=item_table[13],[3]=item_table[14],[4]=item_table[15]},original_price=688,},
{grade=2,reward_item={[0]=item_table[16],[1]=item_table[17],[2]=item_table[18],[3]=item_table[19],[4]=item_table[20]},},
{grade=2,reward_item={[0]=item_table[21],[1]=item_table[12],[2]=item_table[13],[3]=item_table[14],[4]=item_table[15]},},
{grade=3,reward_item={[0]=item_table[22],[1]=item_table[17],[2]=item_table[18],[3]=item_table[19],[4]=item_table[20]},},
{grade=3,reward_item={[0]=item_table[23],[1]=item_table[12],[2]=item_table[13],[3]=item_table[14],[4]=item_table[15]},}
},

rmb_grade_meta_table_map={
[4]=2,	-- depth:1
[6]=2,	-- depth:1
},
rmb_control={
{},
{min_day=15,max_day=22,grade=2,},
{min_day=23,max_day=30,grade=3,},
{min_day=31,max_day=38,},
{min_day=39,max_day=46,},
{min_day=47,max_day=54,},
{min_day=55,max_day=62,},
{min_day=63,max_day=70,},
{min_day=71,max_day=99999,}
},

rmb_control_meta_table_map={
[5]=2,	-- depth:1
[6]=3,	-- depth:1
[8]=2,	-- depth:1
[9]=3,	-- depth:1
},
rmb_model={
{},
{model_seq=2,model_show_itemid=38792,},
{model_seq=3,model_show_itemid=38793,},
{model_seq=4,model_show_itemid=38794,},
{model_seq=5,model_show_itemid=38795,},
{model_seq=6,model_show_itemid=38796,},
{model_seq=7,model_show_itemid=38797,},
{reward_type=1,model_seq=8,model_show_itemid=38791,},
{model_seq=9,model_show_itemid=38798,},
{model_seq=10,model_show_itemid=38799,},
{model_seq=11,model_show_itemid=38800,},
{model_seq=12,model_show_itemid=38030,},
{model_seq=13,model_show_itemid=38031,},
{model_seq=14,model_show_itemid=38032,},
{grade=2,model_show_itemid=38038,},
{model_seq=2,model_show_itemid=38039,},
{model_seq=3,model_show_itemid=38040,},
{model_seq=4,model_show_itemid=38041,},
{model_seq=5,model_show_itemid=38042,},
{model_seq=6,model_show_itemid=38043,},
{model_seq=7,model_show_itemid=38044,},
{grade=2,model_show_itemid=38045,},
{grade=2,model_show_itemid=38046,},
{grade=2,model_show_itemid=38047,},
{grade=2,model_show_itemid=38048,},
{grade=2,model_show_itemid=38049,},
{model_seq=13,model_show_itemid=38050,},
{grade=2,model_show_itemid=38051,},
{grade=3,model_show_itemid=38062,},
{model_seq=2,model_show_itemid=38063,},
{model_seq=3,model_show_itemid=38064,},
{model_seq=4,model_show_itemid=38065,},
{model_seq=5,model_show_itemid=38066,},
{model_seq=6,model_show_itemid=38067,},
{model_seq=7,model_show_itemid=38068,},
{grade=3,model_show_itemid=38055,},
{model_seq=9,model_show_itemid=38056,},
{model_seq=10,model_show_itemid=38057,},
{model_seq=11,model_show_itemid=38058,},
{model_seq=12,model_show_itemid=38059,},
{grade=3,model_show_itemid=38060,},
{model_seq=14,model_show_itemid=38061,}
},

rmb_model_meta_table_map={
[30]=29,	-- depth:1
[31]=29,	-- depth:1
[34]=29,	-- depth:1
[33]=29,	-- depth:1
[20]=15,	-- depth:1
[35]=29,	-- depth:1
[32]=29,	-- depth:1
[19]=15,	-- depth:1
[21]=15,	-- depth:1
[17]=15,	-- depth:1
[9]=8,	-- depth:1
[10]=8,	-- depth:1
[11]=8,	-- depth:1
[12]=8,	-- depth:1
[18]=15,	-- depth:1
[14]=8,	-- depth:1
[13]=8,	-- depth:1
[16]=15,	-- depth:1
[28]=14,	-- depth:2
[26]=12,	-- depth:2
[25]=11,	-- depth:2
[24]=10,	-- depth:2
[23]=9,	-- depth:2
[22]=8,	-- depth:1
[41]=13,	-- depth:2
[36]=8,	-- depth:1
[37]=36,	-- depth:2
[38]=36,	-- depth:2
[39]=36,	-- depth:2
[40]=36,	-- depth:2
[27]=22,	-- depth:2
[42]=36,	-- depth:2
},
task_show={
{},
{task_type=1,name="月光宝盒",show_id=38453,des_title="召唤月光宝盒,等待意中人!",des="如果不能跟我喜欢的人在一起的话，就算让我做玉皇大帝我也不会开心；我的心上人是个大英雄，总有一天他会驾着七彩祥云回来娶我。",title_index=1,},
{grade=2,name="燎原赤兔",show_id=37081,des_title="身骑赤兔,称霸八荒！",des="千军万马一将在，探囊取物有何难？良驹赤兔填虎翼，方天画戟丧敌胆。",title_index=2,},
{task_type=1,grade=2,name="圣诞驯鹿",show_id=37074,des_title="弥鹿引路，寻盖世英雄!",des="天香美誉数貂蝉，闭月佳人貌似仙；千古凤仪成绝剧，连环妙计巧周旋。",title_index=3,},
{grade=3,name="地狱三头犬",show_id=37080,des_title="芸芸众生,由地狱神守护！",des="我诞生至今，只见过地狱，从未见过天堂；誓死守护地狱一方净土！",title_index=4,},
{task_type=1,grade=3,name="灵魂战车",show_id=38445,des_title="引擎一响,潇洒出场!",des="你见过凌晨4点的灵魂战车吗？我和灵魂战车一样，燃烧着我的热情和青春！",title_index=5,}
},

task_show_meta_table_map={
},
other_default_table={reward_item={[0]=item_table[24]},},

task_default_table={grade=1,seq=0,reward_type=0,task_type=3,param1=67,param2=0,get_limit=7,reward_item={[0]=item_table[25],[1]=item_table[2],[2]=item_table[3],[3]=item_table[4],[4]=item_table[5]},show_id=38790,},

reward_default_table={grade=1,reward_type=0,reward_item={[0]=item_table[26]},},

control_default_table={min_day=1,max_day=14,grade=1,},

rmb_grade_default_table={grade=1,reward_type=0,rmb_number=6,buy_limit=7,reward_item={[0]=item_table[27],[1]=item_table[17],[2]=item_table[18],[3]=item_table[19],[4]=item_table[20]},original_price=98,rmb_type=160,},

rmb_control_default_table={min_day=1,max_day=14,grade=1,},

rmb_model_default_table={grade=1,reward_type=0,model_seq=1,model_show_type=1,model_bundle_name="",model_asset_name="",model_show_itemid=38790,display_pos="0|100",rotation="0|0|0",display_scale=0.88,},

task_show_default_table={task_type=0,grade=1,name="七彩祥云",show_id=38452,des_title="脚踏七彩祥云,大圣抢亲！",des="如果上天能够给我一个再来一次的机会,我会对那女孩子说三个字：“我爱你！”如果非要在这份爱加上一个期限,我希望是一万年！",title_index=0,}

}

