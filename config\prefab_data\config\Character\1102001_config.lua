return {
	actorController = {
		projectiles = {},

		hurts = {},

		beHurtEffecct = {},

		hurtEffectName = "",
		beHurtNodeName = "",
		beHurtAttach = false,
		hurtEffectFreeDelay = 0.0,
		QualityCtrlList = {},

	},
	actorTriggers = {
		effects = {},

		halts = {},

		sounds = {
			{
				soundEventName = "combo1_1/begin",
				soundDelay = 0.0,
				soundAudioAsset = {
					BundleName = "audios/sfxs/roleskill/role1",
					AssetName = "role1_attack1",
				},
				soundAudioGoName = "role1_attack1",
				soundBtnName = "combo2_1",
				soundIsMainRole = false,
			},
			{
				soundEventName = "combo1_2/begin",
				soundDelay = 0.0,
				soundAudioAsset = {
					BundleName = "audios/sfxs/roleskill/role1",
					AssetName = "role1_attack2",
				},
				soundAudioGoName = "role1_attack2",
				soundBtnName = "combo2_2",
				soundIsMainRole = false,
			},
			{
				soundEventName = "combo1_3/begin",
				soundDelay = 0.0,
				soundAudioAsset = {
					BundleName = "audios/sfxs/roleskill/role1",
					AssetName = "role1_attack3",
				},
				soundAudioGoName = "role1_attack3",
				soundBtnName = "combo2_3",
				soundIsMainRole = false,
			},
			{
				soundEventName = "combo1_4/begin",
				soundDelay = 0.0,
				soundAudioAsset = {
					BundleName = "audios/sfxs/roleskill/role1",
					AssetName = "role1_attack4",
				},
				soundAudioGoName = "role1_attack4",
				soundBtnName = "combo2_4",
				soundIsMainRole = false,
			},
			{
				soundEventName = "attack1/begin",
				soundDelay = 0.0,
				soundAudioAsset = {
					BundleName = "audios/sfxs/roleskill/role1",
					AssetName = "role1_skill1",
				},
				soundAudioGoName = "role1_skill1",
				soundBtnName = "attack1",
				soundIsMainRole = false,
			},
			{
				soundEventName = "attack2/begin",
				soundDelay = 0.0,
				soundAudioAsset = {
					BundleName = "audios/sfxs/roleskill/role1",
					AssetName = "role1_skill2",
				},
				soundAudioGoName = "role1_skill2",
				soundBtnName = "attack2",
				soundIsMainRole = false,
			},
			{
				soundEventName = "attack3/begin",
				soundDelay = 0.0,
				soundAudioAsset = {
					BundleName = "audios/sfxs/roleskill/role1",
					AssetName = "role1_skill3",
				},
				soundAudioGoName = "role1_skill3",
				soundBtnName = "attack3",
				soundIsMainRole = false,
			},
			{
				soundEventName = "attack4/begin",
				soundDelay = 0.0,
				soundAudioAsset = {
					BundleName = "audios/sfxs/roleskill/role1",
					AssetName = "role1_skill4",
				},
				soundAudioGoName = "role1_skill4",
				soundBtnName = "attack4",
				soundIsMainRole = false,
			},
			{
				soundEventName = "attack5/begin",
				soundDelay = 0.0,
				soundAudioAsset = {
					BundleName = "audios/sfxs/roleskill/jineng",
					AssetName = "role1_attack5",
				},
				soundAudioGoName = "role1_attack5",
				soundBtnName = "attack5",
				soundIsMainRole = false,
			},
			{
				soundEventName = "attack6/begin",
				soundDelay = 0.0,
				soundAudioAsset = {
					BundleName = "audios/sfxs/roleskill/jineng",
					AssetName = "role1_attack6",
				},
				soundAudioGoName = "role1_attack6",
				soundBtnName = "attack6",
				soundIsMainRole = false,
			},
			{
				soundEventName = "attack9/begin",
				soundDelay = 0.0,
				soundAudioAsset = {
					BundleName = "audios/sfxs/roleskill/jineng",
					AssetName = "role1_attack7",
				},
				soundAudioGoName = "role1_attack7",
				soundBtnName = "attack9",
				soundIsMainRole = false,
			},
			{
				soundEventName = "attack10/begin",
				soundDelay = 0.0,
				soundAudioAsset = {
					BundleName = "audios/sfxs/roleskill/jineng",
					AssetName = "role1_attack8",
				},
				soundAudioGoName = "role1_attack8",
				soundBtnName = "attack10",
				soundIsMainRole = false,
			},
			{
				soundEventName = "attack11/begin",
				soundDelay = 0.0,
				soundAudioAsset = {
					BundleName = "audios/sfxs/roleskill/jineng",
					AssetName = "role1_attack9",
				},
				soundAudioGoName = "role1_attack9",
				soundBtnName = "attack11",
				soundIsMainRole = false,
			},
			{
				soundEventName = "attack12/begin",
				soundDelay = 0.0,
				soundAudioAsset = {
					BundleName = "audios/sfxs/roleskill/jineng",
					AssetName = "role1_attack10",
				},
				soundAudioGoName = "role1_attack10",
				soundBtnName = "attack12",
				soundIsMainRole = false,
			},
			{
				soundEventName = "esoterica_attack_1/begin",
				soundDelay = 0.0,
				soundAudioAsset = {
					BundleName = "audios/sfxs/roleskill/jineng",
					AssetName = "miji1",
				},
				soundAudioGoName = "miji1",
				soundBtnName = "miji1",
				soundIsMainRole = false,
			},
			{
				soundEventName = "esoterica_attack_2/begin",
				soundDelay = 0.0,
				soundAudioAsset = {
					BundleName = "audios/sfxs/roleskill/jineng",
					AssetName = "miji2",
				},
				soundAudioGoName = "miji2",
				soundBtnName = "miji2",
				soundIsMainRole = false,
			},
			{
				soundEventName = "esoterica_attack_3/begin",
				soundDelay = 0.0,
				soundAudioAsset = {
					BundleName = "audios/sfxs/roleskill/jineng",
					AssetName = "miji3",
				},
				soundAudioGoName = "miji3",
				soundBtnName = "miji3",
				soundIsMainRole = false,
			},
			{
				soundEventName = "esoterica_attack_4/begin",
				soundDelay = 0.0,
				soundAudioAsset = {
					BundleName = "audios/sfxs/roleskill/jineng",
					AssetName = "miji4",
				},
				soundAudioGoName = "miji4",
				soundBtnName = "miji4",
				soundIsMainRole = false,
			},
			{
				soundEventName = "esoterica_attack_5/begin",
				soundDelay = 0.0,
				soundAudioAsset = {
					BundleName = "audios/sfxs/roleskill/jineng",
					AssetName = "miji5",
				},
				soundAudioGoName = "miji5",
				soundBtnName = "miji5",
				soundIsMainRole = false,
			},
			{
				soundEventName = "esoterica_attack_6/begin",
				soundDelay = 0.0,
				soundAudioAsset = {
					BundleName = "audios/sfxs/roleskill/jineng",
					AssetName = "miji6",
				},
				soundAudioGoName = "miji6",
				soundBtnName = "miji6",
				soundIsMainRole = false,
			},
			{
				soundEventName = "esoterica_attack_7/begin",
				soundDelay = 0.0,
				soundAudioAsset = {
					BundleName = "audios/sfxs/roleskill/jineng",
					AssetName = "miji7",
				},
				soundAudioGoName = "miji7",
				soundBtnName = "miji7",
				soundIsMainRole = false,
			},
		},
		cameraShakes = {
			{
				CameraShakeBtnName = "attack1_1_shake",
				eventName = "attack1/begin",
				numberOfShakes = 1,
				distance = 0.2,
				speed = 300.0,
				delay = 0.4,
				decay = 0.0,
			},
			{
				CameraShakeBtnName = "attack2_1_shake",
				eventName = "attack2/begin",
				numberOfShakes = 8,
				distance = 0.3,
				speed = 200.0,
				delay = 1.4,
				decay = 0.01,
			},
			{
				CameraShakeBtnName = "combo2_4_shake",
				eventName = "combo1_4/begin",
				numberOfShakes = 4,
				distance = 0.2,
				speed = 200.0,
				delay = 0.5,
				decay = 0.0,
			},
			{
				CameraShakeBtnName = "attack3_1_shake",
				eventName = "attack3/begin",
				numberOfShakes = 20,
				distance = 0.15,
				speed = 100.0,
				delay = 0.1,
				decay = 0.1,
			},
			{
				CameraShakeBtnName = "attack1_2_shake",
				eventName = "attack1/begin",
				numberOfShakes = 5,
				distance = 0.1,
				speed = 100.0,
				delay = 1.0,
				decay = 0.0,
			},
			{
				CameraShakeBtnName = "attack3_2_shake",
				eventName = "attack3/begin",
				numberOfShakes = 8,
				distance = 0.6,
				speed = 160.0,
				delay = 1.7,
				decay = 0.1,
			},
			{
				CameraShakeBtnName = "attack4_1_shake",
				eventName = "attack4/begin",
				numberOfShakes = 5,
				distance = 0.5,
				speed = 100.0,
				delay = 1.0,
				decay = 0.02,
			},
			{
				CameraShakeBtnName = "attack4_2_shake",
				eventName = "attack4/begin",
				numberOfShakes = 10,
				distance = 1.0,
				speed = 150.0,
				delay = 1.6,
				decay = 0.15,
			},
			{
				CameraShakeBtnName = "attack5_shake",
				eventName = "attack5/begin",
				numberOfShakes = 20,
				distance = 0.5,
				speed = 500.0,
				delay = 1.0,
				decay = 0.0,
			},
			{
				CameraShakeBtnName = "attack9_shake",
				eventName = "attack9/begin",
				numberOfShakes = 8,
				distance = 0.5,
				speed = 500.0,
				delay = 1.8,
				decay = 0.0,
			},
			{
				CameraShakeBtnName = "attack9_1shake",
				eventName = "attack9/begin",
				numberOfShakes = 50,
				distance = 0.5,
				speed = 4000.0,
				delay = 2.5,
				decay = 0.0,
			},
			{
				CameraShakeBtnName = "attack10_shake",
				eventName = "attack10/begin",
				numberOfShakes = 8,
				distance = 0.5,
				speed = 400.0,
				delay = 2.5,
				decay = 0.0,
			},
			{
				CameraShakeBtnName = "attack11_shake",
				eventName = "attack11/begin",
				numberOfShakes = 20,
				distance = 0.5,
				speed = 500.0,
				delay = 2.5,
				decay = 0.0,
			},
			{
				CameraShakeBtnName = "attack12_shake",
				eventName = "attack12/begin",
				numberOfShakes = 30,
				distance = 0.5,
				speed = 500.0,
				delay = 2.0,
				decay = 0.0,
			},
		},
		cameraFOVs = {},

		sceneFades = {},

		footsteps = {},

	},
	actorBlinker = {
		blinkFadeIn = 0.0,
		blinkFadeHold = 0.0,
		blinkFadeOut = 0.0,
	},
	TimeLineList = {},

}