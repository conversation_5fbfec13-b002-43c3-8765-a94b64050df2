return {
	actorController = {
		projectiles = {},

		hurts = {},

	},
	actorTriggers = {
		effects = {
			{
				triggerEventName = "attack2/begin",
				triggerDelay = 0.3,
				triggerFreeDelay = 0.0,
				effectGoName = "yushou_skill_5",
				effectAsset = {
					BundleName = "effects/prefab/model/yushou/common/yushou_skill_5_prefab",
					AssetName = "yushou_skill_5",
				},
				playerAtTarget = false,
				referenceNodeHierarchyPath = "root",
				isAttach = true,
				isRotation = false,
				isUseCustomTransform = true,
				offsetPosX = 0.0,
				offsetPosY = 0.0,
				offsetPosZ = 0.0,
				triggerStopEvent = "",
				effectBtnName = "attack2",
				playerAtPos = false,
				ignoreParentScale = true,
			},
			{
				triggerEventName = "attack1/begin",
				triggerDelay = 0.0,
				triggerFreeDelay = 0.0,
				effectGoName = "eff_4034yushou_attcak1",
				effectAsset = {
					BundleName = "effects/prefab/model/yushou/4034/eff_4034yushou_attcak1_prefab",
					AssetName = "eff_4034yushou_attcak1",
				},
				playerAtTarget = false,
				referenceNodeHierarchyPath = "root",
				isAttach = true,
				isRotation = false,
				isUseCustomTransform = true,
				offsetPosX = 0.0,
				offsetPosY = 0.0,
				offsetPosZ = 0.0,
				triggerStopEvent = "",
				effectBtnName = "attack1",
				playerAtPos = false,
				ignoreParentScale = true,
			},
			{
				triggerEventName = "rest/begin",
				triggerDelay = 1.0,
				triggerFreeDelay = 0.0,
				effectGoName = "eff_4034yushou_attcak1",
				effectAsset = {
					BundleName = "effects/prefab/model/yushou/4034/eff_4034yushou_attcak1_prefab",
					AssetName = "eff_4034yushou_attcak1",
				},
				playerAtTarget = false,
				referenceNodeHierarchyPath = "root",
				isAttach = true,
				isRotation = false,
				isUseCustomTransform = true,
				offsetPosX = 0.5,
				offsetPosY = -0.9,
				offsetPosZ = -4.0,
				triggerStopEvent = "",
				effectBtnName = "rest",
				playerAtPos = false,
				ignoreParentScale = false,
			},
		},
		sounds = {},

		cameraShakes = {},

		radialBlurs = {},

	},
}